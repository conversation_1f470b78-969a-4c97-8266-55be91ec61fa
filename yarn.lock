# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@alloc/quick-lru@^5.2.0":
  version "5.2.0"
  resolved "https://registry.npmjs.org/@alloc/quick-lru/-/quick-lru-5.2.0.tgz"
  integrity sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==

"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz"
  integrity sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.25.9", "@babel/code-frame@^7.26.0":
  version "7.26.2"
  resolved "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.26.2.tgz"
  integrity sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==
  dependencies:
    "@babel/helper-validator-identifier" "^7.25.9"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/compat-data@^7.25.9":
  version "7.26.2"
  resolved "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.26.2.tgz"
  integrity sha512-Z0WgzSEa+aUcdiJuCIqgujCshpMWgUpgOxXotrYPSA53hA3qopNaqcJpyr0hVb1FeWdnqFA35/fUtXgBK8srQg==

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.14.8", "@babel/core@^7.19.6":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@babel/core/-/core-7.26.0.tgz"
  integrity sha512-i1SLeK+DzNnQ3LL/CswPCa/E5u4lh1k6IAEphON8F+cXt0t9euTshDru0q7/IqMa1PMPz5RnHuHscF8/ZJsStg==
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.26.0"
    "@babel/generator" "^7.26.0"
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-module-transforms" "^7.26.0"
    "@babel/helpers" "^7.26.0"
    "@babel/parser" "^7.26.0"
    "@babel/template" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.26.0"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.25.9", "@babel/generator@^7.26.0":
  version "7.26.2"
  resolved "https://registry.npmjs.org/@babel/generator/-/generator-7.26.2.tgz"
  integrity sha512-zevQbhbau95nkoxSq3f/DC/SC+EEOUZd3DYqfSkMhY2/wfSeaHV1Ew4vk8e+x8lja31IbyuUa2uQ3JONqKbysw==
  dependencies:
    "@babel/parser" "^7.26.2"
    "@babel/types" "^7.26.0"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^3.0.2"

"@babel/helper-annotate-as-pure@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.25.9.tgz"
  integrity sha512-gv7320KBUFJz1RnylIg5WWYPRXKZ884AGkYpgpWW02TH66Dl+HaC1t1CKd0z3R4b6hdYEcmrNZHUmfCP+1u3/g==
  dependencies:
    "@babel/types" "^7.25.9"

"@babel/helper-compilation-targets@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.25.9.tgz"
  integrity sha512-j9Db8Suy6yV/VHa4qzrj9yZfZxhLWQdVnRlXxmKLYlhWUVB1sB2G5sxuWYXk/whHD9iW76PmNzxZ4UCnTQTVEQ==
  dependencies:
    "@babel/compat-data" "^7.25.9"
    "@babel/helper-validator-option" "^7.25.9"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-module-imports@^7.16.7", "@babel/helper-module-imports@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.25.9.tgz"
  integrity sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-module-transforms@^7.26.0":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.26.0.tgz"
  integrity sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-plugin-utils@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.25.9.tgz"
  integrity sha512-kSMlyUVdWe25rEsRGviIgOWnoT/nfABVWlqt9N19/dIPWViAOW2s9wznP5tURbs/IDuNk4gPy3YdYRgH3uxhBw==

"@babel/helper-string-parser@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.25.9.tgz"
  integrity sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==

"@babel/helper-validator-identifier@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.9.tgz"
  integrity sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==

"@babel/helper-validator-option@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.25.9.tgz"
  integrity sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==

"@babel/helpers@^7.26.0":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@babel/helpers/-/helpers-7.26.0.tgz"
  integrity sha512-tbhNuIxNcVb21pInl3ZSjksLCvgdZy9KwJ8brv993QtIVKJBBkYXz4q4ZbAv31GdnC+R90np23L5FbEBlthAEw==
  dependencies:
    "@babel/template" "^7.25.9"
    "@babel/types" "^7.26.0"

"@babel/parser@^7.25.9", "@babel/parser@^7.26.0", "@babel/parser@^7.26.2":
  version "7.26.2"
  resolved "https://registry.npmjs.org/@babel/parser/-/parser-7.26.2.tgz"
  integrity sha512-DWMCZH9WA4Maitz2q21SRKHo9QXZxkDsbNZoVD62gusNtNBBqDg9i7uOhASfTfIGNzW+O+r7+jAlM8dwphcJKQ==
  dependencies:
    "@babel/types" "^7.26.0"

"@babel/plugin-syntax-jsx@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.25.9.tgz"
  integrity sha512-ld6oezHQMZsZfp6pWtbjaNDF2tiiCYYDqQszHt5VV437lewP9aSi2Of99CK0D0XB21k7FLgnLcmQKyKzynfeAA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-react-jsx-development@^7.18.6":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.25.9.tgz"
  integrity sha512-9mj6rm7XVYs4mdLIpbZnHOYdpW42uoiBCTVowg7sP1thUOiANgMb4UtpRivR0pp5iL+ocvUv7X4mZgFRpJEzGw==
  dependencies:
    "@babel/plugin-transform-react-jsx" "^7.25.9"

"@babel/plugin-transform-react-jsx-self@^7.14.5", "@babel/plugin-transform-react-jsx-self@^7.18.6":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.25.9.tgz"
  integrity sha512-y8quW6p0WHkEhmErnfe58r7x0A70uKphQm8Sp8cV7tjNQwK56sNVK0M73LK3WuYmsuyrftut4xAkjjgU0twaMg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-react-jsx-source@^7.14.5", "@babel/plugin-transform-react-jsx-source@^7.19.6":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.25.9.tgz"
  integrity sha512-+iqjT8xmXhhYv4/uiYd8FNQsraMFZIfxVSqxxVSZP0WbbSAWvBXAul0m/zu+7Vv4O/3WtApy9pmaTMiumEZgfg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-react-jsx@^7.19.0", "@babel/plugin-transform-react-jsx@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.25.9.tgz"
  integrity sha512-s5XwpQYCqGerXl+Pu6VDL3x0j2d82eiV77UJ8a2mDHAW7j9SWRqQ2y1fNo1Z74CdcYipl5Z41zvjj4Nfzq36rw==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/plugin-syntax-jsx" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/runtime@^7.12.0", "@babel/runtime@^7.12.1", "@babel/runtime@^7.12.5", "@babel/runtime@^7.13.8", "@babel/runtime@^7.15.4", "@babel/runtime@^7.18.3", "@babel/runtime@^7.20.13", "@babel/runtime@^7.20.7", "@babel/runtime@^7.23.8", "@babel/runtime@^7.25.0", "@babel/runtime@^7.27.1", "@babel/runtime@^7.5.5", "@babel/runtime@^7.6.3", "@babel/runtime@^7.8.7", "@babel/runtime@^7.9.2":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.1.tgz"
  integrity sha512-1x3D2xEk2fRo3PAhwQwu5UubzgiVWSXTBfWpVd2Mx2AzRqJuDJCsgaDVZ7HB5iGzDW1Hl1sWN2mFyKjmR9uAog==

"@babel/template@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/template/-/template-7.25.9.tgz"
  integrity sha512-9DGttpmPvIxBb/2uwpVo3dqJ+O6RooAFOS+lB+xDqoE2PVCE8nfoHMdZLpfCQRLwvohzXISPZcgxt80xLfsuwg==
  dependencies:
    "@babel/code-frame" "^7.25.9"
    "@babel/parser" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/traverse@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/traverse/-/traverse-7.25.9.tgz"
  integrity sha512-ZCuvfwOwlz/bawvAuvcj8rrithP2/N55Tzz342AkTvq4qaWbGfmCk/tKhNaV2cthijKrPAA8SRJV5WWe7IBMJw==
  dependencies:
    "@babel/code-frame" "^7.25.9"
    "@babel/generator" "^7.25.9"
    "@babel/parser" "^7.25.9"
    "@babel/template" "^7.25.9"
    "@babel/types" "^7.25.9"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.25.9", "@babel/types@^7.26.0":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@babel/types/-/types-7.26.0.tgz"
  integrity sha512-Z/yiTPj+lDVnF7lWeKCIJzaIkI0vYO87dMpZ4bg4TDrFe4XXLFWL1TbXU27gBP3QccxV9mZICCrnjnYlJjXHOA==
  dependencies:
    "@babel/helper-string-parser" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"

"@ckeditor/ckeditor-cloud-services-collaboration@52.7.0":
  version "52.7.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor-cloud-services-collaboration/-/ckeditor-cloud-services-collaboration-52.7.0.tgz"
  integrity sha512-e7VeippO5zybYjf2HS/ckHXqbQshu1UeWtplQwO9ix7vD6jRFYr4xtPAIXhjZ7FDODnH5M4ndiYivyZJdcIY1w==
  dependencies:
    protobufjs "7.4.0"
    socket.io-client "4.7.0"
    socket.io-parser "~4.2.3"
    url-parse "1.5.10"
    uuid "^9.0.1"

"@ckeditor/ckeditor5-adapter-ckfinder@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-adapter-ckfinder/-/ckeditor5-adapter-ckfinder-43.3.1.tgz"
  integrity sha512-fOnEq31euR9B/awWZCOc8KfgLwwG4ACtqBhSv7Hu6VOgHa5TKWyWAdhr9ILSiUp7NMfYJoTQStbxcXZIWPqQXQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-upload" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-adapter-ckfinder@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-adapter-ckfinder/-/ckeditor5-adapter-ckfinder-45.0.0.tgz"
  integrity sha512-dv+BOds8K86QTEWETvEdbRyvP54z8v//pcBNq0ewHlUUU7dWiOYEiiRITYRO7T0x3HzzmYg+nL9nGU90w469sw==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-upload" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-alignment@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-alignment/-/ckeditor5-alignment-43.3.1.tgz"
  integrity sha512-E+04zNdNBFDNgQajrWl8iFQqA1sB29y/XDFFRK+bzhcUaWdMadr88yodjHHdcax8/zI+GzBElCvWGEGchyrL+Q==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-alignment@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-alignment/-/ckeditor5-alignment-45.0.0.tgz"
  integrity sha512-5v93RZT9E5p1vRjPpsG80lYG4K+41uNxsUcBy5/gq5CVR7gWm6X12H2l8Z7RQaGlTe89JWhCscFi3MSXS4QRNg==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-autoformat@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-autoformat/-/ckeditor5-autoformat-43.3.1.tgz"
  integrity sha512-hSQxIXIObrMfxijMPmz8odOtz/wD5SwuGZWVoF5km3EtRQxZwAcQr1Vjy+VHHPo6PZ+o3YoLP+IHCaULtNobYg==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-typing" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-autoformat@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-autoformat/-/ckeditor5-autoformat-45.0.0.tgz"
  integrity sha512-0duphMlUFDSqtNtI2PLsle4lpxDgH00znXA6hEaUuvvWCOAp8SyJ2fOFm19YmxEDcH8svYAE/sfoSn7o+Lm64w==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-heading" "45.0.0"
    "@ckeditor/ckeditor5-typing" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-autosave@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-autosave/-/ckeditor5-autosave-43.3.1.tgz"
  integrity sha512-28667m7ea0wBZMb3uIzgipanB4DrDvKn4o+mRUDExlRT8M14vn1u/ILX8ZJy28Rihbg2wPcVh6rP3zoQjcucHw==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-autosave@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-autosave/-/ckeditor5-autosave-45.0.0.tgz"
  integrity sha512-Byu86aceBmki0bxMFXtxq7M9iQB3pppuHq7YuTd7GsAbY7PjZdxs5vWLSFQ0pWL1L8lpEnmiwD2DxRhFjMeTpw==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-basic-styles@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-basic-styles/-/ckeditor5-basic-styles-43.3.1.tgz"
  integrity sha512-1RBnPmgsIoxPL7wZhId2KsfPujITbEAfzHhi0c6m4kuWlkmcVXYldWvUvCvAUguAznx4LOxhKlp6RdFSPTFTbg==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-typing" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-basic-styles@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-basic-styles/-/ckeditor5-basic-styles-45.0.0.tgz"
  integrity sha512-eryGuvf6BXic7AHfBwrjrWCUvgDLNmHGiOZ29ZUtLr8ahNnG4e1LpJ7RkzadbRo8MCdvrY9vDk00hUxjEyfxVg==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-typing" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-block-quote@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-block-quote/-/ckeditor5-block-quote-43.3.1.tgz"
  integrity sha512-cgY4GKwMlIVLnhszPoc1ortp+T/s3TLowrwRFtWYxTKSsHWBGFlZUL6oMASPunpXvvJqHcgnKlCMxVSh2VMCkQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-enter" "43.3.1"
    "@ckeditor/ckeditor5-typing" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-block-quote@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-block-quote/-/ckeditor5-block-quote-45.0.0.tgz"
  integrity sha512-9QOOHMouSa9YfP/a+yIxeUZXsgDpBVd1kgDwE8XwoE+n1PY4KlWo5GErEoSfapueNOubRfFX+dSnksmwKABUqA==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-enter" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-typing" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-bookmark@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-bookmark/-/ckeditor5-bookmark-45.0.0.tgz"
  integrity sha512-l5CsvSWxfhMSQOOiU/S2g4ZiRtc79Vi6EZIGeKhBACdFTtoFTvQjU03Edi8gPa37o519/2cldE4rWKizILDXAw==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-link" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@ckeditor/ckeditor5-widget" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-ckbox@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-ckbox/-/ckeditor5-ckbox-43.3.1.tgz"
  integrity sha512-KObL9w/QBWJi0lG2zfm+x124Kzd7aVt+UaJHJEwsAPwhZvqM0LCUeR6wwb0oCN6ph5qrCjXoj09z7z8Txk5IwA==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-upload" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    blurhash "2.0.5"
    ckeditor5 "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-ckbox@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-ckbox/-/ckeditor5-ckbox-45.0.0.tgz"
  integrity sha512-BrB13JVOU/uPSSqQmxbd6LJP/PDtlvCBTrS9Erxa6//U2ZXvfgA+Dova4ghS3sB60w6eTr3JgGAhp2hAi9J/XQ==
  dependencies:
    "@ckeditor/ckeditor5-cloud-services" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-image" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-upload" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    blurhash "2.0.5"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-ckfinder@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-ckfinder/-/ckeditor5-ckfinder-43.3.1.tgz"
  integrity sha512-Yji6c1/0H5fExDcT+NNyQQePx2cd8Ul1Xuko1UVmsLN2Vhi7VIDJjEkCFndJozd8VQqI62Obe1GTyjmapBV5+A==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-ckfinder@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-ckfinder/-/ckeditor5-ckfinder-45.0.0.tgz"
  integrity sha512-h/jHx49B4sKYt2uGBUTc4gihPRrk4BTK9mYjxbMhAx5tJzt6P5rtmRpQ6rAWXaqIHeZZit+geOk32GEd9fVDIw==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-image" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-clipboard@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-clipboard/-/ckeditor5-clipboard-43.3.1.tgz"
  integrity sha512-Ke6fVEy1fF3AWHMtKvF1pAoDYBVOG4q+gDHD8+dcV6KPK1uA/CR0mw6TZsslQQquT4jC79y05IWu2bq1Mxv01w==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    "@ckeditor/ckeditor5-widget" "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-clipboard@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-clipboard/-/ckeditor5-clipboard-45.0.0.tgz"
  integrity sha512-93iWIiMKY6OhhTNZUqCQVSy4MAWyWsdLWGKEn0AUMZpMOsP3CzCWE5Ot29Nool9IUzp1o1z54sWYPIDLliJvuw==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@ckeditor/ckeditor5-widget" "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-cloud-services@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-cloud-services/-/ckeditor5-cloud-services-43.3.1.tgz"
  integrity sha512-JppySF+uWedDXPTVZBsTfZCe3qedDAdWSgw0Ww/qi4/sPFcgf/MaQ0LBHbl2Ii7JlJjng82F1F2kv9Ny/Rkauw==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-cloud-services@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-cloud-services/-/ckeditor5-cloud-services-45.0.0.tgz"
  integrity sha512-udnFuhJjNjC30e7pqn3rsWwMhyLkhIK3AnwY2QmSUKsD0vfxyUKvhWyxM5KGGD/6zrbHZfOi4bOf2U1F6RYvKg==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-code-block@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-code-block/-/ckeditor5-code-block-43.3.1.tgz"
  integrity sha512-UGhGCPNfFXLua0TmszLSWX6BlkemaPULN1EZ+FBPsUZb757qWWWVWI9GKLmAc4jSPqOv+azU+JAZJzX9bE1oYA==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "43.3.1"
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-enter" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-code-block@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-code-block/-/ckeditor5-code-block-45.0.0.tgz"
  integrity sha512-SSQiVDjhZ+CulRaS8reCsa0ILq9TR0BnyNrGsHxESkKLqn9FiN3CKz63YHnutAk/ZCVcjW9zDozdfgaAKKPCAQ==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-enter" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-collaboration-core@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-collaboration-core/-/ckeditor5-collaboration-core-45.0.0.tgz"
  integrity sha512-p/v9M0Cr1YYBMZhTmcwhMksID5sVTUd5TRr6CnIlAgLdXJ8d9YBJPCPycMQbtBHKO2lejoVsNn74GhvdZLEo3Q==
  dependencies:
    "@ckeditor/ckeditor5-comments" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-theme-lark" "45.0.0"
    "@ckeditor/ckeditor5-track-changes" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@types/luxon" "3.4.2"
    ckeditor5 "45.0.0"
    luxon "3.5.0"

"@ckeditor/ckeditor5-comments@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-comments/-/ckeditor5-comments-45.0.0.tgz"
  integrity sha512-2spVMto4FvzruMEcxEpXetasOdpN8bSGLEENucEf+vLBrRZ4Ck2fWBwQQKEDyrLfgjrmnI1tXvJIVUQDXROTcA==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "45.0.0"
    "@ckeditor/ckeditor5-collaboration-core" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-enter" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-paragraph" "45.0.0"
    "@ckeditor/ckeditor5-select-all" "45.0.0"
    "@ckeditor/ckeditor5-source-editing" "45.0.0"
    "@ckeditor/ckeditor5-theme-lark" "45.0.0"
    "@ckeditor/ckeditor5-typing" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-undo" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@ckeditor/ckeditor5-widget" "45.0.0"
    ckeditor5 "45.0.0"
    ckeditor5-collaboration "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-core@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-core/-/ckeditor5-core-43.3.1.tgz"
  integrity sha512-6pil2OF4auF3PKrg1Oa86CqC91ZYc+NuHih0ebM0JW/I06d+0smnJg5dw4yN7mKbghbJS8mNrusxA5cf6Hkh6w==
  dependencies:
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    "@ckeditor/ckeditor5-watchdog" "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-core@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-core/-/ckeditor5-core-45.0.0.tgz"
  integrity sha512-+0RDW4ZpEnIEv6yLP3JJ3i0Jelcr71lSsVU7u9Wmnm/0jQF7u/QI4YrvOGBmlyLQ5Ji5d/SNCnOF8CXS+Amtrg==
  dependencies:
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@ckeditor/ckeditor5-watchdog" "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-document-outline@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-document-outline/-/ckeditor5-document-outline-45.0.0.tgz"
  integrity sha512-c3uys8i42EhF9IfCJaDfMqcMCw7Ud28l2RDes5ru6BGr+tjY4eDP5MEardgt8IQbqNaTX/ZfoM6VosZFb+GKKg==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-heading" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@ckeditor/ckeditor5-widget" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-easy-image@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-easy-image/-/ckeditor5-easy-image-43.3.1.tgz"
  integrity sha512-Cd5NojL0Vfa1SQj6uzbP3oSHvQY5ys2hXF/2jNsYKLePTCybSvGkg5REv1JifM6kSNRH1VXdad7a2LkqvXnCnA==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-upload" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-easy-image@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-easy-image/-/ckeditor5-easy-image-45.0.0.tgz"
  integrity sha512-KRZf/mcLGUYgmfwuBnCSW5DyVb9JpUjc+UCeFDdPtu0jWbVXgulV0xVpP6R/7NKdenS3PfkBvr1CzJR8GvBAaw==
  dependencies:
    "@ckeditor/ckeditor5-cloud-services" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-upload" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-editor-balloon@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-balloon/-/ckeditor5-editor-balloon-43.3.1.tgz"
  integrity sha512-klS1FZG29nJE/XbfRXrXtwYU/9uCFdi7xGbYfaJnmyNt54h46aiquKacosbiffA87Tr5sT3Oqm3dBbNlsU158w==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-editor-balloon@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-balloon/-/ckeditor5-editor-balloon-45.0.0.tgz"
  integrity sha512-jE+64/8bhA3h/Tut8o4cJu4PHIq/EnhROZmcEdDwoPyon+oBKTPoyUEW5a3w4f/6OvNBA1OWaEo23lZeP2ka2g==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-editor-classic@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-classic/-/ckeditor5-editor-classic-43.3.1.tgz"
  integrity sha512-wjBeXUQBuvz6CmGlb5XncJ9cHE7tozU6eoorycfSTQCzqr5uE57LWTlKclU42w7MgS2ya5V2kLnncr0ZqrZ2Vw==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-editor-classic@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-classic/-/ckeditor5-editor-classic-45.0.0.tgz"
  integrity sha512-7AHVXI/ElCnaP6omdFNaKwaJimlIxKZ+UWV3Lk5VBwroTrqjJYQy4Mav2PRaCSE1INAlBMAdUWdUgaiXlmhOxQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-editor-decoupled@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-decoupled/-/ckeditor5-editor-decoupled-43.3.1.tgz"
  integrity sha512-aw2iZ+WCcCu9sUAnsHhsXZWLeVPyiLhZfpZDuEWjPlvsrCfT0RfSuwMcfx7l9PREA09VR8+6MTstm61EG8dmWQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-editor-decoupled@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-decoupled/-/ckeditor5-editor-decoupled-45.0.0.tgz"
  integrity sha512-SbIrts0JHj7hRg7SbNUrIyd7KLKgKEp/Cr3yoKA2+loMGT9clKUfhiB/ccs3yZ+dChGQk0Y4JIUV5D3YJqdSXw==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-editor-inline@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-inline/-/ckeditor5-editor-inline-43.3.1.tgz"
  integrity sha512-3iZiWl2aM1bCnS52NeBoAqCVowABhWrBlns27JEGKZ+LNPZroMie7uKuMX3YQGYE2awFnsyP6XofoJtu6CcKCA==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-editor-inline@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-inline/-/ckeditor5-editor-inline-45.0.0.tgz"
  integrity sha512-B0Mh8Wm02OD+hWGZSeG9zpLrGBeBR9fsHApi+3wvc2u9FdpdCG2mJHGxtsHvyc1MnOFjn3JYl51kqFT8Cc5v4g==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-editor-multi-root@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-multi-root/-/ckeditor5-editor-multi-root-43.3.1.tgz"
  integrity sha512-HDgfTuotrHW91AZ+x+lumwo1tngRRZ87dnHT8kjSRFWAeXPSd2Kw986++Oj9K080+idZaYLF+IutAOqvCT32sw==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-editor-multi-root@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-multi-root/-/ckeditor5-editor-multi-root-45.0.0.tgz"
  integrity sha512-OJ15Rk90WE68DftCRO6IUA88pR6rdrh1jXH/vrCHru7Sk/9L+C09aysZpHj2wAaQ/dRUdJir9BOd60HCda0DRg==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-emoji@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-emoji/-/ckeditor5-emoji-45.0.0.tgz"
  integrity sha512-tz5aFp/1uHTYVULpES+5xv7u33A/D7lYmxVyPgydMYUFbrn9uMqioKtZ/1VVW6R7AORI50sayT0AkiaqqkD7Aw==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-mention" "45.0.0"
    "@ckeditor/ckeditor5-typing" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"
    fuzzysort "3.1.0"

"@ckeditor/ckeditor5-engine@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-engine/-/ckeditor5-engine-43.3.1.tgz"
  integrity sha512-Fkv3ibQLDPVHFH0z4/+gA5wrkPVWOen+Cjv/NecNBeAszZUo+F2j9RwvQ1zHwtGb0RWj3+BWOPgo8jhSe7tFgA==
  dependencies:
    "@ckeditor/ckeditor5-utils" "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-engine@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-engine/-/ckeditor5-engine-45.0.0.tgz"
  integrity sha512-vkCB1/Dmu5Xay4blb5UaLfy6w0QHTm7MFAa/9S3wjacfJyg7YK51o6tWq1UJspn0q66MjJNZYFXuFc488+xRyg==
  dependencies:
    "@ckeditor/ckeditor5-utils" "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-enter@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-enter/-/ckeditor5-enter-43.3.1.tgz"
  integrity sha512-xaHnU2RbfYi8ilfN260pB3YDvJ9lE4SfiFQusyRdWkeBo5gDAGBbQY+qCC/hmxkr/yftNZfK+d7Ow93xXtqEwg==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"

"@ckeditor/ckeditor5-enter@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-enter/-/ckeditor5-enter-45.0.0.tgz"
  integrity sha512-7wv/Y1egq6xG7/O8737zYh217QJS14Rc7wNfvP+y7Lsd+mqhETC4mTSY6iFpBaJqGQN1Ji5QuI+wm5rbwou80g==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"

"@ckeditor/ckeditor5-essentials@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-essentials/-/ckeditor5-essentials-43.3.1.tgz"
  integrity sha512-bZtzXhmBz8XF9J4eUxOjURmw0HJPKIqo18a6vNxg07W8z3ouHMb9ke//4z4FF9N/1dbtA7a2+jIACO6WvXrX4A==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "43.3.1"
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-enter" "43.3.1"
    "@ckeditor/ckeditor5-select-all" "43.3.1"
    "@ckeditor/ckeditor5-typing" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-undo" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-essentials@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-essentials/-/ckeditor5-essentials-45.0.0.tgz"
  integrity sha512-5YLX+hQrSUz+hsMJ5zozdYxEUMXinR1K6rnrz0l9WxCi12UoYIcUbPuqi1LmlkgZEJk5IcbatnM3TJN9Kef4Hw==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-enter" "45.0.0"
    "@ckeditor/ckeditor5-select-all" "45.0.0"
    "@ckeditor/ckeditor5-typing" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-undo" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-find-and-replace@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-find-and-replace/-/ckeditor5-find-and-replace-43.3.1.tgz"
  integrity sha512-U9dyK8yQgxGTUphRbqdUJbvfi5v7zzijCo3Kj51NxyWwOFh7SGReQxHDGn44DmSRold6lg4F1sbXeFdwu1o+WA==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-find-and-replace@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-find-and-replace/-/ckeditor5-find-and-replace-45.0.0.tgz"
  integrity sha512-Hqnn5aLvmfDmdpMccKXbr+Huzw05RzT7WFYZyNv17Cv7KIS4MYHedrHX45PMTLIh/hkIZIUPPU1t5khbIXsBlw==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-font@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-font/-/ckeditor5-font-43.3.1.tgz"
  integrity sha512-NOeBtScqMuBLVWFPuW0snleh7rMFkNb006yzDIG6JApnF3Vxi0JLQXub/lPHPgw5srqJ3z159DWT++exoyz/mQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-font@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-font/-/ckeditor5-font-45.0.0.tgz"
  integrity sha512-MX7srmuCJjUeUiDDcPu3MlNn3tJpDnDpyvMU9LtvS2/fKFwoBxv92eAfxoiQuCHgNogjySGxXUtroFSgkEcraA==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-fullscreen@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-fullscreen/-/ckeditor5-fullscreen-45.0.0.tgz"
  integrity sha512-KvGY64OQqcZUvsXgO440uvLvJar++0DLEG7zPqrrnKz9soNVils+DLuiRhlUi2QY9xyyWYjA1e8cyPUSHIxuYg==
  dependencies:
    "@ckeditor/ckeditor5-comments" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-document-outline" "45.0.0"
    "@ckeditor/ckeditor5-editor-classic" "45.0.0"
    "@ckeditor/ckeditor5-editor-decoupled" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-pagination" "45.0.0"
    "@ckeditor/ckeditor5-real-time-collaboration" "45.0.0"
    "@ckeditor/ckeditor5-revision-history" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-heading@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-heading/-/ckeditor5-heading-43.3.1.tgz"
  integrity sha512-cc8H027Y2OwvYDGMTbBSzE+oZaiLMZtlUnkgiolMw/OQ59ysONYi+KqyMzBMTuaXrkP3CLM57ZbsVGASQ3IQmQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-paragraph" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-heading@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-heading/-/ckeditor5-heading-45.0.0.tgz"
  integrity sha512-xOc7Qu25q4Ih9Dit3YrlwqN087sPzuzGam6F3kUzF3L3Bb1334BzlzwZI0gJLreDeyN0yrvDMxuNzhMD6g114A==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-paragraph" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-highlight@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-highlight/-/ckeditor5-highlight-43.3.1.tgz"
  integrity sha512-XVJq1YP4IAaWQBAyY1xlKOfzkpnclUH8zTUPaW3TZUGK5t6W/vFT+KAzYfUp7PdBb+PP8/O47FwKTvIQBkbqFw==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-highlight@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-highlight/-/ckeditor5-highlight-45.0.0.tgz"
  integrity sha512-NN4AWohrIlu4lCtwfgSOusqjkt6L47LHz/4tXvgPMuGuZwbVGq67yD16G85EiUoCV7ClI9fJDFW58gYOZhPD1w==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-horizontal-line@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-horizontal-line/-/ckeditor5-horizontal-line-43.3.1.tgz"
  integrity sha512-zkKe0S9gBXwveBUzUuCBPWyrzHQor/zcMCCX9YQk1StUxtRRsURNvWOoFeoG+Vf5jMGSA2gpnBgIo70WrX4A3A==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-widget" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-horizontal-line@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-horizontal-line/-/ckeditor5-horizontal-line-45.0.0.tgz"
  integrity sha512-YyLh5qcQ5jUT65jv2Bv+26hEf/3yXu2EUBj7yjW4arWKGFxwl6mnTnpEsnKPh7HPD75Yo45wdG1Ldyp++q22/w==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@ckeditor/ckeditor5-widget" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-html-embed@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-html-embed/-/ckeditor5-html-embed-43.3.1.tgz"
  integrity sha512-VqIhhPwMgAzmPqjvQUQYaFmCFglkg203W+LSVCwrvgVZ9mVtKbkhwCHBJnLhG7qatar7Gg93bObfAFdAjsaR2A==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    "@ckeditor/ckeditor5-widget" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-html-embed@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-html-embed/-/ckeditor5-html-embed-45.0.0.tgz"
  integrity sha512-r6NcNfuAbask61h+4aA5ZHFi+dDZyXvxDwdyT0h8REBB+ltV/WYeiVpKjKtDlNq6iOmsw3LLSyOssObCi0A0lg==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@ckeditor/ckeditor5-widget" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-html-support@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-html-support/-/ckeditor5-html-support-43.3.1.tgz"
  integrity sha512-cnQ+kCPYH5GiSe5S+13Fr0vuS7DzT4Onx11fvOkssUujtAJ1e/C7hNf5Ehd+SOAgr5IzevutA/+OeR2KHGjIag==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-enter" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    "@ckeditor/ckeditor5-widget" "43.3.1"
    ckeditor5 "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-html-support@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-html-support/-/ckeditor5-html-support-45.0.0.tgz"
  integrity sha512-6RaqoRsIp37TN7VyqCdoF0sVdy+8TD+slndtgJ2RWM4+ncfJcnqWiw+r2cKlcnZPU6oqkxLtdJ5aU60MGJ0p8Q==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-enter" "45.0.0"
    "@ckeditor/ckeditor5-heading" "45.0.0"
    "@ckeditor/ckeditor5-image" "45.0.0"
    "@ckeditor/ckeditor5-list" "45.0.0"
    "@ckeditor/ckeditor5-table" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@ckeditor/ckeditor5-widget" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-icons@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-icons/-/ckeditor5-icons-45.0.0.tgz"
  integrity sha512-DYLH/Qpy2lPkqrZ1f4R6ruqRwgKzGJbzYuq0wXf1cwrcU2QeK3Q1kToUw8wIZvZayiQj9gKbVOHGQHCfbhfmVQ==

"@ckeditor/ckeditor5-image@^45.0.0", "@ckeditor/ckeditor5-image@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-image/-/ckeditor5-image-45.0.0.tgz"
  integrity sha512-WhFuZxNDl9ZhN5/C3DLevmR3jpYNOnaaTtzwr+DOxR3YW/qd5XCHEhapZ6vjz5xpjX+0wySwocy+sAW2bfgFjw==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-typing" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-undo" "45.0.0"
    "@ckeditor/ckeditor5-upload" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@ckeditor/ckeditor5-widget" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-image@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-image/-/ckeditor5-image-43.3.1.tgz"
  integrity sha512-QgHxZtWpclzQ5SUrh1oMsGFCvjykxge5IKe96iKUyAVrhyQp60RhW8DdAElHnPUg3wwILMYE7cKMphknCxcVkQ==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "43.3.1"
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-typing" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-undo" "43.3.1"
    "@ckeditor/ckeditor5-upload" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    "@ckeditor/ckeditor5-widget" "43.3.1"
    ckeditor5 "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-indent@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-indent/-/ckeditor5-indent-43.3.1.tgz"
  integrity sha512-CPU50tumKH7rJ6f9QEB/LHSyzKul9xP/43F1IesvOBWnOkAxQ2QI51oORT5WdKn4B0Z56ojAm48Q/ZUtsef+3w==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-indent@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-indent/-/ckeditor5-indent-45.0.0.tgz"
  integrity sha512-fTaNB0ji9Nxl27+m1QBFlDloObSZp+z+aGdkfw8Xlk33DEv5WWwqv12F3vUeckc8ASUC8Sr0dRd5kfsDSd497g==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-heading" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-list" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-integrations-common@^2.1.0":
  version "2.2.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-integrations-common/-/ckeditor5-integrations-common-2.2.0.tgz"
  integrity sha512-qH68tqgyMibuejo+VAJ+iSH3ZmZweqBEzaawv9hZb4zzSMkBityWBjSc2hKXMtmJgCNsbSK84cyHpa5J/MNyLg==

"@ckeditor/ckeditor5-language@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-language/-/ckeditor5-language-43.3.1.tgz"
  integrity sha512-M7npJRhLoZksnvjZ0fS+6hbAN4RebgZCE2bT9b3Z8Df2Alfy0GJEwJL5aQsYpr+78QFeytTpqzjxXLNLjOyEqA==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-language@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-language/-/ckeditor5-language-45.0.0.tgz"
  integrity sha512-7mgZxProZqKW2aHrJk3keXcgnv+MI+NPHhSIjn7aPZKVfSupMY6dRaE1pZfy9gIfS+dgp/wzkwTEpCuVvdnZfw==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-link@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-link/-/ckeditor5-link-43.3.1.tgz"
  integrity sha512-duTA7harmvZPZ2LbJ8tHnOrhx5lGk6AGavbDzK2xuicMncivm+amrkl/b771uA3Rr6gclHY77ZPcOuVaK+dp/g==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "43.3.1"
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-typing" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    "@ckeditor/ckeditor5-widget" "43.3.1"
    ckeditor5 "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-link@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-link/-/ckeditor5-link-45.0.0.tgz"
  integrity sha512-pdu0y/+E3iR38iSGINYLop3cOZ/hzghHhE2KtRBXxvlNhuUT5MXi+hQKhXEvs08IiEo+J8KS6CkCIxupyJLymA==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-image" "45.0.0"
    "@ckeditor/ckeditor5-typing" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@ckeditor/ckeditor5-widget" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-list@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-list/-/ckeditor5-list-43.3.1.tgz"
  integrity sha512-PuR6uJ/SKvaXIgqTO3MUnX+00/xB/TalStiVqZqqG0xlYg47/eb6hul+4fmTPV7ahlJaon6Y3nO49TsPbbhApQ==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "43.3.1"
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-enter" "43.3.1"
    "@ckeditor/ckeditor5-typing" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-list@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-list/-/ckeditor5-list-45.0.0.tgz"
  integrity sha512-ZKKIOQTU+nSExrc7/pVx2DHRUH/r1HvVD2VzfEXVm7l67kQ/+ZRbsLoN4Ae8OKiN88UkvFEk0/z51Ck6sf/jIw==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-enter" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-typing" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-markdown-gfm@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-markdown-gfm/-/ckeditor5-markdown-gfm-43.3.1.tgz"
  integrity sha512-aVP2FqQP7okSAorQoItcYRbOd0J2O1ubGjtvGGzl3uC5TuKAtlWYWcBfiVTHKxCCtxywPRiEgBxwoGuB5mlwhA==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "43.3.1"
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    ckeditor5 "43.3.1"
    marked "4.0.12"
    turndown "7.2.0"
    turndown-plugin-gfm "1.0.2"

"@ckeditor/ckeditor5-markdown-gfm@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-markdown-gfm/-/ckeditor5-markdown-gfm-45.0.0.tgz"
  integrity sha512-DoXul5fiNqBrGuG8WAnJBd9Rwq/t5j6MLD7kyxVRp43c8vTXFeyXS8/dsvaAU99a0cw+zQlweoBAaGuyKd63bg==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@types/marked" "4.3.2"
    "@types/turndown" "5.0.5"
    ckeditor5 "45.0.0"
    marked "4.0.12"
    turndown "7.2.0"
    turndown-plugin-gfm "1.0.2"

"@ckeditor/ckeditor5-media-embed@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-media-embed/-/ckeditor5-media-embed-43.3.1.tgz"
  integrity sha512-3xMIaH/NTNEKv+lu1cRIIPGgDJgYI1DB+5NMXNVL3UGQkXdqW7PtgFDsOnhQwTAbyKpy+fHDngLb3eZuRdDkKw==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "43.3.1"
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-typing" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-undo" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    "@ckeditor/ckeditor5-widget" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-media-embed@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-media-embed/-/ckeditor5-media-embed-45.0.0.tgz"
  integrity sha512-IFPsG26+WIyBPMHVCXzfgkfPjEFqR7Lc9m5sR2gbQY0H34EjaoSJ7bxocktSxly/z0Hgoe9oMhMFIzZ5sa2r6A==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-typing" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-undo" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@ckeditor/ckeditor5-widget" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-mention@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-mention/-/ckeditor5-mention-43.3.1.tgz"
  integrity sha512-yrOdynVNOS72RjTjhFHzv3Ofbm0eTBKFhuibxdKFfHtTR0QIqSVB5jU+aW1+Jq5LG73E+9eYtip5paSjkqJMWQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-typing" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-mention@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-mention/-/ckeditor5-mention-45.0.0.tgz"
  integrity sha512-ReTMWF6cUgFeB/ZRdxSy6vIoLJlDi2OREPZAsomS1n/7xL0qRsIUeAw3Vb9+CuidP0jepQa6dDicW9RolfFJHQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-typing" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-merge-fields@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-merge-fields/-/ckeditor5-merge-fields-45.0.0.tgz"
  integrity sha512-nQc8GLwFQEungjMSBX7vbT2jkA03jp9v0Aq66Ecx3n1YxqJkq7Qv8rUWr/ot+5MJk6Isly2uwBKchndK+aUP4w==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-image" "45.0.0"
    "@ckeditor/ckeditor5-mention" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@ckeditor/ckeditor5-widget" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-minimap@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-minimap/-/ckeditor5-minimap-43.3.1.tgz"
  integrity sha512-2b0b4mZtRIHAvN/MFAVeqiGt58TZI7ixLcgJo0MHNesYlIk6v13opDWhQ9oefNe8OwJMkD3fAHMlAcg+fUqA9g==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-minimap@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-minimap/-/ckeditor5-minimap-45.0.0.tgz"
  integrity sha512-xEPctHxG3XgxVXdVg6hmn/9pN1nGBqZHZ0XugWUbROopfy9yCgSGX0sUXad4C8eQQk612B5HFaaXd8L9tQ2d0g==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-operations-compressor@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-operations-compressor/-/ckeditor5-operations-compressor-45.0.0.tgz"
  integrity sha512-NwXvgXRXLfoM359LirW3Xf8Hmu8dst3bgxxX9EBJrNdXGTAtLfPRRQ7c5vJa3AZpbfAQAG4DJg9OPrcPdbTN4w==
  dependencies:
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"
    protobufjs "7.4.0"

"@ckeditor/ckeditor5-page-break@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-page-break/-/ckeditor5-page-break-43.3.1.tgz"
  integrity sha512-6AI2GGJveEm/2GESUY01wSPM7AeqHqVuX4Hon20uCAXHYCQkDubOHJ0yV3oFXl7iHeO6Ue2DdlSLayIUXCLoEQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-widget" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-page-break@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-page-break/-/ckeditor5-page-break-45.0.0.tgz"
  integrity sha512-SO/so8ACSw50zp/LxDJz0iOnbWfFLpkvjUaUrvX88+rp03NMxFUXQKeuRV+tgI0puKNq2dvl8dfc+bLKZFgNAw==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@ckeditor/ckeditor5-widget" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-pagination@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-pagination/-/ckeditor5-pagination-45.0.0.tgz"
  integrity sha512-xim17YZdW8rizVBuqKc2OE5DhDTAxYxtQxZJU4GuCvih2JUT9bsmsksUI6VrloH/cDJ1mUy/aAQpo6ivZLqqzQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-theme-lark" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-paragraph@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-paragraph/-/ckeditor5-paragraph-43.3.1.tgz"
  integrity sha512-16ry56X+uXuZEzGZwLS8zpX2DtWN/CHHu5pSz0r2VDZ1zUGLsq/MXutotZfzMMjgdED3x4mJRQE+WgiyRrlKDg==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"

"@ckeditor/ckeditor5-paragraph@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-paragraph/-/ckeditor5-paragraph-45.0.0.tgz"
  integrity sha512-0/zTnYNXfp7HTh40MRPPz0hLJu/+SBa0mHFCkpvzU7pFsuvTib5K35NVcEPEVt84aEoyC3EMkMdTAene0P1LMA==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"

"@ckeditor/ckeditor5-paste-from-office@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-paste-from-office/-/ckeditor5-paste-from-office-43.3.1.tgz"
  integrity sha512-LLf1KB11jeYLDpQPq0d2QVPxQxp9kEibPAF4rGD4stPpRx9d+DbwmE59Y5wVASKvYJo+yNpR9CGWsE4ZgjwTWw==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "43.3.1"
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-paste-from-office@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-paste-from-office/-/ckeditor5-paste-from-office-45.0.0.tgz"
  integrity sha512-rRyXjpFGzknQa+0g5vQ8ufvOUI1YViuHzW1ReXMqYnLU3ubrfwOV0kPOH343ahitGjrHqSciZQ5wdbezroL1gQ==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-react@^9.0.0":
  version "9.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-react/-/ckeditor5-react-9.3.1.tgz"
  integrity sha512-2lc1ICGCOZ0loC6DeMFwhkhrodLYUsOnC2wdgMiaXnEWRI/fU0SWBAoLbsMH7i6zpq29s+ZWMEImRVbly8SmEA==
  dependencies:
    "@ckeditor/ckeditor5-integrations-common" "^2.1.0"
    prop-types "^15.7.2"

"@ckeditor/ckeditor5-real-time-collaboration@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-real-time-collaboration/-/ckeditor5-real-time-collaboration-45.0.0.tgz"
  integrity sha512-WaNQDJgTdl1s+sML0pyFmCs6IvQdG7ra8F2jAdxWIk1WA1YkbdFEKwFkJR/UW5zS0cdvN21SWnWpfoeuzKYyMw==
  dependencies:
    "@ckeditor/ckeditor-cloud-services-collaboration" "52.7.0"
    "@ckeditor/ckeditor5-cloud-services" "45.0.0"
    "@ckeditor/ckeditor5-comments" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-editor-multi-root" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-operations-compressor" "45.0.0"
    "@ckeditor/ckeditor5-revision-history" "45.0.0"
    "@ckeditor/ckeditor5-theme-lark" "45.0.0"
    "@ckeditor/ckeditor5-track-changes" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"
    ckeditor5-collaboration "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-remove-format@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-remove-format/-/ckeditor5-remove-format-43.3.1.tgz"
  integrity sha512-m7zvvYzHN/HExT0NoILXauVFI/AKQyuzPqqCI/VO1Ft5mLswXGuK6vmO1U10SmGz85etYZjEipKuouf2Anyqxg==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-remove-format@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-remove-format/-/ckeditor5-remove-format-45.0.0.tgz"
  integrity sha512-HWVO26OpxDGf9WNmck3TgDZsQc7Rcxzstuu9lwzJc9D1kJlPgNTRuYNwEPEn2ijdNdgDOPCo6PgUvZcmuTkw1g==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-restricted-editing@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-restricted-editing/-/ckeditor5-restricted-editing-43.3.1.tgz"
  integrity sha512-L6sA6UrUPy4Q3AzF8yQGsgEadO1IcZv53Ijevk9KuD7dwLF4f9x4ukUFLlGRpoYHPAW/+RpADp2PPegjKHo9QQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-restricted-editing@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-restricted-editing/-/ckeditor5-restricted-editing-45.0.0.tgz"
  integrity sha512-pYQJxuVTuJvHOvu0DR9D+AVi6OUIYSoOkGdoaak3OULn5pQz2QDzXVfdIOlQA6ooLBkvg4bYcY3V4lWEkrNYQw==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-revision-history@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-revision-history/-/ckeditor5-revision-history-45.0.0.tgz"
  integrity sha512-zTKb7UFEh7e5U6G8o2+PbG6huheZQ1hAj33jMuJ+ZA83cSh2bwVOUL4NBfvzPaQm5W+R41G7o1eOf5NcRFDHdA==
  dependencies:
    "@ckeditor/ckeditor5-autosave" "45.0.0"
    "@ckeditor/ckeditor5-comments" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-editor-classic" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@types/luxon" "3.4.2"
    ckeditor5 "45.0.0"
    ckeditor5-collaboration "45.0.0"
    es-toolkit "1.32.0"
    luxon "3.5.0"

"@ckeditor/ckeditor5-select-all@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-select-all/-/ckeditor5-select-all-43.3.1.tgz"
  integrity sha512-oYQ8uF6hmlX7OefpJ0FflvKddAkEffg3fKMT2FAINwqxhX+O7h9RQZ79AiOkTab7HUTIkbhM5AlhFJIXiX0Z7Q==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"

"@ckeditor/ckeditor5-select-all@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-select-all/-/ckeditor5-select-all-45.0.0.tgz"
  integrity sha512-rOQEZ5BpGbEXqV3YLPvDrtGImM8c0Gy7AMDPH8OlSFHIasQnhdRCj91DhgAiSs3BNvG2xVwpf5TFWvHcOFi4oQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"

"@ckeditor/ckeditor5-show-blocks@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-show-blocks/-/ckeditor5-show-blocks-43.3.1.tgz"
  integrity sha512-o+IhZnjMmoF2qd4l1GqQqroeIEA29QAIOYfvrdMKZGrzVGmjbvwyNkbJRyZlAYhZqX8tLDPaPGn0tl+onhWtzw==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-show-blocks@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-show-blocks/-/ckeditor5-show-blocks-45.0.0.tgz"
  integrity sha512-HqnyWEoORir9zh6EcoIrBCpnpPVGQhM7N2kXPaWPek1mJKC77tEEXavGwGN/KdYkw6jRc7vnG5slJZYz1LPAbQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-source-editing@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-source-editing/-/ckeditor5-source-editing-43.3.1.tgz"
  integrity sha512-Pq7WthQAiKa3A3q82bHqNRjQ/xlOpSX9kZHLm+CDH8XACxZbBF6Unz4JPR9zJRuQxkoFs314DM/PG6pPZQgXXA==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-theme-lark" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-source-editing@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-source-editing/-/ckeditor5-source-editing-45.0.0.tgz"
  integrity sha512-es6kx14NnHew/GYVWBCvdXP0mvRLSwq/WEL4R/rGDkvTyxFICea2bEoGCEeiw47BMYu/5G6b+7GjDBiYJgJSPw==
  dependencies:
    "@ckeditor/ckeditor5-comments" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-document-outline" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-theme-lark" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-special-characters@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-special-characters/-/ckeditor5-special-characters-43.3.1.tgz"
  integrity sha512-3iwrtISndl5hc+/LuSXht69xqkEv95zg8Qxv+ovREA3pvtgt5u9O0t7ELcmUeTTEs/hJkF2FDplIYQj5zIvO+g==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-typing" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"

"@ckeditor/ckeditor5-special-characters@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-special-characters/-/ckeditor5-special-characters-45.0.0.tgz"
  integrity sha512-T/ELE3gpvKne9cfQNRsP4aZl84DFMknsQp6G6lFrk/1QZOnsWRp3uDvYiHhpjtGKw+R/2FXbgA18OVOEFvNC7Q==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-typing" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"

"@ckeditor/ckeditor5-style@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-style/-/ckeditor5-style-43.3.1.tgz"
  integrity sha512-2+ATPa5y4ZUkak5xFTTDeUPhuCAYB4OPNt/QjMvrQjpEwXoWDJ4f8GqR9oFFsqEGMm65GrUp/xIQW8WRH43Kng==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-typing" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-style@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-style/-/ckeditor5-style-45.0.0.tgz"
  integrity sha512-Chhx4CFTDYgmcLBNtMXPNNyzh/xejMOuJ9eOTjHQ7EJDdF7+hMK5cK5Yk+noeHhLMeyxCQe7hmOQNmkE1f9Nbg==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-html-support" "45.0.0"
    "@ckeditor/ckeditor5-list" "45.0.0"
    "@ckeditor/ckeditor5-table" "45.0.0"
    "@ckeditor/ckeditor5-typing" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-table@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-table/-/ckeditor5-table-43.3.1.tgz"
  integrity sha512-Qr3GkKELnG1EY7Bu9dGQBkGTqhVnygeHKDCTEG9m218shYsI5L6jFftGUzWmJzMpm3hNFkyYv+1YWaIoqfRzIQ==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "43.3.1"
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    "@ckeditor/ckeditor5-widget" "43.3.1"
    ckeditor5 "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-table@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-table/-/ckeditor5-table-45.0.0.tgz"
  integrity sha512-LgF5AF9x9epul0UogXBpK9UUE4iamwTyL6niWyGd/qUEl3LQbX0aSBnHTfrATcHhbo+DmIaDQDEsU7IVrDLkDw==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@ckeditor/ckeditor5-widget" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-theme-lark@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-theme-lark/-/ckeditor5-theme-lark-43.3.1.tgz"
  integrity sha512-kAgeGx66jT31FFYwAoc43oX5ehQtiYE57OJWlPTXrDXxyq0Y+LYFW2/bp4UVYdZK+OKv9dp1Do3VQfxJoGzFjg==
  dependencies:
    "@ckeditor/ckeditor5-ui" "43.3.1"

"@ckeditor/ckeditor5-theme-lark@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-theme-lark/-/ckeditor5-theme-lark-45.0.0.tgz"
  integrity sha512-PLtgUsE0mV2HfbBzLLW+BxUrtPQDEbumfIb8RlOVEd1yg6aTp3qNBQWDfiKL3eBJRFlh3qWYVouwu1S35JYn5w==
  dependencies:
    "@ckeditor/ckeditor5-ui" "45.0.0"

"@ckeditor/ckeditor5-track-changes@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-track-changes/-/ckeditor5-track-changes-45.0.0.tgz"
  integrity sha512-2i6H5pB55l1D+BihDT9kUUWcGF8he1IBSMBrQ3uvbEdo3OLd9sgw0ZrREo84Z5S0r5dsdaJA97/JatW2Q3KZXg==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "45.0.0"
    "@ckeditor/ckeditor5-code-block" "45.0.0"
    "@ckeditor/ckeditor5-comments" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-editor-multi-root" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-enter" "45.0.0"
    "@ckeditor/ckeditor5-find-and-replace" "45.0.0"
    "@ckeditor/ckeditor5-font" "45.0.0"
    "@ckeditor/ckeditor5-heading" "45.0.0"
    "@ckeditor/ckeditor5-highlight" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-image" "45.0.0"
    "@ckeditor/ckeditor5-link" "45.0.0"
    "@ckeditor/ckeditor5-list" "45.0.0"
    "@ckeditor/ckeditor5-media-embed" "45.0.0"
    "@ckeditor/ckeditor5-merge-fields" "45.0.0"
    "@ckeditor/ckeditor5-restricted-editing" "45.0.0"
    "@ckeditor/ckeditor5-style" "45.0.0"
    "@ckeditor/ckeditor5-table" "45.0.0"
    "@ckeditor/ckeditor5-typing" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@ckeditor/ckeditor5-widget" "45.0.0"
    ckeditor5 "45.0.0"
    ckeditor5-collaboration "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-typing@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-typing/-/ckeditor5-typing-43.3.1.tgz"
  integrity sha512-sK45GlrOHqWOphVnzDKe3kofVJGhSRk34UQJnyXgMN+35QJqypnJeBYBnnHWL8+nK0S4zk9oQO3PuiRH6gg/WQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-typing@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-typing/-/ckeditor5-typing-45.0.0.tgz"
  integrity sha512-sQonahw1LdeOoXgfeKZ//seh066sBJ7ABK1kyXfI5a7rPVZ1K28G4mOyvVBMwXq4Vot0Gffc5V8sW9CmcOr/5A==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-ui@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-ui/-/ckeditor5-ui-43.3.1.tgz"
  integrity sha512-dbR4FK6mCkI89h4Joyf1PZt0Xsq0N+sZg05Z6BpYz6GS9U35C7J9bHxN469dvaIc8bJws4eYJ5x+St3LcvlduQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    color-convert "2.0.1"
    color-parse "1.4.2"
    lodash-es "4.17.21"
    vanilla-colorful "0.7.2"

"@ckeditor/ckeditor5-ui@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-ui/-/ckeditor5-ui-45.0.0.tgz"
  integrity sha512-kbZ02pu9xBl7OAf5Reou/9VsQwsOChw4n7xMWTqL4SzDpCweULpUugtiGW1n/zpF956PN6hAaWAbpSgXFBjAAw==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-editor-multi-root" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@types/color-convert" "2.0.4"
    color-convert "2.0.1"
    color-parse "1.4.2"
    es-toolkit "1.32.0"
    vanilla-colorful "0.7.2"

"@ckeditor/ckeditor5-undo@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-undo/-/ckeditor5-undo-43.3.1.tgz"
  integrity sha512-UxrWPlHzL/DKuxp4R5mlQvy995Ozehh5hQxY5yvL285Dzv6PY5pk627Wv/qS8AyfLMyVNiFO9bDWBIcT9igQRA==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"

"@ckeditor/ckeditor5-undo@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-undo/-/ckeditor5-undo-45.0.0.tgz"
  integrity sha512-b3ZGzUM4kN6rgNboUxLMhPW0ChvmlmC82I/Up/LAAImUO4BQSKIBc9wxSPnd9raMHFi28v1q3v6g6y041VpKgQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"

"@ckeditor/ckeditor5-upload@^45.0.0", "@ckeditor/ckeditor5-upload@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-upload/-/ckeditor5-upload-45.0.0.tgz"
  integrity sha512-yZI8FNoxJV1CANYuafX8d2LNZD2OvMSiKOoqpRIuqZcRuzxQVOmsnCc+Mbd+MGGRePFZ+izeaxfD1d+YwUBs9Q==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"

"@ckeditor/ckeditor5-upload@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-upload/-/ckeditor5-upload-43.3.1.tgz"
  integrity sha512-uOEhCgqgiK4V/CnbnuwHU/NUOG4ioQE5KUUtVmRG2xjQKg5C1uIT2dig+wnKw8vOdwVTMD2hVt7/OC/whQuheQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"

"@ckeditor/ckeditor5-utils@>= 37.0", "@ckeditor/ckeditor5-utils@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-utils/-/ckeditor5-utils-43.3.1.tgz"
  integrity sha512-4CyM3AP+DcfuPuw+zceI3UTh3HcusnvFVeRPPw6j3Qe29/jadZYsdvkdo9KsDaiwgx0ctooKCuY9SfAcd/CZNQ==
  dependencies:
    "@ckeditor/ckeditor5-ui" "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-utils@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-utils/-/ckeditor5-utils-45.0.0.tgz"
  integrity sha512-U/1uPk/dcrO2uzRUzuzmXM+YCm7Tjfwfi49MNgC2zdSSWzhT+HTeGGXgLSAFMOptfzKYmxmYj5cadfDs13p4eA==
  dependencies:
    "@ckeditor/ckeditor5-ui" "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-watchdog@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-watchdog/-/ckeditor5-watchdog-43.3.1.tgz"
  integrity sha512-d9gh0QIrrImIe2SFLo/IBLdpgC9REVkvUTv//qLbUaM2ffBboMnpJYPAB/hgl8ev4lkDvCrivlGjc/80COfGTQ==
  dependencies:
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-watchdog@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-watchdog/-/ckeditor5-watchdog-45.0.0.tgz"
  integrity sha512-rjZVYQPXM7Scqrheh2gblVWb0GsuH5JRV3rn4e+3+MOcI/UfepmLFifIVzuBIYm1uhrMx9M14THFVyceDbNkCQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-editor-multi-root" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-widget@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-widget/-/ckeditor5-widget-43.3.1.tgz"
  integrity sha512-0naXUVC6BFLnuj3lu5aTfRxmqV6py9+zqGHdJJZ0x8uSg9qcfUCLEQvA59bqzNteRya/lZeZhYKj8IcGnbB1oA==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-enter" "43.3.1"
    "@ckeditor/ckeditor5-typing" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-widget@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-widget/-/ckeditor5-widget-45.0.0.tgz"
  integrity sha512-yANcyKsyniJrcGsAdV3FD+Oz/OWLTezMf83jXaVzaxYU/gImukpGG/62sDWp2OaNMxqc+Qr64vSoi8lXA17LTg==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-enter" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-typing" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    es-toolkit "1.32.0"

"@ckeditor/ckeditor5-word-count@43.3.1":
  version "43.3.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-word-count/-/ckeditor5-word-count-43.3.1.tgz"
  integrity sha512-W0Ic7y4/ePVqW22pHuXv5HRAbaDJFO13rUqyTZqU2H2ExZdMbJN6eT/UVhnO1XvKs/+jdKGO3LGWXt9QmmtkhA==
  dependencies:
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    ckeditor5 "43.3.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-word-count@45.0.0":
  version "45.0.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-word-count/-/ckeditor5-word-count-45.0.0.tgz"
  integrity sha512-72Fd2mUKN8dXRMKGKLPhUiQHhoGnp0ZyPwP8Ezcpbwfn/mh5SBxP3LyQGg35sKy1xJgDYywHlWvjVhw95LgIjg==
  dependencies:
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    ckeditor5 "45.0.0"
    es-toolkit "1.32.0"

"@emotion/babel-plugin@^11.13.5":
  version "11.13.5"
  resolved "https://registry.npmjs.org/@emotion/babel-plugin/-/babel-plugin-11.13.5.tgz"
  integrity sha512-pxHCpT2ex+0q+HH91/zsdHkw/lXd468DIN2zvfvLtPKLLMo6gQj7oLObq8PhkrxOZb/gGCq03S3Z7PDhS8pduQ==
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/runtime" "^7.18.3"
    "@emotion/hash" "^0.9.2"
    "@emotion/memoize" "^0.9.0"
    "@emotion/serialize" "^1.3.3"
    babel-plugin-macros "^3.1.0"
    convert-source-map "^1.5.0"
    escape-string-regexp "^4.0.0"
    find-root "^1.1.0"
    source-map "^0.5.7"
    stylis "4.2.0"

"@emotion/cache@^11.14.0", "@emotion/cache@^11.4.0":
  version "11.14.0"
  resolved "https://registry.npmjs.org/@emotion/cache/-/cache-11.14.0.tgz"
  integrity sha512-L/B1lc/TViYk4DcpGxtAVbx0ZyiKM5ktoIyafGkH6zg/tj+mA+NE//aPYKG0k8kCHSHVJrpLpcAlOBEXQ3SavA==
  dependencies:
    "@emotion/memoize" "^0.9.0"
    "@emotion/sheet" "^1.4.0"
    "@emotion/utils" "^1.4.2"
    "@emotion/weak-memoize" "^0.4.0"
    stylis "4.2.0"

"@emotion/hash@^0.9.2":
  version "0.9.2"
  resolved "https://registry.npmjs.org/@emotion/hash/-/hash-0.9.2.tgz"
  integrity sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g==

"@emotion/memoize@^0.9.0":
  version "0.9.0"
  resolved "https://registry.npmjs.org/@emotion/memoize/-/memoize-0.9.0.tgz"
  integrity sha512-30FAj7/EoJ5mwVPOWhAyCX+FPfMDrVecJAM+Iw9NRoSl4BBAQeqj4cApHHUXOVvIPgLVDsCFoz/hGD+5QQD1GQ==

"@emotion/react@^11.8.1":
  version "11.14.0"
  resolved "https://registry.npmjs.org/@emotion/react/-/react-11.14.0.tgz"
  integrity sha512-O000MLDBDdk/EohJPFUqvnp4qnHeYkVP5B0xEG0D/L7cOKP9kefu2DXn8dj74cQfsEzUqh+sr1RzFqiL1o+PpA==
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@emotion/babel-plugin" "^11.13.5"
    "@emotion/cache" "^11.14.0"
    "@emotion/serialize" "^1.3.3"
    "@emotion/use-insertion-effect-with-fallbacks" "^1.2.0"
    "@emotion/utils" "^1.4.2"
    "@emotion/weak-memoize" "^0.4.0"
    hoist-non-react-statics "^3.3.1"

"@emotion/serialize@^1.3.3":
  version "1.3.3"
  resolved "https://registry.npmjs.org/@emotion/serialize/-/serialize-1.3.3.tgz"
  integrity sha512-EISGqt7sSNWHGI76hC7x1CksiXPahbxEOrC5RjmFRJTqLyEK9/9hZvBbiYn70dw4wuwMKiEMCUlR6ZXTSWQqxA==
  dependencies:
    "@emotion/hash" "^0.9.2"
    "@emotion/memoize" "^0.9.0"
    "@emotion/unitless" "^0.10.0"
    "@emotion/utils" "^1.4.2"
    csstype "^3.0.2"

"@emotion/sheet@^1.4.0":
  version "1.4.0"
  resolved "https://registry.npmjs.org/@emotion/sheet/-/sheet-1.4.0.tgz"
  integrity sha512-fTBW9/8r2w3dXWYM4HCB1Rdp8NLibOw2+XELH5m5+AkWiL/KqYX6dc0kKYlaYyKjrQ6ds33MCdMPEwgs2z1rqg==

"@emotion/unitless@^0.10.0":
  version "0.10.0"
  resolved "https://registry.npmjs.org/@emotion/unitless/-/unitless-0.10.0.tgz"
  integrity sha512-dFoMUuQA20zvtVTuxZww6OHoJYgrzfKM1t52mVySDJnMSEa08ruEvdYQbhvyu6soU+NeLVd3yKfTfT0NeV6qGg==

"@emotion/use-insertion-effect-with-fallbacks@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@emotion/use-insertion-effect-with-fallbacks/-/use-insertion-effect-with-fallbacks-1.2.0.tgz"
  integrity sha512-yJMtVdH59sxi/aVJBpk9FQq+OR8ll5GT8oWd57UpeaKEVGab41JWaCFA7FRLoMLloOZF/c/wsPoe+bfGmRKgDg==

"@emotion/utils@^1.4.2":
  version "1.4.2"
  resolved "https://registry.npmjs.org/@emotion/utils/-/utils-1.4.2.tgz"
  integrity sha512-3vLclRofFziIa3J2wDh9jjbkUz9qk5Vi3IZ/FSTKViB0k+ef0fPV7dYrUIugbgupYDx7v9ud/SjrtEP8Y4xLoA==

"@emotion/weak-memoize@^0.4.0":
  version "0.4.0"
  resolved "https://registry.npmjs.org/@emotion/weak-memoize/-/weak-memoize-0.4.0.tgz"
  integrity sha512-snKqtPW01tN0ui7yu9rGv69aJXr/a/Ywvl11sUjNtEcRc+ng/mQriFL0wLXMef74iHa/EkftbDzU9F8iFbH+zg==

"@faker-js/faker@^8.0.2":
  version "8.4.1"
  resolved "https://registry.npmjs.org/@faker-js/faker/-/faker-8.4.1.tgz"
  integrity sha512-XQ3cU+Q8Uqmrbf2e0cIC/QN43sTBSC8KF12u29Mb47tWrt2hAgBXSgpZMj4Ao8Uk0iJcU99QsOCaIL8934obCg==

"@floating-ui/core@^1.6.0":
  version "1.6.8"
  resolved "https://registry.npmjs.org/@floating-ui/core/-/core-1.6.8.tgz"
  integrity sha512-7XJ9cPU+yI2QeLS+FCSlqNFZJq8arvswefkZrYI1yQBbftw6FyrZOxYSh+9S7z7TpeWlRt9zJ5IhM1WIL334jA==
  dependencies:
    "@floating-ui/utils" "^0.2.8"

"@floating-ui/dom@^1.0.1":
  version "1.6.12"
  resolved "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.6.12.tgz"
  integrity sha512-NP83c0HjokcGVEMeoStg317VD9W7eDlGK7457dMBANbKA6GJZdc7rjujdgqzTaz93jkGgc5P/jeWbaCHnMNc+w==
  dependencies:
    "@floating-ui/core" "^1.6.0"
    "@floating-ui/utils" "^0.2.8"

"@floating-ui/utils@^0.2.8":
  version "0.2.8"
  resolved "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.8.tgz"
  integrity sha512-kym7SodPp8/wloecOpcmSnWJsK7M0E5Wg8UcFA+uO4B9s5d0ywXOEro/8HM9x0rW+TljRzul/14UYz3TleT3ig==

"@fullcalendar/core@^6.1.4", "@fullcalendar/core@~6.1.15":
  version "6.1.15"
  resolved "https://registry.npmjs.org/@fullcalendar/core/-/core-6.1.15.tgz"
  integrity sha512-BuX7o6ALpLb84cMw1FCB9/cSgF4JbVO894cjJZ6kP74jzbUZNjtwffwRdA+Id8rrLjT30d/7TrkW90k4zbXB5Q==
  dependencies:
    preact "~10.12.1"

"@fullcalendar/daygrid@^6.1.4", "@fullcalendar/daygrid@~6.1.15":
  version "6.1.15"
  resolved "https://registry.npmjs.org/@fullcalendar/daygrid/-/daygrid-6.1.15.tgz"
  integrity sha512-j8tL0HhfiVsdtOCLfzK2J0RtSkiad3BYYemwQKq512cx6btz6ZZ2RNc/hVnIxluuWFyvx5sXZwoeTJsFSFTEFA==

"@fullcalendar/interaction@^6.1.4":
  version "6.1.15"
  resolved "https://registry.npmjs.org/@fullcalendar/interaction/-/interaction-6.1.15.tgz"
  integrity sha512-DOTSkofizM7QItjgu7W68TvKKvN9PSEEvDJceyMbQDvlXHa7pm/WAVtAc6xSDZ9xmB1QramYoWGLHkCYbTW1rQ==

"@fullcalendar/list@^6.1.4":
  version "6.1.15"
  resolved "https://registry.npmjs.org/@fullcalendar/list/-/list-6.1.15.tgz"
  integrity sha512-U1bce04tYDwkFnuVImJSy2XalYIIQr6YusOWRPM/5ivHcJh67Gm8CIMSWpi3KdRSNKFkqBxLPkfZGBMaOcJYug==

"@fullcalendar/react@^6.1.4":
  version "6.1.15"
  resolved "https://registry.npmjs.org/@fullcalendar/react/-/react-6.1.15.tgz"
  integrity sha512-L0b9hybS2J4e7lq6G2CD4nqriyLEqOH1tE8iI6JQjAMTVh5JicOo5Mqw+fhU5bJ7hLfMw2K3fksxX3Ul1ssw5w==

"@fullcalendar/timegrid@^6.1.4":
  version "6.1.15"
  resolved "https://registry.npmjs.org/@fullcalendar/timegrid/-/timegrid-6.1.15.tgz"
  integrity sha512-61ORr3A148RtxQ2FNG7JKvacyA/TEVZ7z6I+3E9Oeu3dqTf6M928bFcpehRTIK6zIA6Yifs7BeWHgOE9dFnpbw==
  dependencies:
    "@fullcalendar/daygrid" "~6.1.15"

"@headlessui/react@^1.7.4":
  version "1.7.19"
  resolved "https://registry.npmjs.org/@headlessui/react/-/react-1.7.19.tgz"
  integrity sha512-Ll+8q3OlMJfJbAKM/+/Y2q6PPYbryqNTXDbryx7SXLIDamkF6iQFbriYHga0dY44PvDhvvBWCx1Xj4U5+G4hOw==
  dependencies:
    "@tanstack/react-virtual" "^3.0.0-beta.60"
    client-only "^0.0.1"

"@hookform/resolvers@^2.9.10":
  version "2.9.11"
  resolved "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.9.11.tgz"
  integrity sha512-bA3aZ79UgcHj7tFV7RlgThzwSSHZgvfbt2wprldRkYBcMopdMvHyO17Wwp/twcJasNFischFfS7oz8Katz8DdQ==

"@iconify/react@^5.0.1":
  version "5.0.2"
  resolved "https://registry.npmjs.org/@iconify/react/-/react-5.0.2.tgz"
  integrity sha512-wtmstbYlEbo4NDxFxBJkhkf9gJBDqMGr7FaqLrAUMneRV3Z+fVHLJjOhWbkAF8xDQNFC/wcTYdrWo1lnRhmagQ==
  dependencies:
    "@iconify/types" "^2.0.0"

"@iconify/types@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@iconify/types/-/types-2.0.0.tgz"
  integrity sha512-+wluvCrRhXrhyOmRDJ3q8mux9JkKy5SJ/v8ol2tu4FVjyYvtEzkc/3pK15ET6RKg4b4w4BmTk1+gsCUhf21Ykg==

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  resolved "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz"
  integrity sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@jridgewell/gen-mapping@^0.3.2", "@jridgewell/gen-mapping@^0.3.5":
  version "0.3.5"
  resolved "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz"
  integrity sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  integrity sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz"
  integrity sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.0"
  resolved "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz"
  integrity sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz"
  integrity sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@juggle/resize-observer@^3.3.1":
  version "3.4.0"
  resolved "https://registry.npmjs.org/@juggle/resize-observer/-/resize-observer-3.4.0.tgz"
  integrity sha512-dfLbk+PwWvFzSxwk3n5ySL0hfBog779o8h68wK/7/APo/7cgyWp5jcXockbxdk5kFRkbeXWm4Fbi9FrdN381sA==

"@kurkle/color@^0.3.0":
  version "0.3.2"
  resolved "https://registry.npmjs.org/@kurkle/color/-/color-0.3.2.tgz"
  integrity sha512-fuscdXJ9G1qb7W8VdHi+IwRqij3lBkosAm4ydQtEmbY58OzHXqQhvlxqEkoz0yssNVn38bcpRWgA9PP+OGoisw==

"@mapbox/node-pre-gyp@^1.0.0":
  version "1.0.11"
  resolved "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.11.tgz"
  integrity sha512-Yhlar6v9WQgUp/He7BdgzOz8lqMQ8sU+jkCq7Wx8Myc5YFJLbEe7lgui/V7G1qB1DJykHSGwreceSaD60Y0PUQ==
  dependencies:
    detect-libc "^2.0.0"
    https-proxy-agent "^5.0.0"
    make-dir "^3.1.0"
    node-fetch "^2.6.7"
    nopt "^5.0.0"
    npmlog "^5.0.1"
    rimraf "^3.0.2"
    semver "^7.3.5"
    tar "^6.1.11"

"@miragejs/pretender-node-polyfill@^0.1.0":
  version "0.1.2"
  resolved "https://registry.npmjs.org/@miragejs/pretender-node-polyfill/-/pretender-node-polyfill-0.1.2.tgz"
  integrity sha512-M/BexG/p05C5lFfMunxo/QcgIJnMT2vDVCd00wNqK2ImZONIlEETZwWJu1QtLxtmYlSHlCFl3JNzp0tLe7OJ5g==

"@mixmark-io/domino@^2.2.0":
  version "2.2.0"
  resolved "https://registry.npmjs.org/@mixmark-io/domino/-/domino-2.2.0.tgz"
  integrity sha512-Y28PR25bHXUg88kCV7nivXrP2Nj2RueZ3/l/jdx6J9f8J4nsEGcgX0Qe6lt7Pa+J79+kPiJU3LguR6O/6zrLOw==

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  version "2.0.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@parcel/watcher-linux-x64-glibc@2.5.0":
  version "2.5.0"
  resolved "https://registry.npmjs.org/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.5.0.tgz"
  integrity sha512-d9AOkusyXARkFD66S6zlGXyzx5RvY+chTP9Jp0ypSTC9d4lzyRs9ovGf/80VCxjKddcUvnsGwCHWuF2EoPgWjw==

"@parcel/watcher-linux-x64-musl@2.5.0":
  version "2.5.0"
  resolved "https://registry.npmjs.org/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.5.0.tgz"
  integrity sha512-iqOC+GoTDoFyk/VYSFHwjHhYrk8bljW6zOhPuhi5t9ulqiYq1togGJB5e3PwYVFFfeVgc6pbz3JdQyDoBszVaA==

"@parcel/watcher@^2.4.1":
  version "2.5.0"
  resolved "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.5.0.tgz"
  integrity sha512-i0GV1yJnm2n3Yq1qw6QrUrd/LI9bE8WEBOTtOkpCXHHdyN3TAGgqAK/DAT05z4fq2x04cARXt2pDmjWjL92iTQ==
  dependencies:
    detect-libc "^1.0.3"
    is-glob "^4.0.3"
    micromatch "^4.0.5"
    node-addon-api "^7.0.0"
  optionalDependencies:
    "@parcel/watcher-android-arm64" "2.5.0"
    "@parcel/watcher-darwin-arm64" "2.5.0"
    "@parcel/watcher-darwin-x64" "2.5.0"
    "@parcel/watcher-freebsd-x64" "2.5.0"
    "@parcel/watcher-linux-arm-glibc" "2.5.0"
    "@parcel/watcher-linux-arm-musl" "2.5.0"
    "@parcel/watcher-linux-arm64-glibc" "2.5.0"
    "@parcel/watcher-linux-arm64-musl" "2.5.0"
    "@parcel/watcher-linux-x64-glibc" "2.5.0"
    "@parcel/watcher-linux-x64-musl" "2.5.0"
    "@parcel/watcher-win32-arm64" "2.5.0"
    "@parcel/watcher-win32-ia32" "2.5.0"
    "@parcel/watcher-win32-x64" "2.5.0"

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"
  resolved "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz"
  integrity sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==

"@popperjs/core@^2.11.6", "@popperjs/core@^2.9.0":
  version "2.11.8"
  resolved "https://registry.npmjs.org/@popperjs/core/-/core-2.11.8.tgz"
  integrity sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==

"@protobufjs/aspromise@^1.1.1", "@protobufjs/aspromise@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@protobufjs/aspromise/-/aspromise-1.1.2.tgz"
  integrity sha512-j+gKExEuLmKwvz3OgROXtrJ2UG2x8Ch2YZUxahh+s1F2HZ+wAceUNLkvy6zKCPVRkU++ZWQrdxsUeQXmcg4uoQ==

"@protobufjs/base64@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@protobufjs/base64/-/base64-1.1.2.tgz"
  integrity sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg==

"@protobufjs/codegen@^2.0.4":
  version "2.0.4"
  resolved "https://registry.npmjs.org/@protobufjs/codegen/-/codegen-2.0.4.tgz"
  integrity sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg==

"@protobufjs/eventemitter@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz"
  integrity sha512-j9ednRT81vYJ9OfVuXG6ERSTdEL1xVsNgqpkxMsbIabzSo3goCjDIveeGv5d03om39ML71RdmrGNjG5SReBP/Q==

"@protobufjs/fetch@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/fetch/-/fetch-1.1.0.tgz"
  integrity sha512-lljVXpqXebpsijW71PZaCYeIcE5on1w5DlQy5WH6GLbFryLUrBD4932W/E2BSpfRJWseIL4v/KPgBFxDOIdKpQ==
  dependencies:
    "@protobufjs/aspromise" "^1.1.1"
    "@protobufjs/inquire" "^1.1.0"

"@protobufjs/float@^1.0.2":
  version "1.0.2"
  resolved "https://registry.npmjs.org/@protobufjs/float/-/float-1.0.2.tgz"
  integrity sha512-Ddb+kVXlXst9d+R9PfTIxh1EdNkgoRe5tOX6t01f1lYWOvJnSPDBlG241QLzcyPdoNTsblLUdujGSE4RzrTZGQ==

"@protobufjs/inquire@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/inquire/-/inquire-1.1.0.tgz"
  integrity sha512-kdSefcPdruJiFMVSbn801t4vFK7KB/5gd2fYvrxhuJYg8ILrmn9SKSX2tZdV6V+ksulWqS7aXjBcRXl3wHoD9Q==

"@protobufjs/path@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@protobufjs/path/-/path-1.1.2.tgz"
  integrity sha512-6JOcJ5Tm08dOHAbdR3GrvP+yUUfkjG5ePsHYczMFLq3ZmMkAD98cDgcT2iA1lJ9NVwFd4tH/iSSoe44YWkltEA==

"@protobufjs/pool@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/pool/-/pool-1.1.0.tgz"
  integrity sha512-0kELaGSIDBKvcgS4zkjz1PeddatrjYcmMWOlAuAPwAeccUrPHdUqo/J6LiymHHEiJT5NrF1UVwxY14f+fy4WQw==

"@protobufjs/utf8@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/utf8/-/utf8-1.1.0.tgz"
  integrity sha512-Vvn3zZrhQZkkBE8LSuW3em98c0FwgO4nxzv6OdSxPKJIEKY2bGbHn+mhGIPerzI4twdxaP8/0+06HBpwf345Lw==

"@react-dnd/asap@^5.0.1":
  version "5.0.2"
  resolved "https://registry.npmjs.org/@react-dnd/asap/-/asap-5.0.2.tgz"
  integrity sha512-WLyfoHvxhs0V9U+GTsGilGgf2QsPl6ZZ44fnv0/b8T3nQyvzxidxsg/ZltbWssbsRDlYW8UKSQMTGotuTotZ6A==

"@react-dnd/invariant@^4.0.1":
  version "4.0.2"
  resolved "https://registry.npmjs.org/@react-dnd/invariant/-/invariant-4.0.2.tgz"
  integrity sha512-xKCTqAK/FFauOM9Ta2pswIyT3D8AQlfrYdOi/toTPEhqCuAs1v5tcJ3Y08Izh1cJ5Jchwy9SeAXmMg6zrKs2iw==

"@react-dnd/shallowequal@^4.0.1":
  version "4.0.2"
  resolved "https://registry.npmjs.org/@react-dnd/shallowequal/-/shallowequal-4.0.2.tgz"
  integrity sha512-/RVXdLvJxLg4QKvMoM5WlwNR9ViO9z8B/qPcc+C0Sa/teJY7QG7kJ441DwzOjMYEY7GmU4dj5EcGHIkKZiQZCA==

"@react-leaflet/core@^2.1.0":
  version "2.1.0"
  resolved "https://registry.npmjs.org/@react-leaflet/core/-/core-2.1.0.tgz"
  integrity sha512-Qk7Pfu8BSarKGqILj4x7bCSZ1pjuAPZ+qmRwH5S7mDS91VSbVVsJSrW4qA+GPrro8t69gFYVMWb1Zc4yFmPiVg==

"@react-pdf-viewer/core@^3.12.0":
  version "3.12.0"
  resolved "https://registry.npmjs.org/@react-pdf-viewer/core/-/core-3.12.0.tgz"
  integrity sha512-8MsdlQJ4jaw3GT+zpCHS33nwnvzpY0ED6DEahZg9WngG++A5RMhk8LSlxdHelwaFFHFiXBjmOaj2Kpxh50VQRg==

"@react-pdf/fns@2.2.1":
  version "2.2.1"
  resolved "https://registry.npmjs.org/@react-pdf/fns/-/fns-2.2.1.tgz"
  integrity sha512-s78aDg0vDYaijU5lLOCsUD+qinQbfOvcNeaoX9AiE7+kZzzCo6B/nX+l48cmt9OosJmvZvE9DWR9cLhrhOi2pA==
  dependencies:
    "@babel/runtime" "^7.20.13"

"@react-pdf/font@^2.5.2":
  version "2.5.2"
  resolved "https://registry.npmjs.org/@react-pdf/font/-/font-2.5.2.tgz"
  integrity sha512-Ud0EfZ2FwrbvwAWx8nz+KKLmiqACCH9a/N/xNDOja0e/YgSnqTpuyHegFBgIMKjuBtO5dNvkb4dXkxAhGe/ayw==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/types" "^2.6.0"
    cross-fetch "^3.1.5"
    fontkit "^2.0.2"
    is-url "^1.2.4"

"@react-pdf/image@^2.3.6":
  version "2.3.6"
  resolved "https://registry.npmjs.org/@react-pdf/image/-/image-2.3.6.tgz"
  integrity sha512-7iZDYZrZlJqNzS6huNl2XdMcLFUo68e6mOdzQeJ63d5eApdthhSHBnkGzHfLhH5t8DCpZNtClmklzuLL63ADfw==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/png-js" "^2.3.1"
    cross-fetch "^3.1.5"
    jay-peg "^1.0.2"

"@react-pdf/layout@^3.13.0":
  version "3.13.0"
  resolved "https://registry.npmjs.org/@react-pdf/layout/-/layout-3.13.0.tgz"
  integrity sha512-lpPj/EJYHFOc0ALiJwLP09H28B4ADyvTjxOf67xTF+qkWd+dq1vg7dw3wnYESPnWk5T9NN+HlUenJqdYEY9AvA==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/fns" "2.2.1"
    "@react-pdf/image" "^2.3.6"
    "@react-pdf/pdfkit" "^3.2.0"
    "@react-pdf/primitives" "^3.1.1"
    "@react-pdf/stylesheet" "^4.3.0"
    "@react-pdf/textkit" "^4.4.1"
    "@react-pdf/types" "^2.6.0"
    cross-fetch "^3.1.5"
    emoji-regex "^10.3.0"
    queue "^6.0.1"
    yoga-layout "^2.0.1"

"@react-pdf/pdfkit@^3.2.0":
  version "3.2.0"
  resolved "https://registry.npmjs.org/@react-pdf/pdfkit/-/pdfkit-3.2.0.tgz"
  integrity sha512-OBfCcnTC6RpD9uv9L2woF60Zj1uQxhLFzTBXTdcYE9URzPE/zqXIyzpXEA4Vf3TFbvBCgFE2RzJ2ZUS0asq7yA==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/png-js" "^2.3.1"
    browserify-zlib "^0.2.0"
    crypto-js "^4.2.0"
    fontkit "^2.0.2"
    jay-peg "^1.0.2"
    vite-compatible-readable-stream "^3.6.1"

"@react-pdf/png-js@^2.3.1":
  version "2.3.1"
  resolved "https://registry.npmjs.org/@react-pdf/png-js/-/png-js-2.3.1.tgz"
  integrity sha512-pEZ18I4t1vAUS4lmhvXPmXYP4PHeblpWP/pAlMMRkEyP7tdAeHUN7taQl9sf9OPq7YITMY3lWpYpJU6t4CZgZg==
  dependencies:
    browserify-zlib "^0.2.0"

"@react-pdf/primitives@^3.1.1":
  version "3.1.1"
  resolved "https://registry.npmjs.org/@react-pdf/primitives/-/primitives-3.1.1.tgz"
  integrity sha512-miwjxLwTnO3IjoqkTVeTI+9CdyDggwekmSLhVCw+a/7FoQc+gF3J2dSKwsHvAcVFM0gvU8mzCeTofgw0zPDq0w==

"@react-pdf/render@^3.5.0":
  version "3.5.0"
  resolved "https://registry.npmjs.org/@react-pdf/render/-/render-3.5.0.tgz"
  integrity sha512-gFOpnyqCgJ6l7VzfJz6rG1i2S7iVSD8bUHDjPW9Mze8TmyksHzN2zBH3y7NbsQOw1wU6hN4NhRmslrsn+BRDPA==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/fns" "2.2.1"
    "@react-pdf/primitives" "^3.1.1"
    "@react-pdf/textkit" "^4.4.1"
    "@react-pdf/types" "^2.6.0"
    abs-svg-path "^0.1.1"
    color-string "^1.9.1"
    normalize-svg-path "^1.1.0"
    parse-svg-path "^0.1.2"
    svg-arc-to-cubic-bezier "^3.2.0"

"@react-pdf/renderer@^3.4.4":
  version "3.4.5"
  resolved "https://registry.npmjs.org/@react-pdf/renderer/-/renderer-3.4.5.tgz"
  integrity sha512-O1N8q45bTs7YuC+x9afJSKQWDYQy2RjoCxlxEGdbCwP+WD5G6dWRUWXlc8F0TtzU3uFglYMmDab2YhXTmnVN9g==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/font" "^2.5.2"
    "@react-pdf/layout" "^3.13.0"
    "@react-pdf/pdfkit" "^3.2.0"
    "@react-pdf/primitives" "^3.1.1"
    "@react-pdf/render" "^3.5.0"
    "@react-pdf/types" "^2.6.0"
    events "^3.3.0"
    object-assign "^4.1.1"
    prop-types "^15.6.2"
    queue "^6.0.1"
    scheduler "^0.17.0"

"@react-pdf/stylesheet@^4.3.0":
  version "4.3.0"
  resolved "https://registry.npmjs.org/@react-pdf/stylesheet/-/stylesheet-4.3.0.tgz"
  integrity sha512-x7IVZOqRrUum9quuDeFXBveXwBht+z/6B0M+z4a4XjfSg1vZVvzoTl07Oa1yvQ/4yIC5yIkG2TSMWeKnDB+hrw==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/fns" "2.2.1"
    "@react-pdf/types" "^2.6.0"
    color-string "^1.9.1"
    hsl-to-hex "^1.0.0"
    media-engine "^1.0.3"
    postcss-value-parser "^4.1.0"

"@react-pdf/textkit@^4.4.1":
  version "4.4.1"
  resolved "https://registry.npmjs.org/@react-pdf/textkit/-/textkit-4.4.1.tgz"
  integrity sha512-Jl9wdTqIvJ5pX+vAGz0EOhP7ut5Two9H6CzTKo/YYPeD79cM2yTXF3JzTERBC28y7LR0Waq9D2LHQjI+b/EYUQ==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/fns" "2.2.1"
    bidi-js "^1.0.2"
    hyphen "^1.6.4"
    unicode-properties "^1.4.1"

"@react-pdf/types@^2.6.0":
  version "2.7.0"
  resolved "https://registry.npmjs.org/@react-pdf/types/-/types-2.7.0.tgz"
  integrity sha512-7KrPPCpgRPKR+g+T127PE4bpw9Q84ZiY07EYRwXKVtTEVW9wJ5BZiF9smT9IvH19s+MQaDLmYRgjESsnqlyH0Q==

"@reduxjs/toolkit@^1.9.0":
  version "1.9.7"
  resolved "https://registry.npmjs.org/@reduxjs/toolkit/-/toolkit-1.9.7.tgz"
  integrity sha512-t7v8ZPxhhKgOKtU+uyJT13lu4vL7az5aFi4IdoDs/eS548edn2M8Ik9h8fxgvMjGoAUVFSt6ZC1P5cWmQ014QQ==
  dependencies:
    immer "^9.0.21"
    redux "^4.2.1"
    redux-thunk "^2.4.2"
    reselect "^4.1.8"

"@remix-run/router@1.21.0":
  version "1.21.0"
  resolved "https://registry.npmjs.org/@remix-run/router/-/router-1.21.0.tgz"
  integrity sha512-xfSkCAchbdG5PnbrKqFWwia4Bi61nH+wm8wLEqfHDyp7Y3dZzgqS2itV8i4gAq9pC2HsTpwyBC6Ds8VHZ96JlA==

"@restart/hooks@^0.4.7":
  version "0.4.16"
  resolved "https://registry.npmjs.org/@restart/hooks/-/hooks-0.4.16.tgz"
  integrity sha512-f7aCv7c+nU/3mF7NWLtVVr0Ra80RqsO89hO72r+Y/nvQr5+q0UFGkocElTH6MJApvReVh6JHUFYn2cw1WdHF3w==
  dependencies:
    dequal "^2.0.3"

"@rollup/plugin-replace@^5.0.2":
  version "5.0.7"
  resolved "https://registry.npmjs.org/@rollup/plugin-replace/-/plugin-replace-5.0.7.tgz"
  integrity sha512-PqxSfuorkHz/SPpyngLyg5GCEkOcee9M1bkxiVDr41Pd61mqP1PLOoDPbpl44SB2mQGKwV/In74gqQmGITOhEQ==
  dependencies:
    "@rollup/pluginutils" "^5.0.1"
    magic-string "^0.30.3"

"@rollup/pluginutils@^4.1.1":
  version "4.2.1"
  resolved "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-4.2.1.tgz"
  integrity sha512-iKnFXr7NkdZAIHiIWE+BX5ULi/ucVFYWD6TbAV+rZctiRTY2PL6tsIKhoIOaoskiWAkgu+VsbXgUVDNLHf+InQ==
  dependencies:
    estree-walker "^2.0.1"
    picomatch "^2.2.2"

"@rollup/pluginutils@^5.0.1":
  version "5.1.3"
  resolved "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-5.1.3.tgz"
  integrity sha512-Pnsb6f32CD2W3uCaLZIzDmeFyQ2b8UWMFI7xtwUezpcGBDVDW6y9XgAWIlARiGAo6eNF5FK5aQTr0LFyNyqq5A==
  dependencies:
    "@types/estree" "^1.0.0"
    estree-walker "^2.0.2"
    picomatch "^4.0.2"

"@socket.io/component-emitter@~3.1.0":
  version "3.1.2"
  resolved "https://registry.npmjs.org/@socket.io/component-emitter/-/component-emitter-3.1.2.tgz"
  integrity sha512-9BCxFwvbGg/RsZK9tjXd8s4UcwR0MWeFQ1XEKIQVVvAGJyINdrqKMcTRyLoK8Rse1GjzLV9cwjWV1olXRWEXVA==

"@south-paw/react-vector-maps@^3.2.0":
  version "3.2.0"
  resolved "https://registry.npmjs.org/@south-paw/react-vector-maps/-/react-vector-maps-3.2.0.tgz"
  integrity sha512-4Y88ZA8RuXxlBD7hgguVZjiTZsxvnN0Eheip/7YeM22B8hwae058C4Xx7Fi1PyIhOF5yWU/yXxyCMwFE7Awrwg==

"@svg-maps/world@^1.0.1":
  version "1.0.1"
  resolved "https://registry.npmjs.org/@svg-maps/world/-/world-1.0.1.tgz"
  integrity sha512-Mawh/jEYBBHnug9S17PyePLYKJ+Xd0Bbh96mCePebpbvcbJu5YKpfKhpyMeLFmmdWPrSFxl0f0MTsJfXU0gSaQ==

"@swc/helpers@^0.5.12":
  version "0.5.15"
  resolved "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.15.tgz"
  integrity sha512-JQ5TuMi45Owi4/BIMAJBoSQoOJu12oOk/gADqlcUL9JEdHB8vyjUSsxqeNXnmXHjYKMi2WcYtezGEEhqUI/E2g==
  dependencies:
    tslib "^2.8.0"

"@tanstack/react-virtual@^3.0.0-beta.60":
  version "3.10.9"
  resolved "https://registry.npmjs.org/@tanstack/react-virtual/-/react-virtual-3.10.9.tgz"
  integrity sha512-OXO2uBjFqA4Ibr2O3y0YMnkrRWGVNqcvHQXmGvMu6IK8chZl3PrDxFXdGZ2iZkSrKh3/qUYoFqYe+Rx23RoU0g==
  dependencies:
    "@tanstack/virtual-core" "3.10.9"

"@tanstack/virtual-core@3.10.9":
  version "3.10.9"
  resolved "https://registry.npmjs.org/@tanstack/virtual-core/-/virtual-core-3.10.9.tgz"
  integrity sha512-kBknKOKzmeR7lN+vSadaKWXaLS0SZZG+oqpQ/k80Q6g9REn6zRHS/ZYdrIzHnpHgy/eWs00SujveUN/GJT2qTw==

"@tippyjs/react@^4.2.6":
  version "4.2.6"
  resolved "https://registry.npmjs.org/@tippyjs/react/-/react-4.2.6.tgz"
  integrity sha512-91RicDR+H7oDSyPycI13q3b7o4O60wa2oRbjlz2fyRLmHImc4vyDwuUP8NtZaN0VARJY5hybvDYrFzhY9+Lbyw==
  dependencies:
    tippy.js "^6.3.1"

"@types/color-convert@2.0.4":
  version "2.0.4"
  resolved "https://registry.npmjs.org/@types/color-convert/-/color-convert-2.0.4.tgz"
  integrity sha512-Ub1MmDdyZ7mX//g25uBAoH/mWGd9swVbt8BseymnaE18SU4po/PjmCrHxqIIRjBo3hV/vh1KGr0eMxUhp+t+dQ==
  dependencies:
    "@types/color-name" "^1.1.0"

"@types/color-name@^1.1.0":
  version "1.1.5"
  resolved "https://registry.npmjs.org/@types/color-name/-/color-name-1.1.5.tgz"
  integrity sha512-j2K5UJqGTxeesj6oQuGpMgifpT5k9HprgQd8D1Y0lOFqKHl3PJu5GMeS4Y5EgjS55AE6OQxf8mPED9uaGbf4Cg==

"@types/d3-array@^3.0.3":
  version "3.2.1"
  resolved "https://registry.npmjs.org/@types/d3-array/-/d3-array-3.2.1.tgz"
  integrity sha512-Y2Jn2idRrLzUfAKV2LyRImR+y4oa2AntrgID95SHJxuMUrkNXmanDSed71sRNZysveJVt1hLLemQZIady0FpEg==

"@types/d3-color@*":
  version "3.1.3"
  resolved "https://registry.npmjs.org/@types/d3-color/-/d3-color-3.1.3.tgz"
  integrity sha512-iO90scth9WAbmgv7ogoq57O9YpKmFBbmoEoCHDB2xMBY0+/KVrqAaCDyCE16dUspeOvIxFFRI+0sEtqDqy2b4A==

"@types/d3-ease@^3.0.0":
  version "3.0.2"
  resolved "https://registry.npmjs.org/@types/d3-ease/-/d3-ease-3.0.2.tgz"
  integrity sha512-NcV1JjO5oDzoK26oMzbILE6HW7uVXOHLQvHshBUW4UMdZGfiY6v5BeQwh9a9tCzv+CeefZQHJt5SRgK154RtiA==

"@types/d3-interpolate@^3.0.1":
  version "3.0.4"
  resolved "https://registry.npmjs.org/@types/d3-interpolate/-/d3-interpolate-3.0.4.tgz"
  integrity sha512-mgLPETlrpVV1YRJIglr4Ez47g7Yxjl1lj7YKsiMCb27VJH9W8NVM6Bb9d8kkpG/uAQS5AmbA48q2IAolKKo1MA==
  dependencies:
    "@types/d3-color" "*"

"@types/d3-path@*":
  version "3.1.0"
  resolved "https://registry.npmjs.org/@types/d3-path/-/d3-path-3.1.0.tgz"
  integrity sha512-P2dlU/q51fkOc/Gfl3Ul9kicV7l+ra934qBFXCFhrZMOL6du1TM0pm1ThYvENukyOn5h9v+yMJ9Fn5JK4QozrQ==

"@types/d3-scale@^4.0.2":
  version "4.0.8"
  resolved "https://registry.npmjs.org/@types/d3-scale/-/d3-scale-4.0.8.tgz"
  integrity sha512-gkK1VVTr5iNiYJ7vWDI+yUFFlszhNMtVeneJ6lUTKPjprsvLLI9/tgEGiXJOnlINJA8FyA88gfnQsHbybVZrYQ==
  dependencies:
    "@types/d3-time" "*"

"@types/d3-shape@^3.1.0":
  version "3.1.6"
  resolved "https://registry.npmjs.org/@types/d3-shape/-/d3-shape-3.1.6.tgz"
  integrity sha512-5KKk5aKGu2I+O6SONMYSNflgiP0WfZIQvVUMan50wHsLG1G94JlxEVnCpQARfTtzytuY0p/9PXXZb3I7giofIA==
  dependencies:
    "@types/d3-path" "*"

"@types/d3-time@*", "@types/d3-time@^3.0.0":
  version "3.0.3"
  resolved "https://registry.npmjs.org/@types/d3-time/-/d3-time-3.0.3.tgz"
  integrity sha512-2p6olUZ4w3s+07q3Tm2dbiMZy5pCDfYwtLXXHUnVzXgQlZ/OyPtUz6OL382BkOuGlLXqfT+wqv8Fw2v8/0geBw==

"@types/d3-timer@^3.0.0":
  version "3.0.2"
  resolved "https://registry.npmjs.org/@types/d3-timer/-/d3-timer-3.0.2.tgz"
  integrity sha512-Ps3T8E8dZDam6fUyNiMkekK3XUsaUEik+idO9/YjPtfj2qruF8tFBXS7XhtE4iIXBLxhmLjP3SXpLhVf21I9Lw==

"@types/estree@^1.0.0":
  version "1.0.6"
  resolved "https://registry.npmjs.org/@types/estree/-/estree-1.0.6.tgz"
  integrity sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==

"@types/hoist-non-react-statics@^3.3.0", "@types/hoist-non-react-statics@^3.3.1", "@types/hoist-non-react-statics@>= 3.3.1":
  version "3.3.5"
  resolved "https://registry.npmjs.org/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.5.tgz"
  integrity sha512-SbcrWzkKBw2cdwRTwQAswfpB9g9LJWfjtUeW/jvNwbhC8cpmmNYVePa+ncbUe0rGTQ7G3Ff6mYUN2VMfLVr+Sg==
  dependencies:
    "@types/react" "*"
    hoist-non-react-statics "^3.3.0"

"@types/lodash@^4.14.175":
  version "4.17.13"
  resolved "https://registry.npmjs.org/@types/lodash/-/lodash-4.17.13.tgz"
  integrity sha512-lfx+dftrEZcdBPczf9d0Qv0x+j/rfNCMuC6OcfXmO8gkfeNAY88PgKUbvG56whcN23gc27yenwF6oJZXGFpYxg==

"@types/luxon@3.4.2":
  version "3.4.2"
  resolved "https://registry.npmjs.org/@types/luxon/-/luxon-3.4.2.tgz"
  integrity sha512-TifLZlFudklWlMBfhubvgqTXRzLDI5pCbGa4P8a3wPyUQSW+1xQ5eDsreP9DWHX3tjq1ke96uYG/nwundroWcA==

"@types/marked@4.3.2":
  version "4.3.2"
  resolved "https://registry.npmjs.org/@types/marked/-/marked-4.3.2.tgz"
  integrity sha512-a79Yc3TOk6dGdituy8hmTTJXjOkZ7zsFYV10L337ttq/rec8lRMDBpV7fL3uLx6TgbFCa5DU/h8FmIBQPSbU0w==

"@types/node@>= 12", "@types/node@>= 14", "@types/node@>=13.7.0":
  version "22.14.1"
  resolved "https://registry.npmjs.org/@types/node/-/node-22.14.1.tgz"
  integrity sha512-u0HuPQwe/dHrItgHHpmw3N2fYCR6x4ivMNbPHRkBVP4CvN+kiRrKHWk3i8tXiO/joPwXLMYvF9TTF0eqgHIuOw==
  dependencies:
    undici-types "~6.21.0"

"@types/parse-json@^4.0.0":
  version "4.0.2"
  resolved "https://registry.npmjs.org/@types/parse-json/-/parse-json-4.0.2.tgz"
  integrity sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==

"@types/prop-types@*":
  version "15.7.13"
  resolved "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.13.tgz"
  integrity sha512-hCZTSvwbzWGvhqxp/RqVqwU999pBf2vp7hzIjiYOsl8wqOmUxkQ6ddw1cV3l8811+kdUFus/q4d1Y3E3SyEifA==

"@types/react-dom@^16.8 || ^17.0 || ^18.0", "@types/react-dom@^18.0.8":
  version "18.3.1"
  resolved "https://registry.npmjs.org/@types/react-dom/-/react-dom-18.3.1.tgz"
  integrity sha512-qW1Mfv8taImTthu4KoXgDfLuk4bydU6Q/TkADnDWWHwi4NX4BR+LWfTp2sVmTqRrsHvyDDTelgelxJ+SsejKKQ==
  dependencies:
    "@types/react" "*"

"@types/react-redux@^7.1.20":
  version "7.1.34"
  resolved "https://registry.npmjs.org/@types/react-redux/-/react-redux-7.1.34.tgz"
  integrity sha512-GdFaVjEbYv4Fthm2ZLvj1VSCedV7TqE5y1kNwnjSdBOTXuRSgowux6J8TAct15T3CKBr63UMk+2CO7ilRhyrAQ==
  dependencies:
    "@types/hoist-non-react-statics" "^3.3.0"
    "@types/react" "*"
    hoist-non-react-statics "^3.3.0"
    redux "^4.0.0"

"@types/react-transition-group@^4.4.0":
  version "4.4.11"
  resolved "https://registry.npmjs.org/@types/react-transition-group/-/react-transition-group-4.4.11.tgz"
  integrity sha512-RM05tAniPZ5DZPzzNFP+DmrcOdD0efDUxMy3145oljWSl3x9ZV5vhme98gTxFrj2lhXvmGNnUiuDyJgY9IKkNA==
  dependencies:
    "@types/react" "*"

"@types/react@*", "@types/react@^16.8 || ^17.0 || ^18.0", "@types/react@^16.8.0 || ^17.0.0 || ^18.0.0", "@types/react@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "@types/react@^18.0.24", "@types/react@>= 16", "@types/react@>=16.9.11":
  version "18.3.12"
  resolved "https://registry.npmjs.org/@types/react/-/react-18.3.12.tgz"
  integrity sha512-D2wOSq/d6Agt28q7rSI3jhU7G6aiuzljDGZ2hTZHIkrTLUI+AF3WMeKkEZ9nN2fkBAlcktT6vcZjDFiIhMYEQw==
  dependencies:
    "@types/prop-types" "*"
    csstype "^3.0.2"

"@types/turndown@5.0.5":
  version "5.0.5"
  resolved "https://registry.npmjs.org/@types/turndown/-/turndown-5.0.5.tgz"
  integrity sha512-TL2IgGgc7B5j78rIccBtlYAnkuv8nUQqhQc+DSYV5j9Be9XOcm/SKOVRuA47xAVI3680Tk9B1d8flK2GWT2+4w==

"@types/use-sync-external-store@^0.0.3":
  version "0.0.3"
  resolved "https://registry.npmjs.org/@types/use-sync-external-store/-/use-sync-external-store-0.0.3.tgz"
  integrity sha512-EwmlvuaxPNej9+T4v5AuBPJa2x2UOJVdjCtDHgcDqitUeOtjnJKJ+apYjVcAoBEMjKW1VVFGZLUb5+qqa09XFA==

"@types/uuid@8.3.4":
  version "8.3.4"
  resolved "https://registry.npmjs.org/@types/uuid/-/uuid-8.3.4.tgz"
  integrity sha512-c/I8ZRb51j+pYGAu5CrFMRxqZ2ke4y2grEBO5AUjgSkSk+qT2Ea+OdWElz/OiMf5MNpn2b17kuVBwZLQJXzihw==

"@types/warning@^3.0.0":
  version "3.0.3"
  resolved "https://registry.npmjs.org/@types/warning/-/warning-3.0.3.tgz"
  integrity sha512-D1XC7WK8K+zZEveUPY+cf4+kgauk8N4eHr/XIHXGlGYkHLud6hK9lYfZk1ry1TNh798cZUCgb6MqGEG8DkJt6Q==

"@vitejs/plugin-react-refresh@^1.3.6":
  version "1.3.6"
  resolved "https://registry.npmjs.org/@vitejs/plugin-react-refresh/-/plugin-react-refresh-1.3.6.tgz"
  integrity sha512-iNR/UqhUOmFFxiezt0em9CgmiJBdWR+5jGxB2FihaoJfqGt76kiwaKoVOJVU5NYcDWMdN06LbyN2VIGIoYdsEA==
  dependencies:
    "@babel/core" "^7.14.8"
    "@babel/plugin-transform-react-jsx-self" "^7.14.5"
    "@babel/plugin-transform-react-jsx-source" "^7.14.5"
    "@rollup/pluginutils" "^4.1.1"
    react-refresh "^0.10.0"

"@vitejs/plugin-react@^2.2.0":
  version "2.2.0"
  resolved "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-2.2.0.tgz"
  integrity sha512-FFpefhvExd1toVRlokZgxgy2JtnBOdp4ZDsq7ldCWaqGSGn9UhWMAVm/1lxPL14JfNS5yGz+s9yFrQY6shoStA==
  dependencies:
    "@babel/core" "^7.19.6"
    "@babel/plugin-transform-react-jsx" "^7.19.0"
    "@babel/plugin-transform-react-jsx-development" "^7.18.6"
    "@babel/plugin-transform-react-jsx-self" "^7.18.6"
    "@babel/plugin-transform-react-jsx-source" "^7.19.6"
    magic-string "^0.26.7"
    react-refresh "^0.14.0"

"@wojtekmaj/date-utils@^1.1.3":
  version "1.5.1"
  resolved "https://registry.npmjs.org/@wojtekmaj/date-utils/-/date-utils-1.5.1.tgz"
  integrity sha512-+i7+JmNiE/3c9FKxzWFi2IjRJ+KzZl1QPu6QNrsgaa2MuBgXvUy4gA1TVzf/JMdIIloB76xSKikTWuyYAIVLww==

"@yr/monotone-cubic-spline@^1.0.3":
  version "1.0.3"
  resolved "https://registry.npmjs.org/@yr/monotone-cubic-spline/-/monotone-cubic-spline-1.0.3.tgz"
  integrity sha512-FQXkOta0XBSUPHndIKON2Y9JeQz5ZeMqLYZVVK93FliNBFm7LNMIZmY6FrMEB9XPcDbE2bekMbZD6kzDkxwYjA==

abbrev@1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/abbrev/-/abbrev-1.1.1.tgz"
  integrity sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==

abs-svg-path@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmjs.org/abs-svg-path/-/abs-svg-path-0.1.1.tgz"
  integrity sha512-d8XPSGjfyzlXC3Xx891DJRyZfqk5JU0BJrDQcsWomFIV1/BIzPW5HDH5iDdWpqWaav0YVIEzT1RHTwWr0FFshA==

agent-base@6:
  version "6.0.2"
  resolved "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz"
  integrity sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==
  dependencies:
    debug "4"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-regex@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz"
  integrity sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==

ansi-styles@^4.0.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^6.1.0:
  version "6.2.1"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz"
  integrity sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==

any-promise@^1.0.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz"
  integrity sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==

anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
  integrity sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

apexcharts@^3.36.3, apexcharts@^3.41.0:
  version "3.54.1"
  resolved "https://registry.npmjs.org/apexcharts/-/apexcharts-3.54.1.tgz"
  integrity sha512-E4et0h/J1U3r3EwS/WlqJCQIbepKbp6wGUmaAwJOMjHUP4Ci0gxanLa7FR3okx6p9coi4st6J853/Cb1NP0vpA==
  dependencies:
    "@yr/monotone-cubic-spline" "^1.0.3"
    svg.draggable.js "^2.2.2"
    svg.easing.js "^2.0.0"
    svg.filter.js "^2.0.2"
    svg.pathmorphing.js "^0.1.3"
    svg.resize.js "^1.4.3"
    svg.select.js "^3.0.1"

"aproba@^1.0.3 || ^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/aproba/-/aproba-2.0.0.tgz"
  integrity sha512-lYe4Gx7QT+MKGbDsA+Z+he/Wtef0BiwDOlK/XkBrdfsh9J/jPPXbX0tE9x9cl27Tmu5gg3QUbUrQYa/y+KOHPQ==

are-we-there-yet@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-2.0.0.tgz"
  integrity sha512-Ci/qENmwHnsYo9xKIcUJN5LeDKdJ6R1Z1j9V/J5wyq8nh/mYPEpIKJbBZXtZjG04HiK7zV/p6Vs9952MrMeUIw==
  dependencies:
    delegates "^1.0.0"
    readable-stream "^3.6.0"

arg@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmjs.org/arg/-/arg-5.0.2.tgz"
  integrity sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==

attr-accept@^2.2.4:
  version "2.2.5"
  resolved "https://registry.npmjs.org/attr-accept/-/attr-accept-2.2.5.tgz"
  integrity sha512-0bDNnY/u6pPwHDMoF0FieU354oBi0a8rD9FcsLwzcGWbc8KS8KPIi7y+s13OlVY+gMWc/9xEMUgNE6Qm8ZllYQ==

autoprefixer@^10.4.13:
  version "10.4.20"
  resolved "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.20.tgz"
  integrity sha512-XY25y5xSv/wEoqzDyXXME4AFfkZI0P23z6Fs3YgymDnKJkCGOnkL0iTxCa85UTqaSgfcqyf3UA6+c7wUvx/16g==
  dependencies:
    browserslist "^4.23.3"
    caniuse-lite "^1.0.30001646"
    fraction.js "^4.3.7"
    normalize-range "^0.1.2"
    picocolors "^1.0.1"
    postcss-value-parser "^4.2.0"

babel-plugin-macros@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-3.1.0.tgz"
  integrity sha512-Cg7TFGpIr01vOQNODXOOaGz2NpCU5gl8x1qJFbb6hbZxR7XrcE2vtbAsTAbJ7/xwJtUuJEw8K8Zr/AE0LHlesg==
  dependencies:
    "@babel/runtime" "^7.12.5"
    cosmiconfig "^7.0.0"
    resolve "^1.19.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

base64-js@^1.1.2, base64-js@^1.3.0:
  version "1.5.1"
  resolved "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

bidi-js@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/bidi-js/-/bidi-js-1.0.3.tgz"
  integrity sha512-RKshQI1R3YQ+n9YJz2QQ147P66ELpa1FQEg20Dk8oW9t2KgLbpDLLp9aGZ7y8WHSshDknG0bknqGw5/tyCs5tw==
  dependencies:
    require-from-string "^2.0.2"

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz"
  integrity sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==

blurhash@2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/blurhash/-/blurhash-2.0.5.tgz"
  integrity sha512-cRygWd7kGBQO3VEhPiTgq4Wc43ctsM+o46urrmPOiuAe+07fzlSB9OJVdpgDL0jPqXUVQ9ht7aq7kxOeJHRK+w==

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz"
  integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz"
  integrity sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==
  dependencies:
    fill-range "^7.1.1"

brotli@^1.3.2:
  version "1.3.3"
  resolved "https://registry.npmjs.org/brotli/-/brotli-1.3.3.tgz"
  integrity sha512-oTKjJdShmDuGW94SyyaoQvAjf30dZaHnjJ8uAF+u2/vGJkJbJPJAT1gDiOJP5v1Zb6f9KEyW/1HpuaWIXtGHPg==
  dependencies:
    base64-js "^1.1.2"

browserify-zlib@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/browserify-zlib/-/browserify-zlib-0.2.0.tgz"
  integrity sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA==
  dependencies:
    pako "~1.0.5"

browserslist@^4.23.3, browserslist@^4.24.0, "browserslist@>= 4.21.0":
  version "4.24.2"
  resolved "https://registry.npmjs.org/browserslist/-/browserslist-4.24.2.tgz"
  integrity sha512-ZIc+Q62revdMcqC6aChtW4jz3My3klmCO1fEmINZY/8J3EpBg5/A/D0AKmBveUh6pgoeycoMkVMko84tuYS+Gg==
  dependencies:
    caniuse-lite "^1.0.30001669"
    electron-to-chromium "^1.5.41"
    node-releases "^2.0.18"
    update-browserslist-db "^1.1.1"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==

camelcase-css@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz"
  integrity sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==

can-use-dom@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/can-use-dom/-/can-use-dom-0.1.0.tgz"
  integrity sha512-ceOhN1DL7Y4O6M0j9ICgmTYziV89WMd96SvSl0REd8PMgrY0B/WBOPoed5S1KUmJqXgUXh8gzSe6E3ae27upsQ==

caniuse-lite@^1.0.30001646, caniuse-lite@^1.0.30001669:
  version "1.0.30001712"
  resolved "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001712.tgz"
  integrity sha512-MBqPpGYYdQ7/hfKiet9SCI+nmN5/hp4ZzveOJubl5DTAMa5oggjAuoi0Z4onBpKPFI2ePGnQuQIzF3VxDjDJig==

canvas@^2.11.2:
  version "2.11.2"
  resolved "https://registry.npmjs.org/canvas/-/canvas-2.11.2.tgz"
  integrity sha512-ItanGBMrmRV7Py2Z+Xhs7cT+FNt5K0vPL4p9EZ/UX/Mu7hFbkxSjKF2KVtPwX7UYWp7dRKnrTvReflgrItJbdw==
  dependencies:
    "@mapbox/node-pre-gyp" "^1.0.0"
    nan "^2.17.0"
    simple-get "^3.0.3"

chart.js@^4.1.1, chart.js@^4.2.0:
  version "4.4.6"
  resolved "https://registry.npmjs.org/chart.js/-/chart.js-4.4.6.tgz"
  integrity sha512-8Y406zevUPbbIBA/HRk33khEmQPk5+cxeflWE/2rx1NJsjVWMPw/9mSP9rxHP5eqi6LNoPBVMfZHxbwLSgldYA==
  dependencies:
    "@kurkle/color" "^0.3.0"

chokidar@^3.5.3:
  version "3.6.0"
  resolved "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz"
  integrity sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chokidar@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/chokidar/-/chokidar-4.0.1.tgz"
  integrity sha512-n8enUVCED/KVRQlab1hr3MVpcVMvxtZjmEa956u+4YijlmQED223XMSYj2tLuKvr4jcCTzNNMpQDUer72MMmzA==
  dependencies:
    readdirp "^4.0.1"

chownr@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/chownr/-/chownr-2.0.0.tgz"
  integrity sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==

ckeditor5-collaboration@45.0.0:
  version "45.0.0"
  resolved "https://registry.npmjs.org/ckeditor5-collaboration/-/ckeditor5-collaboration-45.0.0.tgz"
  integrity sha512-5Uz3VJOYfepeFNgiJvdIwLuKqj582kx8Yldwfdmn/GMWemhDXpz2XYmvZhCPPPQXQl/aRrUu4ewpjNHnShHr2A==
  dependencies:
    "@ckeditor/ckeditor5-collaboration-core" "45.0.0"

ckeditor5@^43.0.0, "ckeditor5@>= 37.0", "ckeditor5@>=42.0.0 || ^0.0.0-nightly", ckeditor5@43.3.1:
  version "43.3.1"
  resolved "https://registry.npmjs.org/ckeditor5/-/ckeditor5-43.3.1.tgz"
  integrity sha512-ZZ6nIdlr9rCCp21o9d5/mVUeVPwpQKEVxkeq1MU/Jax1w8U6rnMiQWxB954Ky/HNjhZ1v1ll2+VRzb7XA+1emA==
  dependencies:
    "@ckeditor/ckeditor5-adapter-ckfinder" "43.3.1"
    "@ckeditor/ckeditor5-alignment" "43.3.1"
    "@ckeditor/ckeditor5-autoformat" "43.3.1"
    "@ckeditor/ckeditor5-autosave" "43.3.1"
    "@ckeditor/ckeditor5-basic-styles" "43.3.1"
    "@ckeditor/ckeditor5-block-quote" "43.3.1"
    "@ckeditor/ckeditor5-ckbox" "43.3.1"
    "@ckeditor/ckeditor5-ckfinder" "43.3.1"
    "@ckeditor/ckeditor5-clipboard" "43.3.1"
    "@ckeditor/ckeditor5-cloud-services" "43.3.1"
    "@ckeditor/ckeditor5-code-block" "43.3.1"
    "@ckeditor/ckeditor5-core" "43.3.1"
    "@ckeditor/ckeditor5-easy-image" "43.3.1"
    "@ckeditor/ckeditor5-editor-balloon" "43.3.1"
    "@ckeditor/ckeditor5-editor-classic" "43.3.1"
    "@ckeditor/ckeditor5-editor-decoupled" "43.3.1"
    "@ckeditor/ckeditor5-editor-inline" "43.3.1"
    "@ckeditor/ckeditor5-editor-multi-root" "43.3.1"
    "@ckeditor/ckeditor5-engine" "43.3.1"
    "@ckeditor/ckeditor5-enter" "43.3.1"
    "@ckeditor/ckeditor5-essentials" "43.3.1"
    "@ckeditor/ckeditor5-find-and-replace" "43.3.1"
    "@ckeditor/ckeditor5-font" "43.3.1"
    "@ckeditor/ckeditor5-heading" "43.3.1"
    "@ckeditor/ckeditor5-highlight" "43.3.1"
    "@ckeditor/ckeditor5-horizontal-line" "43.3.1"
    "@ckeditor/ckeditor5-html-embed" "43.3.1"
    "@ckeditor/ckeditor5-html-support" "43.3.1"
    "@ckeditor/ckeditor5-image" "43.3.1"
    "@ckeditor/ckeditor5-indent" "43.3.1"
    "@ckeditor/ckeditor5-language" "43.3.1"
    "@ckeditor/ckeditor5-link" "43.3.1"
    "@ckeditor/ckeditor5-list" "43.3.1"
    "@ckeditor/ckeditor5-markdown-gfm" "43.3.1"
    "@ckeditor/ckeditor5-media-embed" "43.3.1"
    "@ckeditor/ckeditor5-mention" "43.3.1"
    "@ckeditor/ckeditor5-minimap" "43.3.1"
    "@ckeditor/ckeditor5-page-break" "43.3.1"
    "@ckeditor/ckeditor5-paragraph" "43.3.1"
    "@ckeditor/ckeditor5-paste-from-office" "43.3.1"
    "@ckeditor/ckeditor5-remove-format" "43.3.1"
    "@ckeditor/ckeditor5-restricted-editing" "43.3.1"
    "@ckeditor/ckeditor5-select-all" "43.3.1"
    "@ckeditor/ckeditor5-show-blocks" "43.3.1"
    "@ckeditor/ckeditor5-source-editing" "43.3.1"
    "@ckeditor/ckeditor5-special-characters" "43.3.1"
    "@ckeditor/ckeditor5-style" "43.3.1"
    "@ckeditor/ckeditor5-table" "43.3.1"
    "@ckeditor/ckeditor5-theme-lark" "43.3.1"
    "@ckeditor/ckeditor5-typing" "43.3.1"
    "@ckeditor/ckeditor5-ui" "43.3.1"
    "@ckeditor/ckeditor5-undo" "43.3.1"
    "@ckeditor/ckeditor5-upload" "43.3.1"
    "@ckeditor/ckeditor5-utils" "43.3.1"
    "@ckeditor/ckeditor5-watchdog" "43.3.1"
    "@ckeditor/ckeditor5-widget" "43.3.1"
    "@ckeditor/ckeditor5-word-count" "43.3.1"

ckeditor5@45.0.0:
  version "45.0.0"
  resolved "https://registry.npmjs.org/ckeditor5/-/ckeditor5-45.0.0.tgz"
  integrity sha512-83LLE6nK6oeq2zGf2H5dwECtKET9/WBkFFwbaks7INmW2EqgijBluLwm2gnX/FPLpHzXxUUESAisr9HlH1gerQ==
  dependencies:
    "@ckeditor/ckeditor5-adapter-ckfinder" "45.0.0"
    "@ckeditor/ckeditor5-alignment" "45.0.0"
    "@ckeditor/ckeditor5-autoformat" "45.0.0"
    "@ckeditor/ckeditor5-autosave" "45.0.0"
    "@ckeditor/ckeditor5-basic-styles" "45.0.0"
    "@ckeditor/ckeditor5-block-quote" "45.0.0"
    "@ckeditor/ckeditor5-bookmark" "45.0.0"
    "@ckeditor/ckeditor5-ckbox" "45.0.0"
    "@ckeditor/ckeditor5-ckfinder" "45.0.0"
    "@ckeditor/ckeditor5-clipboard" "45.0.0"
    "@ckeditor/ckeditor5-cloud-services" "45.0.0"
    "@ckeditor/ckeditor5-code-block" "45.0.0"
    "@ckeditor/ckeditor5-core" "45.0.0"
    "@ckeditor/ckeditor5-easy-image" "45.0.0"
    "@ckeditor/ckeditor5-editor-balloon" "45.0.0"
    "@ckeditor/ckeditor5-editor-classic" "45.0.0"
    "@ckeditor/ckeditor5-editor-decoupled" "45.0.0"
    "@ckeditor/ckeditor5-editor-inline" "45.0.0"
    "@ckeditor/ckeditor5-editor-multi-root" "45.0.0"
    "@ckeditor/ckeditor5-emoji" "45.0.0"
    "@ckeditor/ckeditor5-engine" "45.0.0"
    "@ckeditor/ckeditor5-enter" "45.0.0"
    "@ckeditor/ckeditor5-essentials" "45.0.0"
    "@ckeditor/ckeditor5-find-and-replace" "45.0.0"
    "@ckeditor/ckeditor5-font" "45.0.0"
    "@ckeditor/ckeditor5-fullscreen" "45.0.0"
    "@ckeditor/ckeditor5-heading" "45.0.0"
    "@ckeditor/ckeditor5-highlight" "45.0.0"
    "@ckeditor/ckeditor5-horizontal-line" "45.0.0"
    "@ckeditor/ckeditor5-html-embed" "45.0.0"
    "@ckeditor/ckeditor5-html-support" "45.0.0"
    "@ckeditor/ckeditor5-icons" "45.0.0"
    "@ckeditor/ckeditor5-image" "45.0.0"
    "@ckeditor/ckeditor5-indent" "45.0.0"
    "@ckeditor/ckeditor5-language" "45.0.0"
    "@ckeditor/ckeditor5-link" "45.0.0"
    "@ckeditor/ckeditor5-list" "45.0.0"
    "@ckeditor/ckeditor5-markdown-gfm" "45.0.0"
    "@ckeditor/ckeditor5-media-embed" "45.0.0"
    "@ckeditor/ckeditor5-mention" "45.0.0"
    "@ckeditor/ckeditor5-minimap" "45.0.0"
    "@ckeditor/ckeditor5-page-break" "45.0.0"
    "@ckeditor/ckeditor5-paragraph" "45.0.0"
    "@ckeditor/ckeditor5-paste-from-office" "45.0.0"
    "@ckeditor/ckeditor5-remove-format" "45.0.0"
    "@ckeditor/ckeditor5-restricted-editing" "45.0.0"
    "@ckeditor/ckeditor5-select-all" "45.0.0"
    "@ckeditor/ckeditor5-show-blocks" "45.0.0"
    "@ckeditor/ckeditor5-source-editing" "45.0.0"
    "@ckeditor/ckeditor5-special-characters" "45.0.0"
    "@ckeditor/ckeditor5-style" "45.0.0"
    "@ckeditor/ckeditor5-table" "45.0.0"
    "@ckeditor/ckeditor5-theme-lark" "45.0.0"
    "@ckeditor/ckeditor5-typing" "45.0.0"
    "@ckeditor/ckeditor5-ui" "45.0.0"
    "@ckeditor/ckeditor5-undo" "45.0.0"
    "@ckeditor/ckeditor5-upload" "45.0.0"
    "@ckeditor/ckeditor5-utils" "45.0.0"
    "@ckeditor/ckeditor5-watchdog" "45.0.0"
    "@ckeditor/ckeditor5-widget" "45.0.0"
    "@ckeditor/ckeditor5-word-count" "45.0.0"

cleave.js@^1.6.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/cleave.js/-/cleave.js-1.6.0.tgz"
  integrity sha512-ivqesy3j5hQVG3gywPfwKPbi/7ZSftY/UNp5uphnqjr25yI2CP8FS2ODQPzuLXXnNLi29e2+PgPkkiKUXLs/Nw==

client-only@^0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/client-only/-/client-only-0.0.1.tgz"
  integrity sha512-IV3Ou0jSMzZrd3pZ48nLkT9DA7Ag1pnPzaiQhpW7c3RbcqqzvzzVu+L8gfqMp/8IM2MQtSiqaCxrrcfu8I8rMA==

clone@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz"
  integrity sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==

clsx@^1.1.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/clsx/-/clsx-1.2.1.tgz"
  integrity sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==

clsx@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/clsx/-/clsx-1.2.1.tgz"
  integrity sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==

clsx@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz"
  integrity sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==

color-convert@^2.0.1, color-convert@2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-parse@1.4.2:
  version "1.4.2"
  resolved "https://registry.npmjs.org/color-parse/-/color-parse-1.4.2.tgz"
  integrity sha512-RI7s49/8yqDj3fECFZjUI1Yi0z/Gq1py43oNJivAIIDSyJiOZLfYCRQEgn8HEVAj++PcRe8AnL2XF0fRJ3BTnA==
  dependencies:
    color-name "^1.0.0"

color-string@^1.9.1:
  version "1.9.1"
  resolved "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz"
  integrity sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color-support@^1.1.2:
  version "1.1.3"
  resolved "https://registry.npmjs.org/color-support/-/color-support-1.1.3.tgz"
  integrity sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==

commander@^4.0.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz"
  integrity sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==

console-control-strings@^1.0.0, console-control-strings@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/console-control-strings/-/console-control-strings-1.1.0.tgz"
  integrity sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==

convert-source-map@^1.5.0:
  version "1.9.0"
  resolved "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.9.0.tgz"
  integrity sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz"
  integrity sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==

core-js@^3.0.1:
  version "3.39.0"
  resolved "https://registry.npmjs.org/core-js/-/core-js-3.39.0.tgz"
  integrity sha512-raM0ew0/jJUqkJ0E6e8UDtl+y/7ktFivgWvqw8dNSQeNWoSDLvQ1H/RN3aPXB9tBd4/FhyR4RDPGhsNIMsAn7g==

cosmiconfig@^7.0.0:
  version "7.1.0"
  resolved "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.1.0.tgz"
  integrity sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

cross-fetch@^3.1.5:
  version "3.1.8"
  resolved "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.8.tgz"
  integrity sha512-cvA+JwZoU0Xq+h6WkMvAUqPEYy92Obet6UdKLfW60qn99ftItKjB5T+BkyWOFWe2pUyfQ+IJHmpOTznqk1M6Kg==
  dependencies:
    node-fetch "^2.6.12"

cross-spawn@^7.0.0:
  version "7.0.5"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.5.tgz"
  integrity sha512-ZVJrKKYunU38/76t0RMOulHOnUcbU9GbpWKAOZ0mhjr7CX6FVrH+4FrAapSOekrgFQ3f/8gwMEuIft0aKq6Hug==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-js@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/crypto-js/-/crypto-js-4.2.0.tgz"
  integrity sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==

css-box-model@^1.2.0:
  version "1.2.1"
  resolved "https://registry.npmjs.org/css-box-model/-/css-box-model-1.2.1.tgz"
  integrity sha512-a7Vr4Q/kd/aw96bnJG332W9V9LkJO69JRcaCYDUqjp6/z0w6VcZjgAcTbgFxEPfBgdnAwlh3iwu+hLopa+flJw==
  dependencies:
    tiny-invariant "^1.0.6"

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
  integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/*****************************/Vg==

csstype@^3.0.2:
  version "3.1.3"
  resolved "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz"
  integrity sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==

d3-array@^3.1.6, d3-array@^3.2.2, "d3-array@2 - 3", "d3-array@2.10.0 - 3":
  version "3.2.4"
  resolved "https://registry.npmjs.org/d3-array/-/d3-array-3.2.4.tgz"
  integrity sha512-tdQAmyA18i4J7wprpYq8ClcxZy3SC31QMeByyCFyRt7BVHdREQZ5lpzoe5mFEYZUWe+oq8HBvk9JjpibyEV4Jg==
  dependencies:
    internmap "1 - 2"

"d3-color@1 - 3":
  version "3.1.0"
  resolved "https://registry.npmjs.org/d3-color/-/d3-color-3.1.0.tgz"
  integrity sha512-zg/chbXyeBtMQ1LbD/WSoW2DpC3I0mpmPdW+ynRTj/x2DAWYrIY7qeZIHidozwV24m4iavr15lNwIwLxRmOxhA==

d3-ease@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/d3-ease/-/d3-ease-3.0.1.tgz"
  integrity sha512-wR/XK3D3XcLIZwpbvQwQ5fK+8Ykds1ip7A2Txe0yxncXSdq1L9skcG7blcedkOX+ZcgxGAmLX1FrRGbADwzi0w==

"d3-format@1 - 3":
  version "3.1.0"
  resolved "https://registry.npmjs.org/d3-format/-/d3-format-3.1.0.tgz"
  integrity sha512-YyUI6AEuY/Wpt8KWLgZHsIU86atmikuoOmCfommt0LYHiQSPjvX2AcFc38PX0CBpr2RCyZhjex+NS/LPOv6YqA==

d3-interpolate@^3.0.1, "d3-interpolate@1.2.0 - 3":
  version "3.0.1"
  resolved "https://registry.npmjs.org/d3-interpolate/-/d3-interpolate-3.0.1.tgz"
  integrity sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==
  dependencies:
    d3-color "1 - 3"

d3-path@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/d3-path/-/d3-path-3.1.0.tgz"
  integrity sha512-p3KP5HCf/bvjBSSKuXid6Zqijx7wIfNW+J/maPs+iwR35at5JCbLUT0LzF1cnjbCHWhqzQTIN2Jpe8pRebIEFQ==

d3-scale@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/d3-scale/-/d3-scale-4.0.2.tgz"
  integrity sha512-GZW464g1SH7ag3Y7hXjf8RoUuAFIqklOAq3MRl4OaWabTFJY9PN/E1YklhXLh+OQ3fM9yS2nOkCoS+WLZ6kvxQ==
  dependencies:
    d3-array "2.10.0 - 3"
    d3-format "1 - 3"
    d3-interpolate "1.2.0 - 3"
    d3-time "2.1.1 - 3"
    d3-time-format "2 - 4"

d3-shape@^3.1.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/d3-shape/-/d3-shape-3.2.0.tgz"
  integrity sha512-SaLBuwGm3MOViRq2ABk3eLoxwZELpH6zhl3FbAoJ7Vm1gofKx6El1Ib5z23NUEhF9AsGl7y+dzLe5Cw2AArGTA==
  dependencies:
    d3-path "^3.1.0"

"d3-time-format@2 - 4":
  version "4.1.0"
  resolved "https://registry.npmjs.org/d3-time-format/-/d3-time-format-4.1.0.tgz"
  integrity sha512-dJxPBlzC7NugB2PDLwo9Q8JiTR3M3e4/XANkreKSUxF8vvXKqm1Yfq4Q5dl8budlunRVlUUaDUgFt7eA8D6NLg==
  dependencies:
    d3-time "1 - 3"

d3-time@^3.0.0, "d3-time@1 - 3", "d3-time@2.1.1 - 3":
  version "3.1.0"
  resolved "https://registry.npmjs.org/d3-time/-/d3-time-3.1.0.tgz"
  integrity sha512-VqKjzBLejbSMT4IgbmVgDjpkYrNWUYJnbCGo874u7MMKIWsILRX+OpX/gTk8MqjpT1A/c6HY2dCA77ZN0lkQ2Q==
  dependencies:
    d3-array "2 - 3"

d3-timer@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/d3-timer/-/d3-timer-3.0.1.tgz"
  integrity sha512-ndfJ/JxxMd3nw31uyKoY2naivF+r29V+Lc0svZxe1JvvIRmi8hUsrMvdOwgS1o6uBHmiz91geQ0ylPP0aj1VUA==

date-arithmetic@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/date-arithmetic/-/date-arithmetic-4.1.0.tgz"
  integrity sha512-QWxYLR5P/6GStZcdem+V1xoto6DMadYWpMXU82ES3/RfR3Wdwr3D0+be7mgOJ+Ov0G9D5Dmb9T17sNLQYj9XOg==

date-fns@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/date-fns/-/date-fns-3.6.0.tgz"
  integrity sha512-fRHTG8g/Gif+kSh50gaGEdToemgfj74aRX3swtiouboip5JDLAyDE9F11nHMIcvOaXeOC6D7SpNhi7uFyB7Uww==

dateformat@^5.0.3:
  version "5.0.3"
  resolved "https://registry.npmjs.org/dateformat/-/dateformat-5.0.3.tgz"
  integrity sha512-Kvr6HmPXUMerlLcLF+Pwq3K7apHpYmGDVqrxcDasBg86UcKeTSNWbEzU8bwdXnxnR44FtMhJAxI4Bov6Y/KUfA==

dayjs@^1.11.12, dayjs@^1.11.7:
  version "1.11.13"
  resolved "https://registry.npmjs.org/dayjs/-/dayjs-1.11.13.tgz"
  integrity sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==

debug@^4.1.0, debug@^4.3.1, debug@~4.3.1, debug@~4.3.2, debug@4:
  version "4.3.7"
  resolved "https://registry.npmjs.org/debug/-/debug-4.3.7.tgz"
  integrity sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ==
  dependencies:
    ms "^2.1.3"

decimal.js-light@^2.4.1:
  version "2.5.1"
  resolved "https://registry.npmjs.org/decimal.js-light/-/decimal.js-light-2.5.1.tgz"
  integrity sha512-qIMFpTMZmny+MMIitAB6D7iVPEorVw6YQRWkvarTkT4tBeSLLiHzcwj6q0MmYSFCiVpiqPJTJEYIrpcPzVEIvg==

decompress-response@^4.2.0:
  version "4.2.1"
  resolved "https://registry.npmjs.org/decompress-response/-/decompress-response-4.2.1.tgz"
  integrity sha512-jOSne2qbyE+/r8G1VU+G/82LBs2Fs4LAsTiLSHOCOMZQl2OKZ6i8i4IyHemTe+/yIXOtTcRQMzPcgyhoFlqPkw==
  dependencies:
    mimic-response "^2.0.0"

deepmerge@^2.1.1:
  version "2.2.1"
  resolved "https://registry.npmjs.org/deepmerge/-/deepmerge-2.2.1.tgz"
  integrity sha512-R9hc1Xa/NOBi9WRVUWg19rl1UB7Tt4kuPd+thNJgFZoxXsTz7ncaPaeIm+40oSGuP33DfMb4sZt1QIGiJzC4EA==

deepmerge@^4.0.0:
  version "4.3.1"
  resolved "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz"
  integrity sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delegates/-/delegates-1.0.0.tgz"
  integrity sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==

dequal@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/dequal/-/dequal-2.0.3.tgz"
  integrity sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==

detect-libc@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.3.tgz"
  integrity sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==

detect-libc@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.3.tgz"
  integrity sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==

dfa@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/dfa/-/dfa-1.2.0.tgz"
  integrity sha512-ED3jP8saaweFTjeGX8HQPjeC1YYyZs98jGNZx6IiBvxW7JG5v492kamAQB3m2wop07CvU/RQmzcKr6bgcC5D/Q==

didyoumean@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.2.tgz"
  integrity sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==

dlv@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz"
  integrity sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==

dnd-core@^16.0.1:
  version "16.0.1"
  resolved "https://registry.npmjs.org/dnd-core/-/dnd-core-16.0.1.tgz"
  integrity sha512-HK294sl7tbw6F6IeuK16YSBUoorvHpY8RHO+9yFfaJyCDVb6n7PRcezrOEOa2SBCqiYpemh5Jx20ZcjKdFAVng==
  dependencies:
    "@react-dnd/asap" "^5.0.1"
    "@react-dnd/invariant" "^4.0.1"
    redux "^4.2.0"

dom-helpers@^5.0.1, dom-helpers@^5.2.0, dom-helpers@^5.2.1:
  version "5.2.1"
  resolved "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz"
  integrity sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==
  dependencies:
    "@babel/runtime" "^7.8.7"
    csstype "^3.0.2"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz"
  integrity sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==

electron-to-chromium@^1.5.41:
  version "1.5.56"
  resolved "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.56.tgz"
  integrity sha512-7lXb9dAvimCFdvUMTyucD4mnIndt/xhRKFAlky0CyFogdnNmdPQNoHI23msF/2V4mpTxMzgMdjK4+YRlFlRQZw==

emoji-regex@^10.3.0:
  version "10.4.0"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-10.4.0.tgz"
  integrity sha512-EC+0oUMY1Rqm4O6LLrgjtYDvcVYTy7chDnM4Q7030tP4Kwj3u/pR6gP9ygnp2CJMK5Gq+9Q2oqmrFJAz01DXjw==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz"
  integrity sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==

engine.io-client@~6.5.0:
  version "6.5.4"
  resolved "https://registry.npmjs.org/engine.io-client/-/engine.io-client-6.5.4.tgz"
  integrity sha512-GeZeeRjpD2qf49cZQ0Wvh/8NJNfeXkXXcoGh+F77oEAgo9gUHwT1fCRxSNU+YEEaysOJTnsFHmM5oAcPy4ntvQ==
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    debug "~4.3.1"
    engine.io-parser "~5.2.1"
    ws "~8.17.1"
    xmlhttprequest-ssl "~2.0.0"

engine.io-parser@~5.2.1:
  version "5.2.3"
  resolved "https://registry.npmjs.org/engine.io-parser/-/engine.io-parser-5.2.3.tgz"
  integrity sha512-HqD3yTBfnBxIrbnM1DoD6Pcq8NECnh8d4As1Qgh0z5Gg3jRRIqijury0CL3ghu/edArpUYiYqQiDUQBIs4np3Q==

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
  integrity sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==
  dependencies:
    is-arrayish "^0.2.1"

es-toolkit@1.32.0:
  version "1.32.0"
  resolved "https://registry.npmjs.org/es-toolkit/-/es-toolkit-1.32.0.tgz"
  integrity sha512-ZfSfHP1l6ubgW/B/FRtqb9bYdMvI6jizbOSfbwwJNcOQ1QE6TFsC3jpQkZ900uUPSR3t3SU5Ds7UWKnYz+uP8Q==

esbuild-linux-64@0.15.18:
  version "0.15.18"
  resolved "https://registry.npmjs.org/esbuild-linux-64/-/esbuild-linux-64-0.15.18.tgz"
  integrity sha512-hNSeP97IviD7oxLKFuii5sDPJ+QHeiFTFLoLm7NZQligur8poNOWGIgpQ7Qf8Balb69hptMZzyOBIPtY09GZYw==

esbuild@^0.15.9:
  version "0.15.18"
  resolved "https://registry.npmjs.org/esbuild/-/esbuild-0.15.18.tgz"
  integrity sha512-x/R72SmW3sSFRm5zrrIjAhCeQSAWoni3CmHEqfQrZIQTM3lVCdehdwuIqaOtfC2slvpdlLa62GYoN8SxT23m6Q==
  optionalDependencies:
    "@esbuild/android-arm" "0.15.18"
    "@esbuild/linux-loong64" "0.15.18"
    esbuild-android-64 "0.15.18"
    esbuild-android-arm64 "0.15.18"
    esbuild-darwin-64 "0.15.18"
    esbuild-darwin-arm64 "0.15.18"
    esbuild-freebsd-64 "0.15.18"
    esbuild-freebsd-arm64 "0.15.18"
    esbuild-linux-32 "0.15.18"
    esbuild-linux-64 "0.15.18"
    esbuild-linux-arm "0.15.18"
    esbuild-linux-arm64 "0.15.18"
    esbuild-linux-mips64le "0.15.18"
    esbuild-linux-ppc64le "0.15.18"
    esbuild-linux-riscv64 "0.15.18"
    esbuild-linux-s390x "0.15.18"
    esbuild-netbsd-64 "0.15.18"
    esbuild-openbsd-64 "0.15.18"
    esbuild-sunos-64 "0.15.18"
    esbuild-windows-32 "0.15.18"
    esbuild-windows-64 "0.15.18"
    esbuild-windows-arm64 "0.15.18"

escalade@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz"
  integrity sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  integrity sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==

estree-walker@^2.0.1, estree-walker@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz"
  integrity sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==

eventemitter3@^4.0.1:
  version "4.0.7"
  resolved "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz"
  integrity sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==

events@^3.3.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/events/-/events-3.3.0.tgz"
  integrity sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==

fake-xml-http-request@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/fake-xml-http-request/-/fake-xml-http-request-2.1.2.tgz"
  integrity sha512-HaFMBi7r+oEC9iJNpc3bvcW7Z7iLmM26hPDmlb0mFwyANSsOQAtJxbdWsXITKOzZUyMYK0zYCv3h5yDj9TsiXg==

fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-equals@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/fast-equals/-/fast-equals-5.0.1.tgz"
  integrity sha512-WF1Wi8PwwSY7/6Kx0vKXtw8RwuSGoM1bvDaJbu7MxDlR1vovZjIAKrnzyrThgAjm6JDTu0fVgWXDlMGspodfoQ==

fast-glob@^3.3.0:
  version "3.3.2"
  resolved "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.2.tgz"
  integrity sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fastq@^1.6.0:
  version "1.17.1"
  resolved "https://registry.npmjs.org/fastq/-/fastq-1.17.1.tgz"
  integrity sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==
  dependencies:
    reusify "^1.0.4"

file-selector@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/file-selector/-/file-selector-2.1.0.tgz"
  integrity sha512-ZuXAqGePcSPz4JuerOY06Dzzq0hrmQ6VGoXVzGyFI1npeOfBgqGIKKpznfYWRkSLJlXutkqVC5WvGZtkFVhu9Q==
  dependencies:
    tslib "^2.7.0"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz"
  integrity sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==
  dependencies:
    to-regex-range "^5.0.1"

find-root@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/find-root/-/find-root-1.1.0.tgz"
  integrity sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng==

flatpickr@^4.6.2:
  version "4.6.13"
  resolved "https://registry.npmjs.org/flatpickr/-/flatpickr-4.6.13.tgz"
  integrity sha512-97PMG/aywoYpB4IvbvUJi0RQi8vearvU0oov1WW3k0WZPBMrTQVqekSX5CjSG/M4Q3i6A/0FKXC7RyAoAUUSPw==

fontkit@^2.0.2:
  version "2.0.4"
  resolved "https://registry.npmjs.org/fontkit/-/fontkit-2.0.4.tgz"
  integrity sha512-syetQadaUEDNdxdugga9CpEYVaQIxOwk7GlwZWWZ19//qW4zE5bknOKeMBDYAASwnpaSHKJITRLMF9m1fp3s6g==
  dependencies:
    "@swc/helpers" "^0.5.12"
    brotli "^1.3.2"
    clone "^2.1.2"
    dfa "^1.2.0"
    fast-deep-equal "^3.1.3"
    restructure "^3.0.0"
    tiny-inflate "^1.0.3"
    unicode-properties "^1.4.0"
    unicode-trie "^2.0.0"

foreground-child@^3.1.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.0.tgz"
  integrity sha512-Ld2g8rrAyMYFXBhEqMz8ZAHBi4J4uS1i/CxGMDnjyFWddMXLVcDp051DZfu+t7+ab7Wv6SMqpWmyFIj5UbfFvg==
  dependencies:
    cross-spawn "^7.0.0"
    signal-exit "^4.0.1"

formik@^2.4.6:
  version "2.4.6"
  resolved "https://registry.npmjs.org/formik/-/formik-2.4.6.tgz"
  integrity sha512-A+2EI7U7aG296q2TLGvNapDNTZp1khVt5Vk0Q/fyfSROss0V/V6+txt2aJnwEos44IxTCW/LYAi/zgWzlevj+g==
  dependencies:
    "@types/hoist-non-react-statics" "^3.3.1"
    deepmerge "^2.1.1"
    hoist-non-react-statics "^3.3.0"
    lodash "^4.17.21"
    lodash-es "^4.17.21"
    react-fast-compare "^2.0.1"
    tiny-warning "^1.0.2"
    tslib "^2.0.0"

fraction.js@^4.3.7:
  version "4.3.7"
  resolved "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz"
  integrity sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==

framer-motion@^11.15.0:
  version "11.15.0"
  resolved "https://registry.npmjs.org/framer-motion/-/framer-motion-11.15.0.tgz"
  integrity sha512-MLk8IvZntxOMg7lDBLw2qgTHHv664bYoYmnFTmE0Gm/FW67aOJk0WM3ctMcG+Xhcv+vh5uyyXwxvxhSeJzSe+w==
  dependencies:
    motion-dom "^11.14.3"
    motion-utils "^11.14.3"
    tslib "^2.4.0"

fs-minipass@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/fs-minipass/-/fs-minipass-2.1.0.tgz"
  integrity sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==
  dependencies:
    minipass "^3.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

fuzzysort@3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/fuzzysort/-/fuzzysort-3.1.0.tgz"
  integrity sha512-sR9BNCjBg6LNgwvxlBd0sBABvQitkLzoVY9MYYROQVX/FvfJ4Mai9LsGhDgd8qYdds0bY77VzYd5iuB+v5rwQQ==

gauge@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmjs.org/gauge/-/gauge-3.0.2.tgz"
  integrity sha512-+5J6MS/5XksCuXq++uFRsnUd7Ovu1XenbeuIuNRJxYWjgQbPuFhT14lAvsWfqfAmnwluf1OwMjz39HjfLPci0Q==
  dependencies:
    aproba "^1.0.3 || ^2.0.0"
    color-support "^1.1.2"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.1"
    object-assign "^4.1.1"
    signal-exit "^3.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"
    wide-align "^1.1.2"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz"
  integrity sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==

get-user-locale@^2.2.1:
  version "2.3.2"
  resolved "https://registry.npmjs.org/get-user-locale/-/get-user-locale-2.3.2.tgz"
  integrity sha512-O2GWvQkhnbDoWFUJfaBlDIKUEdND8ATpBXD6KXcbhxlfktyD/d8w6mkzM/IlQEqGZAMz/PW6j6Hv53BiigKLUQ==
  dependencies:
    mem "^8.0.0"

glob-parent@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
  dependencies:
    is-glob "^4.0.3"

glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob@^10.3.10:
  version "10.4.5"
  resolved "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz"
  integrity sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^1.11.1"

glob@^7.1.3:
  version "7.2.3"
  resolved "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz"
  integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globalize@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmjs.org/globalize/-/globalize-0.1.1.tgz"
  integrity sha512-5e01v8eLGfuQSOvx2MsDMOWS0GFtCx1wPzQSmcHw4hkxFzrQDBO3Xwg/m8Hr/7qXMrHeOIE29qWVzyv06u1TZA==

globals@^11.1.0:
  version "11.12.0"
  resolved "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
  integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==

has-unicode@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz"
  integrity sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==

hasown@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

hls.js@^1.5.19:
  version "1.5.19"
  resolved "https://registry.npmjs.org/hls.js/-/hls.js-1.5.19.tgz"
  integrity sha512-C020dKWEJcyvLnrqsFKW4q6D/6IEzKWdhktIS5bgoyEFE8lHgrFBq4RIngdy113abJOlIruhv8qjg7UX8hwxOw==

hoist-non-react-statics@^3.3.0, hoist-non-react-statics@^3.3.1, hoist-non-react-statics@^3.3.2:
  version "3.3.2"
  resolved "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz"
  integrity sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==
  dependencies:
    react-is "^16.7.0"

hsl-to-hex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/hsl-to-hex/-/hsl-to-hex-1.0.0.tgz"
  integrity sha512-K6GVpucS5wFf44X0h2bLVRDsycgJmf9FF2elg+CrqD8GcFU8c6vYhgXn8NjUkFCwj+xDFb70qgLbTUm6sxwPmA==
  dependencies:
    hsl-to-rgb-for-reals "^1.1.0"

hsl-to-rgb-for-reals@^1.1.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/hsl-to-rgb-for-reals/-/hsl-to-rgb-for-reals-1.1.1.tgz"
  integrity sha512-LgOWAkrN0rFaQpfdWBQlv/VhkOxb5AsBjk6NQVx4yEzWS923T07X0M1Y0VNko2H52HeSpZrZNNMJ0aFqsdVzQg==

html-parse-stringify@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/html-parse-stringify/-/html-parse-stringify-3.0.1.tgz"
  integrity sha512-KknJ50kTInJ7qIScF3jeaFRpMpE8/lfiTdzf/twXyPBLAGrLRTmkz3AdTnKeh40X8k9L2fdYwEp/42WGXIRGcg==
  dependencies:
    void-elements "3.1.0"

https-proxy-agent@^5.0.0:
  version "5.0.1"
  resolved "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz"
  integrity sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==
  dependencies:
    agent-base "6"
    debug "4"

hyphen@^1.6.4:
  version "1.10.6"
  resolved "https://registry.npmjs.org/hyphen/-/hyphen-1.10.6.tgz"
  integrity sha512-fXHXcGFTXOvZTSkPJuGOQf5Lv5T/R2itiiCVPg9LxAje5D00O0pP83yJShFq5V89Ly//Gt6acj7z8pbBr34stw==

i18next@^25.1.3, "i18next@>= 23.2.3":
  version "25.1.3"
  resolved "https://registry.npmjs.org/i18next/-/i18next-25.1.3.tgz"
  integrity sha512-VY1iKox3YWPRTNMHFdgk5TV+Jq6rNKexLCLpPmP5oXXJ5Kl7yDBi3ycZ5fyEKZ1tNBW5gOqD4WV0XqE7rl3JUg==
  dependencies:
    "@babel/runtime" "^7.27.1"

immediate@~3.0.5:
  version "3.0.6"
  resolved "https://registry.npmjs.org/immediate/-/immediate-3.0.6.tgz"
  integrity sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ==

immer@^9.0.21:
  version "9.0.21"
  resolved "https://registry.npmjs.org/immer/-/immer-9.0.21.tgz"
  integrity sha512-bc4NBHqOqSfRW7POMkHd51LvClaeMXpm8dx0e8oE2GORbq5aRK7Bxl4FyzVLdGtLmvLKL7BTDBG5ACQm4HWjTA==

immutability-helper@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/immutability-helper/-/immutability-helper-3.1.1.tgz"
  integrity sha512-Q0QaXjPjwIju/28TsugCHNEASwoCcJSyJV3uO1sOIQGI0jKgm9f41Lvz0DZj3n46cNCyAZTsEYoY4C2bVRUzyQ==

immutable@^4.0.0:
  version "4.3.7"
  resolved "https://registry.npmjs.org/immutable/-/immutable-4.3.7.tgz"
  integrity sha512-1hqclzwYwjRDFLjcFxOM5AYkkG0rpFPpr1RLPMEuGczoS7YA8gLhy8SWXYRAA/XwfEHpfo3cw5JGioS32fnMRw==

import-fresh@^3.2.1:
  version "3.3.0"
  resolved "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz"
  integrity sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

inflected@^2.0.4:
  version "2.1.0"
  resolved "https://registry.npmjs.org/inflected/-/inflected-2.1.0.tgz"
  integrity sha512-hAEKNxvHf2Iq3H60oMBHkB4wl5jn3TPF3+fXek/sRwAB5gP9xWs4r7aweSF95f99HFoz69pnZTcu8f0SIHV18w==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@^2.0.3, inherits@~2.0.3, inherits@2:
  version "2.0.4"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

"internmap@1 - 2":
  version "2.0.3"
  resolved "https://registry.npmjs.org/internmap/-/internmap-2.0.3.tgz"
  integrity sha512-5Hh7Y1wQbvY5ooGgPbDaL5iYLAPzMTUrjMulskHLH6wnv/A+1q5rgEaiuqEjB+oxGXIVZs1FF+R/KPN3ZSQYYg==

invariant@^2.2.4:
  version "2.2.4"
  resolved "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz"
  integrity sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==
  dependencies:
    loose-envify "^1.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
  integrity sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz"
  integrity sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-core-module@^2.13.0:
  version "2.15.1"
  resolved "https://registry.npmjs.org/is-core-module/-/is-core-module-2.15.1.tgz"
  integrity sha512-z0vtXSwucUJtANQWldhbtbt7BnL0vxiFjIdDLAatwhDYty2bad6s+rijD6Ri4YuYJubLzIJLUidCh09e1djEVQ==
  dependencies:
    hasown "^2.0.2"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-url@^1.2.4:
  version "1.2.4"
  resolved "https://registry.npmjs.org/is-url/-/is-url-1.2.4.tgz"
  integrity sha512-ITvGim8FhRiYe4IQ5uHSkj7pVaPDrCTkNd3yq3cV7iZAcJdHTUMPMEHcqSOy9xZ9qFenQCvi+2wjH9a1nXqHww==

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==

jackspeak@^3.1.2:
  version "3.4.3"
  resolved "https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz"
  integrity sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jay-peg@^1.0.2:
  version "1.1.0"
  resolved "https://registry.npmjs.org/jay-peg/-/jay-peg-1.1.0.tgz"
  integrity sha512-WhyKySfx5CEFoKDnpmHyJUrpX5fUrr/X3kqVHISmiO9jrJC73RQBOAZJB8bDrWT4PHEkl0QgNZLlWJfAWAIFew==
  dependencies:
    restructure "^3.0.0"

jiti@^1.21.0:
  version "1.21.6"
  resolved "https://registry.npmjs.org/jiti/-/jiti-1.21.6.tgz"
  integrity sha512-2yTgeWTWzMWkHu6Jp9NKgePDaYHbntiwvYuuJLbbN9vl7DC9DvXKOB2BC3ZZ92D3cvV/aflH0osDfwpHepQ53w==

js-cookie@^3.0.5:
  version "3.0.5"
  resolved "https://registry.npmjs.org/js-cookie/-/js-cookie-3.0.5.tgz"
  integrity sha512-cEiJEAEoIbWfCZYKWhVwFuvPX1gETRYPw6LlaTKoxD3s2AkXzkCjnp6h0V77ozyqj0jakteJ4YqDJT830+lVGw==

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

jsesc@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/jsesc/-/jsesc-3.0.2.tgz"
  integrity sha512-xKqzzWXDttJuOcawBt4KnKHHIf5oQ/Cxax+0PWFG+DFDgHNAdi+TXECADI+RYiFUMmx8792xsMbbgXj4CwnP4g==

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==

json5@^2.2.3:
  version "2.2.3"
  resolved "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz"
  integrity sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==

leaflet@^1.9.0, leaflet@^1.9.3:
  version "1.9.4"
  resolved "https://registry.npmjs.org/leaflet/-/leaflet-1.9.4.tgz"
  integrity sha512-nxS1ynzJOmOlHp+iL3FyWqK89GtNL8U8rvlMOsQdTTssxZwCXh8N2NB3GDQOL+YR3XnWyZAxwQixURb+FA74PA==

lie@3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/lie/-/lie-3.1.1.tgz"
  integrity sha512-RiNhHysUjhrDQntfYSfY4MU24coXXdEOgw9WGcKHNeEwffDYbF//u87M1EWaMGzuFoSbqW0C9C6lEEhDOAswfw==
  dependencies:
    immediate "~3.0.5"

lilconfig@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/lilconfig/-/lilconfig-2.1.0.tgz"
  integrity sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==

lilconfig@^3.0.0:
  version "3.1.2"
  resolved "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.2.tgz"
  integrity sha512-eop+wDAvpItUys0FWkHIKeC9ybYrTGbU41U5K7+bttZZeohvnY7M9dZ5kB21GNWiFT2q1OoPTvncPCgSOVO5ow==

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==

load-script@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/load-script/-/load-script-1.0.0.tgz"
  integrity sha512-kPEjMFtZvwL9TaZo0uZ2ml+Ye9HUMmPwbYRJ324qF9tqMejwykJ5ggTyvzmrbBeapCAbk98BSbTeovHEEP1uCA==

localforage@^1.10.0:
  version "1.10.0"
  resolved "https://registry.npmjs.org/localforage/-/localforage-1.10.0.tgz"
  integrity sha512-14/H1aX7hzBBmmh7sGPd+AOMkkIrHM3Z1PAyGgZigA1H1p5O5ANnMyWzvpAETtG68/dC4pC0ncy3+PPGzXZHPg==
  dependencies:
    lie "3.1.1"

lodash-es@^4.17.21, lodash-es@4.17.21:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz"
  integrity sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  integrity sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz"
  integrity sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag==

lodash.throttle@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/lodash.throttle/-/lodash.throttle-4.1.1.tgz"
  integrity sha512-wIkUCfVKpVsWo3JSZlc+8MB5it+2AN5W8J7YVMST30UrvcQNZ1Okbj+rbVniijTWE6FGYy4XJq/rHkas8qJMLQ==

lodash@^4.0.0, lodash@^4.17.21:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

long@^5.0.0:
  version "5.3.2"
  resolved "https://registry.npmjs.org/long/-/long-5.3.2.tgz"
  integrity sha512-mNAgZ1GmyNhD7AuqnTG3/VQ26o760+ZYBPKjPvugO8+nLbYfX6TVpJPseBvopbdY+qpZ/lKUnmEc1LeZYS3QAA==

loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
  integrity sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lru-cache@^10.2.0:
  version "10.4.3"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz"
  integrity sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz"
  integrity sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==
  dependencies:
    yallist "^3.0.2"

lucide-react@^0.462.0:
  version "0.462.0"
  resolved "https://registry.npmjs.org/lucide-react/-/lucide-react-0.462.0.tgz"
  integrity sha512-NTL7EbAao9IFtuSivSZgrAh4fZd09Lr+6MTkqIxuHaH2nnYiYIzXPo06cOxHg9wKLdj6LL8TByG4qpePqwgx/g==

luxon@^3.2.1, luxon@3.5.0:
  version "3.5.0"
  resolved "https://registry.npmjs.org/luxon/-/luxon-3.5.0.tgz"
  integrity sha512-rh+Zjr6DNfUYR3bPwJEnuwDdqMbxZW7LOQfUN4B54+Cl+0o5zaU9RJ6bcidfDtC1cWCZXQ+nvX8bf6bAji37QQ==

magic-string@^0.26.7:
  version "0.26.7"
  resolved "https://registry.npmjs.org/magic-string/-/magic-string-0.26.7.tgz"
  integrity sha512-hX9XH3ziStPoPhJxLq1syWuZMxbDvGNbVchfrdCtanC7D13888bMFow61x8axrx+GfHLtVeAx2kxL7tTGRl+Ow==
  dependencies:
    sourcemap-codec "^1.4.8"

magic-string@^0.30.3:
  version "0.30.12"
  resolved "https://registry.npmjs.org/magic-string/-/magic-string-0.30.12.tgz"
  integrity sha512-Ea8I3sQMVXr8JhN4z+H/d8zwo+tYDgHE9+5G4Wnrwhs0gaK9fXTKx0Tw5Xwsd/bCPTTZNRAdpyzvoeORe9LYpw==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

make-cancellable-promise@^1.3.1:
  version "1.3.2"
  resolved "https://registry.npmjs.org/make-cancellable-promise/-/make-cancellable-promise-1.3.2.tgz"
  integrity sha512-GCXh3bq/WuMbS+Ky4JBPW1hYTOU+znU+Q5m9Pu+pI8EoUqIHk9+tviOKC6/qhHh8C4/As3tzJ69IF32kdz85ww==

make-dir@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz"
  integrity sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==
  dependencies:
    semver "^6.0.0"

make-event-props@^1.6.0:
  version "1.6.2"
  resolved "https://registry.npmjs.org/make-event-props/-/make-event-props-1.6.2.tgz"
  integrity sha512-iDwf7mA03WPiR8QxvcVHmVWEPfMY1RZXerDVNCRYW7dUr2ppH3J58Rwb39/WG39yTZdRSxr3x+2v22tvI0VEvA==

map-age-cleaner@^0.1.3:
  version "0.1.3"
  resolved "https://registry.npmjs.org/map-age-cleaner/-/map-age-cleaner-0.1.3.tgz"
  integrity sha512-bJzx6nMoP6PDLPBFmg7+xRKeFZvFboMrGlxmNj9ClvX53KrmvM5bXFXEWjbz4cz1AFn+jWJ9z/DJSz7hrs0w3w==
  dependencies:
    p-defer "^1.0.0"

marked@4.0.12:
  version "4.0.12"
  resolved "https://registry.npmjs.org/marked/-/marked-4.0.12.tgz"
  integrity sha512-hgibXWrEDNBWgGiK18j/4lkS6ihTe9sxtV4Q1OQppb/0zzyPSzoFANBa5MfsG/zgsWklmNnhm0XACZOH/0HBiQ==

match-sorter@^6.3.1:
  version "6.3.4"
  resolved "https://registry.npmjs.org/match-sorter/-/match-sorter-6.3.4.tgz"
  integrity sha512-jfZW7cWS5y/1xswZo8VBOdudUiSd9nifYRWphc9M5D/ee4w4AoXLgBEdRbgVaxbMuagBPeUC5y2Hi8DO6o9aDg==
  dependencies:
    "@babel/runtime" "^7.23.8"
    remove-accents "0.5.0"

media-engine@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/media-engine/-/media-engine-1.0.3.tgz"
  integrity sha512-aa5tG6sDoK+k70B9iEX1NeyfT8ObCKhNDs6lJVpwF6r8vhUfuKMslIcirq6HIUYuuUYLefcEQOn9bSBOvawtwg==

mem@^8.0.0:
  version "8.1.1"
  resolved "https://registry.npmjs.org/mem/-/mem-8.1.1.tgz"
  integrity sha512-qFCFUDs7U3b8mBDPyz5EToEKoAkgCzqquIgi9nkkR9bixxOVOre+09lbuH7+9Kn2NFpm56M3GUWVbU2hQgdACA==
  dependencies:
    map-age-cleaner "^0.1.3"
    mimic-fn "^3.1.0"

memoize-one@^5.1.1:
  version "5.2.1"
  resolved "https://registry.npmjs.org/memoize-one/-/memoize-one-5.2.1.tgz"
  integrity sha512-zYiwtZUcYyXKo/np96AGZAckk+FWWsUdJ3cHGGmld7+AhvcWmQyGCYUh1hc4Q/pkOhb65dQR/pqCyK0cOaHz4Q==

memoize-one@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/memoize-one/-/memoize-one-6.0.0.tgz"
  integrity sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw==

merge-refs@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/merge-refs/-/merge-refs-1.3.0.tgz"
  integrity sha512-nqXPXbso+1dcKDpPCXvwZyJILz+vSLqGGOnDrYHQYE+B8n9JTCekVLC65AfCpR4ggVyA/45Y0iR9LDyS2iI+zA==

merge2@^1.3.0:
  version "1.4.1"
  resolved "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==

micromatch@^4.0.4, micromatch@^4.0.5:
  version "4.0.8"
  resolved "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz"
  integrity sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mimic-fn@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/mimic-fn/-/mimic-fn-3.1.0.tgz"
  integrity sha512-Ysbi9uYW9hFyfrThdDEQuykN4Ey6BuwPD2kpI5ES/nFTDn/98yxYNLZJcgUAKPT/mcrLLKaGzJR9YVxJrIdASQ==

mimic-response@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/mimic-response/-/mimic-response-2.1.0.tgz"
  integrity sha512-wXqjST+SLt7R009ySCglWBCFpjUygmCIfD790/kVbiGmUgfYGuB14PiTd5DwVxSV4NcYHjzMkoj5LjQZwTQLEA==

minimatch@^3.1.1:
  version "3.1.2"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.4:
  version "9.0.5"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz"
  integrity sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==
  dependencies:
    brace-expansion "^2.0.1"

minipass@^3.0.0:
  version "3.3.6"
  resolved "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz"
  integrity sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==
  dependencies:
    yallist "^4.0.0"

minipass@^5.0.0, "minipass@^5.0.0 || ^6.0.2 || ^7.0.0":
  version "5.0.0"
  resolved "https://registry.npmjs.org/minipass/-/minipass-5.0.0.tgz"
  integrity sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==

minipass@^7.1.2:
  version "7.1.2"
  resolved "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz"
  integrity sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==

minizlib@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npmjs.org/minizlib/-/minizlib-2.1.2.tgz"
  integrity sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==
  dependencies:
    minipass "^3.0.0"
    yallist "^4.0.0"

miragejs@^0.1.47:
  version "0.1.48"
  resolved "https://registry.npmjs.org/miragejs/-/miragejs-0.1.48.tgz"
  integrity sha512-MGZAq0Q3OuRYgZKvlB69z4gLN4G3PvgC4A2zhkCXCXrLD5wm2cCnwNB59xOBVA+srZ0zEes6u+VylcPIkB4SqA==
  dependencies:
    "@miragejs/pretender-node-polyfill" "^0.1.0"
    inflected "^2.0.4"
    lodash "^4.0.0"
    pretender "^3.4.7"

mkdirp@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz"
  integrity sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==

moment-timezone@^0.5.40, moment-timezone@^0.5.46:
  version "0.5.46"
  resolved "https://registry.npmjs.org/moment-timezone/-/moment-timezone-0.5.46.tgz"
  integrity sha512-ZXm9b36esbe7OmdABqIWJuBBiLLwAjrN7CE+7sYdCCx82Nabt1wHDj8TVseS59QIlfFPbOoiBPm6ca9BioG4hw==
  dependencies:
    moment "^2.29.4"

moment@^2.29.4, moment@^2.30.1:
  version "2.30.1"
  resolved "https://registry.npmjs.org/moment/-/moment-2.30.1.tgz"
  integrity sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==

motion-dom@^11.14.3:
  version "11.14.3"
  resolved "https://registry.npmjs.org/motion-dom/-/motion-dom-11.14.3.tgz"
  integrity sha512-lW+D2wBy5vxLJi6aCP0xyxTxlTfiu+b+zcpVbGVFUxotwThqhdpPRSmX8xztAgtZMPMeU0WGVn/k1w4I+TbPqA==

motion-utils@^11.14.3:
  version "11.14.3"
  resolved "https://registry.npmjs.org/motion-utils/-/motion-utils-11.14.3.tgz"
  integrity sha512-Xg+8xnqIJTpr0L/cidfTTBFkvRw26ZtGGuIhA94J9PQ2p4mEa06Xx7QVYZH0BP+EpMSaDlu+q0I0mmvwADPsaQ==

ms@^2.1.3:
  version "2.1.3"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

mz@^2.7.0:
  version "2.7.0"
  resolved "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz"
  integrity sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nan@^2.17.0:
  version "2.22.0"
  resolved "https://registry.npmjs.org/nan/-/nan-2.22.0.tgz"
  integrity sha512-nbajikzWTMwsW+eSsNm3QwlOs7het9gGJU5dDZzRTQGk03vyBOauxgI4VakDzE0PtsGTmXPsXTbbjVhRwR5mpw==

nanoclone@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmjs.org/nanoclone/-/nanoclone-0.2.1.tgz"
  integrity sha512-wynEP02LmIbLpcYw8uBKpcfF6dmg2vcpKqxeH5UcoKEYdExslsdUA4ugFauuaeYdTB76ez6gJW8XAZ6CgkXYxA==

nanoid@^3.3.7:
  version "3.3.7"
  resolved "https://registry.npmjs.org/nanoid/-/nanoid-3.3.7.tgz"
  integrity sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==

node-addon-api@^7.0.0:
  version "7.1.1"
  resolved "https://registry.npmjs.org/node-addon-api/-/node-addon-api-7.1.1.tgz"
  integrity sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==

node-fetch@^2.6.12, node-fetch@^2.6.7:
  version "2.7.0"
  resolved "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz"
  integrity sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==
  dependencies:
    whatwg-url "^5.0.0"

node-releases@^2.0.18:
  version "2.0.18"
  resolved "https://registry.npmjs.org/node-releases/-/node-releases-2.0.18.tgz"
  integrity sha512-d9VeXT4SJ7ZeOqGX6R5EM022wpL+eWPooLI+5UpWn2jCT1aosUQEhQP214x33Wkwx3JQMvIm+tIoVOdodFS40g==

nopt@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/nopt/-/nopt-5.0.0.tgz"
  integrity sha512-Tbj67rffqceeLpcRXrT7vKAN8CwfPeIBgM7E6iBkmKLV7bEMwpGgYLGv0jACUsECaa/vuxP0IjEont6umdMgtQ==
  dependencies:
    abbrev "1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz"
  integrity sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==

normalize-svg-path@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/normalize-svg-path/-/normalize-svg-path-1.1.0.tgz"
  integrity sha512-r9KHKG2UUeB5LoTouwDzBy2VxXlHsiM6fyLQvnJa0S5hrhzqElH/CH7TUGhT1fVvIYBIKf3OpY4YJ4CK+iaqHg==
  dependencies:
    svg-arc-to-cubic-bezier "^3.0.0"

npmlog@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/npmlog/-/npmlog-5.0.1.tgz"
  integrity sha512-AqZtDUWOMKs1G/8lwylVjrdYgqA4d9nu8hc+0gzRxlDb1I10+FHBGMXs6aiQHFdCUUlqH99MUMuLfzWDNDtfxw==
  dependencies:
    are-we-there-yet "^2.0.0"
    console-control-strings "^1.1.0"
    gauge "^3.0.0"
    set-blocking "^2.0.0"

object-assign@^4.0.1, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==

object-hash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz"
  integrity sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==

once@^1.3.0, once@^1.3.1:
  version "1.4.0"
  resolved "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
  dependencies:
    wrappy "1"

p-defer@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/p-defer/-/p-defer-1.0.0.tgz"
  integrity sha512-wB3wfAxZpk2AzOfUMJNL+d36xothRSyj8EXOa4f6GMqYDN9BJaaSISbsk+wS9abmnebVw95C2Kb5t85UmpCxuw==

package-json-from-dist@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz"
  integrity sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==

pako@^0.2.5:
  version "0.2.9"
  resolved "https://registry.npmjs.org/pako/-/pako-0.2.9.tgz"
  integrity sha512-NUcwaKxUxWrZLpDG+z/xZaCgQITkA/Dv4V/T6bw7VON6l1Xz/VnrBqrYjZQ12TamKHzITTfOEIYUj48y2KXImA==

pako@~1.0.5:
  version "1.0.11"
  resolved "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz"
  integrity sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
  dependencies:
    callsites "^3.0.0"

parse-json@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz"
  integrity sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse-svg-path@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/parse-svg-path/-/parse-svg-path-0.1.2.tgz"
  integrity sha512-JyPSBnkTJ0AI8GGJLfMXvKq42cj5c006fnLz6fXy6zfoVjJizi8BNTpu8on8ziI1cKy9d9DGNuY17Ce7wuejpQ==

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==

path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

path-scurry@^1.11.1:
  version "1.11.1"
  resolved "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz"
  integrity sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz"
  integrity sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==

path2d-polyfill@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/path2d-polyfill/-/path2d-polyfill-2.0.1.tgz"
  integrity sha512-ad/3bsalbbWhmBo0D6FZ4RNMwsLsPpL6gnvhuSaU5Vm7b06Kr5ubSltQQ0T7YKsiJQO+g22zJ4dJKNTXIyOXtA==

path2d@^0.2.0:
  version "0.2.2"
  resolved "https://registry.npmjs.org/path2d/-/path2d-0.2.2.tgz"
  integrity sha512-+vnG6S4dYcYxZd+CZxzXCNKdELYZSKfohrk98yajCo1PtRoDgCTrrwOvK1GT0UoAdVszagDVllQc0U1vaX4NUQ==

"pdfjs-dist@^2.16.105 || ^3.0.279", pdfjs-dist@^3.4.120:
  version "3.11.174"
  resolved "https://registry.npmjs.org/pdfjs-dist/-/pdfjs-dist-3.11.174.tgz"
  integrity sha512-TdTZPf1trZ8/UFu5Cx/GXB7GZM30LT+wWUNfsi6Bq8ePLnb+woNKtDymI2mxZYBpMbonNFqKmiz684DIfnd8dA==
  optionalDependencies:
    canvas "^2.11.2"
    path2d-polyfill "^2.0.1"

pdfjs-dist@4.4.168:
  version "4.4.168"
  resolved "https://registry.npmjs.org/pdfjs-dist/-/pdfjs-dist-4.4.168.tgz"
  integrity sha512-MbkAjpwka/dMHaCfQ75RY1FXX3IewBVu6NGZOcxerRFlaBiIkZmUoR0jotX5VUzYZEXAGzSFtknWs5xRKliXPA==
  optionalDependencies:
    canvas "^2.11.2"
    path2d "^0.2.0"

picocolors@^1.0.0, picocolors@^1.0.1, picocolors@^1.1.0, picocolors@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz"
  integrity sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==

picomatch@^2.0.4:
  version "2.3.1"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

picomatch@^2.2.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

picomatch@^2.2.2:
  version "2.3.1"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

picomatch@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-4.0.2.tgz"
  integrity sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==

pify@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz"
  integrity sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==

pirates@^4.0.1:
  version "4.0.6"
  resolved "https://registry.npmjs.org/pirates/-/pirates-4.0.6.tgz"
  integrity sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==

postcss-import@^15.1.0:
  version "15.1.0"
  resolved "https://registry.npmjs.org/postcss-import/-/postcss-import-15.1.0.tgz"
  integrity sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==
  dependencies:
    postcss-value-parser "^4.0.0"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-js@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.1.tgz"
  integrity sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==
  dependencies:
    camelcase-css "^2.0.1"

postcss-load-config@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-4.0.2.tgz"
  integrity sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==
  dependencies:
    lilconfig "^3.0.0"
    yaml "^2.3.4"

postcss-nested@^6.0.1:
  version "6.2.0"
  resolved "https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.2.0.tgz"
  integrity sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==
  dependencies:
    postcss-selector-parser "^6.1.1"

postcss-selector-parser@^6.0.11, postcss-selector-parser@^6.1.1:
  version "6.1.2"
  resolved "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz"
  integrity sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.0.0, postcss-value-parser@^4.1.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

postcss@^8.0.0, postcss@^8.1.0, postcss@^8.2.14, postcss@^8.4.18, postcss@^8.4.19, postcss@^8.4.21, postcss@^8.4.23, postcss@>=8.0.9:
  version "8.4.49"
  resolved "https://registry.npmjs.org/postcss/-/postcss-8.4.49.tgz"
  integrity sha512-OCVPnIObs4N29kxTjzLfUryOkvZEq+pf8jTF0lg8E7uETuWHA+v7j3c/xJmiqpX450191LlmZfUKkXxkTry7nA==
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

preact@~10.12.1:
  version "10.12.1"
  resolved "https://registry.npmjs.org/preact/-/preact-10.12.1.tgz"
  integrity sha512-l8386ixSsBdbreOAkqtrwqHwdvR35ID8c3rKPa8lCWuO86dBi32QWHV4vfsZK1utLLFMvw+Z5Ad4XLkZzchscg==

pretender@^3.4.7:
  version "3.4.7"
  resolved "https://registry.npmjs.org/pretender/-/pretender-3.4.7.tgz"
  integrity sha512-jkPAvt1BfRi0RKamweJdEcnjkeu7Es8yix3bJ+KgBC5VpG/Ln4JE3hYN6vJym4qprm8Xo5adhWpm3HCoft1dOw==
  dependencies:
    fake-xml-http-request "^2.1.2"
    route-recognizer "^0.3.3"

prop-types@^15.5.10, prop-types@^15.6.0, prop-types@^15.6.1, prop-types@^15.6.2, prop-types@^15.7.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz"
  integrity sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

property-expr@^2.0.4:
  version "2.0.6"
  resolved "https://registry.npmjs.org/property-expr/-/property-expr-2.0.6.tgz"
  integrity sha512-SVtmxhRE/CGkn3eZY1T6pC8Nln6Fr/lu1mKSgRud0eC73whjGfoAogbn78LkD8aFL0zz3bAFerKSnOl7NlErBA==

protobufjs@7.4.0:
  version "7.4.0"
  resolved "https://registry.npmjs.org/protobufjs/-/protobufjs-7.4.0.tgz"
  integrity sha512-mRUWCc3KUU4w1jU8sGxICXH/gNS94DvI1gxqDvBzhj1JpcsimQkYiOJfwsPUykUI5ZaspFbSgmBLER8IrQ3tqw==
  dependencies:
    "@protobufjs/aspromise" "^1.1.2"
    "@protobufjs/base64" "^1.1.2"
    "@protobufjs/codegen" "^2.0.4"
    "@protobufjs/eventemitter" "^1.1.0"
    "@protobufjs/fetch" "^1.1.0"
    "@protobufjs/float" "^1.0.2"
    "@protobufjs/inquire" "^1.1.0"
    "@protobufjs/path" "^1.1.2"
    "@protobufjs/pool" "^1.1.0"
    "@protobufjs/utf8" "^1.1.0"
    "@types/node" ">=13.7.0"
    long "^5.0.0"

querystringify@^2.1.1:
  version "2.2.0"
  resolved "https://registry.npmjs.org/querystringify/-/querystringify-2.2.0.tgz"
  integrity sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

queue@^6.0.1:
  version "6.0.2"
  resolved "https://registry.npmjs.org/queue/-/queue-6.0.2.tgz"
  integrity sha512-iHZWu+q3IdFZFX36ro/lKBkSvfkztY5Y7HMiPlOUjhupPcG2JMfst2KKEpu5XndviX/3UhFbRngUPNKtgvtZiA==
  dependencies:
    inherits "~2.0.3"

raf-schd@^4.0.2:
  version "4.0.3"
  resolved "https://registry.npmjs.org/raf-schd/-/raf-schd-4.0.3.tgz"
  integrity sha512-tQkJl2GRWh83ui2DiPTJz9wEiMN20syf+5oKfB03yYP7ioZcJwsIK8FjrtLwH1m7C7e+Tt2yYBlrOpdT+dyeIQ==

react-apexcharts@^1.4.0:
  version "1.5.0"
  resolved "https://registry.npmjs.org/react-apexcharts/-/react-apexcharts-1.5.0.tgz"
  integrity sha512-RwIqhYee8tT6WsDR9I15bhDuPitM+z/P092QPttFR5D57M21/WtYKHE9JQMbcz9bmF35rfDSym1SZuZ7AhidhQ==
  dependencies:
    prop-types "^15.8.1"

react-beautiful-dnd@^13.1.1:
  version "13.1.1"
  resolved "https://registry.npmjs.org/react-beautiful-dnd/-/react-beautiful-dnd-13.1.1.tgz"
  integrity sha512-0Lvs4tq2VcrEjEgDXHjT98r+63drkKEgqyxdA7qD3mvKwga6a5SscbdLPO2IExotU1jW8L0Ksdl0Cj2AF67nPQ==
  dependencies:
    "@babel/runtime" "^7.9.2"
    css-box-model "^1.2.0"
    memoize-one "^5.1.1"
    raf-schd "^4.0.2"
    react-redux "^7.2.0"
    redux "^4.0.4"
    use-memo-one "^1.1.1"

react-big-calendar@^1.17.1:
  version "1.17.1"
  resolved "https://registry.npmjs.org/react-big-calendar/-/react-big-calendar-1.17.1.tgz"
  integrity sha512-LltUAMSGODWQBKx4013bRe6R0jaINV9hrs970+F860KedpozwRGGMT66esV9mA3mAhfSKoazF/QH1WCyLkXYZA==
  dependencies:
    "@babel/runtime" "^7.20.7"
    clsx "^1.2.1"
    date-arithmetic "^4.1.0"
    dayjs "^1.11.7"
    dom-helpers "^5.2.1"
    globalize "^0.1.1"
    invariant "^2.2.4"
    lodash "^4.17.21"
    lodash-es "^4.17.21"
    luxon "^3.2.1"
    memoize-one "^6.0.0"
    moment "^2.29.4"
    moment-timezone "^0.5.40"
    prop-types "^15.8.1"
    react-overlays "^5.2.1"
    uncontrollable "^7.2.1"

react-calendar@^4.0.0:
  version "4.8.0"
  resolved "https://registry.npmjs.org/react-calendar/-/react-calendar-4.8.0.tgz"
  integrity sha512-qFgwo+p58sgv1QYMI1oGNaop90eJVKuHTZ3ZgBfrrpUb+9cAexxsKat0sAszgsizPMVo7vOXedV7Lqa0GQGMvA==
  dependencies:
    "@wojtekmaj/date-utils" "^1.1.3"
    clsx "^2.0.0"
    get-user-locale "^2.2.1"
    prop-types "^15.6.0"
    warning "^4.0.0"

react-chartjs-2@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/react-chartjs-2/-/react-chartjs-2-5.2.0.tgz"
  integrity sha512-98iN5aguJyVSxp5U3CblRLH67J8gkfyGNbiK3c+l1QI/G4irHMPQw44aEPmjVag+YKTyQ260NcF82GTQ3bdscA==

react-collapse@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/react-collapse/-/react-collapse-5.1.1.tgz"
  integrity sha512-k6cd7csF1o9LBhQ4AGBIdxB60SUEUMQDAnL2z1YvYNr9KoKr+nDkhN6FK7uGaBd/rYrYfrMpzpmJEIeHRYogBw==

react-colorful@^5.6.1:
  version "5.6.1"
  resolved "https://registry.npmjs.org/react-colorful/-/react-colorful-5.6.1.tgz"
  integrity sha512-1exovf0uGTGyq5mXQT0zgQ80uvj2PCwvF8zY1RN9/vbJVSjSo3fsB/4L3ObbF7u70NduSiK4xu4Y6q1MHoUGEw==

react-dnd-html5-backend@^16.0.1:
  version "16.0.1"
  resolved "https://registry.npmjs.org/react-dnd-html5-backend/-/react-dnd-html5-backend-16.0.1.tgz"
  integrity sha512-Wu3dw5aDJmOGw8WjH1I1/yTH+vlXEL4vmjk5p+MHxP8HuHJS1lAGeIdG/hze1AvNeXWo/JgULV87LyQOr+r5jw==
  dependencies:
    dnd-core "^16.0.1"

react-dnd@^16.0.1:
  version "16.0.1"
  resolved "https://registry.npmjs.org/react-dnd/-/react-dnd-16.0.1.tgz"
  integrity sha512-QeoM/i73HHu2XF9aKksIUuamHPDvRglEwdHL4jsp784BgUuWcg6mzfxT0QDdQz8Wj0qyRKx2eMg8iZtWvU4E2Q==
  dependencies:
    "@react-dnd/invariant" "^4.0.1"
    "@react-dnd/shallowequal" "^4.0.1"
    dnd-core "^16.0.1"
    fast-deep-equal "^3.1.3"
    hoist-non-react-statics "^3.3.2"

"react-dom@^0.14.9 || ^15.3.0 || ^16.0.0-rc || ^16.0 || ^17.0 || ^18.0.0", "react-dom@^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom@^15.3.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "react-dom@^16 || ^17 || ^18", "react-dom@^16.0.0 || ^17.0.0 || ^18.0.0", "react-dom@^16.14.0 || ^17 || ^18", "react-dom@^16.7.0 || ^17 || ^18 || ^19", "react-dom@^16.8 || ^17.0 || ^18.0", "react-dom@^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom@^16.8.5 || ^17.0.0 || ^18.0.0", react-dom@^18.0.0, "react-dom@^18.0.0 || ^19.0.0", react-dom@^18.2.0, react-dom@>=16, react-dom@>=16.3.0, react-dom@>=16.6.0, react-dom@>=16.8, react-dom@>=16.8.0:
  version "18.3.1"
  resolved "https://registry.npmjs.org/react-dom/-/react-dom-18.3.1.tgz"
  integrity sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==
  dependencies:
    loose-envify "^1.1.0"
    scheduler "^0.23.2"

react-dropzone@^14.2.3:
  version "14.3.5"
  resolved "https://registry.npmjs.org/react-dropzone/-/react-dropzone-14.3.5.tgz"
  integrity sha512-9nDUaEEpqZLOz5v5SUcFA0CjM4vq8YbqO0WRls+EYT7+DvxUdzDPKNCPLqGfj3YL9MsniCLCD4RFA6M95V6KMQ==
  dependencies:
    attr-accept "^2.2.4"
    file-selector "^2.1.0"
    prop-types "^15.8.1"

react-fast-compare@^2.0.1:
  version "2.0.4"
  resolved "https://registry.npmjs.org/react-fast-compare/-/react-fast-compare-2.0.4.tgz"
  integrity sha512-suNP+J1VU1MWFKcyt7RtjiSWUjvidmQSlqu+eHslq+342xCbGTYmC0mEhPCOHxlW0CywylOC1u2DFAT+bv4dBw==

react-fast-compare@^3.0.1:
  version "3.2.2"
  resolved "https://registry.npmjs.org/react-fast-compare/-/react-fast-compare-3.2.2.tgz"
  integrity sha512-nsO+KSNgo1SbJqJEYRE9ERzo7YtYbou/OqjSQKxV7jcKox7+usiUVZOAC+XnDOABXggQTno0Y1CpVnuWEc1boQ==

react-flatpickr@^3.10.13:
  version "3.10.13"
  resolved "https://registry.npmjs.org/react-flatpickr/-/react-flatpickr-3.10.13.tgz"
  integrity sha512-4m+K1K8jhvRFI8J/AHmQfA5hLALzhebEtEK8mLevXjX24MV3u502crzBn+EGFIBOfNUtrL5PId9FsGwgtuz/og==
  dependencies:
    flatpickr "^4.6.2"
    prop-types "^15.5.10"

react-hook-form@^7.0.0, react-hook-form@^7.39.5:
  version "7.53.2"
  resolved "https://registry.npmjs.org/react-hook-form/-/react-hook-form-7.53.2.tgz"
  integrity sha512-YVel6fW5sOeedd1524pltpHX+jgU2u3DSDtXEaBORNdqiNrsX/nUI/iGXONegttg0mJVnfrIkiV0cmTU6Oo2xw==

react-i18next@^15.5.1:
  version "15.5.1"
  resolved "https://registry.npmjs.org/react-i18next/-/react-i18next-15.5.1.tgz"
  integrity sha512-C8RZ7N7H0L+flitiX6ASjq9p5puVJU1Z8VyL3OgM/QOMRf40BMZX+5TkpxzZVcTmOLPX5zlti4InEX5pFyiVeA==
  dependencies:
    "@babel/runtime" "^7.25.0"
    html-parse-stringify "^3.0.1"

react-icons@^5.3.0:
  version "5.3.0"
  resolved "https://registry.npmjs.org/react-icons/-/react-icons-5.3.0.tgz"
  integrity sha512-DnUk8aFbTyQPSkCfF8dbX6kQjXA9DktMeJqfjrg6cK9vwQVMxmcA3BfP4QoiztVmEHtwlTgLFsPuH2NskKT6eg==

react-intersection-observer@^9.13.1:
  version "9.13.1"
  resolved "https://registry.npmjs.org/react-intersection-observer/-/react-intersection-observer-9.13.1.tgz"
  integrity sha512-tSzDaTy0qwNPLJHg8XZhlyHTgGW6drFKTtvjdL+p6um12rcnp8Z5XstE+QNBJ7c64n5o0Lj4ilUleA41bmDoMw==

react-is@^16.13.1, react-is@^16.7.0:
  version "16.13.1"
  resolved "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  integrity sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==

react-is@^17.0.2:
  version "17.0.2"
  resolved "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz"
  integrity sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==

react-is@^18.0.0:
  version "18.3.1"
  resolved "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz"
  integrity sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==

react-is@^18.3.1:
  version "18.3.1"
  resolved "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz"
  integrity sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==

react-leaflet@^4.2.0:
  version "4.2.1"
  resolved "https://registry.npmjs.org/react-leaflet/-/react-leaflet-4.2.1.tgz"
  integrity sha512-p9chkvhcKrWn/H/1FFeVSqLdReGwn2qmiobOQGO3BifX+/vV/39qhY8dGqbdcPh1e6jxh/QHriLXr7a4eLFK4Q==
  dependencies:
    "@react-leaflet/core" "^2.1.0"

react-lifecycles-compat@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmjs.org/react-lifecycles-compat/-/react-lifecycles-compat-3.0.4.tgz"
  integrity sha512-fBASbA6LnOU9dOU2eW7aQ8xmYBSXUIWr+UmF9b1efZBazGNO+rcXT/icdKnYm2pTwcRylVUYwW7H1PHfLekVzA==

react-overlays@^5.2.1:
  version "5.2.1"
  resolved "https://registry.npmjs.org/react-overlays/-/react-overlays-5.2.1.tgz"
  integrity sha512-GLLSOLWr21CqtJn8geSwQfoJufdt3mfdsnIiQswouuQ2MMPns+ihZklxvsTDKD3cR2tF8ELbi5xUsvqVhR6WvA==
  dependencies:
    "@babel/runtime" "^7.13.8"
    "@popperjs/core" "^2.11.6"
    "@restart/hooks" "^0.4.7"
    "@types/warning" "^3.0.0"
    dom-helpers "^5.2.0"
    prop-types "^15.7.2"
    uncontrollable "^7.2.1"
    warning "^4.0.3"

react-pdf@^9.1.0:
  version "9.1.1"
  resolved "https://registry.npmjs.org/react-pdf/-/react-pdf-9.1.1.tgz"
  integrity sha512-Cn3RTJZMqVOOCgLMRXDamLk4LPGfyB2Np3OwQAUjmHIh47EpuGW1OpAA1Z1GVDLoHx4d5duEDo/YbUkDbr4QFQ==
  dependencies:
    clsx "^2.0.0"
    dequal "^2.0.3"
    make-cancellable-promise "^1.3.1"
    make-event-props "^1.6.0"
    merge-refs "^1.3.0"
    pdfjs-dist "4.4.168"
    tiny-invariant "^1.0.0"
    warning "^4.0.0"

react-player@^2.16.0:
  version "2.16.0"
  resolved "https://registry.npmjs.org/react-player/-/react-player-2.16.0.tgz"
  integrity sha512-mAIPHfioD7yxO0GNYVFD1303QFtI3lyyQZLY229UEAp/a10cSW+hPcakg0Keq8uWJxT2OiT/4Gt+Lc9bD6bJmQ==
  dependencies:
    deepmerge "^4.0.0"
    load-script "^1.0.0"
    memoize-one "^5.1.1"
    prop-types "^15.7.2"
    react-fast-compare "^3.0.1"

react-redux@^7.2.0:
  version "7.2.9"
  resolved "https://registry.npmjs.org/react-redux/-/react-redux-7.2.9.tgz"
  integrity sha512-Gx4L3uM182jEEayZfRbI/G11ZpYdNAnBs70lFVMNdHJI76XYtR+7m0MN+eAs7UHBPhWXcnFPaS+9owSCJQHNpQ==
  dependencies:
    "@babel/runtime" "^7.15.4"
    "@types/react-redux" "^7.1.20"
    hoist-non-react-statics "^3.3.2"
    loose-envify "^1.4.0"
    prop-types "^15.7.2"
    react-is "^17.0.2"

"react-redux@^7.2.1 || ^8.0.2", react-redux@^8.0.5:
  version "8.1.3"
  resolved "https://registry.npmjs.org/react-redux/-/react-redux-8.1.3.tgz"
  integrity sha512-n0ZrutD7DaX/j9VscF+uTALI3oUPa/pO4Z3soOBIjuRn/FzVu6aehhysxZCLi6y7duMf52WNZGMl7CtuK5EnRw==
  dependencies:
    "@babel/runtime" "^7.12.1"
    "@types/hoist-non-react-statics" "^3.3.1"
    "@types/use-sync-external-store" "^0.0.3"
    hoist-non-react-statics "^3.3.2"
    react-is "^18.0.0"
    use-sync-external-store "^1.0.0"

react-refresh@^0.10.0:
  version "0.10.0"
  resolved "https://registry.npmjs.org/react-refresh/-/react-refresh-0.10.0.tgz"
  integrity sha512-PgidR3wST3dDYKr6b4pJoqQFpPGNKDSCDx4cZoshjXipw3LzO7mG1My2pwEzz2JVkF+inx3xRpDeQLFQGH/hsQ==

react-refresh@^0.14.0:
  version "0.14.2"
  resolved "https://registry.npmjs.org/react-refresh/-/react-refresh-0.14.2.tgz"
  integrity sha512-jCvmsr+1IUSMUyzOkRcvnVbX3ZYC6g9TDrDbFuFmRDq7PD4yaGbLKNQL6k2jnArV8hjYxh7hVhAZB6s9HDGpZA==

react-router-dom@^6.4.3:
  version "6.28.0"
  resolved "https://registry.npmjs.org/react-router-dom/-/react-router-dom-6.28.0.tgz"
  integrity sha512-kQ7Unsl5YdyOltsPGl31zOjLrDv+m2VcIEcIHqYYD3Lp0UppLjrzcfJqDJwXxFw3TH/yvapbnUvPlAj7Kx5nbg==
  dependencies:
    "@remix-run/router" "1.21.0"
    react-router "6.28.0"

react-router@6.28.0:
  version "6.28.0"
  resolved "https://registry.npmjs.org/react-router/-/react-router-6.28.0.tgz"
  integrity sha512-HrYdIFqdrnhDw0PqG/AKjAqEqM7AvxCz0DQ4h2W8k6nqmc5uRBYDag0SBxx9iYz5G8gnuNVLzUe13wl9eAsXXg==
  dependencies:
    "@remix-run/router" "1.21.0"

react-select@^5.7.0:
  version "5.8.3"
  resolved "https://registry.npmjs.org/react-select/-/react-select-5.8.3.tgz"
  integrity sha512-lVswnIq8/iTj1db7XCG74M/3fbGB6ZaluCzvwPGT5ZOjCdL/k0CLWhEK0vCBLuU5bHTEf6Gj8jtSvi+3v+tO1w==
  dependencies:
    "@babel/runtime" "^7.12.0"
    "@emotion/cache" "^11.4.0"
    "@emotion/react" "^11.8.1"
    "@floating-ui/dom" "^1.0.1"
    "@types/react-transition-group" "^4.4.0"
    memoize-one "^6.0.0"
    prop-types "^15.6.0"
    react-transition-group "^4.3.0"
    use-isomorphic-layout-effect "^1.1.2"

react-smooth@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/react-smooth/-/react-smooth-4.0.1.tgz"
  integrity sha512-OE4hm7XqR0jNOq3Qmk9mFLyd6p2+j6bvbPJ7qlB7+oo0eNcL2l7WQzG6MBnT3EXY6xzkLMUBec3AfewJdA0J8w==
  dependencies:
    fast-equals "^5.0.1"
    prop-types "^15.8.1"
    react-transition-group "^4.4.5"

react-switch@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/react-switch/-/react-switch-7.0.0.tgz"
  integrity sha512-KkDeW+cozZXI6knDPyUt3KBN1rmhoVYgAdCJqAh7st7tk8YE6N0iR89zjCWO8T8dUTeJGTR0KU+5CHCRMRffiA==
  dependencies:
    prop-types "^15.7.2"

react-table@^7.8.0:
  version "7.8.0"
  resolved "https://registry.npmjs.org/react-table/-/react-table-7.8.0.tgz"
  integrity sha512-hNaz4ygkZO4bESeFfnfOft73iBUj8K5oKi1EcSHPAibEydfsX2MyU6Z8KCr3mv3C9Kqqh71U+DhZkFvibbnPbA==

react-tailwindcss-datepicker@^1.4.2:
  version "1.7.2"
  resolved "https://registry.npmjs.org/react-tailwindcss-datepicker/-/react-tailwindcss-datepicker-1.7.2.tgz"
  integrity sha512-/NIRLB1evT69pt3Syol3cZpsAnLZlGvFWav98/Rr77Gey382C1fjKW2Emgu+SC4NtiRt6CBFwx/0Wbkn4iI+nA==

react-toastify@^9.1.1:
  version "9.1.3"
  resolved "https://registry.npmjs.org/react-toastify/-/react-toastify-9.1.3.tgz"
  integrity sha512-fPfb8ghtn/XMxw3LkxQBk3IyagNpF/LIKjOBflbexr2AWxAH1MJgvnESwEwBn9liLFXgTKWgBSdZpw9m4OTHTg==
  dependencies:
    clsx "^1.1.1"

react-transition-group@^4.3.0, react-transition-group@^4.4.5:
  version "4.4.5"
  resolved "https://registry.npmjs.org/react-transition-group/-/react-transition-group-4.4.5.tgz"
  integrity sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==
  dependencies:
    "@babel/runtime" "^7.5.5"
    dom-helpers "^5.0.1"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"

react@*, "react@^0.14.9 || ^15.3.0 || ^16.0.0-rc || ^16.0 || ^17.0 || ^18.0.0", "react@^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^15.3.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "react@^16 || ^17 || ^18", "react@^16.0.0 || ^17.0.0 || ^18.0.0", "react@^16.13.1 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^16.14.0 || ^17 || ^18", "react@^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0-rc", "react@^16.7.0 || ^17 || ^18 || ^19", "react@^16.8 || ^17.0 || ^18.0", "react@^16.8.0 || ^17 || ^18 || ^19", "react@^16.8.0 || ^17.0.0 || ^18.0.0", "react@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^16.8.3 || ^17 || ^18", "react@^16.8.3 || ^17.0.0-0 || ^18.0.0", "react@^16.8.5 || ^17.0.0 || ^18.0.0", "react@^16.9.0 || ^17.0.0 || ^18", "react@^17.0.2 || ^18.2.0", react@^18.0.0, "react@^18.0.0 || ^19.0.0", react@^18.2.0, react@^18.3.1, "react@>= 16.14", "react@>= 16.8 || 18.0.0", "react@>= 16.8.0", react@>=0.13, react@>=15.0.0, react@>=16, "react@>=16, <=18", react@>=16.3.0, react@>=16.6.0, react@>=16.8, react@>=16.8.0:
  version "18.3.1"
  resolved "https://registry.npmjs.org/react/-/react-18.3.1.tgz"
  integrity sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==
  dependencies:
    loose-envify "^1.1.0"

read-cache@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz"
  integrity sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==
  dependencies:
    pify "^2.3.0"

readable-stream@^3.6.0:
  version "3.6.2"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz"
  integrity sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmjs.org/readdirp/-/readdirp-4.0.2.tgz"
  integrity sha512-yDMz9g+VaZkqBYS/ozoBJwaBhTbZo3UNYQHNRw1D3UFQB8oHB4uS/tAODO+ZLjGWmUbKnIlOWO+aaIiAxrUWHA==

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

recharts-scale@^0.4.4:
  version "0.4.5"
  resolved "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.4.5.tgz"
  integrity sha512-kivNFO+0OcUNu7jQquLXAxz1FIwZj8nrj+YkOKc5694NbjCvcT6aSZiIzNzd2Kul4o4rTto8QVR9lMNtxD4G1w==
  dependencies:
    decimal.js-light "^2.4.1"

recharts@^2.3.2:
  version "2.13.3"
  resolved "https://registry.npmjs.org/recharts/-/recharts-2.13.3.tgz"
  integrity sha512-YDZ9dOfK9t3ycwxgKbrnDlRC4BHdjlY73fet3a0C1+qGMjXVZe6+VXmpOIIhzkje5MMEL8AN4hLIe4AMskBzlA==
  dependencies:
    clsx "^2.0.0"
    eventemitter3 "^4.0.1"
    lodash "^4.17.21"
    react-is "^18.3.1"
    react-smooth "^4.0.0"
    recharts-scale "^0.4.4"
    tiny-invariant "^1.3.1"
    victory-vendor "^36.6.8"

redux-thunk@^2.4.2:
  version "2.4.2"
  resolved "https://registry.npmjs.org/redux-thunk/-/redux-thunk-2.4.2.tgz"
  integrity sha512-+P3TjtnP0k/FEjcBL5FZpoovtvrTNT/UXd4/sluaSyrURlSlhLSzEdfsTBW7WsKB6yPvgd7q/iZPICFjW4o57Q==

redux@^4, "redux@^4 || ^5.0.0-beta.0", redux@^4.0.0, redux@^4.0.4, redux@^4.2.0, redux@^4.2.1:
  version "4.2.1"
  resolved "https://registry.npmjs.org/redux/-/redux-4.2.1.tgz"
  integrity sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w==
  dependencies:
    "@babel/runtime" "^7.9.2"

remove-accents@0.5.0:
  version "0.5.0"
  resolved "https://registry.npmjs.org/remove-accents/-/remove-accents-0.5.0.tgz"
  integrity sha512-8g3/Otx1eJaVD12e31UbJj1YzdtVvzH85HV7t+9MJYk/u3XmkOUJ5Ys9wQrf9PCPK8+xn4ymzqYCiZl6QWKn+A==

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz"
  integrity sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz"
  integrity sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==

reselect@^4.1.8:
  version "4.1.8"
  resolved "https://registry.npmjs.org/reselect/-/reselect-4.1.8.tgz"
  integrity sha512-ab9EmR80F/zQTMNeneUr4cv+jSwPJgIlvEmVwLerwrWVbpLlBuls9XHzIeTFy4cegU2NHBp3va0LKOzU5qFEYQ==

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==

resolve@^1.1.7, resolve@^1.19.0, resolve@^1.22.1, resolve@^1.22.2:
  version "1.22.8"
  resolved "https://registry.npmjs.org/resolve/-/resolve-1.22.8.tgz"
  integrity sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restructure@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmjs.org/restructure/-/restructure-3.0.2.tgz"
  integrity sha512-gSfoiOEA0VPE6Tukkrr7I0RBdE0s7H1eFCDBk05l1KIQT1UIKNc5JZy6jdyW6eYH3aR3g5b3PuL77rq0hvwtAw==

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz"
  integrity sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==

rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
  integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
  dependencies:
    glob "^7.1.3"

rollup@^1.20.0||^2.0.0||^3.0.0||^4.0.0, rollup@^2.79.1:
  version "2.79.2"
  resolved "https://registry.npmjs.org/rollup/-/rollup-2.79.2.tgz"
  integrity sha512-fS6iqSPZDs3dr/y7Od6y5nha8dW1YnbgtsyotCVvoFGKbERG++CVRFv1meyGDE1SNItQA8BrnCw7ScdAhRJ3XQ==
  optionalDependencies:
    fsevents "~2.3.2"

route-recognizer@^0.3.3:
  version "0.3.4"
  resolved "https://registry.npmjs.org/route-recognizer/-/route-recognizer-0.3.4.tgz"
  integrity sha512-2+MhsfPhvauN1O8KaXpXAOfR/fwe8dnUXVM+xw7yt40lJRfPVQxV6yryZm0cgRvAj5fMF/mdRZbL2ptwbs5i2g==

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

sass@*, sass@^1.56.1:
  version "1.80.6"
  resolved "https://registry.npmjs.org/sass/-/sass-1.80.6.tgz"
  integrity sha512-ccZgdHNiBF1NHBsWvacvT5rju3y1d/Eu+8Ex6c21nHp2lZGLBEtuwc415QfiI1PJa1TpCo3iXwwSRjRpn2Ckjg==
  dependencies:
    chokidar "^4.0.0"
    immutable "^4.0.0"
    source-map-js ">=0.6.2 <2.0.0"
  optionalDependencies:
    "@parcel/watcher" "^2.4.1"

scheduler@^0.17.0:
  version "0.17.0"
  resolved "https://registry.npmjs.org/scheduler/-/scheduler-0.17.0.tgz"
  integrity sha512-7rro8Io3tnCPuY4la/NuI5F2yfESpnfZyT6TtkXnSWVkcu0BCDJ+8gk5ozUaFaxpIyNuWAPXrH0yFcSi28fnDA==
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

scheduler@^0.23.2:
  version "0.23.2"
  resolved "https://registry.npmjs.org/scheduler/-/scheduler-0.23.2.tgz"
  integrity sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==
  dependencies:
    loose-envify "^1.1.0"

semver@^6.0.0, semver@^6.3.1:
  version "6.3.1"
  resolved "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz"
  integrity sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==

semver@^7.3.5:
  version "7.6.3"
  resolved "https://registry.npmjs.org/semver/-/semver-7.6.3.tgz"
  integrity sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz"
  integrity sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

signal-exit@^3.0.0:
  version "3.0.7"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

signal-exit@^4.0.1:
  version "4.1.0"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz"
  integrity sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==

simple-concat@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/simple-concat/-/simple-concat-1.0.1.tgz"
  integrity sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q==

simple-get@^3.0.3:
  version "3.1.1"
  resolved "https://registry.npmjs.org/simple-get/-/simple-get-3.1.1.tgz"
  integrity sha512-CQ5LTKGfCpvE1K0n2us+kuMPbk/q0EKl82s4aheV9oXjFEz6W/Y7oQFVJuU6QG77hRT4Ghb5RURteF5vnWjupA==
  dependencies:
    decompress-response "^4.2.0"
    once "^1.3.1"
    simple-concat "^1.0.0"

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz"
  integrity sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==
  dependencies:
    is-arrayish "^0.3.1"

simplebar-react@^2.4.3:
  version "2.4.3"
  resolved "https://registry.npmjs.org/simplebar-react/-/simplebar-react-2.4.3.tgz"
  integrity sha512-Ep8gqAUZAS5IC2lT5RE4t1ZFUIVACqbrSRQvFV9a6NbVUzXzOMnc4P82Hl8Ak77AnPQvmgUwZS7aUKLyBoMAcg==
  dependencies:
    prop-types "^15.6.1"
    simplebar "^5.3.9"

simplebar@^5.3.9:
  version "5.3.9"
  resolved "https://registry.npmjs.org/simplebar/-/simplebar-5.3.9.tgz"
  integrity sha512-1vIIpjDvY9sVH14e0LGeiCiTFU3ILqAghzO6OI9axeG+mvU/vMSrvXeAXkBolqFFz3XYaY8n5ahH9MeP3sp2Ag==
  dependencies:
    "@juggle/resize-observer" "^3.3.1"
    can-use-dom "^0.1.0"
    core-js "^3.0.1"
    lodash.debounce "^4.0.8"
    lodash.memoize "^4.1.2"
    lodash.throttle "^4.1.1"

socket.io-client@4.7.0:
  version "4.7.0"
  resolved "https://registry.npmjs.org/socket.io-client/-/socket.io-client-4.7.0.tgz"
  integrity sha512-7Q8CeDrhuZzg4QLXl3tXlk5yb086oxYzehAVZRLiGCzCmtDneiHz1qHyyWcxhTgxXiokVpWQXoG/u60HoXSQew==
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    debug "~4.3.2"
    engine.io-client "~6.5.0"
    socket.io-parser "~4.2.4"

socket.io-parser@~4.2.3, socket.io-parser@~4.2.4:
  version "4.2.4"
  resolved "https://registry.npmjs.org/socket.io-parser/-/socket.io-parser-4.2.4.tgz"
  integrity sha512-/GbIKmo8ioc+NIWIhwdecY0ge+qVBSMdgxGygevmdHj24bsfgtCmcUUcQ5ZzcylGFHsN3k4HB4Cgkl96KVnuew==
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    debug "~4.3.1"

sort-by@^0.0.2:
  version "0.0.2"
  resolved "https://registry.npmjs.org/sort-by/-/sort-by-0.0.2.tgz"
  integrity sha512-iOX5oHA4a0eqTMFiWrHYqv924UeRKFBLhym7iwSVG37Egg2wApgZKAjyzM9WZjMwKv6+8Zi+nIaJ7FYsO9EkoA==

source-map-js@^1.2.1, "source-map-js@>=0.6.2 <2.0.0":
  version "1.2.1"
  resolved "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz"
  integrity sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==

source-map@^0.5.7:
  version "0.5.7"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  integrity sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==

sourcemap-codec@^1.4.8:
  version "1.4.8"
  resolved "https://registry.npmjs.org/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz"
  integrity sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA==

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==
  dependencies:
    safe-buffer "~5.2.0"

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

"string-width@^1.0.2 || 2 || 3 || 4", string-width@^4.1.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1:
  version "5.1.2"
  resolved "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz"
  integrity sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string-width@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz"
  integrity sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz"
  integrity sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==
  dependencies:
    ansi-regex "^6.0.1"

stylis@4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/stylis/-/stylis-4.2.0.tgz"
  integrity sha512-Orov6g6BB1sDfYgzWfTHDOxamtX1bE/zo104Dh9e6fqJ3PooipYyfJ0pUmrZO2wAvO8YbEyeFrkV91XTsGMSrw==

sucrase@^3.32.0:
  version "3.35.0"
  resolved "https://registry.npmjs.org/sucrase/-/sucrase-3.35.0.tgz"
  integrity sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.2"
    commander "^4.0.0"
    glob "^10.3.10"
    lines-and-columns "^1.1.6"
    mz "^2.7.0"
    pirates "^4.0.1"
    ts-interface-checker "^0.1.9"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

svg-arc-to-cubic-bezier@^3.0.0, svg-arc-to-cubic-bezier@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/svg-arc-to-cubic-bezier/-/svg-arc-to-cubic-bezier-3.2.0.tgz"
  integrity sha512-djbJ/vZKZO+gPoSDThGNpKDO+o+bAeA4XQKovvkNCqnIS2t+S4qnLAGQhyyrulhCFRl1WWzAp0wUDV8PpTVU3g==

svg.draggable.js@^2.2.2:
  version "2.2.2"
  resolved "https://registry.npmjs.org/svg.draggable.js/-/svg.draggable.js-2.2.2.tgz"
  integrity sha512-JzNHBc2fLQMzYCZ90KZHN2ohXL0BQJGQimK1kGk6AvSeibuKcIdDX9Kr0dT9+UJ5O8nYA0RB839Lhvk4CY4MZw==
  dependencies:
    svg.js "^2.0.1"

svg.easing.js@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/svg.easing.js/-/svg.easing.js-2.0.0.tgz"
  integrity sha512-//ctPdJMGy22YoYGV+3HEfHbm6/69LJUTAqI2/5qBvaNHZ9uUFVC82B0Pl299HzgH13rKrBgi4+XyXXyVWWthA==
  dependencies:
    svg.js ">=2.3.x"

svg.filter.js@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/svg.filter.js/-/svg.filter.js-2.0.2.tgz"
  integrity sha512-xkGBwU+dKBzqg5PtilaTb0EYPqPfJ9Q6saVldX+5vCRy31P6TlRCP3U9NxH3HEufkKkpNgdTLBJnmhDHeTqAkw==
  dependencies:
    svg.js "^2.2.5"

svg.js@^2.0.1, svg.js@^2.2.5, svg.js@^2.4.0, svg.js@^2.6.5, svg.js@>=2.3.x:
  version "2.7.1"
  resolved "https://registry.npmjs.org/svg.js/-/svg.js-2.7.1.tgz"
  integrity sha512-ycbxpizEQktk3FYvn/8BH+6/EuWXg7ZpQREJvgacqn46gIddG24tNNe4Son6omdXCnSOaApnpZw6MPCBA1dODA==

svg.pathmorphing.js@^0.1.3:
  version "0.1.3"
  resolved "https://registry.npmjs.org/svg.pathmorphing.js/-/svg.pathmorphing.js-0.1.3.tgz"
  integrity sha512-49HWI9X4XQR/JG1qXkSDV8xViuTLIWm/B/7YuQELV5KMOPtXjiwH4XPJvr/ghEDibmLQ9Oc22dpWpG0vUDDNww==
  dependencies:
    svg.js "^2.4.0"

svg.resize.js@^1.4.3:
  version "1.4.3"
  resolved "https://registry.npmjs.org/svg.resize.js/-/svg.resize.js-1.4.3.tgz"
  integrity sha512-9k5sXJuPKp+mVzXNvxz7U0uC9oVMQrrf7cFsETznzUDDm0x8+77dtZkWdMfRlmbkEEYvUn9btKuZ3n41oNA+uw==
  dependencies:
    svg.js "^2.6.5"
    svg.select.js "^2.1.2"

svg.select.js@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/svg.select.js/-/svg.select.js-2.1.2.tgz"
  integrity sha512-tH6ABEyJsAOVAhwcCjF8mw4crjXSI1aa7j2VQR8ZuJ37H2MBUbyeqYr5nEO7sSN3cy9AR9DUwNg0t/962HlDbQ==
  dependencies:
    svg.js "^2.2.5"

svg.select.js@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/svg.select.js/-/svg.select.js-3.0.1.tgz"
  integrity sha512-h5IS/hKkuVCbKSieR9uQCj9w+zLHoPh+ce19bBYyqF53g6mnPB8sAtIbe1s9dh2S2fCmYX2xel1Ln3PJBbK4kw==
  dependencies:
    svg.js "^2.6.5"

sweetalert2@^11.6.13:
  version "11.14.5"
  resolved "https://registry.npmjs.org/sweetalert2/-/sweetalert2-11.14.5.tgz"
  integrity sha512-8MWk5uc/r6bWhiJWkUXyEuApfXAhSCZT8FFX7pZXL7YwaPxq+9Ynhi2dUzWkOFn9jvLjKj22CXuccZ+IHcnjvQ==

swiper@^11.0.6:
  version "11.1.15"
  resolved "https://registry.npmjs.org/swiper/-/swiper-11.1.15.tgz"
  integrity sha512-IzWeU34WwC7gbhjKsjkImTuCRf+lRbO6cnxMGs88iVNKDwV+xQpBCJxZ4bNH6gSrIbbyVJ1kuGzo3JTtz//CBw==

tailwindcss@^3.2.4:
  version "3.4.14"
  resolved "https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.4.14.tgz"
  integrity sha512-IcSvOcTRcUtQQ7ILQL5quRDg7Xs93PdJEk1ZLbhhvJc7uj/OAhYOnruEiwnGgBvUtaUAJ8/mhSw1o8L2jCiENA==
  dependencies:
    "@alloc/quick-lru" "^5.2.0"
    arg "^5.0.2"
    chokidar "^3.5.3"
    didyoumean "^1.2.2"
    dlv "^1.1.3"
    fast-glob "^3.3.0"
    glob-parent "^6.0.2"
    is-glob "^4.0.3"
    jiti "^1.21.0"
    lilconfig "^2.1.0"
    micromatch "^4.0.5"
    normalize-path "^3.0.0"
    object-hash "^3.0.0"
    picocolors "^1.0.0"
    postcss "^8.4.23"
    postcss-import "^15.1.0"
    postcss-js "^4.0.1"
    postcss-load-config "^4.0.1"
    postcss-nested "^6.0.1"
    postcss-selector-parser "^6.0.11"
    resolve "^1.22.2"
    sucrase "^3.32.0"

tar@^6.1.11:
  version "6.2.1"
  resolved "https://registry.npmjs.org/tar/-/tar-6.2.1.tgz"
  integrity sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==
  dependencies:
    chownr "^2.0.0"
    fs-minipass "^2.0.0"
    minipass "^5.0.0"
    minizlib "^2.1.1"
    mkdirp "^1.0.3"
    yallist "^4.0.0"

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz"
  integrity sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz"
  integrity sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==
  dependencies:
    any-promise "^1.0.0"

tiny-inflate@^1.0.0, tiny-inflate@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/tiny-inflate/-/tiny-inflate-1.0.3.tgz"
  integrity sha512-pkY1fj1cKHb2seWDy0B16HeWyczlJA9/WW3u3c4z/NiWDsO3DOU5D7nhTLE9CF0yXv/QZFY7sEJmj24dK+Rrqw==

tiny-invariant@^1.0.0, tiny-invariant@^1.0.6, tiny-invariant@^1.3.1:
  version "1.3.3"
  resolved "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.3.3.tgz"
  integrity sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg==

tiny-warning@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/tiny-warning/-/tiny-warning-1.0.3.tgz"
  integrity sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA==

tippy.js@^6.3.1:
  version "6.3.7"
  resolved "https://registry.npmjs.org/tippy.js/-/tippy.js-6.3.7.tgz"
  integrity sha512-E1d3oP2emgJ9dRQZdf3Kkn0qJgI6ZLpyS5z6ZkY1DF3kaQaBsGZsndEpHwx+eC+tYM41HaSNvNtLx8tU57FzTQ==
  dependencies:
    "@popperjs/core" "^2.9.0"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

toposort@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/toposort/-/toposort-2.0.2.tgz"
  integrity sha512-0a5EOkAUp8D4moMi2W8ZF8jcga7BgZd91O/yabJCFY8az+XSzeGyTKs0Aoo897iV1Nj6guFq8orWDS96z91oGg==

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz"
  integrity sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==

ts-interface-checker@^0.1.9:
  version "0.1.13"
  resolved "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz"
  integrity sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==

tslib@^2.0.0, tslib@^2.4.0, tslib@^2.7.0, tslib@^2.8.0:
  version "2.8.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz"
  integrity sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==

turndown-plugin-gfm@1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/turndown-plugin-gfm/-/turndown-plugin-gfm-1.0.2.tgz"
  integrity sha512-vwz9tfvF7XN/jE0dGoBei3FXWuvll78ohzCZQuOb+ZjWrs3a0XhQVomJEb2Qh4VHTPNRO4GPZh0V7VRbiWwkRg==

turndown@7.2.0:
  version "7.2.0"
  resolved "https://registry.npmjs.org/turndown/-/turndown-7.2.0.tgz"
  integrity sha512-eCZGBN4nNNqM9Owkv9HAtWRYfLA4h909E/WGAWWBpmB275ehNhZyk87/Tpvjbp0jjNl9XwCsbe6bm6CqFsgD+A==
  dependencies:
    "@mixmark-io/domino" "^2.2.0"

uncontrollable@^7.2.1:
  version "7.2.1"
  resolved "https://registry.npmjs.org/uncontrollable/-/uncontrollable-7.2.1.tgz"
  integrity sha512-svtcfoTADIB0nT9nltgjujTi7BzVmwjZClOmskKu/E8FW9BXzg9os8OLr4f8Dlnk0rYWJIWr4wv9eKUXiQvQwQ==
  dependencies:
    "@babel/runtime" "^7.6.3"
    "@types/react" ">=16.9.11"
    invariant "^2.2.4"
    react-lifecycles-compat "^3.0.4"

undici-types@~6.21.0:
  version "6.21.0"
  resolved "https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz"
  integrity sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==

unicode-properties@^1.4.0, unicode-properties@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/unicode-properties/-/unicode-properties-1.4.1.tgz"
  integrity sha512-CLjCCLQ6UuMxWnbIylkisbRj31qxHPAurvena/0iwSVbQ2G1VY5/HjV0IRabOEbDHlzZlRdCrD4NhB0JtU40Pg==
  dependencies:
    base64-js "^1.3.0"
    unicode-trie "^2.0.0"

unicode-trie@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/unicode-trie/-/unicode-trie-2.0.0.tgz"
  integrity sha512-x7bc76x0bm4prf1VLg79uhAzKw8DVboClSN5VxJuQ+LKDOVEW9CdH+VY7SP+vX7xCYQqzzgQpFqz15zeLvAtZQ==
  dependencies:
    pako "^0.2.5"
    tiny-inflate "^1.0.0"

update-browserslist-db@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.1.tgz"
  integrity sha512-R8UzCaa9Az+38REPiJ1tXlImTJXlVfgHZsglwBD/k6nj76ctsH1E3q4doGrukiLQd3sGQYu56r5+lo5r94l29A==
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.0"

url-parse@1.5.10:
  version "1.5.10"
  resolved "https://registry.npmjs.org/url-parse/-/url-parse-1.5.10.tgz"
  integrity sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

use-isomorphic-layout-effect@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/use-isomorphic-layout-effect/-/use-isomorphic-layout-effect-1.1.2.tgz"
  integrity sha512-49L8yCO3iGT/ZF9QttjwLF/ZD9Iwto5LnH5LmEdk/6cFmXddqi2ulF0edxTwjj+7mqvpVVGQWvbXZdn32wRSHA==

use-memo-one@^1.1.1:
  version "1.1.3"
  resolved "https://registry.npmjs.org/use-memo-one/-/use-memo-one-1.1.3.tgz"
  integrity sha512-g66/K7ZQGYrI6dy8GLpVcMsBp4s17xNkYJVSMvTEevGy3nDxHOfE6z8BVE22+5G5x7t3+bhzrlTDB7ObrEE0cQ==

use-sync-external-store@^1.0.0:
  version "1.2.2"
  resolved "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.2.2.tgz"
  integrity sha512-PElTlVMwpblvbNqQ82d2n6RjStvdSoNe9FG28kNfz3WiXilJm4DdNkEzRhCZuIDwY8U08WVihhGR5iRqAwfDiw==

util-deprecate@^1.0.1, util-deprecate@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

uuid@^9.0.1:
  version "9.0.1"
  resolved "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz"
  integrity sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==

uuid@8.3.2:
  version "8.3.2"
  resolved "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz"
  integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==

uuidv4@^6.2.13:
  version "6.2.13"
  resolved "https://registry.npmjs.org/uuidv4/-/uuidv4-6.2.13.tgz"
  integrity sha512-AXyzMjazYB3ovL3q051VLH06Ixj//Knx7QnUSi1T//Ie3io6CpsPu9nVMOx5MoLWh6xV0B9J0hIaxungxXUbPQ==
  dependencies:
    "@types/uuid" "8.3.4"
    uuid "8.3.2"

vanilla-colorful@0.7.2:
  version "0.7.2"
  resolved "https://registry.npmjs.org/vanilla-colorful/-/vanilla-colorful-0.7.2.tgz"
  integrity sha512-z2YZusTFC6KnLERx1cgoIRX2CjPRP0W75N+3CC6gbvdX5Ch47rZkEMGO2Xnf+IEmi3RiFLxS18gayMA27iU7Kg==

victory-vendor@^36.6.8:
  version "36.9.2"
  resolved "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.9.2.tgz"
  integrity sha512-PnpQQMuxlwYdocC8fIJqVXvkeViHYzotI+NJrCuav0ZYFoq912ZHBk3mCeuj+5/VpodOjPe1z0Fk2ihgzlXqjQ==
  dependencies:
    "@types/d3-array" "^3.0.3"
    "@types/d3-ease" "^3.0.0"
    "@types/d3-interpolate" "^3.0.1"
    "@types/d3-scale" "^4.0.2"
    "@types/d3-shape" "^3.1.0"
    "@types/d3-time" "^3.0.0"
    "@types/d3-timer" "^3.0.0"
    d3-array "^3.1.6"
    d3-ease "^3.0.1"
    d3-interpolate "^3.0.1"
    d3-scale "^4.0.2"
    d3-shape "^3.1.0"
    d3-time "^3.0.0"
    d3-timer "^3.0.1"

vite-compatible-readable-stream@^3.6.1:
  version "3.6.1"
  resolved "https://registry.npmjs.org/vite-compatible-readable-stream/-/vite-compatible-readable-stream-3.6.1.tgz"
  integrity sha512-t20zYkrSf868+j/p31cRIGN28Phrjm3nRSLR2fyc2tiWi4cZGVdv68yNlwnIINTkMTmPoMiSlc0OadaO7DXZaQ==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

vite@^3.0.0, vite@^3.2.3:
  version "3.2.11"
  resolved "https://registry.npmjs.org/vite/-/vite-3.2.11.tgz"
  integrity sha512-K/jGKL/PgbIgKCiJo5QbASQhFiV02X9Jh+Qq0AKCRCRKZtOTVi4t6wh75FDpGf2N9rYOnzH87OEFQNaFy6pdxQ==
  dependencies:
    esbuild "^0.15.9"
    postcss "^8.4.18"
    resolve "^1.22.1"
    rollup "^2.79.1"
  optionalDependencies:
    fsevents "~2.3.2"

void-elements@3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/void-elements/-/void-elements-3.1.0.tgz"
  integrity sha512-Dhxzh5HZuiHQhbvTW9AMetFfBHDMYpo23Uo9btPXgdYP+3T5S+p+jgNy7spra+veYhBP2dCSgxR/i2Y02h5/6w==

warning@^4.0.0, warning@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmjs.org/warning/-/warning-4.0.3.tgz"
  integrity sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w==
  dependencies:
    loose-envify "^1.0.0"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  integrity sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz"
  integrity sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.2:
  version "1.1.5"
  resolved "https://registry.npmjs.org/wide-align/-/wide-align-1.1.5.tgz"
  integrity sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==
  dependencies:
    string-width "^1.0.2 || 2 || 3 || 4"

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz"
  integrity sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==

ws@~8.17.1:
  version "8.17.1"
  resolved "https://registry.npmjs.org/ws/-/ws-8.17.1.tgz"
  integrity sha512-6XQFvXTkbfUOZOKKILFG1PDK2NDQs4azKQl26T0YS5CxqWLgXajbPZ+h4gZekJyRqFU8pvnbAbbs/3TgRPy+GQ==

xmlhttprequest-ssl@~2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/xmlhttprequest-ssl/-/xmlhttprequest-ssl-2.0.0.tgz"
  integrity sha512-QKxVRxiRACQcVuQEYFsI1hhkrMlrXHPegbbd1yn9UHOmRxY+si12nQYzri3vbzt8VdTTRviqcKxcyllFas5z2A==

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz"
  integrity sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
  integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==

yaml@^1.10.0:
  version "1.10.2"
  resolved "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz"
  integrity sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==

yaml@^2.3.4:
  version "2.6.0"
  resolved "https://registry.npmjs.org/yaml/-/yaml-2.6.0.tgz"
  integrity sha512-a6ae//JvKDEra2kdi1qzCyrJW/WZCgFi8ydDV+eXExl95t+5R+ijnqHJbz9tmMh8FUjx3iv2fCQ4dclAQlO2UQ==

yarn@^1.22.19:
  version "1.22.22"
  resolved "https://registry.npmjs.org/yarn/-/yarn-1.22.22.tgz"
  integrity sha512-prL3kGtyG7o9Z9Sv8IPfBNrWTDmXB4Qbes8A9rEzt6wkJV8mUvoirjU0Mp3GGAU06Y0XQyA3/2/RQFVuK7MTfg==

yoga-layout@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/yoga-layout/-/yoga-layout-2.0.1.tgz"
  integrity sha512-tT/oChyDXelLo2A+UVnlW9GU7CsvFMaEnd9kVFsaiCQonFAXd3xrHhkLYu+suwwosrAEQ746xBU+HvYtm1Zs2Q==

yup@^0.32.11:
  version "0.32.11"
  resolved "https://registry.npmjs.org/yup/-/yup-0.32.11.tgz"
  integrity sha512-Z2Fe1bn+eLstG8DRR6FTavGD+MeAwyfmouhHsIUgaADz8jvFKbO/fXc2trJKZg+5EBjh4gGm3iU/t3onKlXHIg==
  dependencies:
    "@babel/runtime" "^7.15.4"
    "@types/lodash" "^4.14.175"
    lodash "^4.17.21"
    lodash-es "^4.17.21"
    nanoclone "^0.2.1"
    property-expr "^2.0.4"
    toposort "^2.0.2"
