# Visitor Tracking Implementation Documentation

## Overview
This implementation provides comprehensive visitor tracking for the promotional pages: `/`, `/pricing`, and `/try-new-lms`. The system captures detailed visitor information and sends it to the backend API for storage in the database.

## API Endpoints

### 1. Track Visitor
**Endpoint:** `POST /website/track-visitor`
**Purpose:** Records a visitor's page visit with comprehensive tracking data

### 2. Get Visitor Statistics (Admin)
**Endpoint:** `GET /admin/visitor-stats`
**Purpose:** Retrieves aggregated visitor statistics for admin dashboard

### 3. Get Visitor List (Admin)
**Endpoint:** `GET /admin/visitor-list`
**Purpose:** Retrieves detailed list of visitor records for admin review

## API Payload Structure

### Track Visitor Payload (`POST /website/track-visitor`)

```json
{
  // Page Information
  "page_name": "home",
  "page_url": "https://yourdomain.com/",
  "page_path": "/",
  
  // Session Information
  "session_id": "session_1703123456789_abc123def",
  "visitor_id": "visitor_1703123456789_xyz789ghi",
  
  // Browser Information
  "browser_name": "Chrome",
  "browser_version": "120.0.6099.109",
  "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...",
  
  // Device Information
  "device_type": "Desktop",
  "operating_system": "Windows",
  
  // Screen Information
  "screen_width": 1920,
  "screen_height": 1080,
  "viewport_width": 1536,
  "viewport_height": 864,
  
  // Location Information
  "timezone": "America/New_York",
  "language": "en-US",
  
  // Referrer Information
  "referrer": "https://google.com",
  "utm_source": "google",
  "utm_medium": "cpc",
  "utm_campaign": "winter_sale",
  "utm_term": "lms_software",
  "utm_content": "ad_variant_a",
  
  // Timestamp
  "visited_at": "2023-12-21T10:30:45.123Z",
  
  // Page-specific Additional Data
  "page_type": "landing",
  "section": "promotional"
}
```

### Page-Specific Additional Data

#### Home Page (`/`)
```json
{
  "page_type": "landing",
  "section": "promotional"
}
```

#### Pricing Page (`/pricing`)
```json
{
  "page_type": "pricing",
  "section": "promotional",
  "is_authenticated": false
}
```

#### Try New LMS Page (`/try-new-lms`)
```json
{
  "page_type": "registration",
  "section": "trial",
  "step": 0,
  "package_id": "pkg_123",
  "package_month": 12,
  "is_trial": 1,
  "referrer_email": "<EMAIL>"
}
```

## Database Schema Recommendation

```sql
CREATE TABLE visitor_tracking (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    
    -- Page Information
    page_name VARCHAR(100) NOT NULL,
    page_url TEXT NOT NULL,
    page_path VARCHAR(500) NOT NULL,
    
    -- Session Information
    session_id VARCHAR(100) NOT NULL,
    visitor_id VARCHAR(100) NOT NULL,
    
    -- Browser Information
    browser_name VARCHAR(50),
    browser_version VARCHAR(50),
    user_agent TEXT,
    
    -- Device Information
    device_type VARCHAR(20),
    operating_system VARCHAR(50),
    
    -- Screen Information
    screen_width INT,
    screen_height INT,
    viewport_width INT,
    viewport_height INT,
    
    -- Location Information
    timezone VARCHAR(100),
    language VARCHAR(10),
    
    -- Referrer Information
    referrer TEXT,
    utm_source VARCHAR(100),
    utm_medium VARCHAR(100),
    utm_campaign VARCHAR(100),
    utm_term VARCHAR(100),
    utm_content VARCHAR(100),
    
    -- Additional Data (JSON for flexibility)
    additional_data JSON,
    
    -- Timestamps
    visited_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_page_name (page_name),
    INDEX idx_visitor_id (visitor_id),
    INDEX idx_session_id (session_id),
    INDEX idx_visited_at (visited_at),
    INDEX idx_page_name_visited_at (page_name, visited_at)
);
```

## Implementation Files

### 1. API Slice
**File:** `src/store/api/master/visitorTrackingSlice.js`
- Defines RTK Query endpoints for visitor tracking
- Handles API communication with backend

### 2. Custom Hook
**File:** `src/hooks/useVisitorTracking.js`
- Captures comprehensive visitor information
- Automatically tracks page visits
- Prevents duplicate tracking per session

### 3. Component Integration
- **Home:** `src/pages/Promotional/Home.jsx`
- **Pricing:** `src/pages/Promotional/Price.jsx`
- **Try New LMS:** `src/pages/auth/TryNewLMS.jsx`

## Usage Examples

### Basic Usage
```javascript
import useVisitorTracking from '@/hooks/useVisitorTracking';

const MyComponent = () => {
  // Basic tracking
  useVisitorTracking('page_name');
  
  return <div>My Component</div>;
};
```

### Advanced Usage with Additional Data
```javascript
import useVisitorTracking from '@/hooks/useVisitorTracking';

const MyComponent = () => {
  // Advanced tracking with additional data
  useVisitorTracking('page_name', {
    custom_field: 'custom_value',
    user_segment: 'premium',
    experiment_variant: 'A'
  });
  
  return <div>My Component</div>;
};
```

## Features

### Automatic Data Collection
- Browser information (name, version, user agent)
- Device information (type, operating system)
- Screen and viewport dimensions
- Timezone and language preferences
- Referrer and UTM parameters
- Session and visitor identification

### Privacy Considerations
- No personally identifiable information is collected
- Uses browser-generated session/visitor IDs
- Respects user privacy while providing valuable analytics

### Performance Optimizations
- Single tracking per component mount
- Debounced tracking to prevent spam
- Minimal performance impact on page load

## Backend Implementation Notes

### Required Backend Endpoints
1. `POST /website/track-visitor` - Store visitor tracking data
2. `GET /admin/visitor-stats` - Retrieve visitor statistics
3. `GET /admin/visitor-list` - Retrieve visitor records

### Response Format
```json
{
  "success": true,
  "message": "Visitor tracked successfully",
  "data": {
    "tracking_id": "track_123456789"
  }
}
```

### Error Handling
```json
{
  "success": false,
  "message": "Validation error",
  "errors": {
    "page_name": ["Page name is required"]
  }
}
```

## Analytics Insights

This tracking system enables analysis of:
- Page popularity and traffic patterns
- User journey through promotional pages
- Device and browser usage statistics
- Geographic distribution (via timezone)
- Marketing campaign effectiveness (UTM tracking)
- Conversion funnel analysis
- User engagement patterns

## Security Considerations

- All data is anonymized
- No sensitive user information is collected
- GDPR compliant data collection
- Secure API endpoints with proper authentication
- Rate limiting to prevent abuse
