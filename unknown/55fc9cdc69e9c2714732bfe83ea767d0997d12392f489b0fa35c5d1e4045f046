import react, { useEffect } from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import { useSelector } from "react-redux";

const PrivateOutlet = () => {
  const navigate = useNavigate();
  
  const { user } = useSelector((state) => state.auth);
  
  useEffect(() => {
    const isAuthenticated = () => {
       return localStorage.getItem("lms_token") != null;
   };
    const localAuth = isAuthenticated();
    if ( user == null && !localAuth) {navigate('/', {replace: true})}
  }, [navigate])

  return <Outlet />
};

export default PrivateOutlet;