/* Drag and Drop Styles - Simplified for smooth vertical-only dragging */

/* Basic cursor styles */
.dragging-active,
.dragging-active *,
.vertical-drag-only,
.vertical-drag-only * {
  cursor: ns-resize !important;
}

/* Drag handle styles */
.drag-handle {
  cursor: grab;
  transition: color 0.2s ease, background-color 0.2s ease;
  background-color: #f9fafb; /* gray-50 */
  border-right: 1px solid #e5e7eb; /* gray-200 */
  padding: 8px 12px;
}

.drag-handle:hover {
  background-color: #f3f4f6; /* gray-100 */
  color: #3b82f6; /* blue-500 */
}

.drag-handle:active {
  cursor: grabbing;
  color: #1d4ed8; /* blue-700 */
}

/* Draggable item styles */
.draggable-item {
  transition: box-shadow 0.2s ease, border-color 0.2s ease, background-color 0.2s ease;
  touch-action: pan-y; /* Allow only vertical panning on touch devices */
  user-select: none; /* Prevent text selection during drag */
}

.draggable-item.dragging {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: #93c5fd; /* blue-300 */
  background-color: #eff6ff; /* blue-50 */
  z-index: 50;
}

/* Droppable area styles */
.droppable-area {
  transition: background-color 0.2s ease;
}

.droppable-area.dragging-over {
  background-color: #f0f9ff; /* sky-50 */
}

/* Placeholder styles */
.placeholder {
  background-color: #f3f4f6; /* gray-100 */
  border: 2px dashed #d1d5db; /* gray-300 */
  border-radius: 0.5rem;
  margin: 0.75rem 0;
}

/* Override react-beautiful-dnd styles for smooth vertical-only dragging */
[data-rbd-draggable-id] {
  transition: transform 0.1s ease !important;
}

/* Fix for Firefox */
@-moz-document url-prefix() {
  .draggable-item {
    transform: translate3d(0, 0, 0);
  }
}
