import React from "react";
import * as yup from "yup";

export const initialValues = { 
    chapter_quiz_id: '',
    class_level_id: '',
    subject_id: '',
    chapter_id: '',
    question_text:  '',
    option1:  '',
    option2:  '',
    option3:  '',
    option4:  '',
    answer1: false,
    answer2: false,
    answer3: false,
    answer4: false,
    explanation_text:  '',
    is_active: '',
    question_set_id: '',
    chapter_quiz_subject_id: '',
};

export const validationSchema =  yup.object({
    question_text: yup.string()
        .max(250, "Should not be more than 250 characters")
        .min(3, "Should not be less than 3 characters")
        .required("Question Text is Required"),
    explanation_text : yup.string().max(250, "Should not be more than 250 characters")

})