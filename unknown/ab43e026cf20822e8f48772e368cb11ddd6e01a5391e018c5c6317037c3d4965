import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { Formik, Form, Field } from "formik";
import { validationSchema } from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { useParams } from "react-router-dom";
import { useGetMentorListQuery } from "@/store/api/master/mentorSlice";
import avatar from "@/assets/images/avatar/av-1.svg";
import Select from "@/components/ui/Select";

const Create = ({ showModal, setShowModal, course }) => {
  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();
  const dispatch = useDispatch();
  const { id } = useParams();

  const [isActive, setIsActive] = useState(false);

  const [search, setSearch] = useState("");
  const [apiParam, setApiParam] = useState("pagination=false");
  const mentorList = useGetMentorListQuery(apiParam)?.data;

  const initialValues = {
    course_id: id,
    mentor_ids: course.course_mentor.map((item) => item.mentor_id),
  };

  const onSubmit = async (values, { resetForm }) => {
    console.log(values);
    const response = await postApi({
      end_point: "admin/mentor-assign-save-or-update",
      body: values,
    });
    setShowModal(false);
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      title={course.title + `: Assign Mentor`}
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowModal(false)}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={initialValues}
        onSubmit={onSubmit}
      >
        {({ values, errors, setFieldValue }) => (
          <Form>
            <div className="h-[450px] overflow-y-auto">
              <div className="mb-4">
                <InputField
                  type="text"
                  name="seonChangearch"
                  placeholder="Search"
                  value={values.search}
                  onChange={(e) => {
                    setFieldValue("search", e.target.value);
                    setApiParam("pagination=false&search=" + e.target.value);
                  }}
                />
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                {mentorList?.map((item, index) => (
                  <label
                    key={index}
                    className="flex items-center shadow-sm border border-gray-200 rounded-md p-2 cursor-pointer"
                    style={{
                      backgroundColor: values?.mentor_ids?.includes(item.id)
                        ? "#E0EAFF"
                        : "white",
                    }}
                    htmlFor={`mentor_ids-${index}`}
                  >
                    <div className="flex items-center">
                      <label className="inline-flex items-center">
                        <input
                          id={`mentor_ids-${index}`}
                          type="checkbox"
                          className="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-offset-0 focus:ring-indigo-200 h-4 w-4 mr-2"
                          checked={values?.mentor_ids?.includes(item.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setFieldValue("mentor_ids", [
                                ...values.mentor_ids,
                                item.id,
                              ]);
                            } else {
                              setFieldValue(
                                "mentor_ids",
                                values.mentor_ids.filter((i) => i !== item.id)
                              );
                            }
                          }}
                        />
                      </label>
                    </div>
                    <div className="flex items-center md:flex-row flex-col md:space-x-2 space-x-0 space-y-2 md:space-y-0">
                      <div className="md:flex md:items-center md:justify-start gap-2">
                        <img
                          src={
                            item.image
                              ? import.meta.env.VITE_ASSET_HOST_URL + item.image
                              : avatar
                          }
                          className=" md:block hidden rounded-full w-8 h-8 md:mr-2 md:mb-0 mb-2"
                          alt="avatar"
                        />
                        <span className="font-bold text-sm md:block hidden">
                          {item.name}
                        </span>
                        <span className="text-sm md:block hidden">
                          {item.email}
                        </span>

                        <div className="md:hidden flex items-center gap-2">
                          <img
                            src={
                              item.image
                                ? import.meta.env.VITE_ASSET_HOST_URL +
                                  item.image
                                : avatar
                            }
                            className="rounded-full w-8 h-8 md:mr-2 md:mb-0 mb-2"
                            alt="avatar"
                          />
                          <div className="grid grid-cols-1">
                            <span className="font-bold text-sm mr-2">
                              {item.name}
                            </span>
                            <span className="text-sm">{item.email}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className={`btn text-center btn-primary ${
                  values.mentor_ids.length === 0
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }`}
                disabled={values.mentor_ids.length === 0}
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default Create;
