import React, { useState } from "react";
import { useLocation } from "react-router-dom";
import Icon from "@/components/ui/Icon";
import { useDispatch } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";
import EditCourseModal from "./EditCourseModal";

const CourseDetails = () => {
  const location = useLocation();
  const dispatch = useDispatch();
  const { course } = location.state || {};

  if (!course) {
    return <div>No course data available.</div>;
  }
  const handleEditopenClick = () => {
    dispatch(setEditData(course)); // Set the course data to edit
    dispatch(setEditShowModal(true)); // Show the modal
  };

  const handleEditClick = () => {
    dispatch(setEditData(course)); // Set the course data to edit
    setIsSidePanelOpen(true); // Open the side panel
  };

  const handleBackClick = () => {
    setIsSidePanelOpen(false); // Close the side panel
  };

  return (
    <>
      <div className="bg-primary-100 p-10 border-primary-500 border-2 rounded-lg my-10">
        <h6 className="my-2">Course Title :{course.title}</h6>
        <h6>Progress Bar</h6>
      </div>
      {/* <div className="bg-primary-100 p-10 border-primary-500 border-2 rounded-lg">
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl overflow-hidden w-80 h-80">
          <div className="relative">
            <img
              src={`${import.meta.env.VITE_ASSET_HOST_URL}${course.thumbnail}`}
              alt={course.title}
              className="w-full h-36 object-cover"
            />
            
            {
              <span
                className={`absolute top-2 left-2 px-2 py-1 text-xs font-medium text-white rounded-full ${
                  course.is_active ? "bg-green-500" : "bg-red-500"
                }`}
              >
                {course.is_active ? "Active" : "Inactive"}
              </span>
            }
          </div>
          <div className="p-1">
            <h6 className="text-base font-semibold text-gray-900 dark:text-white">
              
              {course.title.length > 28
                ? `${course.title.slice(0, 28)}..`
                : course.title}
            </h6>
            <p className="my-2 text-sm text-gray-600 dark:text-gray-300 tracking-tighter">
              {course.description.length > 70
                ? `${course.description.slice(0, 70)}..`
                : course.description}
            </p>
          </div>
          <div className="p-1 flex justify-between">
            <p className="bg-primary-500 text-base font-bold text-white rounded-lg flex justify-between items-center px-2 gap-2">
              <Icon size={30} icon="tabler:coin-taka-filled" className="" />
              <span className="line-through text-base font-bold">
                {course.regular_price}
              </span>
              <span className="text-base font-bold">{course.sale_price}</span>
            </p>
          </div>
        </div>
      </div> */}

      <div className="bg-white p-10 border-primary-100 border-2 rounded-lg my-10 relative shadow-2xl">
        <div className="absolute top-2 left-3 flex space-x-3">
          <button
            className="text-primary-500 hover:text-primary-700 -mt-8 border-4 rounded-full p-2 border-primary-300 hover:bg-white bg-primary-100"
            // onClick={handleEditClick}
          >
            <Icon size={40} icon="mage:edit" className="" />
          </button>
          {/* <button className="text-primary-500 hover:text-primary-700 -mt-8 border-4 rounded-full p-2 border-primary-300 hover:bg-white bg-primary-100">
            <Icon
              size={30}
              icon="oui:ml-create-single-metric-job"
              className=""
            />
          </button> */}
        </div>
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl overflow-hidden w-80 h-80">
          <div className="relative">
            <img
              src={`${import.meta.env.VITE_ASSET_HOST_URL}${course.thumbnail}`}
              alt={course.title}
              className="w-full h-36 object-cover"
            />
            {/* {showBadge()} */}
            {
              <span
                className={`absolute top-2 left-2 px-2 py-1 text-xs font-medium text-white rounded-full ${
                  course.is_active ? "bg-green-500" : "bg-red-500"
                }`}
              >
                {course.is_active ? "Active" : "Inactive"}
              </span>
            }
          </div>
          <div className="p-1">
            <h6 className="text-base font-semibold text-gray-900 dark:text-white">
              {/* {course.title} */}
              {course.title.length > 28
                ? `${course.title.slice(0, 28)}..`
                : course.title}
            </h6>
            {course?.description.length > 0 ? (
            <p className="my-2 text-sm text-gray-600 dark:text-gray-300 tracking-tighter">
              {course?.description.length > 70
                ? `${course.description.slice(0, 70)}..`
                : course.description}
            </p>
            ): 
            <p className="my-2 text-sm text-red-400 dark:text-gray-300 tracking-tighter">
              Please add a description
            </p>
            }
          </div>
          <div className="p-1 flex justify-between">
            <p className="bg-primary-500 text-base font-bold text-white rounded-lg flex justify-between items-center px-2 gap-2">
              <Icon size={30} icon="tabler:coin-taka-filled" className="" />
              <span className="line-through text-base font-bold">
                {course.regular_price}
              </span>
              <span className="text-base font-bold">{course.sale_price}</span>
            </p>
          </div>
        </div>
      </div>

      <div className="bg-white p-10 border-primary-100 border-2 rounded-lg my-10 relative shadow-2xl">
        <div className="absolute top-2 right-3 flex space-x-3">
          <button
            className="text-primary-500 hover:text-primary-700 -mt-8 border-4 rounded-full p-2 border-primary-300 hover:bg-white bg-primary-100"
            onClick={handleEditopenClick}
          >
            <Icon size={40} icon="mage:edit" className="" />
            {/* <EditCourseModal /> */}
          </button>
          <button className="text-primary-500 hover:text-primary-700 -mt-8 border-4 rounded-full p-2 border-primary-300 hover:bg-white bg-primary-100">
            <Icon
              size={30}
              icon="oui:ml-create-single-metric-job"
              className=""
            />
          </button>
        </div>
        <div className="my-5 p-4">
          <h2 className="text-base font-semibold text-gray-900 dark:text-white">
            <span className="text-primary-500">Title:</span> {course.title}
          </h2>
          <p className="my-2 text-sm text-gray-600 dark:text-gray-300 tracking-tighter">
            <span className="text-primary-500">Description:</span>{" "}
            {course.description}  
          </p>
        </div>
      </div>
    </>
  );
};

export default CourseDetails;
