import React, { useState } from "react";
import InputField from "@/components/ui/InputField";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { Formik, Form } from "formik";
import SimpleBar from "simplebar-react";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { useParams } from "react-router-dom";
import Icon from "@/components/ui/Icon";
import * as yup from "yup";

import ColorPicker from "@/components/ui/ColorPicker";
import Fileinput from "@/components/ui/Fileinput";
import { useDispatch } from "react-redux";

const EditSubject = ({ showEditModal, setShowEditModal, editData }) => {
  const [postApi, { isLoading }] = usePostApiMutation();
  const { id } = useParams();
  const [isActive, setIsActive] = useState(true);
  const [color, setColor] = useState("#000000");
  const dispatch = useDispatch();

  const validationSchema = yup.object({
    name: yup.string().required("Subject name is required"),
  });

  const initialValues = {
    name: editData?.name || "",
    color_code: editData?.color_code || "",
    icon: editData?.icon || "",
  };

  console.log(editData)
  // import {
  //   initialValues,
  //   validationSchema,
  // } from "../CourseCategory/formSettings";
  const onSubmit = async (values, { resetForm }) => {
    let formData = new FormData();
    formData.append("id", editData.id);
    formData.append("course_id", id);
    formData.append("name", values.name);
    formData.append("is_active", isActive ? 1 : 0);
    if (values.icon && typeof values.icon !== "string") {
      formData.append("icon", values.icon);
    }
    if (color) {
      formData.append("color_code", color);
    }

    await postApi({
      end_point: "admin/subject-save-or-update",
      body: formData,
    });
    resetForm();
    dispatch(setShowEditModal(false));
  };

  return (
    <>
      {showEditModal && (
        <div className="fixed right-0 top-0 w-[450px] bg-white dark:bg-slate-800 h-screen z-[9999] shadow-xl border-l border-slate-200 dark:border-slate-700 transition-transform duration-300 ease-in-out transform">
          <SimpleBar className="h-full">
            <header className="sticky top-0 z-10 bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 px-6 py-4 flex items-center justify-between">
              <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
                Edit Subject
              </h2>
              <button
                onClick={() => dispatch(setShowEditModal(false))}
                className="p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-full transition-colors duration-200"
              >
                <Icon icon="heroicons-outline:x" className="w-5 h-5" />
              </button>
            </header>

            <div className="p-6">
              <Formik
                validationSchema={validationSchema}
                initialValues={initialValues}
                onSubmit={onSubmit}
              >
                {({ setFieldValue, errors, touched, values }) => (
                  <Form className="space-y-6">
                    <InputField
                      label="Subject Name"
                      name="name"
                      type="text"
                      placeholder="Enter Subject Name"
                      required
                      className="w-full"
                    />
                    <div>
                      <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                        Icon
                      </label>{console.log(values)}
                      <Fileinput
                        name="icon"
                        onChange={(e) =>
                          setFieldValue("icon", e.currentTarget.files[0])
                        }
                        accept="image/*"
                        selectedFile={values.icon}
                        placeholder="Choose an icon"
                        className="w-full"
                      />
                      {errors.icon && touched.icon && (
                        <p className="mt-1 text-sm text-red-500">
                          {errors.icon}
                        </p>
                      )}
                      {/* {values.icon && (
                        <img
                          src={URL.createObjectURL(values.icon)}
                          alt="Preview"
                          className="mt-2 h-20 w-20 object-cover rounded"
                        />
                      )} */}
                    </div>
                    <div className="">
                      <ColorPicker
                        label="Select Color"
                        value={color}
                        onChange={(newColor) => setColor(newColor)}
                      />
                    </div>

                    <div className="pt-2">
                      <Switch
                        label="Active Status"
                        activeClass="bg-success-500"
                        value={isActive}
                        name="is_active"
                        onChange={() => setIsActive(!isActive)}
                      />
                    </div>

                    <div className="pt-4">
                      <Button
                        isLoading={isLoading}
                        type="submit"
                        className="w-full btn btn-primary h-11 font-medium"
                      >
                        {isLoading ? "Submitting..." : "Submit"}
                      </Button>
                    </div>
                  </Form>
                )}
              </Formik>
            </div>
          </SimpleBar>
        </div>
      )}
    </>
  );
};

export default EditSubject;
