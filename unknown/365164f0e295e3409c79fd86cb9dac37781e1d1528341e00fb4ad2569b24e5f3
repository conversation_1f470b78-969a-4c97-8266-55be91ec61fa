import React, { useState } from "react";
import { useDropzone } from "react-dropzone";

// Image import
import uploadSvgImage from "@/assets/images/svg/upload.svg";

const DropZone = ({
  classLabel = "form-label",
  horizontal,
  label, onChange
}) => {
  const [files, setFiles] = useState([]);
  const { getRootProps, getInputProps, isDragAccept } = useDropzone({
    accept: {
      "image/*": [],
    },
    onDrop: (acceptedFiles) => {
      setFiles(
        acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        )
      );
    },
  });

  // Function to handle image click and clear the file
  const handleImageClick = (index) => {
    const newFiles = [...files];
    newFiles.splice(index, 1); // Remove the clicked file
    setFiles(newFiles); // Update the state
  };

  return (
    <div>
      {label && (
        <label
          className={`block capitalize ${classLabel} ${
            horizontal ? "flex-0 mr-6 md:w-[100px] w-[60px] break-words" : ""
          }`}
        >
          {label}
        </label>
      )}
      <div className="w-full text-center border-dashed border border-secondary-500 rounded-md py-[52px] flex flex-col justify-center items-center">
        {files.length === 0 && (
          <div {...getRootProps({ className: "dropzone" })}>
            <input className="hidden" {...getInputProps()} 
            
            onChange={(e) => {
              console.log(e.target.files);
              onChange(e);
            }}
            />
            <img src={uploadSvgImage} alt="" className="mx-auto mb-4" />
            {isDragAccept ? (
              <p className="text-sm text-slate-500 dark:text-slate-300">
                Drop the files here ...
              </p>
            ) : (
              <p className="text-sm text-slate-500 dark:text-slate-300">
                Drop files here or click to upload.
              </p>
            )}
          </div>
        )}
        <div className="flex space-x-4 flex-wrap justify-center">
          {files.map((file, i) => (
            <div key={i} className="mb-0 flex-none relative">
              <div className="h-[100px] w-[100px] mx-auto mt-0 rounded-md overflow-hidden cursor-pointer" onClick={() => handleImageClick(i)}>
                <img
                  src={file.preview}
                  className="object-cover h-full w-full block rounded-md" // Use w-full to make the image fill the container
                  onLoad={() => {
                    URL.revokeObjectURL(file.preview);
                  }}
                  alt={`preview-${i}`}
                />
              </div>
              <button
                className="btn btn-sm absolute top-0 right-0 bg-red-500 text-white rounded-full p-1"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent triggering the onClick of the image
                  handleImageClick(i);
                }}
              >
                X
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DropZone;
