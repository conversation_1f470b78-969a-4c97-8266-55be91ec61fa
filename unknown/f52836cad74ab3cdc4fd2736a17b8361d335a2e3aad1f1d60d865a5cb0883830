import React, { useState } from "react";
import { useDeleteApiMutation } from "@/store/api/master/commonSlice";
import Icon from "@/components/ui/Icon";

const UploadVideo = ({ setFieldValue }) => {
  const [videoUrl, setVideoUrl] = useState(null);
  const [videoObject, setVideoObject] = useState(null);
  const [progress, setProgress] = useState(0);
  const [deleteApi, { isLoading }] = useDeleteApiMutation();

  const handleUpload = async (video) => {
    let formData = new FormData();
    formData.append("video", video);

    // Use XMLHttpRequest for progress tracking
    const xhr = new XMLHttpRequest();
    xhr.open("POST", `${import.meta.env.VITE_HOST_URL}admin/upload-video`); // Replace with your actual endpoint

    xhr.upload.onprogress = (event) => {
      if (event.lengthComputable) {
        const percentCompleted = Math.round((event.loaded / event.total) * 100);
        setProgress(percentCompleted); // Update progress state
      }
    };

    xhr.onload = () => {
      if (xhr.status === 200) {
        const response = JSON.parse(xhr.responseText);
        console.log(response?.data);
        if (response?.data) {
          setFieldValue("temp_video_id", response?.data?.id);
          setFieldValue("raw_url", response?.data?.raw_url);
          setVideoObject(response?.data);
          setVideoUrl(response?.data?.raw_url);
        }
        setProgress(0); // Reset progress after completion
      } else {
        console.error("Upload failed:", xhr.responseText);
        setProgress(0); // Reset progress in case of an error
      }
    };

    xhr.onerror = () => {
      console.error("Upload error");
      setProgress(0); // Reset progress in case of an error
    };

    xhr.send(formData);
  };

  const handleInputChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleUpload(file);
      e.target.value = ""; // Reset the input value to ensure `onChange` triggers every time
    }
  };

  const handleDeleteVideo = async () => {
    console.log(videoObject);

    const response = await deleteApi({end_point: '/admin/delete-video/' + videoObject?.id, body: {}})
    if (response.data) {
      setVideoUrl(null);
      setVideoObject(null);
      setFieldValue("raw_url", null); 
    }
  };

  return (
    <div className="video-upload-container">
      {/* Video Upload Button */}
      {!videoUrl && (
        <label className="upload-button py-2 px-4 bg-blue-500 text-white rounded cursor-pointer">
          Upload Video
          <input
            type="file"
            accept="video/*"
            style={{ display: "none" }}
            onChange={handleInputChange}
          />
        </label>
      )}

      {/* Upload Progress */}
      {progress > 0 && (
        <div className="progress-bar mt-2">
          <div
            className="progress-bar-fill bg-green-500 h-2 rounded"
            style={{ width: `${progress}%` }}
          ></div>
          <p className="text-sm mt-1 text-gray-500">{progress}%</p>
        </div>
      )}

      {/* Video Preview Player */}
      {videoUrl && (
        <div className="relative video-preview mt-4">
          <video width="600" controls>
            <source src={videoUrl} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
          {/* Delete Button */}
          <Icon className="p-2 absolute top-2 right-2 cursor-pointer text-red-500 text-4xl bg-white rounded-full" onClick={handleDeleteVideo}  icon="heroicons-outline:x" />
       
        </div>
      )}
    </div>
  );
};

export default UploadVideo;
