import React from "react";
import {
  Users,
  UserCheck,
  GraduationCap,
  Calendar,
  BookOpen,
  Activity,
  ArrowLeft,
} from "lucide-react";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import { useParams } from "react-router-dom";
import defaultBatchImage from "@/assets/images/batches/batch-default-image.jpg";
import avater from "@/assets/images/avatar/av-1.svg";
import { setEditData, setEditShowModal } from "@/features/commonSlice";
import AddToBatchModal from "./AddStudnetToBatchModal";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import AddMentorToBatchModal from "./AddMentorToBatchModal";

const BatchDetails = ({ batch, onClose }) => {
  const dispatch = useDispatch();
  const { showEditModal, editData } = useSelector((common) => common.commonReducer);
  const { id } = useParams();
  // console.log(showModal);
  const {
    data: batchDetails,
    isLoading,
    error,
  } = useGetApiQuery(`/admin/batches/${id}`);
  // console.log(batchDetails);

  const stats = [
    {
      label: "Total Students",
      value: batchDetails?.students.length,
      icon: GraduationCap,
    },
    {
      label: "Total Teachers",
      value: batchDetails?.mentors.length,
      icon: UserCheck,
    },
    { label: "Capacity", value: batchDetails?.capacity, icon: Users },
  ];

  // console.log(batchDetails?.image);
  const imageSrc = batchDetails?.image
    ? `${import.meta.env.VITE_ASSET_HOST_URL}${batchDetails?.image}`
    : defaultBatchImage;

  return (
    <div className=" mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl">
      <div className="relative group">
        <img
          src={imageSrc}
          alt={batchDetails?.name || "Batch Image"}
          className="w-full h-72 object-cover transition-transform duration-300 group-hover:scale-105"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-gray-500/60 to-transparent" />
        <div className="absolute bottom-4 left-4 right-4">
          <h1 className="text-3xl font-bold text-white mb-2">
            {batchDetails?.name}
          </h1>
          <div className="flex items-center space-x-2 text-white/80">
            <BookOpen className="w-4 h-4" />
            <span className="text-sm">
              Course Name:{" "}
              {batchDetails?.course?.title}
            </span>
          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="flex items-center border border-indigo-100 hover:border-indigo-200 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg transition-all duration-300 hover:shadow-md hover:scale-105"
            >
              <stat.icon className="w-8 h-8 text-indigo-500 dark:text-indigo-400" />
              <div className="ml-4">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {stat.label}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stat.value}
                </p>
              </div>
            </div>
          ))}
        </div>

        <div className="space-y-6">
          <div className="p-2 shadow-md rounded-md">
            <p>{batchDetails?.description}</p>
          </div>

          <div className="w-full flex justify-between">
            <h2 className="text-2xl font-bold mb-4">Students</h2>
            <button
              onClick={() => dispatch(setEditShowModal(true), dispatch(setEditData({'for_student': true})))}
              className="py-1 px-4 bg-indigo-500 text-white rounded-lg text-base hover:bg-indigo-600"
            >
              Add Student
            </button>
          </div>
          <ul className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {batchDetails?.students.map((student, index) => (
              <li
                key={index}
                className="flex items-center p-2 bg-gray-50 border border-indigo-100 hover:shadow-sm hover:border-indigo-200 rounded-md"
              >
                <img
                  src={
                    student.image
                      ? `${import.meta.env.VITE_ASSET_HOST_URL + student.image}`
                      : avater
                  }
                  alt={student.name || "Student Image"}
                  className="w-12 h-12 rounded-full mr-4"
                />
                <div>
                  <p className="text-lg font-bold">{student.name}</p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {student.email || "N/A"}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {student.contact_no || "N/A"}
                  </p>
                </div>
              </li>
            ))}
          </ul>

          <div className="w-full flex justify-between">
            <h2 className="text-2xl font-bold mb-4">Teachers</h2>
            <button
              onClick={() => dispatch(setEditShowModal(true), dispatch(setEditData({'for_mentor': true})))}
              className="py-1 px-4 bg-indigo-500 text-white rounded-lg text-base hover:bg-indigo-600"
            >
              Add Teacher
            </button>
          </div>
          <ul className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {batchDetails?.mentors.map((mentor, index) => (
              <li
                key={index}
                className="flex items-center p-2 bg-gray-50 border border-indigo-100 hover:shadow-sm hover:border-indigo-200 rounded-md"
              >
                <img
                  src={
                    mentor.image
                      ? `${import.meta.env.VITE_ASSET_HOST_URL}${mentor.image}`
                      : avater
                  }
                  alt={mentor.name || "Teacher Image"}
                  className="w-12 h-12 rounded-full mr-4"
                />
                <div>
                  <p className="text-lg font-bold">{mentor.name}</p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {mentor.email || "N/A"}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {mentor.contact_no || "N/A"}
                  </p>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>


      {(showEditModal && editData?.for_student) && (
        <AddToBatchModal
          showEditModal={showEditModal}
          setEditShowModal={setEditShowModal}
          data={{ studentList: batchDetails?.students, course_id: batchDetails?.course_id, batch_id: id }}
        />
      )}

      {(showEditModal && editData?.for_mentor) && (
        <AddMentorToBatchModal
          showEditModal={showEditModal}
          setEditShowModal={setEditShowModal}
          data={{ mentorList: batchDetails?.mentors, batch_id: id, course_id: batchDetails?.course_id }}
        />
      )}
    </div>
  );
};

export default BatchDetails;
