import React from "react";
import * as yup from "yup";

export const initialValues = { 
    name: '', 
    // link: '',
    // icon: '',
    is_authentication_needed: false,
    is_content: false
};

export const validationSchema =  yup.object({
    name: yup.string().max(50, "Should not be more than 50 characters").min(3, "Should not be less than 3 characters").required("Name is Required"),
    // icon: yup.string().required("Link/URL is Required")
})