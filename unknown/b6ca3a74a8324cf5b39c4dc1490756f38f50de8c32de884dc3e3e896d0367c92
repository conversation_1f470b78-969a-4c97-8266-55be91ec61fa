import { apiSlice } from "../apiSlice";
export const masterSettingsApi = apiSlice.injectEndpoints({
  reducerPath: "masterSettingsApi",
  tagTypes: ["Master"],
  endpoints: (builder) => ({

    getMenuList: builder.query({
      query: (params) => ({
        url: "admin/menu-list",
        method: "GET",
        params: params
      }),
      providesTags: ["Master"],
    }),
    menuCreateOrUpdate: builder.mutation({
      query: (body) => {
        return {
          url: `admin/menu-save-or-update`,
          method: "POST",
          body: body,
        };
      },
      invalidatesTags: ["Master"],
    }),
  }),
});

export const {
  useMenuCreateOrUpdateMutation,
  useGetMenuListQuery,
  // useOrganizationCreateOrUpdateMutation,
  // useGetOrganizationListQuery,
  // useSettingUpdateMutation,
  // useWebsitePageSaveOrUpdateMutation,
  // useGetWebsitePageListQuery,
  // useGetTagsListQuery,
  // useTagsCreateOrUpdateMutation,
  // useTagsDeleteMutation
 
} = masterSettingsApi;
