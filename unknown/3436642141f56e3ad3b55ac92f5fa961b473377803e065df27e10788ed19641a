import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Pencil } from 'lucide-react';
import EditDescription from './EditDescription';
import EditorData from '@/pages/components/EditorData';
const CourseDescription = ({ description, course }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showModal, setShowModal] = useState(false);

  return (
    <div className="bg-gray-50 rounded-xl p-6 mt-4 relative">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-lg font-semibold text-gray-900">Course Description</h3>
        <button onClick={() => setShowModal(true)} className="text-gray-600 hover:text-gray-800">
          <Pencil className="w-5 h-5" />
        </button>
      </div>

      {description?.length > 0 ? (
        <div>
          <EditorData htmlData={isExpanded ? description : description.slice(0, 300) + (description.length > 300 ? "..." : "")} />
          {/* <div
            className="text-gray-700 leading-relaxed ckeditor-content"
            dangerouslySetInnerHTML={{
              __html: isExpanded ? description : description.slice(0, 300) + (description.length > 300 ? "..." : ""),
            }}
          /> */}

          {description.length > 300 && (
            <button
              className="inline-flex items-center ml-1 text-blue-600 hover:text-blue-700 font-medium transition-colors"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? (
                <>
                  Show less
                  <ChevronUp className="w-4 h-4 ml-1" />
                </>
              ) : (
                <>
                  Show more
                  <ChevronDown className="w-4 h-4 ml-1" />
                </>
              )}
            </button>
          )}
        </div>
      ) : (
        <p className="text-red-500 leading-relaxed">No description available.</p>
      )}

      {showModal && <EditDescription showModal={showModal} setShowModal={setShowModal} course={course} />}
    </div>
  );
};

export default CourseDescription;
