/* Smooth Vertical-Only Drag CSS */

/* Core styles for smooth vertical dragging */
.smooth-draggable {
  user-select: none !important;
  position: relative !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  width: 100% !important;
  transform-origin: center center !important;
}

/* Fix for the transform to ensure vertical-only movement */
.smooth-draggable[data-rbd-draggable-id][data-rbd-dragging="true"] {
  left: 0 !important;
  right: 0 !important;
  position: relative !important;
  transform: translate3d(0, var(--translate-y, 0), 0) !important;
}

/* Override react-beautiful-dnd's transform */
[data-rbd-draggable-id] {
  transform: translate3d(0, var(--translate-y, 0), 0) !important;
}

/* Ensure the draggable item maintains its horizontal position */
.smooth-draggable-inner {
  width: 100%;
  position: relative;
  left: 0 !important;
  right: 0 !important;
}

/* Style for the dragging state */
.smooth-draggable.dragging {
  z-index: 9999 !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

/* Drag handle styles */
.smooth-drag-handle {
  cursor: grab;
  touch-action: none;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  background-color: #f9fafb;
  border-right: 1px solid #e5e7eb;
  transition: background-color 0.2s ease;
}

.smooth-drag-handle:hover {
  background-color: #f3f4f6;
  color: #3b82f6;
}

.smooth-drag-handle:active {
  cursor: grabbing;
}

/* Droppable area styles */
.smooth-droppable {
  transition: background-color 0.2s ease;
}

.smooth-droppable.dragging-over {
  background-color: #f0f9ff;
}

/* Ensure the body has the right cursor during dragging */
body.smooth-dragging * {
  cursor: grabbing !important;
}

/* Fix for Firefox */
@-moz-document url-prefix() {
  .smooth-draggable {
    transform: translate3d(0, 0, 0);
  }
}
