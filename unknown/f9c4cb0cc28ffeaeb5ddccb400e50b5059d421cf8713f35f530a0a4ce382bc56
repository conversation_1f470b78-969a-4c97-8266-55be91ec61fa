import React from "react";
import ClassSchedulesIcon from "@/assets/StudentDashboard/ClassSchedulesIcon.svg";
import { Icon } from "@iconify/react/dist/iconify.js";
import { Link } from "react-router-dom";

const ClassSchedules = ({ classes }) => {
  // Sample Data for live classes
  const Classes = [
    {
      title: "Product Management",
      date: "20/11/2024",
      time: "(10.30 P.M)",
      status: "Join Now",
      nextClass: "N Class 9th Oct,24",
    },
    {
      title: "Product Management",
      date: "20/11/2024",
      time: "(10.30 P.M)",
      status: "Class 9th Oct,24",
    },
  ];
  console.log(classes);

  return (
    <div className="lg:p-4 p-1 rounded-lg bg-white">
      {/* Header Section */}
      <div className="flex justify-between items-center mb-4 p-2 px-3 shadow-md border rounded-lg">
        <div className="flex items-center gap-2">
          <img
            src={ClassSchedulesIcon}
            alt="Class Schedules Icon"
            className="w-8 h-8"
          />
          <h3 className="text-lg font-semibold text-blue-900">
            Class Schedules
          </h3>
        </div>
        <a href="#" className="text-blue-600 font-medium">
          See All
        </a>
      </div>

      {/* Class Cards */}
      <div className="space-y-4">
        {/* {Classes.map((data, index) => ( */}
        {classes ? <div
          // key={index}
          className="flex flex-col bg-white rounded-lg p-4 border border-gray-200 shadow-md"
        >
          {/* Class Details */}
          <div className="flex justify-between items-center divide-x gap-2">
            <div className="flex flex-col gap-1">
              <p className="text-sm text-gray-700 flex items-center gap-1">
                <Icon icon="uiw:date" className="text-sky-600" />{" "}
                {classes?.schedule_datetime?.slice(0, 10)}
              </p>
              {classes?.time && <p className="text-sm text-gray-700 flex items-center gap-1">
                <Icon icon="mingcute:time-line" className="text-sky-600" />{" "}
                {classes.time}
              </p>}
            </div>

            <div className="flex flex-col gap-2 ps-1">
              <span className="text-blue-700 font-semibold text-sm">
                {classes?.title}
              </span>
              {/* Action Button or Next Class */}
              {(classes?.class_url && classes?.has_started && !classes?.has_completed) && (
                <Link to={classes.class_url} className="self-start bg-green-600 hover:bg-green-500 text-white font-medium text-xs py-2 px-4 rounded-lg flex items-center gap-2  justify-center">
                  Join Now <Icon icon="flowbite:arrow-right-outline" className="text-lg" />
                </Link>
              ) 
              // : (
              //   <span className="text-sm text-gray-600 mt-2">
              //     <br />
              //     {classes?.status}
              //   </span>
              // )
              }
            </div>
          </div>
        </div> : <p className="text-center">No Classs Available</p>}
        {/* ))} */}
      </div>
    </div>
  );
};

export default ClassSchedules;
