import React, { useState } from 'react';
import BasicTablePage from '@/components/partials/common-table/table-basic';
import { useDispatch } from 'react-redux';
import { setEditData, setEditShowModal } from '@/features/commonSlice';
import PromotionalItemForm from './PromotionalItemForm';
import PromotionalItemViewModal from './PromotionalItemViewModal';
import DeleteConfirmationModal from './DeleteConfirmationModal';
import { useGetApiQuery, useDeleteApiMutation } from '@/store/api/master/commonSlice';

const PromotionalItems = () => {
    const dispatch = useDispatch();
    const [viewModalOpen, setViewModalOpen] = useState(false);
    const [selectedItem, setSelectedItem] = useState(null);
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);
    const [itemToDelete, setItemToDelete] = useState(null);

    const [apiParam, setApiParam] = useState('');

    const { data, isLoading, refetch } = useGetApiQuery(
        `admin/promotional-items${apiParam}`
    );

    const [deleteApi, { isLoading: isDeleting }] = useDeleteApiMutation();

    const rawItems = data?.data || [];
    const promotionalItems = rawItems.map(item => ({
        ...item,
        item_name_display: item.type === 'course' ? (item.course_name || 'N/A') :
                          item.type === 'ebook' ? (item.ebook_name || 'N/A') : 'N/A'
    }));

    const columns = [
        {
            label: 'Title',
            field: 'title'
        },
        {
            label: 'Type',
            field: 'type',
            formatter: (value) => value.charAt(0).toUpperCase() + value.slice(1)
        },
        {
            label: 'Item Name',
            field: 'item_name_display'
        },

        {
            label: 'Actions',
            field: ''
        }
    ];

    const actions = [
        {
            name: 'view',
            icon: 'heroicons-outline:eye',
            onClick: (index) => {
                setSelectedItem(promotionalItems[index]);
                setViewModalOpen(true);
            }
        },
        {
            name: 'edit',
            icon: 'heroicons:pencil-square',
            onClick: (index) => {
                dispatch(setEditData({
                    ...promotionalItems[index]
                }));
                dispatch(setEditShowModal(true));
            }
        },
        {
            name: 'delete',
            icon: 'heroicons-outline:trash',
            onClick: (index) => {
                setItemToDelete(promotionalItems[index]);
                setDeleteModalOpen(true);
            }
        }
    ];

    const changePage = (val) => {
        setApiParam(val);
    };

    const handleCloseViewModal = () => {
        setViewModalOpen(false);
        setSelectedItem(null);
    };

    const handleCloseDeleteModal = () => {
        setDeleteModalOpen(false);
        setItemToDelete(null);
    };

    const handleDeleteConfirm = async () => {
        if (itemToDelete) {
            try {
                await deleteApi({
                    end_point: `admin/promotional-items/${itemToDelete.id}`,
                    body: {}
                });
                refetch();
                handleCloseDeleteModal();
            } catch (error) {
                console.error('Error deleting promotional item:', error);
            }
        }
    };

    const handleFormSubmit = () => {
        refetch();
    };

    return (
        <div>
            <BasicTablePage
                title="Promotional Items"
                createButton="Add New Promotional Item"
                actions={actions}
                columns={columns}
                data={promotionalItems}
                changePage={changePage}
                currentPage={data?.current_page}
                totalPages={Math.ceil(data?.total / data?.per_page)}
                filter={true}
                setFilter={setApiParam}
                isLoading={isLoading}
                createPage={<PromotionalItemForm onSubmitSuccess={handleFormSubmit} />}
                editPage={<PromotionalItemForm onSubmitSuccess={handleFormSubmit} />}
            />

            <PromotionalItemViewModal
                item={selectedItem}
                isOpen={viewModalOpen}
                onClose={handleCloseViewModal}
            />

            <DeleteConfirmationModal
                isOpen={deleteModalOpen}
                onClose={handleCloseDeleteModal}
                onConfirm={handleDeleteConfirm}
                itemTitle={itemToDelete?.title || ''}
                isLoading={isDeleting}
            />
        </div>
    );
};

export default PromotionalItems;