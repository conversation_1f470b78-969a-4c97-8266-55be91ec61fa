import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { useDeleteApiMutation } from "@/store/api/master/commonSlice";

const BatchDelete = ({ showDeleteModal, setShowDeleteModal, data, }) => {
    console.log(data);
  const [deleteApi, { isLoading }] = useDeleteApiMutation();
console.log(showDeleteModal);
  const handleDelete = async () => {
    try {
      await deleteApi({
        end_point: `/admin/batches/${data?.id}`,
        body: {},
      });
      setShowDeleteModal(false);
    } catch (error) {
      console.error("Delete failed:", error);
    }
  };

  const handleClose = () => {
    setShowDeleteModal(false);
  };

//   if (!data) return null;

  return (
    <Modal
      activeModal={showDeleteModal}
      onClose={handleClose}
      title="Delete Content"
      className="max-w-md"
    >
      <div className="p-6">
        <h3 className="text-xl font-semibold text-center mb-4">
          Are you sure?
        </h3>
        <p className="text-center text-slate-600 mb-6">
          You are about to delete <span className="font-semibold">"{data?.title || data?.name}"</span>. 
          This action cannot be undone.
        </p>

        <div className="flex justify-end gap-4">
          <Button
            onClick={handleClose}
            className="btn-secondary px-6"
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleDelete}
            className="btn-danger px-6"
            isLoading={isLoading}
          >
            Delete
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default BatchDelete;