import React, { useState, useEffect } from 'react';
import { useGetApiQuery } from '@/store/api/master/commonSlice';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import Icon from '@/components/ui/Icon';
import Loading from '@/components/Loading';
import Select from 'react-select';

const StudentSelectionModal = ({ courseId, onClose, onSubmit, selectedStudentIds = [] }) => {
  const [activeTab, setActiveTab] = useState('students'); // 'students' or 'batch'
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStudents, setSelectedStudents] = useState(selectedStudentIds);
  const [selectedBatch, setSelectedBatch] = useState(null);

  // Fetch students for the course
  const {
    data: studentData,
    isLoading: studentsLoading,
  } = useGetApiQuery(courseId ? `admin/student-Participant-list-by-course-id/${courseId}` : null, {
    skip: !courseId
  });

  // Fetch batches for the course
  const {
    data: batchList,
    isLoading: batchesLoading,
  } = useGetApiQuery(courseId ? `admin/batches?pagination=false&is_active=1&mentor=1&course_id=${courseId}` : null, {
    skip: !courseId
  });

  useEffect(() => {
    setSelectedStudents(selectedStudentIds);
  }, [selectedStudentIds]);

  const handleToggleStudent = (studentId) => {
    setSelectedStudents(prev =>
      prev.includes(studentId)
        ? prev.filter(id => id !== studentId)
        : [...prev, studentId]
    );
  };

  const handleSelectAll = () => {
    if (!studentData?.student_list) return;

    if (selectedStudents.length === studentData.student_list.length) {
      // Deselect all
      setSelectedStudents([]);
    } else {
      // Select all
      setSelectedStudents(studentData.student_list.map(student => student.id));
    }
  };

  const handleSubmit = () => {
    if (activeTab === 'students') {
      onSubmit({
        mode: 'students',
        selectedStudentIds: selectedStudents
      });
    } else if (activeTab === 'batch' && selectedBatch) {
      onSubmit({
        mode: 'batch',
        batchId: selectedBatch.value
      });
    }
  };

  const filteredStudents = studentData?.student_list?.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (student.email && student.email.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <Modal
      title="Select Students or Batch for Assignment"
      activeModal={true}
      onClose={onClose}
      className="max-w-4xl"
    >
      <div className="space-y-4">
        {/* Tabs */}
        <div className="flex border-b">
          <button
            className={`px-4 py-2 font-medium ${activeTab === 'students'
              ? 'text-primary-600 border-b-2 border-primary-600'
              : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('students')}
          >
            Select Students
          </button>
          <button
            className={`px-4 py-2 font-medium ${activeTab === 'batch'
              ? 'text-primary-600 border-b-2 border-primary-600'
              : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('batch')}
          >
            Select Batch
          </button>
        </div>

        {/* Students Tab */}
        {activeTab === 'students' && (
          <>
            <div className="flex items-center gap-3 mb-4">
              <div className="flex-grow relative">
                <input
                  type="text"
                  placeholder="Search students..."
                  className="form-input pl-10 w-full"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">
                  <Icon icon="heroicons-outline:search" className="text-lg" />
                </span>
              </div>
              {studentData?.student_list?.length > 0 && (
                <Button
                  text={selectedStudents.length === studentData.student_list.length ? "Deselect All" : "Select All"}
                  btnClass="btn-outline-primary"
                  onClick={handleSelectAll}
                />
              )}
            </div>

            {studentsLoading ? (
              <div className="flex justify-center py-8">
                <Loading />
              </div>
            ) : studentData?.student_list?.length > 0 ? (
              <div className="max-h-96 overflow-y-auto">
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                  {filteredStudents?.map((student) => (
                    <div
                      key={student.id}
                      className={`flex items-center p-3 border rounded-md cursor-pointer transition-all duration-200 ${
                        selectedStudents.includes(student.id) ? 'border-primary-500 bg-primary-50' : 'border-gray-200 hover:bg-gray-50'
                      }`}
                      onClick={() => handleToggleStudent(student.id)}
                    >
                      <input
                        type="checkbox"
                        checked={selectedStudents.includes(student.id)}
                        onChange={() => handleToggleStudent(student.id)}
                        className="form-checkbox h-4 w-4 text-primary-500 mr-3"
                        onClick={(e) => e.stopPropagation()}
                      />
                      <div className="flex items-center space-x-3 flex-1">
                        <div className="rounded-full w-10 h-10 bg-slate-100 flex items-center justify-center">
                          {student.image ? (
                            <img
                              src={import.meta.env.VITE_ASSET_HOST_URL + student.image}
                              className="rounded-full w-full h-full object-cover"
                              alt={student.name}
                            />
                          ) : (
                            <Icon icon="heroicons-outline:user" className="text-xl text-slate-400" />
                          )}
                        </div>
                        <div className="flex flex-col overflow-hidden">
                          <span className="font-medium text-sm text-gray-800 truncate">
                            {student.name}
                          </span>
                          <span className="text-xs text-gray-600 truncate">
                            {student.email || 'No Email'}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-gray-500">
                <Icon icon="heroicons-outline:user-group" className="w-16 h-16 mb-2 text-slate-300" />
                <p className="text-lg font-medium text-slate-600 mb-1">No Students Found</p>
                <p className="text-sm text-slate-500">
                  There are no students enrolled in this course yet.
                </p>
              </div>
            )}
          </>
        )}

        {/* Batch Tab */}
        {activeTab === 'batch' && (
          <div className="py-4">
            <label className="block text-gray-600 text-sm font-medium mb-2">
              Select Batch
            </label>
            <Select
              placeholder="Select a batch"
              options={batchList?.map(batch => ({
                value: batch.id,
                label: batch.name
              }))}
              value={selectedBatch}
              onChange={setSelectedBatch}
              isLoading={batchesLoading}
              className="mb-4"
            />

            {batchesLoading ? (
              <div className="flex justify-center py-8">
                <Loading />
              </div>
            ) : batchList?.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8 text-gray-500">
                <Icon icon="heroicons-outline:collection" className="w-16 h-16 mb-2 text-slate-300" />
                <p className="text-lg font-medium text-slate-600 mb-1">No Batches Found</p>
                <p className="text-sm text-slate-500">
                  There are no batches available for this course.
                </p>
              </div>
            ) : null}
          </div>
        )}

        <div className="flex justify-end space-x-3 pt-4 border-t">
          <Button
            text="Cancel"
            btnClass="btn-outline-danger"
            onClick={onClose}
          />
          <Button
            text={activeTab === 'students' ? "Select Students" : "Select Batch"}
            btnClass="btn-primary"
            onClick={handleSubmit}
            disabled={(activeTab === 'students' && selectedStudents.length === 0) ||
                     (activeTab === 'batch' && !selectedBatch)}
          />
        </div>
      </div>
    </Modal>
  );
};

export default StudentSelectionModal;
