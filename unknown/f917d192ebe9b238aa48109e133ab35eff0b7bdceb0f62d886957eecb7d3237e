import React, { useState, useEffect } from "react";
import { useLocation, NavLink } from "react-router-dom";
import { menuItems } from "@/constant/data";
import Icon from "@/components/ui/Icon";

const Breadcrumbs = () => {
  const location = useLocation();
  const locationName = location.pathname.replace("/", "");

  const [isHide, setIsHide] = useState(null);
  const [menuItem, setMenuItem] = useState(null);
  const [childMenuItem, setChildMenuItem] = useState(null);
  const [groupTitle, setGroupTitle] = useState("");

  useEffect(() => {
    const currentMenuItem = menuItems.find(
      (item) => item.link === locationName
    );
    const currentChild = menuItems.find((item) =>
      item.child?.find((child) => child.childlink === locationName)
    );
    
    setMenuItem(currentMenuItem);
    setChildMenuItem(currentChild);
    if (currentMenuItem) {
      setIsHide(currentMenuItem.isHide);
    } else if (currentChild) {
      setIsHide(currentChild?.isHide || false);
      setGroupTitle(currentChild?.title);
    }
  }, [location, locationName]);

  return (
    <>
      {!isHide ? (
        <div className="md:mb-6 mb-4 flex space-x-3 rtl:space-x-reverse">
          <ul className="breadcrumbs">

            {(groupTitle || menuItem?.title) ? (
              <>
              <li className="text-primary-500">
                  <NavLink to="/dashboard" className="text-lg">
                    <Icon icon="heroicons-outline:home" />
                  </NavLink>
                  <span className="breadcrumbs-icon rtl:transform rtl:rotate-180">
                    <Icon icon="heroicons:chevron-right" />
                  </span>
                </li>
                {groupTitle  && 
              <li className="text-primary-500">
                <button type="button" className="capitalize">
                  {groupTitle}
                </button>
                <span className="breadcrumbs-icon rtl:transform rtl:rotate-180">
                  <Icon icon="heroicons:chevron-right" />
                </span>
              </li>
              }
              <li className="capitalize text-slate-500 dark:text-slate-400">
                {menuItem?.title}
              </li>
              </>
            ): 
            <>
            <li className="text-primary-500">
            <span className="breadcrumbs-icon rtl:transform rtl:rotate-180">
              <Icon icon="heroicons:chevron-left" />
            </span>
              <NavLink to={-1} className="capitalize">
                Go back
              </NavLink>
            </li>
            </>
            }
          </ul>
        </div>
      ) : null}
    </>
  );
};

export default Breadcrumbs;
