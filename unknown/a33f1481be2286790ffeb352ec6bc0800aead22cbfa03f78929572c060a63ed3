import React from 'react';
import Icon from '@/components/ui/Icon';

/**
 * StrictVerticalHandle - A drag handle specifically designed for vertical-only dragging
 */
const StrictVerticalHandle = ({ dragHandleProps, className = '' }) => {
  return (
    <div
      {...dragHandleProps}
      className={`strict-vertical-handle flex items-center justify-center p-3 ${className}`}
      title="Drag vertically to reorder"
    >
      <Icon icon="heroicons-outline:dots-vertical" className="text-xl text-gray-500" />
    </div>
  );
};

export default StrictVerticalHandle;
