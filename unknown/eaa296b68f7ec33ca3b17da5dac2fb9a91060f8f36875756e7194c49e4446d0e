import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
// import { useGetApiQuery } from "@/store/api/master/commonSlice";
import CreateOutline from "./createOutline";
import EditOutline from "./Edit";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";
import { useParams } from "react-router-dom";

const index = ({outlines}) => {
  const [showModal, setShowModal] = useState(false);
  const dispatch = useDispatch();
  const { id } = useParams();
  // const data = useGetApiQuery('admin/course-outline-list/' + id);
  // console.log(res.data, "Outline");
  // const data = res.data;
  console.log(outlines);
  const columns = [
    {
      label: "Title",
      field: "title",
    },
    {
      label: "Title (Bangla)",
      field: "title_bn",
    },
    {
      label: "Class - Subject - Chapter",
      field: "parents",
    },
    {
      label: "Action",
      field: "",
    },
  ];

  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      title: (<div>{item.title} </div>),
      title_bn: (<div>{item.title_bn} </div>),
      parents: item.class_name + " -> " + item.subject_name + " -> " + item.chapter_name,
    };
  });

  const actions = [
    {
      name: "Edit",
      icon: "lucide:edit",
      onClick: (val) => {
        // console.log(data.data[val]);
        dispatch(setEditData(data.data[val]));
        dispatch(setEditShowModal(true));
      },
    },

    {
      name: "Delete",
      icon: "uil:eye",
      onClick: (val) => {
        console.log(val);
      },
    },
  ];
  const changePage = (item) => {
    console.log(item);
  };
  const handleSubmit = () => {
    setShowModal(false);
  };

  const createPage = <CreateOutline />;
  const editPage = <EditOutline />;

  return (
    <div>
      
        <BasicTablePage
          title="Course Outline"
          createButton="Add New Outline"
          createPage={createPage}
          editPage={editPage}
          submitForm={handleSubmit}
          actions={actions}
          columns={columns}
          data={tableData}
          changePage={changePage}
          currentPage={data?.current_page}
          totalPages={Math.ceil(data?.total / data?.per_page)}
        />
    
    </div>
  );
};

export default index;
