import { Icon } from "@iconify/react";
import React from "react";
import ReactPlayer from "react-player";
import { <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";

const textVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: { opacity: 1, y: 0 },
};

// Animation variants for images
const imageVariants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { opacity: 1, scale: 1 },
};

const Demo = () => {
  return (
    <section className="mt-14 min-h-[90vh] max-w-7xl mx-auto flex items-center p-5">
      <div className="flex flex-col lg:flex-row items-center gap-5 justify-center w-full mx-auto">
        <motion.div
          //   className={`w-full md:w-1/2 space-y-2 ${contentOrder}`}
          variants={textVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="flex-1 h-[500px] w-full rounded-xl flex justify-center overflow-hidden"
        >
          <ReactPlayer
            url={"https://www.youtube.com/watch?v=88jH_04zmIw"}
            playing={false}
            muted={true}
            controls={true}
            // loop={true}
            width="100%"
            height="100%"
          />
        </motion.div>
        <div className="flex-1 space-y-5 w-full">
          <motion.div
            //   className={`w-full md:w-1/2 ${imageOrder}`}
            variants={imageVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
            className="bg-white rounded-xl border border-indigo-400 border-dashed hover:border-solid  w-full lg:w-[70%] mx-auto p-4"
          >
            <h2 className="text-xl font-semibold text-center text-indigo-500 font-serif flex items-center gap-2 justify-center">
              <Icon icon="eos-icons:admin-outlined" className="text-2xl" />{" "}
              Admin Panel
            </h2>
            <div className="w-full flex items-center justify-center">
              <div>
                <Link
                  to={"https://demo.edupackbd.com/login"}
                  target="_blank"
                  className="flex items-center justify-center gap-2 my-2 group hover:text-blue-600"
                >
                  <Icon icon="dashicons:admin-site-alt3" />
                  URL:{" "}
                  <span className="group-hover:underline">
                    https://demo.edupackbd.com
                  </span>
                </Link>
                <p className="flex items-center justify-center gap-2 my-2 hover:text-indigo-500">
                  <Icon icon="fluent-mdl2:contact" /> User: <EMAIL>
                </p>
                <p className="flex items-center justify-center gap-2 hover:text-indigo-500">
                  <Icon icon="teenyicons:password-outline" /> Password:
                  <EMAIL>
                </p>
              </div>
            </div>
          </motion.div>

          <motion.div
            //   className={`w-full md:w-1/2 ${imageOrder}`}
            variants={imageVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
            className="bg-white rounded-xl border border-indigo-400 border-dashed hover:border-solid  w-full p-4"
          >
            <h2 className="text-xl font-semibold text-center text-indigo-500 font-serif flex items-center gap-2 justify-center max-sm:border-b max-sm:pb-2 border-gray-300">
              <Icon icon="hugeicons:teacher" className="text-2xl" /> Client Side
            </h2>
            <div className="flex gap-4 flex-col md:flex-row justify-between items-center max-sm:pt-3">
              <div className="w-full text-center">
                <p className="text-lg font-serif font-semibold text-gray-500">
                  Student Panel
                </p>
                <div>
                  <Link
                    to={"/login"}
                    target="_blank"
                    className="flex items-center justify-center gap-2 my-2 group hover:text-blue-600"
                  >
                    <Icon icon="dashicons:admin-site-alt3" />
                    URL:{" "}
                    <span className="group-hover:underline">
                      {" "}
                      <EMAIL>
                    </span>
                  </Link>
                  <p className="flex items-center justify-center gap-2 my-2">
                    <Icon icon="fluent-mdl2:contact" /> User: <EMAIL>
                  </p>
                  <p className="flex items-center justify-center gap-2">
                    <Icon icon="teenyicons:password-outline" /> Password:
                    <EMAIL>
                  </p>
                </div>
              </div>
              <div className="h-40 border border-indigo-200 mt-3 hidden md:block"></div>
              <div className="w-full text-center">
                <p className="text-lg font-serif font-semibold text-gray-500">
                  Teacher Panel
                </p>
                <div>
                  <Link
                    to={"/login"}
                    target="_blank"
                    className="flex items-center justify-center gap-2 my-2 group hover:text-blue-600"
                  >
                    <Icon icon="dashicons:admin-site-alt3" />
                    URL:{" "}
                    <span className="group-hover:underline">
                      {" "}
                      <EMAIL>
                    </span>
                  </Link>
                  <p className="flex items-center justify-center gap-2 my-2">
                    <Icon icon="fluent-mdl2:contact" /> User: <EMAIL>
                  </p>
                  <p className="flex items-center justify-center gap-2">
                    <Icon icon="teenyicons:password-outline" /> Password:
                    <EMAIL>
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Demo;
