/* Vertical-only dragging styles */

/* Basic styles for draggable items */
.vertical-draggable {
  user-select: none;
  touch-action: pan-y; /* Allow only vertical panning on touch devices */
  position: relative;
  cursor: ns-resize;
}

/* Style for the item while being dragged */
.vertical-draggable.dragging {
  z-index: 1000;
  opacity: 0.9;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
}

/* Force horizontal position to stay fixed */
.vertical-draggable-wrapper {
  transform: translateX(0) !important;
  position: relative;
  width: 100%;
}

/* For react-beautiful-dnd */
[data-rbd-draggable-id] {
  transform: translate3d(0, var(--translate-y, 0), 0) !important;
  transition: transform 0.2s;
}

/* For react-draggable */
.react-draggable {
  transform: translate(0, var(--translate-y, 0)) !important;
}

/* Styles for the drag handle */
.vertical-drag-handle {
  cursor: ns-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  color: #6b7280;
}

.vertical-drag-handle:hover {
  color: #3b82f6;
}

/* Styles for when dragging is active */
body.vertical-dragging * {
  cursor: ns-resize !important;
}

/* Fix for Firefox */
@-moz-document url-prefix() {
  .vertical-draggable {
    transform: translate3d(0, 0, 0);
  }
}
