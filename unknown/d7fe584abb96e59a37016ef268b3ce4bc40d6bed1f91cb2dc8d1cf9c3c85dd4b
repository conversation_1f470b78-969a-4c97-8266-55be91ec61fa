import { apiSlice } from "../apiSlice";
export const commonApi = apiSlice.injectEndpoints({
  reducerPath: "commonSliceApi",
  tagTypes: ["Division", "City", "Area", "LIST_API"],
  endpoints: (builder) => ({
    getDivisionList: builder.query({
      query: () => ({
        url: "division-list",
        method: "GET",
      }),
      providesTags: ["Division"],
    }),
    getDistrictList: builder.query({
      query: (params) => ({
        url: "district-list/" + params,
        method: "GET",
      }),
      providesTags: ["Division"],
    }),
    getCityList: builder.query({
      query: (params) => ({
        url: "upazila-list/" + params,
        method: "GET",
      }),
      providesTags: ["Division"],
    }),
    getAreaList: builder.query({
      query: (params) => ({
        url: "area-list/" + params,
        method: "GET",
      }),
      providesTags: ["Division"],
    }),


    getApi: builder.query({
      query: (url) => ({
        url: url,
        method: "GET",
      }),
      providesTags: ["LIST_API"],
    }),

    postApi: builder.mutation({
      query: (data) => {
        return {
          url: data.end_point,
          method: "POST",
          body: data.body,
          showToast: data.notoast ? false : true
        };
      },
      invalidatesTags: ["LIST_API"],
    }),

    updateApi: builder.mutation({
      query: (data) => {
        // data.body._method = "PUT";
        data.body.append("_method", "PUT");
        return {
          url: data.end_point,
          method: "POST",
          body: data.body,
          showToast: data.notoast ? false : true
        };
      },
      invalidatesTags: ["LIST_API"],
    }),

    updateFormDataApi: builder.mutation({
      query: (data) => {
        // data.body._method = "PUT";
        // data.body.append("_method", "PUT");
        return {
          url: data.end_point,
          method: "POST",
          body: data.body,
          showToast: data.notoast ? false : true
        };
      },
      invalidatesTags: ["LIST_API"],
    }),
    deleteApi: builder.mutation({
      query: (data) => {
        data.body._method = "DELETE";
        return {
          url: data.end_point,
          method: "POST",
          body: data.body,
          showToast: data.notoast ? false : true
        };
      },
      invalidatesTags: ["LIST_API"],
    }),
  }),
});

export const { useGetDivisionListQuery,
  useGetDistrictListQuery,
  useGetCityListQuery,
  useGetAreaListQuery,
  
  useGetApiQuery,
  usePostApiMutation,
  useUpdateApiMutation,
  useDeleteApiMutation
 } = commonApi;
