import React, { useState } from 'react';
import { DragDropContext, Droppable } from 'react-beautiful-dnd';
import VerticalDraggable from '../ui/VerticalDraggable';
import VerticalDragHandle from '../ui/VerticalDragHandle';
import Card from '@/components/ui/Card';

const VerticalDragExample = () => {
  // Sample data
  const [items, setItems] = useState([
    { id: '1', content: 'Item 1' },
    { id: '2', content: 'Item 2' },
    { id: '3', content: 'Item 3' },
    { id: '4', content: 'Item 4' },
    { id: '5', content: 'Item 5' },
  ]);

  // Handle drag end event
  const handleDragEnd = (result) => {
    // Drop outside the list or no change
    if (!result.destination || result.source.index === result.destination.index) {
      return;
    }

    // Reorder the items
    const reorderedItems = Array.from(items);
    const [removed] = reorderedItems.splice(result.source.index, 1);
    reorderedItems.splice(result.destination.index, 0, removed);

    setItems(reorderedItems);
  };

  return (
    <Card title="Vertical-Only Drag Example">
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="vertical-list" direction="vertical">
          {(provided, snapshot) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className={`bg-white p-4 rounded-lg ${
                snapshot.isDraggingOver ? 'bg-blue-50' : ''
              }`}
            >
              {items.map((item, index) => (
                <VerticalDraggable
                  key={item.id}
                  id={item.id}
                  index={index}
                >
                  {(provided, snapshot) => (
                    <>
                      <div
                        className={`flex items-center p-3 mb-2 border rounded-lg ${
                          snapshot.isDragging
                            ? 'bg-blue-50 border-blue-300'
                            : 'bg-gray-50 border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <VerticalDragHandle dragHandleProps={provided.dragHandleProps} />
                        <div className="ml-3 flex-1">{item.content}</div>
                      </div>
                    </>
                  )}
                </VerticalDraggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </Card>
  );
};

export default VerticalDragExample;
