import React, {useState} from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetSubjectListQuery } from "@/store/api/master/rowContentSubjectListSlice";
import CreateSubject from "./createSubject"
import EditSubject from "./editSubject";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";

const index = () => {

  const dispatch = useDispatch();

  const [showModal, setShowModal] = useState(false);
  const [apiParam, setApiParam] = useState('');
  
  const res = useGetSubjectListQuery(apiParam);

  const changePage = (val) => {
    setApiParam(val);
  };

  console.log(res.data?.data);
  const data = res.data;
  const columns = [
    {
      label: "SL",
      field: "id",
    },
    {
      label: "Name",
      field: "name",
    },
    {
      label:"Class",
      field:"class_name"
    },
    {
      label: "Price",
      field: "price",
    },
    {
      label: "Free",
      field: "is_free",
    },
    {
      label: "Status",
      field: "status",
    },
    {
      label: "Action",
      field: "",
    },
  ];
  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      name: item.name,
      class_name:item.class_name,
      price: item.price,
      is_free: (
        <Badge
          className={
            item.status == "true"
              ? `bg-danger-500 text-white`
              : `bg-success-500 text-white`
          }
        >
          {item.is_active ? "Yes" : "No"}
        </Badge>
      ),
      status: (
        <Badge
          className={
            item.status == "true"
              ? `bg-danger-500 text-white`
              : `bg-success-500 text-white`
          }
        >
           {item.is_active ? "Active" : "Inactive"}
        </Badge>
      ),
    };
  });

  const actions = [
    {
      name: "edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        console.log(data.data[val]);
        dispatch(setEditData(data.data[val]));
        dispatch(setEditShowModal(true));
      }
    }
  ];
  // const changePage = (item) => {
  //   console.log(item);
  // };
  const handleSubmit = () => {
    setShowModal(false);
  };

  const createPage = <CreateSubject />
  const editPage = <EditSubject />

  return (
    <div>
        <BasicTablePage
          title="Subject List"
          createButton="Add New Subject"
          createPage={createPage}
          editPage={editPage}
          actions={actions}
          columns={columns}
          data={tableData}
          changePage={changePage}
          submitForm={handleSubmit}
          currentPage={data?.current_page}
          totalPages={Math.ceil(data?.total / data?.per_page)}
        />
    </div>
  );
};

export default index;