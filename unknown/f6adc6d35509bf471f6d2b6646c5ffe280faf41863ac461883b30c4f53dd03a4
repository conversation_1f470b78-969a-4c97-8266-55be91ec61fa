import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetVideoContentListQuery } from "@/store/api/master/rowContentVideoContentlistSlice";
import { useGetClassListQuery } from "@/store/api/master/rowContentClassListSlice";
import { useGetSubjectChapterListQuery } from "@/store/api/master/rowContentChapterListSlice";
import { useGetChapterListBySubjectQuery } from "@/store/api/master/rowContentChapterListSlice";
import Select from "@/components/ui/Select";
import {
  useGetCourseListFilterQuery,
  useGetMentorListFilterQuery,
} from "@/store/api/master/CompletedClassSlice";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";

const Filter = ({ setApiParam }) => {
  const [classId, setClassId] = useState(null);
  const [subjectId, setSubjectId] = useState(null);
  const classList = useGetClassListQuery("?pagination=false").data;
  console.log("class", classList);
  const subjectList = useGetSubjectChapterListQuery(classId).data;
  const chapterList = useGetChapterListBySubjectQuery(subjectId).data;
  console.log(classList);

  const [courseId, setCourseId] = useState(null);
  const [mentorId, setMentorId] = useState(null);
  const courseList = useGetCourseListFilterQuery().data;
  // console.log(courseList);
  const mentorList = useGetMentorListFilterQuery().data;
  console.log("mentor", mentorList);

  return (
    <div className="flex gap-2">
      <Select
        className="w-52"
        defaultValue=""
        placeholder="Select Course"
        options={courseList?.map((item) => {
          return { label: item.title, value: item.id };
        })}
        name="course_Id"
        onChange={(e) => {
          setCourseId(e.target.value);
          setApiParam("?course_id=" + e.target.value);
        }}
      />

      <Select
        className="w-40"
        defaultValue=""
        placeholder="Select Mentor"
        options={mentorList?.map((item) => {
          return { label: item.name, value: item.id };
        })}
        name="mentor"
        onChange={(e) => {
          setMentorId(e.target.value);
          setApiParam(`?class_id=${courseId}&subject_id=${e.target.value}`);
        }}
      />

      <Select
        className="w-40"
        defaultValue=""
        placeholder="Select Student"
        options={chapterList?.map((item) => {
          return { label: item.name, value: item.id };
        })}
        name="chapter"
        onChange={(e) => {
          setApiParam(
            `?class_id=${classId}&subject_id=${subjectId}&chapter_id=${e.target.value}`
          );
        }}
      />

      <DatePicker
        // label="Date Of Birth"
        placeholder="YYYY-MM-DD"
        format="YYYY/MM/DD"
        name="date_of_birth"
        // error={errors?.date_of_birth}
        onChange={(e) => {
          setFieldValue("date_of_birth", e);
        }}
      />
      <DatePicker
        // label="Date Of Birth"
        placeholder="YYYY-MM-DD"
        format="YYYY/MM/DD"
        name="date_of_birth"
        // error={errors?.date_of_birth}
        onChange={(e) => {
          setFieldValue("date_of_birth", e);
        }}
      />

      {/* <Select
        className="w-52"
        defaultValue=""
        placeholder="Select Class"
        options={classList?.map((item) => {
          return { label: item.name, value: item.id };
        })}
        name="class_level"
        onChange={(e) => {
          setClassId(e.target.value);
          setApiParam("?class_id=" + e.target.value);
        }}
      /> */}
      {/* <Select
        className="w-40"
        defaultValue=""
        placeholder="Select Subject"
        options={subjectList?.map((item) => {
          return { label: item.name, value: item.id };
        })}
        name="subject"
        onChange={(e) => {
          setSubjectId(e.target.value);
          setApiParam(`?class_id=${classId}&subject_id=${e.target.value}`);
        }}
      /> */}
      {/* <Select
        className="w-40"
        defaultValue=""
        placeholder="Select Chapter"
        options={chapterList?.map((item) => {
          return { label: item.name, value: item.id };
        })}
        name="chapter"
        onChange={(e) => {
          setApiParam(
            `?class_id=${classId}&subject_id=${subjectId}&chapter_id=${e.target.value}`
          );
        }}
      /> */}
    </div>
  );
};

const index = () => {
  const [apiParam, setApiParam] = useState("");

  const res = useGetVideoContentListQuery(apiParam);

  const changePage = (val) => {
    setApiParam(val);
  };

  console.log(res.data?.data);
  const data = res.dat;
  const columns = [
    {
      label: "SL",
      field: "id",
    },
    {
      label: "Course  - Teacher - Student",
      field: "title",
    },
    {
      label: "Class-Subject-Chapter",
      field: "class_name",
    },
    {
      label: "Date",
      field: "price",
    },
    {
      label: "Start",
      field: "is_free",
    },
    {
      label: "End",
      field: "is_active",
    },
    {
      label: "Total (Min)",
      field: "",
    },
  ];
  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      title: item.title,
      class_name: item.class_name,
      price: item.price,
      is_free: (
        <Badge
          className={
            item.is_free == "true"
              ? `bg-danger-500 text-white`
              : `bg-success-500 text-white`
          }
        >
          {item.is_free ? "Yes" : "No"}
        </Badge>
      ),
      is_active: (
        <Badge
          className={
            item.is_active == "true"
              ? `bg-danger-500 text-white`
              : `bg-success-500 text-white`
          }
        >
          {item.is_active ? "Yes" : "No"}
        </Badge>
      ),
    };
  });

  const actions = [
    {
      name: "view",
      icon: "heroicons-outline:eye",
      onClick: (val) => {
        console.log(val);
      },
    },
    {
      name: "edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        console.log(val);
      },
    },
    {
      name: "delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        console.log(val);
      },
    },
  ];
  //   const changePage = (item) => {
  //     console.log(item);
  //   };
  const filter = <Filter setApiParam={setApiParam} />;
  return (
    <div>
      {/* {tableData?.length > 0 && ( */}
      <BasicTablePage
        title="Completed Class List"
        // createButton="Enrollment Quiz"
        actions={actions}
        columns={columns}
        data={tableData}
        filter={filter}
        changePage={changePage}
        currentPage={data?.current_page}
        totalPages={Math.ceil(data?.total / data?.per_page)}
      />
      {/* )} */}
    </div>
  );
};

export default index;
