import React from 'react';
import Icon from '@/components/ui/Icon';

/**
 * SmoothDragHandle - A drag handle for smooth vertical dragging
 */
const SmoothDragHandle = ({ dragHandleProps }) => {
  return (
    <div
      {...dragHandleProps}
      className="smooth-drag-handle"
      title="Drag to reorder"
    >
      <Icon icon="heroicons-outline:dots-vertical" className="text-xl text-gray-500" />
    </div>
  );
};

export default SmoothDragHandle;
