import React, { useRef, useEffect } from 'react';
import { Draggable } from 'react-beautiful-dnd';
import '../../assets/css/smooth-vertical-drag.css';

/**
 * SmoothVerticalDraggable - A component that provides smooth vertical-only dragging
 */
const SmoothVerticalDraggable = ({ id, index, children, className = '' }) => {
  const nodeRef = useRef(null);

  // Function to extract vertical transform value
  const extractVerticalTransform = (style) => {
    if (!style || !style.transform) return style;

    // Extract the y-transform value
    const match = style.transform.match(/translate(?:3d)?\([^,]+,\s*([^,)]+)/);
    if (match && match[1]) {
      // Set CSS variable for vertical position
      if (nodeRef.current) {
        nodeRef.current.style.setProperty('--translate-y', match[1]);
      }
    }

    return style;
  };

  // Add/remove body class for dragging state
  useEffect(() => {
    const handleDragStart = () => {
      document.body.classList.add('smooth-dragging');
    };

    const handleDragEnd = () => {
      document.body.classList.remove('smooth-dragging');
    };

    document.addEventListener('dragstart', handleDragStart);
    document.addEventListener('dragend', handleDragEnd);

    return () => {
      document.removeEventListener('dragstart', handleDragStart);
      document.removeEventListener('dragend', handleDragEnd);
      document.body.classList.remove('smooth-dragging');
    };
  }, []);

  return (
    <Draggable
      draggableId={id.toString()}
      index={index}
      axis="y" // Restrict to vertical movement only
    >
      {(provided, snapshot) => {
        // Process the style to extract vertical transform
        const style = extractVerticalTransform(provided.draggableProps.style);

        return (
          <div
            ref={(el) => {
              nodeRef.current = el;
              provided.innerRef(el);
            }}
            {...provided.draggableProps}
            className={`smooth-draggable ${className} ${snapshot.isDragging ? 'dragging' : ''}`}
            data-rbd-dragging={snapshot.isDragging}
            style={{
              ...style,
              // Ensure smooth transitions when not dragging
              transition: snapshot.isDragging
                ? 'none'
                : 'transform 0.2s cubic-bezier(0.2, 0, 0, 1)',
              // Fix positioning issues
              left: 0,
              right: 0,
              width: '100%',
              // Ensure proper z-index during drag
              zIndex: snapshot.isDragging ? 9999 : 'auto'
            }}
        >
          <div className="smooth-draggable-inner">
            {typeof children === 'function'
              ? children(provided, snapshot)
              : children}
          </div>
        </div>
        );
      }}
    </Draggable>
  );
};

export default SmoothVerticalDraggable;
