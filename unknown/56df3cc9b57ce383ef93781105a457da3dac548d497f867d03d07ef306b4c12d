import React from 'react';
import { Users, Calendar, BookOpen, Star, Users2, HelpCircle, DollarSign, Wallet, Tag, Percent } from 'lucide-react';

const StatCard = ({ bgColor, icon: Icon, title, value }) => (
  <div className={`${bgColor} rounded-xl px-4 py-3 flex items-center space-x-3 transition-all duration-300 hover:shadow-lg`}>
    <div className={`${bgColor.replace('50', '100')} p-2 rounded-lg`}>
      <Icon className={`w-5 h-5 ${bgColor.replace('50', '600').replace('bg', 'text')}`} />
    </div>
    <div>
      <p className="text-sm text-gray-600">{title}</p>
      <p className="text-lg font-semibold text-gray-900">{value}</p>
    </div>
  </div>
);

const CourseStats = ({ course }) => {
  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getDuration = () => {
    if (course?.appeared_from && course?.appeared_to) {
      return <span className="text-sm text-gray-600">{formatDate(course.appeared_from)} - {formatDate(course.appeared_to)} </span>;
    }
    return 'No dates available';
  };

  return (
    <div className="space-y-8">
      {/* Enrollment Stats */}

      {/* Course Meta Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
         <StatCard
          bgColor="bg-amber-50"
          icon={Star}
          title="Category"
          value={course?.category?.name}
        />
        <StatCard
          bgColor="bg-rose-50"
          icon={Star}
          title="Sub Category"
          value={course?.sub_category?.name}
        />
        <StatCard
          bgColor="bg-green-50"
          icon={Calendar}
          title="Duration"
          value={getDuration()}
        />
      </div>


      {/* Price Stats */}
      {course.is_free ? (
        <div className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl p-4 flex items-center justify-center mb-8">
          <span className="text-lg font-semibold text-emerald-600 px-4 py-2 bg-emerald-100 rounded-full">
            Free Course
          </span>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">

            <StatCard
              bgColor="bg-emerald-50"
              icon={Wallet}
              title="Payment Type"
              value={course.installment_type}
            /> 
          {course.regular_price > 0 && 
            <StatCard
              bgColor="bg-emerald-50"
              icon={DollarSign}
              title="Regular Price"
              value={`${course.regular_price} ${course.currency}`}
            /> 
          }
           {course.sale_price > 0 && 
            <StatCard
              bgColor="bg-violet-50"
              icon={Tag}
              title="Sale Price"
              value={`${course.sale_price} ${course.currency}`}
            />
           }
           {course.discount_percentage > 0 && 
            <StatCard
              bgColor="bg-pink-50"
              icon={Percent}
              title="Discount"
              value={`${parseFloat(course.discount_percentage).toFixed(2)}%`}
            />
           }

          {course.installment_type == 'Monthly' && 
            <StatCard
              bgColor="bg-pink-50"
              icon={Tag}
              title="Per Month"
              value={`${course.monthly_amount}  ${course.currency}`}
            />
           }

          {course.installment_type != 'One Time' && 
            <StatCard
              bgColor="bg-pink-50"
              icon={Tag}
              title="Admission Fee"
              value={` ${course.minimum_enroll_amount}  ${course.currency}`}
            />
           }


          {course.installment_type === "Installment"  && 
            <StatCard
              bgColor="bg-pink-50"
              icon={Tag}
              title="Admission Fee"
              value={` ${course.max_installment_qty}  ${course.currency}`}
            />
           }
        <StatCard
          bgColor="bg-blue-50"
          icon={Users}
          title="Enrolled Students"
          value={course.number_of_enrolled}
        />
        </div>
      )}

    </div>
  );
};

export default CourseStats;