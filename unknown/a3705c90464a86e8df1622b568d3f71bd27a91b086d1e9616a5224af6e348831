import React from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Button from "@/components/ui/Button";
import InputSelect from "@/components/ui/InputSelect";
import { Formik, Form } from "formik";
import { initialValues, validationSchema } from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import Textarea from "@/components/ui/Textarea";
import Fileinput from "@/components/ui/Fileinput";

const CreateTestimonial = () => {
  const [postApi, { isLoading }] = usePostApiMutation();
  const dispatch = useDispatch();
  const { showModal } = useSelector((state) => state.commonReducer);

  const onSubmit = async (values) => {
    let formData = new FormData();
    formData.append("name", values.name);
    formData.append("designation", values.designation);
    formData.append("user_type", values.user_type);
    formData.append("message", values.message);
    formData.append("is_active", 1);

    if (values.image) {
      formData.append("image", values.image);
    }

    await postApi({
      end_point: "admin/testimonials",
      body: formData,
    });

    dispatch(setShowModal(false));
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={() => dispatch(setShowModal(false))}
      title="Add New Testimonial"
      className="max-w-3xl"
      footerContent={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setShowModal(false))}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={initialValues}
        onSubmit={onSubmit}
      >
        {({ setFieldValue }) => (
          <Form>
            <div className="grid md:grid-cols-2 gap-4">
              <InputField
                label="Name"
                name="name"
                type="text"
                placeholder="Enter Name"
                required
              />
              <InputField
                label="Designation"
                name="designation"
                type="text"
                placeholder="Enter Designation"
                required
              />
            </div>
            <div className="grid md:grid-cols-2 gap-4 my-4">
              <InputSelect
                label="User Type"
                name="user_type"
                options={[
                  { label: "Teacher", value: "Teacher" },
                  { label: "Student", value: "Student" },
                  { label: "Guardian", value: "Guardian" },
                ]}
                required
              />
              <Fileinput
                name="image"
                label="Browse"
                labelText="Select Image"
                onChange={(e) => {
                  if (e.target.files.length > 0) {
                    setFieldValue("image", e.target.files[0]);
                  }
                }}
                placeholder="Choose an image file"
                accept="image/*"
              />
            </div>
            <div className="grid md:grid-cols-1 gap-4 my-4">
              <Textarea
                label="Message"
                name="message"
                placeholder="Enter Testimonial Message"
                row={4}
                onChange={(e) => setFieldValue("message", e.target.value)}
              />
            </div>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default CreateTestimonial;
