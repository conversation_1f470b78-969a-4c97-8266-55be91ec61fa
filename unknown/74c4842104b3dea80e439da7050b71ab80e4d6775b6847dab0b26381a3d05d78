import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Select from "@/components/ui/Select";
import { Link, useNavigate } from "react-router-dom";

const Filter = ({ setApiParam }) => {
  const classList = useGetApiQuery().data;

  return (
    <div className="flex gap-2">
      <Select
        className="w-52"
        defaultValue=""
        placeholder="Select Quiz"
        options={classList?.map((item) => {
          return { label: item.name, value: item.id };
        })}
        name="class_level"
        onChange={(e) => {
          setClassId(e.target.value);
          setApiParam("?class_id=" + e.target.value);
        }}
      />

      <Select
        className="w-40"
        defaultValue=""
        placeholder="Select Subject"
        options={subjectList?.map((item) => {
          return { label: item.name, value: item.id };
        })}
        name="subject"
        onChange={(e) => {
          setSubjectId(e.target.value);
          setApiParam(`?class_id=${classId}&subject_id=${e.target.value}`);
        }}
      />
      <Select
        className="w-40"
        defaultValue=""
        placeholder="Select Chapter"
        options={chapterList?.map((item) => {
          return { label: item.name, value: item.id };
        })}
        name="chapter"
        onChange={(e) => {
          setApiParam(
            `?class_id=${classId}&subject_id=${subjectId}&chapter_id=${e.target.value}`
          );
        }}
      />
    </div>
  );
};

const index = () => {
  const [apiParam, setApiParam] = useState("");
  const navigate = useNavigate();

  // const res = useGetVideoContentListQuery(apiParam);
  const participationList = useGetApiQuery("admin/quiz-participated-list").data;

  const changePage = (val) => {
    setApiParam(val);
  };

  // console.log(participationList);
  // const data = res.dat;
  const columns = [
    {
      label: "SL",
      field: "id",
    },
    {
      label: "Exam Name",
      field: "title",
    },
    {
      label: "Participate Student",
      field: "student_name",
    },
    {
      label: "Date Of Participate",
      field: "created_at",
    },
    {
      label: "Total Mark",
      field: "exam_mark",
    },
    {
      label: "Score",
      field: "mark",
    },
    {
      label: "Action",
      field: "",
    },
  ];
  const tableData = participationList?.data.map((item, index) => {
    return {
      id: item.id,
      title: (
        <Link
          state={{ exam: item }}
          className="hover:text-blue-600 hover:underline"
          to={`/exam-result-details/${item?.id}`}
        >
          {item.title}
        </Link>
      ),
      student_name: item.student_name,
      exam_mark: item.exam_mark,
      mark: item.mark,
      created_at: new Intl.DateTimeFormat("en-US", {
        day: "numeric",
        month: "short",
        year: "numeric",
      }).format(new Date(item.created_at)),

    };
  });

  const actions = [
    {
      name: "view",
      icon: "heroicons-outline:eye",
      onClick: (val) => {
        const selectedItem = participationList?.data[val];
        navigate(`/exam-result-details/${selectedItem?.id}`, {
          state: { exam: selectedItem },
        });
        console.log(selectedItem);
      },
    },

  ];
 
  const filter = <Filter setApiParam={setApiParam} />;
  return (
    <div>
      <BasicTablePage
        title="Online Exam Results"
        actions={actions}
        columns={columns}
        data={tableData}
        filter={filter}
        changePage={changePage}
        currentPage={participationList?.current_page}
        totalPages={Math.ceil(
          participationList?.total / participationList?.per_page
        )}
      />
    </div>
  );
};

export default index;
