import React, { useState } from "react";
import Icon from "@/components/ui/Icon";
import Button from "@/components/ui/Button";
import { useNavigate } from "react-router-dom";
import Dropdown from "@/components/ui/Dropdown";
import { Menu } from "@headlessui/react";
import Delete from "./Delete";
import Duplicate from "./Duplicate";
const CourseCard = ({ course }) => {
  const navigate = useNavigate();

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDuplicateModal, setShowDuplicateModal] = useState(false);
  const handleCourseDetails = () => {
    if (course.is_draft) {
      navigate(`/course-create/${course.id}`, {
        state: { course },
      });
    } else {
      navigate(`/course-details/${course.id}`, {
        state: { course },
      });
    }
  };

  const showBadge = () =>
    course.discount_percentage > 0 ? (
      <span className="absolute top-2 right-2 px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full dark:bg-red-900 dark:text-red-300 flex items-center">
        {Number.isInteger(course.discount_percentage) ? course.discount_percentage : parseFloat(course.discount_percentage).toFixed(2)}% Off
      </span>
    ) : // ) : course.is_free ? (
      //   <span className="absolute top-2 right-2 px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full dark:bg-green-900 dark:text-green-300">
      //     Free
      //   </span>
      null;

  const showPrice = () =>
    !course.is_free ? (
      <p className="text-base font-bold rounded-lg flex justify-between items-center gap-2">
        <Icon size={30} icon="tabler:coin-taka-filled" className="" />
        {course.monthly_amount > 1 && (
          <span>
            {course.monthly_amount}/Month
          </span>
        )}
        {course.sale_price < course.regular_price &&
          <span
            className={`text-base font-bold ${course.sale_price < course.regular_price ? "line-through" : ""
              }`}
          >
            {course.regular_price}
          </span>
        }
        {/* Conditionally render sale_price if it's greater than 0 */}
        {course.sale_price > 0 && (
          <span className="text-base font-bold">{course.sale_price} </span>
        )}
      </p>
    ) : (
      <span className="text-base font-bold rounded-lg justify-between items-center flex gap-2">
        <Icon
          size={30}
          icon="icon-park-outline:check-one"
          className="text-green-500"
        />
        Free
      </span>
    );

  return (
    <div
      className=" bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm overflow-hidden cursor-pointer"

    >
      <div className="relative">
        <img
          src={`${import.meta.env.VITE_ASSET_HOST_URL}${course.thumbnail}`}
          alt={course.title}
          className="w-full h-36 object-cover"
          onClick={handleCourseDetails}
        />
        {showBadge()}
        {
          <span
            className={`absolute top-2 left-2 px-2 py-1 text-xs font-medium text-white rounded-full ${course.is_active ? "bg-green-500" : "bg-red-500"
              }`}
          >
            {course.is_active ? "Active" : "Inactive"}
          </span>
        }

        <span className="absolute top-2 right-1 px-1 py-1 text-md font-large text-white">

          <Dropdown
            classMenuItems="right-0 top-[110%] flex"
            label={
              <span className="text-xl text-center block px-2 text-white">
                <Icon icon="heroicons-outline:dots-vertical" />
              </span>
            }
          >
            <div className="divide-y divide-slate-100 dark:divide-slate-800">

              <Menu.Item 
                onClick={() => {
                  setShowDeleteModal(true);
                }}
              >
                <div
                  className={`
                          border-b border-b-gray-500 border-opacity-10 px-4 py-2 text-sm cursor-pointer 
                          first:rounded-t last:rounded-b flex gap-2 items-center rtl:space-x-reverse whitespace-nowrap hover:bg-gray-50`}

                >
                  <span className="text-red-500 text-sm">
                    <Icon icon="heroicons-outline:trash" />
                  </span>
                  <span className="text-red-500 text-sm">Delete </span>
                </div>
              </Menu.Item>

              <Menu.Item
                onClick={() => {
                  setShowDuplicateModal(true);
                }} >
                <div
                  className={`
                          border-b border-b-gray-500 border-opacity-10 px-4 py-2 text-sm cursor-pointer 
                          first:rounded-t last:rounded-b flex gap-2 items-center rtl:space-x-reverse whitespace-nowrap hover:bg-gray-50`}

                >
                  <span className="text-blue-500 text-sm">
                    <Icon icon="heroicons-outline:clipboard-copy" />
                  </span>
                  <span className="text-blue-500 text-sm">Duplicate </span>
                </div>
              </Menu.Item>
            </div>
          </Dropdown>
        </span>

      </div>
      <div className="p-1 flex justify-between items-center">
        <h6
          className="text-base font-semibold text-gray-900 dark:text-white overflow-hidden text-ellipsis whitespace-nowrap"
          style={{ width: "ch" }} // `ch` is the average width of a single character
        >
          {course.title}
        </h6>
        <div className="flex items-center gap-4 mt-2">
          {course.is_draft ? (
            <span className="text-sm font-bold flex items-center gap-2 text-red-500">
              <Icon size={24} icon="icon-park-outline:check-one" />
              Draft
            </span>
          ) : (
            showBadge()
          )}
        </div>
      </div>
      <div className="p-1 flex justify-between">{showPrice()}</div>

      {showDeleteModal && (
        <Delete showDeleteModal={showDeleteModal} setShowDeleteModal={setShowDeleteModal} data={course}/>
      )}
      
      {showDuplicateModal && (
        <Duplicate showDuplicateModal={showDuplicateModal} setShowDuplicateModal={setShowDuplicateModal} data={course}/>
      )}
    </div>
  );
};

export default CourseCard;
