import React, { forwardRef } from "react";
import { useField } from "formik";

const InputField = forwardRef(({ 
   classLabel = "form-label", horizontal,
   label, required, type, error='', ...props }, ref) => {
   const [field, meta] = useField(props);
   const isError = meta.touched && meta.error;

   return (
      <div>
      {/* Label styling */}
      {label && (
            <label
               htmlFor={props.id || props.name}
               className={`block capitalize ${classLabel}  ${
                  horizontal ? "flex-0 mr-6 md:w-[100px] w-[60px] break-words" : ""
                }`}
            >
               {label} {required && <span className="text-red-500">*</span>}
            </label>
         )}
         <input
            {...field}
            {...props}
            type={type === "number" ? "text" : type}
            className={`appearance-none border rounded h-10 w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:-outline ${
               isError ? "border-red-500" : ""
            }`}
            ref={ref}
            onKeyDown={type === "number" ? (e) => {
               if (e.key === "ArrowUp" || e.key === "ArrowDown") {
                  e.preventDefault();
               }
            } : undefined}
         />
         {error && <span className="text-red-500 text-xs">{error}</span>}
         {isError && <span className="text-red-500 text-xs">{meta.error}</span>}
      </div>
   );
});

export default InputField;
