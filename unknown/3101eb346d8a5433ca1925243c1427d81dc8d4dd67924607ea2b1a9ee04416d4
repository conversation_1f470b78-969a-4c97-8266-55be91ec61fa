import React from "react";
import * as yup from "yup";

export const initialValues = {
  title: "",
  title_bn: "",
  gp_product_id: "",
  youtube_url: "",
  description: "",
  thumbnail: "",
  icon: "",
  number_of_enrolled: "",
  regular_price: "",
  sale_price: "",
  discount_percentage: "",
  rating: "",
  sequence: "",
  appeared_from: "",
  appeared_to: "",
  category_id: "",
  is_active: "",
  is_free: "",
};

export const validationSchema = yup.object({
  title: yup
    .string()
    .max(50, "Should not be more than 50 characters")
    .min(3, "Should not be less than 3 characters")
    .required("Title is Required"),
});
