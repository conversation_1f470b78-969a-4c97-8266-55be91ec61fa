// src/components/Batch/EditBatch.jsx

import React, { useState, useEffect } from "react";
import { Formik, Form } from "formik";
import { useUpdateApiMutation, useGetApiQuery } from "@/store/api/master/commonSlice";
import { useParams } from "react-router-dom";
import Input<PERSON>ield from "@/components/ui/InputField";
import Textarea from "@/components/ui/Textarea";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import Fileinput from "@/components/ui/Fileinput";
import * as yup from "yup";

const EditBatch = ({ onClose, Mentors, Student, selectedBatch }) => {
  const { id } = useParams();
  const [updateApi] = useUpdateApiMutation();
  const Students = Student?.data?.student_list;

  // Extract batch_id from selectedBatch prop
  const batch_id = selectedBatch?.id;

  // Fetch batch details using batch_id
  const { data: batchEdit, isLoading, error } = useGetApiQuery(
    `/admin/batches/${batch_id}`,
    {
      skip: !batch_id, // Skip the query if batch_id is not available
    }
  );

  // Define the API endpoint for updating the batch
  const updateUrl = batchEdit ? `admin/batches/${batchEdit.id}` : null;

  // Validation schema for the form
  const validationSchema = yup.object({
    name: yup.string().required("Batch name is required"),
    capacity: yup
      .number()
      .required("Capacity is required")
      .min(1, "Capacity must be at least 1")
      .typeError("Capacity must be a number"),
  });

  // Function to handle batch update
  const updateBatch = async (formData) => {
    try {
      await updateApi({ end_point: updateUrl, body: formData });
    } catch (error) {
      console.error("Error updating batch:", error);
    }
  };

  // Function to handle form submission
  const onSubmit = async (values, { resetForm }) => {
    const mentorIds =
      values.mentor_ids && values.mentor_ids.length > 0
        ? values.mentor_ids
        : [];
    const studentIds =
      Students && Students.length > 0
        ? Students.map((student) => student.id)
        : [];

    const formData = new FormData();
    formData.append("id", selectedBatch.id);
    formData.append("course_id", selectedBatch.course_id);
    formData.append("name", values.name);
    formData.append("description", values.description || "");
    formData.append("capacity", values.capacity);
    if (values.image && typeof values.image !== "string") {
      formData.append("image", values.image);
    }
    formData.append("mentor_ids", JSON.stringify(mentorIds));
    formData.append("student_ids", JSON.stringify(studentIds));

    await updateBatch(formData);
    resetForm();
    if (onClose) onClose();
  };

  // Define initial values based on fetched batchEdit data
  const getInitialValues = () => {
    if (!batchEdit) {
      return {
        course_id: id || "",
        name: "",
        description: "",
        capacity: "",
        image: "",
        student_ids: [],
        mentor_ids: [],
      };
    }

    return {
      course_id: batchEdit.course_id || "",
      name: batchEdit.name || "",
      description: batchEdit.description || "",
      capacity: batchEdit.capacity || "",
      image: batchEdit.image || "",
      student_ids: batchEdit.students
        ? batchEdit.students.map((student) => student.id)
        : [],
      mentor_ids: batchEdit.mentors
        ? batchEdit.mentors.map((mentor) => mentor.mentor_id)
        : [],
    };
  };

  // If the data is still loading, show a loading indicator
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-gray-600">Loading...</div>
      </div>
    );
  }

  // If there's an error fetching batch details, display an error message
  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-red-500">Error loading batch details.</div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-2xl mb-4">Edit Batch</h2>
      <Formik
        initialValues={getInitialValues()}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
        enableReinitialize
      >
        {({ isSubmitting, setFieldValue, values }) => {
          // State to control modal visibility
          const [isMentorModalOpen, setMentorModalOpen] = useState(false);
          // State to manage temporary selected mentors in the modal
          const [tempSelectedMentors, setTempSelectedMentors] = useState(
            values.mentor_ids || []
          );

          useEffect(() => {
            setTempSelectedMentors(values.mentor_ids || []);
          }, [values.mentor_ids]);

          // Handle checkbox change for individual mentors
          const handleMentorCheckboxChange = (mentorId) => {
            if (tempSelectedMentors.includes(mentorId)) {
              setTempSelectedMentors(
                tempSelectedMentors.filter((id) => id !== mentorId)
              );
            } else {
              setTempSelectedMentors([...tempSelectedMentors, mentorId]);
            }
          };

          // Handle "Select All" checkbox
          const handleSelectAll = () => {
            if (tempSelectedMentors.length === Mentors.length) {
              setTempSelectedMentors([]);
            } else {
              const allMentorIds = Mentors.map((mentor) => mentor.mentor_id);
              setTempSelectedMentors(allMentorIds);
            }
          };

          // Handle modal submission
          const handleModalSubmit = () => {
            setFieldValue("mentor_ids", tempSelectedMentors);
            setMentorModalOpen(false);
          };

          return (
            <>
              <Form>
                <div>
                  <InputField
                    label="Name"
                    name="name"
                    type="text"
                    placeholder="Enter Batch Name"
                    required
                  />
                </div>
                <div className="mt-2">
                  <Textarea
                    label="Description"
                    placeholder="Enter Description"
                    name="description"
                    value={values.description} // Bind value here
                    onChange={(e) =>
                      setFieldValue("description", e.target.value)
                    }
                  />
                </div>
                <div className="grid grid-cols-2 gap-4 items-center">
                  <div>
                    <NumberInput
                      label="Batch Capacity"
                      type="number"
                      name="capacity"
                      id="capacity"
                      placeholder="Batch capacity"
                      required
                    />
                  </div>
                  <div className="mb-4">
                    <label className="block capitalize mt-4 pb-1">
                      Batch Image
                    </label>
                    <Fileinput
                      label="Choose Image"
                      className="py-2"
                      name="image"
                      accept="image/*"
                      type="file"
                      placeholder="Batch Image"
                      preview={true}
                      selectedFile={values.image}
                      onChange={(e) => {
                        setFieldValue("image", e.target.files[0]);
                      }}
                    />
                  </div>
                </div>

                {/* Mentor Section */}
                

                {/* Display Assigned Mentors */}
                <div className="mt-2">
                  <h6 className="text-base">
                    Assigned Teachers: {values?.mentor_ids.length}
                  </h6>
                  {/* {values.mentor_ids.length > 0 && (
                    <ul className="list-disc list-inside">
                      {Mentors
                        .filter((mentor) =>
                          values.mentor_ids.includes(mentor.mentor_id)
                        )
                        .map((mentor) => (
                          <li key={mentor.mentor_id}>{mentor?.mentor?.name}</li>
                        ))}
                    </ul>
                  )} */}
                </div>

                <div className="flex space-x-4 items-center mt-4">
                  <h6 className="text-base">
                    Assigned Students: {Students?.length}
                  </h6>
                </div>
                <div className="flex justify-end space-x-2 mt-6">
                  {onClose && (
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200"
                    >
                      Cancel
                    </button>
                  )}
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-lg hover:bg-blue-700 transition-colors duration-200"
                  >
                    {isSubmitting ? "Submitting..." : "Submit"}
                  </button>
                </div>
              </Form>

              {/* Mentor Selection Modal */}
              {isMentorModalOpen && (
                <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
                  <div className="bg-white p-6 rounded-lg w-11/12 md:w-1/2 lg:w-1/3">
                    <h3 className="text-xl mb-4">Select Teachers</h3>
                    <div className="mb-4">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={
                            tempSelectedMentors.length === Mentors.length &&
                            Mentors.length > 0
                          }
                          onChange={handleSelectAll}
                          className="form-checkbox h-4 w-4 text-blue-600"
                        />
                        <span className="ml-2">Select All</span>
                      </label>
                    </div>
                    <div className="max-h-60 overflow-y-auto mb-4">
                      {Mentors.map((mentor) => (
                        <div
                          key={mentor.mentor_id}
                          className="flex items-center mb-2"
                        >
                          <label className="flex items-center w-full">
                            <input
                              type="checkbox"
                              checked={tempSelectedMentors.includes(
                                mentor.mentor_id
                              )}
                              onChange={() =>
                                handleMentorCheckboxChange(mentor.mentor_id)
                              }
                              className="form-checkbox h-4 w-4 text-blue-600"
                            />
                            <span className="ml-2">{mentor?.mentor?.name}</span>
                          </label>
                        </div>
                      ))}
                    </div>
                    <div className="flex justify-end space-x-2">
                      <button
                        type="button"
                        onClick={() => setMentorModalOpen(false)}
                        className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200"
                      >
                        Cancel
                      </button>
                      <button
                        type="button"
                        onClick={handleModalSubmit}
                        className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors duration-200"
                      >
                        Submit
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          );
        }}
      </Formik>
    </div>
  );
};

export default EditBatch;
