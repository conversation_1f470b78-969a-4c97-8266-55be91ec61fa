import { apiSlice } from "../apiSlice";
export const rowContentChapterListApi = apiSlice.injectEndpoints({
  reducerPath: "rowContentChapterListApi",
  tagTypes: ["ChapterList"],
  endpoints: (builder) => ({
    getChapterList: builder.query({
      query: (params) => ({
        url: "admin/chapter-list", 
        method: "GET",
        params:params,
      }),
      providesTags: ["ChapterList"],
    }),
    getChapterListBySubject: builder.query({
      query: (id) => ({
        url: "admin/chapter-list-by-subject-id/" + id, 
        method: "GET"
      }),
      providesTags: ["ChapterList"],
    }),

    chapterCreateOrUpdate: builder.mutation({
      query: (body) => {
        return {
          url: `admin/chapter-save-or-update`,
          method: "POST",
          body: body,
        };
      },
      invalidatesTags: ["ChapterList"],
    }),

    getSubjectChapterList: builder.query({
      query: (id) => ({
        url: `admin/subject-by-class-id/${id}`, 
        method: "GET",
      }),
      providesTags: ["ChapterList"],
    }),

  }),
});

export const { 
  useGetChapterListQuery,
  useGetChapterListBySubjectQuery,
  useChapterCreateOrUpdateMutation,
  useGetSubjectChapterListQuery } = rowContentChapterListApi;
