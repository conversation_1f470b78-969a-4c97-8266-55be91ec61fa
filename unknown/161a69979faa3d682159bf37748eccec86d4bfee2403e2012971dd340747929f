.animate-fade-in-out {
  animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  70% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.preview-fill-blanks {
  line-height: 2;
  word-spacing: 0.1em;
}

.preview-fill-blanks span {
  margin: 0 0.5em;
  display: inline-flex;
  align-items: center;
}

.preview-fill-blanks input {
  margin: 0 0.5em;
}
