import { useState, useRef, useEffect } from "react";
import { Check } from "lucide-react";
import { toast } from "react-toastify";

const Title = ({ course, postApi }) => {
  const [title, setTitle] = useState(course?.title || "");
  const [isEditing, setIsEditing] = useState(false);
  const spanRef = useRef(null);
  const inputRef = useRef(null);

  useEffect(() => {
    if (spanRef.current && inputRef.current) {
      inputRef.current.style.width = `${spanRef.current.offsetWidth + 5}px`;
    }
  }, [title]);

  const handleUpdate = async () => {

    let formData = new FormData();
    formData.append('id', course?.id);
    formData.append('title', title);
    const response = await postApi({
      end_point: 'admin/course-save-or-update',
      body: formData,
      notoast: true,
    }).unwrap();
    setIsEditing(false);
    if (response.status) {
    toast.success("Course title is updated successfully");
    }

  };

  return (
    <div className="relative flex items-center gap-2 max-w-[100%]">
      {/* Hidden span to measure text width */}
      <span
        ref={spanRef}
        className="absolute top-0 left-0 invisible whitespace-nowrap px-2 text-3xl font-semibold text-sky-600"
      >
        {title || course?.title || " "}
      </span>

      {/* Editable Input */}
      <input
        ref={inputRef}
        type="text"
        value={title}
        onChange={(e) => {
          setIsEditing(course?.title !== e.target.value);
          setTitle(e.target.value);
        }}
    className="border-none bg-transparent text-3xl font-semibold text-sky-600 focus:outline-none px-2 max-sm:text-2xl flex-grow min-w-[150px] hover:cursor-text hover:bg-slate-200 focus:bg-slate-200"
      />

      {/* Update Button */}
      {isEditing && (
        <button
          onClick={handleUpdate}
          className="p-2 text-white bg-sky-600 rounded-xl hover:bg-sky-700 transition duration-300 flex items-center justify-center"
        >
          <Check size={20} />
        </button>
      )}
    </div>
  );
};

export default Title;
