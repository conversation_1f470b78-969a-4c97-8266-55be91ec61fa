import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Textarea from "@/components/ui/Textarea";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { Formik, Form, Field } from "formik";
import { initialValues, validationSchema } from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { useParams } from "react-router-dom";
import { useGetMentorListQuery } from "@/store/api/master/mentorSlice";
import Select from "@/components/ui/Select"






const Create = ({showModal, setShowModal}) => {
  const [postApi, { isLoading, isError, error, isSuccess }] = usePostApiMutation();
  const dispatch = useDispatch();
  const { id } = useParams();
  const [value, setValue] = useState('');

  const [isNote, setIsNote] = useState(false);
  

  // Quill.register({
  //   'modules/table': QuillTable
  // }, true);

// const modules = {
//   toolbar: [
//     [{ 'header': [1, 2, false] }],
//     ['bold', 'italic', 'underline','strike', 'blockquote'],
//     [{'list': 'ordered'}, {'list': 'bullet'}, {'indent': '-1'}, {'indent': '+1'}],
//     ['link', 'image'],
//     [{ 'table': true }],  // Add table button to toolbar
//     ['clean']
//   ],
//   tableUI: true,  // Enable table UI module
// };

//   const modules = {
//     toolbar: [
//       [{ 'header': [1, 2, false] }],
//       ['bold', 'italic', 'underline','strike', 'blockquote'],
//       [{'list': 'ordered'}, {'list': 'bullet'}, {'indent': '-1'}, {'indent': '+1'}],
//       ['link', 'image'],
//       ['clean']
//     ],
//  };
  const onSubmit = async (values, { resetForm }) => {
    let list = [];
    values.is_note = isNote;
    values.course_id = id;
    list.push(values);
    let obj = JSON.stringify(list);
    const response = await postApi({end_point: "admin/routine-save-or-update", body: {routine: obj}});
    setShowModal(false);
  };
  return (
    <Modal
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      title="Update Routine"
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowModal(false)}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={initialValues}
        onSubmit={onSubmit}
      >
        {({
          values,
          errors,
          setFieldValue,
        }) => (
          
          <Form>
            {console.log(errors)}
            <>
              <div className="grid md:grid-cols-1 gap-4">
              {/* <ReactQuill 
                value={value} 
                onChange={setValue} 
                modules={modules} 
                theme="snow" 
              /> */}
              {/* <ReactQuill 
              theme="snow" 
              value={value} 
              onChange={setValue}
              modules={modules}
              /> */}

{/*         
                <InputField
                label="Day"
                name="day"
                type="text"
                placeholder="Day"
                required
                />
                
                
                <InputField
                label="Subject and Time"
                name="class_title"
                type="text"
                placeholder="Physics 1st Paper 10:30AM - 11:30PM"
                required
                /> */}
                
          
              </div>

            
              
            </>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default Create;
