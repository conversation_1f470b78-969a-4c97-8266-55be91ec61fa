import React, { useRef } from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
// import ReactPlayer from 'react-player';
import VideoPlayer from "@/components/ui/VideoPlayer";
import HLSVideoPlayer from "@/components/partials/HLSVideoPlayer";

const viewVideo = ({showModal, setShowModal, content}) => {
  console.log(content);
  const videoRef = useRef(null);

  const handlePlayPause = () => {
    if (videoRef.current.paused) {
      videoRef.current.play();
    } else {
      videoRef.current.pause();
    }
  };

  const handleFullscreen = () => {
    if (videoRef.current.requestFullscreen) {
      videoRef.current.requestFullscreen();
    } else if (videoRef.current.mozRequestFullScreen) { /* Firefox */
      videoRef.current.mozRequestFullScreen();
    } else if (videoRef.current.webkitRequestFullscreen) { /* Chrome, Safari & Opera */
      videoRef.current.webkitRequestFullscreen();
    } else if (videoRef.current.msRequestFullscreen) { /* IE/Edge */
      videoRef.current.msRequestFullscreen();
    }
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      title={content.title}
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowModal(false)}
        />
      }
    >
    {content?.video?.s3_url && (
      <>
        <HLSVideoPlayer videoUrl={content?.video?.s3_url} />
        </>
    )}
    {content?.video?.youtube_url && (


          <iframe
          width="100%"
          height="500px"
          src={`https://www.youtube.com/embed/${content?.video?.youtube_url.split("v=")[1]}`}
          title={content.title}
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          ></iframe>
   
    )}
    {content?.video?.raw_url && (

      <VideoPlayer url={content?.video?.raw_url} />

    )}
    

      <div className="mt-4 p-4 rounded">
       <h4 className="border-b pb-2 mb-2">{content.title} 
       </h4>

       <p>{content.description}</p>
      </div>

    </Modal>
  );
}

export default viewVideo;
