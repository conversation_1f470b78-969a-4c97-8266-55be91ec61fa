import React, { useState, useEffect } from 'react';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import { useDispatch, useSelector } from 'react-redux';
import { setShowModal, setEditShowModal } from '@/features/commonSlice';
import { Formik, Form } from 'formik';
import * as yup from 'yup';
import InputField from '@/components/ui/InputField';
import Textarea from '@/components/ui/Textarea';
import FileInput from '@/components/ui/FileInput';
import InputSelect from '@/components/ui/InputSelect';
import Icon from '@/components/ui/Icon';
import ItemSelectionModal from './ItemSelectionModal';
import { useGetApiQuery, usePostApiMutation, useUpdateApiMutation } from '@/store/api/master/commonSlice';

const validationSchema = yup.object({
  title: yup.string()
    .required('Title is required')
    .max(100, 'Title should not be more than 100 characters'),
  description: yup.string()
    .max(500, 'Description should not be more than 500 characters'),
  type: yup.string()
    .required('Type is required')
    .oneOf(['course', 'ebook'], 'Type must be either course or ebook'),
  item_id: yup.number()
    .required('Item is required')
    .positive('Item ID must be positive'),
  image: yup.mixed()
    .test('fileType', 'Only image files are allowed', function(value) {
      if (!value) return true;
      return value && (
        (value instanceof File && value.type.startsWith('image/')) ||
        (typeof value === 'string')
      );
    })
});

const PromotionalItemForm = ({ onSubmitSuccess }) => {
  const dispatch = useDispatch();
  const { showModal, showEditModal, editData } = useSelector((state) => state.commonReducer);
  const [selectedType, setSelectedType] = useState('');

  const isEditMode = showEditModal && editData;

  const [postApi, { isLoading: isCreating }] = usePostApiMutation();
  const [updateApi, { isLoading: isUpdating }] = useUpdateApiMutation();

  const { data: coursesData, isLoading: isCoursesLoading } = useGetApiQuery('admin/course-list?pagination=0&is_active=1');
  const { data: ebooksData, isLoading: isEbooksLoading } = useGetApiQuery('admin/ebooks?pagination=0&is_active=1');

  const courses = coursesData || [];
  const ebooks = ebooksData || [];

  const [selectedItemId, setSelectedItemId] = useState(null);
  const [showSelectionPopup, setShowSelectionPopup] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  useEffect(() => {
    if (isEditMode && editData) {
      setSelectedType(editData.type || '');
      if (editData.type === 'course') {
        setSelectedItemId(editData.course_id || null);
      } else if (editData.type === 'ebook') {
        setSelectedItemId(editData.ebook_id || null);
      }
    }
  }, [isEditMode, editData]);

  useEffect(() => {
    if (isEditMode && editData && editData.type) {
      if (editData.type === 'course' && courses.length > 0 && editData.course_id) {
        const item = courses.find(item => item.id === editData.course_id);
        if (item) {
          setSelectedItem(item);
        }
      } else if (editData.type === 'ebook' && ebooks.length > 0 && editData.ebook_id) {
        const item = ebooks.find(item => item.id === editData.ebook_id);
        if (item) {
          setSelectedItem(item);
        }
      }
    }
  }, [isEditMode, editData, courses, ebooks]);

  const onSubmit = async (values, { resetForm }) => {
    try {
      const formData = new FormData();
      Object.keys(values).forEach(key => {
        if (key === 'image' && typeof values[key] === 'string') {
          return;
        }
        if (key === 'item_id') {
          return;
        }
        if (key === 'is_active') {
          formData.append(key, values[key] ? 1 : 0);
          return;
        }
        formData.append(key, values[key]);
      });
      if (!values.type || !values.item_id) {
        throw new Error('Please select a type and an item');
      }
      if (values.type === 'course') {
        formData.append('course_id', Number(values.item_id));
      } else if (values.type === 'ebook') {
        formData.append('ebook_id', Number(values.item_id));
      }

      let response;

      if (isEditMode) {
        response = await updateApi({
          end_point: `admin/promotional-items/${editData.id}`,
          body: formData
        });
      } else {
        response = await postApi({
          end_point: 'admin/promotional-items',
          body: formData
        });
      }

      if (response?.data) {
        onSubmitSuccess(response.data, isEditMode);
        handleCloseModal();
        resetForm();
      }
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const handleCloseModal = () => {
    if (showModal) {
      dispatch(setShowModal(false));
    }
    if (showEditModal) {
      dispatch(setEditShowModal(false));
    }
    setSelectedType('');
    setSelectedItemId(null);
    setSelectedItem(null);
    setShowSelectionPopup(false);
  };

  const handleTypeChange = (value) => {
    setSelectedType(value);
    setSelectedItemId(null);
    setSelectedItem(null);
    setShowSelectionPopup(true);
  };

  const handleItemSelect = (setFieldValue, item) => {
    setSelectedItemId(item.id);
    setSelectedItem(item);
    setShowSelectionPopup(false);
    setFieldValue('item_id', item.id);
    setFieldValue('item_name', item.title);
  };

  const closeSelectionPopup = () => {
    setShowSelectionPopup(false);
  };

  return (
    <Modal
      title={isEditMode ? 'Edit Promotional Item' : 'Add New Promotional Item'}
      activeModal={showModal || showEditModal}
      onClose={handleCloseModal}
      className={'max-w-5xl'}
    >
      <Formik
        initialValues={{
          title: isEditMode && editData ? editData.title || '' : '',
          description: isEditMode && editData ? editData.description || '' : '',
          type: isEditMode && editData ? editData.type || '' : '',
          item_id: isEditMode && editData ?
            (editData.type === 'course' ? editData.course_id || '' :
             editData.type === 'ebook' ? editData.ebook_id || '' : '') : '',
          item_name: isEditMode && editData ? editData.item_name || '' : '',
          image: isEditMode && editData ? editData.image || null : null,
          is_active: isEditMode && editData ? (editData.is_active ? 1 : 0) : 1
        }}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
        enableReinitialize
      >
        {({ setFieldValue, errors, touched, isSubmitting }) => (
          <Form className="space-y-4 p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <InputField
                  name="title"
                  label="Title"
                  placeholder="Enter title"
                  required
                />
              </div>
              <div>
                <InputSelect
                  name="type"
                  label="Type"
                  required
                  options={[
                    { value: 'course', label: 'Course' },
                    { value: 'ebook', label: 'Ebook' }
                  ]}
                  onRoleSelect={(value) => {
                    handleTypeChange(value);
                    setFieldValue('item_id', '');
                    setFieldValue('item_name', '');
                  }}
                />
              </div>
            </div>

            {selectedType && (
              <div className="space-y-4">
                <div>
                  <label className="block text-gray-600 text-sm font-medium mb-2">
                    Select {selectedType === 'course' ? 'Course' : 'Ebook'} <span className="text-rose-500"> *</span>
                  </label>

                  <div className="flex items-center space-x-4">
                    {selectedItem ? (
                      <div className="flex-1 flex items-center p-2 border rounded-md bg-gray-50">
                        <div className="h-10 w-10 mr-3 flex-shrink-0 flex items-center justify-center rounded">
                          {selectedType === 'course' ? (
                            <div className="bg-blue-100 text-blue-600 h-full w-full flex items-center justify-center rounded">
                              <Icon icon="heroicons-outline:academic-cap" className="w-5 h-5" />
                            </div>
                          ) : (
                            <div className="bg-green-100 text-green-600 h-full w-full flex items-center justify-center rounded">
                              <Icon icon="heroicons-outline:book-open" className="w-5 h-5" />
                            </div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-gray-800 truncate">{selectedItem.title}</p>
                        </div>
                        <button
                          type="button"
                          className="text-gray-400 hover:text-red-500 p-1"
                          onClick={() => {
                            setSelectedItemId(null);
                            setSelectedItem(null);
                            setFieldValue('item_id', '');
                            setFieldValue('item_name', '');
                          }}
                        >
                          <Icon icon="heroicons-outline:x" className="w-5 h-5" />
                        </button>
                      </div>
                    ) : (
                      <Button
                        text={`Select ${selectedType === 'course' ? 'Course' : 'Ebook'}`}
                        className="btn-outline-primary flex items-center"
                        onClick={() => setShowSelectionPopup(true)}
                        type="button"
                        icon="heroicons-outline:selector"
                      />
                    )}
                  </div>

                  {errors.item_id && touched.item_id && (
                    <div className="text-red-500 text-xs mt-1">{errors.item_id}</div>
                  )}
                </div>

                <div className="hidden">
                  {/* Hidden fields to store item data */}
                  <InputField name="item_id" type="hidden" />
                  <InputField name="item_name" type="hidden" />
                  <InputField name="is_active" type="hidden" />
                </div>
              </div>
            )}

            {/* Selection Modal Component */}
            <ItemSelectionModal
              isOpen={showSelectionPopup}
              onClose={closeSelectionPopup}
              onSelect={(item) => handleItemSelect(setFieldValue, item)}
              items={selectedType === 'course' ? courses : ebooks}
              selectedItemId={selectedItemId}
              itemType={selectedType}
              isLoading={selectedType === 'course' ? isCoursesLoading : isEbooksLoading}
            />

            <div>
              <Textarea
                name="description"
                label="Description"
                placeholder="Enter promotional item description"
                rows={4}
              />
            </div>

            <div>
              <FileInput
                name="image"
                label="Promotional Image"
                accept={{ 'image/*': ['.jpg', '.jpeg', '.png', '.gif'] }}
                accepts="image/"
              />
              {errors.image && touched.image && (
                <div className="text-red-500 text-xs mt-1">{errors.image}</div>
              )}
            </div>

            <div className="flex justify-end space-x-3 mt-4">
              <Button
                text="Cancel"
                className="btn-outline-dark"
                onClick={handleCloseModal}
                type="button"
              />
              <Button
                text={isEditMode ? 'Update' : 'Save'}
                className="btn-primary"
                type="submit"
                isLoading={isCreating || isUpdating || isSubmitting}
                disabled={isCreating || isUpdating || isSubmitting}
              />
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default PromotionalItemForm;
