import React, { useState, useRef } from "react";
import { useSelector } from "react-redux";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";

const HeadlineEditor = ({ organization }) => {
  const { user } = useSelector((state) => state.auth);
  const [isEditingHeadline, setIsEditingHeadline] = useState(false);
  const [isEditingSubHeadline, setIsEditingSubHeadline] = useState(false);
  const [headlineValue, setHeadlineValue] = useState(organization?.headline || "");
  const [subHeadlineValue, setSubHeadlineValue] = useState(organization?.sub_headline || "");
  const [headlineError, setHeadlineError] = useState("");
  const [subHeadlineError, setSubHeadlineError] = useState("");
  
  const [updateApi, { isLoading }] = useUpdateApiMutation();
  const headlineInputRef = useRef(null);
  const subHeadlineInputRef = useRef(null);

  return (
    <div className="space-y-5">
      <div className="relative group space-y-2">
        <div className="relative flex-1">
          {isEditingHeadline ? (
            <input
              ref={headlineInputRef}
              type="text"
              value={headlineValue}
              onChange={(e) => {
                setHeadlineValue(e.target.value);
                if (headlineError) setHeadlineError("");
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  if (headlineValue.trim() === "") return;

                  const submitHeadline = async () => {
                    try {
                      const formData = new FormData();
                      formData.append("headline", headlineValue.trim());

                      await updateApi({
                        end_point: `admin/organizations/${user?.organization_id}`,
                        body: formData
                      }).unwrap();

                      setIsEditingHeadline(false);
                    } catch (err) {
                      console.error("Failed to update headline:", err);
                    }
                  };

                  submitHeadline();
                } else if (e.key === 'Escape') {
                  setIsEditingHeadline(false);
                  setHeadlineValue(organization.headline);
                }
              }}
              className={`w-full text-2xl md:text-4xl text-[#1B69B3] bg-transparent outline-none border-b ${headlineValue.trim() === "" ? 'border-red-500' : 'border-gray-400'} py-1 pr-8 transition-all duration-200 ease-in-out focus:border-blue-500`}
              autoFocus
            />
          ) : (
            <h2
              className="text-2xl md:text-4xl text-[#1B69B3] cursor-pointer"
              onClick={() => {
                setIsEditingHeadline(true);
                setHeadlineValue(organization.headline);
              }}
            >
              {organization.headline}
            </h2>
          )}

          {/* Edit/Submit buttons */}
          <div className="absolute right-0 top-1/2 -translate-y-1/2 flex space-x-1">
            {!isEditingHeadline && (
              <button
                onClick={() => {
                  setIsEditingHeadline(true);
                  setHeadlineValue(organization.headline);
                }}
                className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 bg-blue-50 hover:bg-blue-100 rounded-full"
                aria-label="Edit Headline"
              >
                <Icon icon="heroicons-outline:pencil" className="text-blue-600 w-5 h-5" />
              </button>
            )}
            {isEditingHeadline && (
              <button
                onClick={async () => {
                  if (headlineValue.trim() === "") return;

                  try {
                    const formData = new FormData();
                    formData.append("headline", headlineValue.trim());

                    await updateApi({
                      end_point: `admin/organizations/${user?.organization_id}`,
                      body: formData
                    }).unwrap();

                    setIsEditingHeadline(false);
                  } catch (err) {
                    console.error("Failed to update headline:", err);
                  }
                }}
                className={`text-green-500 hover:text-green-700 ${headlineValue.trim() === "" ? 'opacity-50 cursor-not-allowed' : ''}`}
                disabled={headlineValue.trim() === "" || isLoading}
                aria-label="Submit Headline"
              >
                <Icon icon="heroicons-outline:check" className="w-6 h-6" />
              </button>
            )}
          </div>
        </div>
        {headlineError && (
          <div className="text-red-500 text-sm">{headlineError}</div>
        )}
      </div>
      <div className="relative group mt-4 space-y-2">
        <div className="relative flex-1">
          {isEditingSubHeadline ? (
            <input
              ref={subHeadlineInputRef}
              type="text"
              value={subHeadlineValue}
              onChange={(e) => {
                setSubHeadlineValue(e.target.value);
                if (subHeadlineError) setSubHeadlineError("");
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  if (subHeadlineValue.trim() === "") return;

                  const submitSubHeadline = async () => {
                    try {
                      const formData = new FormData();
                      formData.append("sub_headline", subHeadlineValue.trim());

                      await updateApi({
                        end_point: `admin/organizations/${user?.organization_id}`,
                        body: formData
                      }).unwrap();

                      setIsEditingSubHeadline(false);
                    } catch (err) {
                      console.error("Failed to update sub-headline:", err);
                    }
                  };

                  submitSubHeadline();
                } else if (e.key === 'Escape') {
                  setIsEditingSubHeadline(false);
                  setSubHeadlineValue(organization.sub_headline);
                }
              }}
              className={`w-full text-sm md:text-base bg-transparent outline-none border-b ${subHeadlineValue.trim() === "" ? 'border-red-500' : 'border-gray-400'} py-1 pr-8 transition-all duration-200 ease-in-out focus:border-blue-500`}
              autoFocus
            />
          ) : (
            <p
              className="text-sm md:text-base cursor-pointer"
              onClick={() => {
                setIsEditingSubHeadline(true);
                setSubHeadlineValue(organization.sub_headline);
              }}
            >
              {organization.sub_headline}
            </p>
          )}

          {/* Edit/Submit buttons */}
          <div className="absolute right-0 top-1/2 -translate-y-1/2 flex space-x-1">
            {!isEditingSubHeadline && (
              <button
                onClick={() => {
                  setIsEditingSubHeadline(true);
                  setSubHeadlineValue(organization.sub_headline);
                }}
                className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 bg-blue-50 hover:bg-blue-100 rounded-full"
                aria-label="Edit Sub-headline"
              >
                <Icon icon="heroicons-outline:pencil" className="text-blue-600 w-5 h-5" />
              </button>
            )}
            {isEditingSubHeadline && (
              <button
                onClick={async () => {
                  if (subHeadlineValue.trim() === "") return;

                  try {
                    const formData = new FormData();
                    formData.append("sub_headline", subHeadlineValue.trim());

                    await updateApi({
                      end_point: `admin/organizations/${user?.organization_id}`,
                      body: formData
                    }).unwrap();

                    setIsEditingSubHeadline(false);
                  } catch (err) {
                    console.error("Failed to update sub-headline:", err);
                  }
                }}
                className={`text-green-500 hover:text-green-700 ${subHeadlineValue.trim() === "" ? 'opacity-50 cursor-not-allowed' : ''}`}
                disabled={subHeadlineValue.trim() === "" || isLoading}
                aria-label="Submit Sub-headline"
              >
                <Icon icon="heroicons-outline:check" className="w-6 h-6" />
              </button>
            )}
          </div>
        </div>
        {subHeadlineError && (
          <div className="text-red-500 text-sm">{subHeadlineError}</div>
        )}
      </div>
    </div>
  );
};

export default HeadlineEditor;
