import React, { useState } from 'react';
import Badge from '@/components/ui/Badge';
import Button from '@/components/ui/Button';
import Icon from '@/components/ui/Icon';
import Modal from "@/components/ui/Modal";
import { showModal } from '@/features/commonSlice';
import { toast } from "react-toastify";
import Title from './Title';

const CourseHeader = ({ course, postApi, onEdit, isLoading }) => {
  const [showModal, setShowModal] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [isUnPublishing, setIsUnPublishing] = useState(false);

  const handlePublish = async () => {
    let formData = new FormData();
    formData.append('id', course?.id);
    formData.append('title', course?.title);
    formData.append('course_type_id', course?.course_type_id);
    formData.append('is_active', course?.is_active == 1 ? 0 : 1);

    const response = await postApi({
      end_point: 'admin/course-save-or-update',
      body: formData,
      notoast: true,
    });

    console.log(response);
    let msg = isUnPublishing ? "Course unpublished successfully" : "Course published successfully";
    toast.success(msg);

    setShowModal(false);
    setIsPublishing(false);
    setIsUnPublishing(false);
  };



  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center border-b py-4 px-4">
      {/* Editable Title */}
      <div className="relative flex items-center flex-wrap gap-2 max-w-[80%]">
        <Title course={course} postApi={postApi} />
      </div>

      <div className="flex gap-4 items-center mt-4 md:mt-0">
        {course?.is_active ? (
          <Button
            className="btn btn-success btn-sm"
            onClick={() => {
              setShowModal(true);
              setIsUnPublishing(true);
              setIsPublishing(false);
            }}
          >
            Unpublish
          </Button>
        ) : (
          <Button
            className="btn btn-success btn-sm"
            onClick={() => {
              setShowModal(true);
              setIsUnPublishing(false);
              setIsPublishing(true);
            }}
          >
            Publish
          </Button>
        )}
        <Button className="btn btn-primary btn-sm" onClick={onEdit}>
          <Icon icon="lucide:edit" />
        </Button>
      </div>

      {/* Modal for Publish/Unpublish */}
      <Modal
        activeModal={showModal}
        onClose={() => setShowModal(false)}
        title={isPublishing ? "Publish Course" : "Unpublish Course"}
        className="max-w-2xl"
        footer={
          <Button
            text="Close"
            btnClass="btn-primary"
            onClick={() => setShowModal(false)}
          />
        }
      >
        <h3 className="text-center">Are you sure?</h3>
        <p className="text-center text-slate-500 text-sm mt-4">
          You are going to {isPublishing ? "publish" : "unpublish"}{" "}
          <b>{course.title}</b>.
        </p>

        <div className="ltr:text-right rtl:text-left mt-5 gap-4">
          <Button
            onClick={() => setShowModal(false)}
            type="button"
            className="btn text-center btn-primary mr-4"
          >
            Cancel
          </Button>
          <Button
            isLoading={isLoading}
            type="button"
            className="btn text-center btn-danger"
            onClick={handlePublish}
          >
            {isPublishing ? "Publish Now" : "Unpublish Now"}
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default CourseHeader;
