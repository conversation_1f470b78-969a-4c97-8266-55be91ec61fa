import React from "react";

const FeatureSummary = () => {
  const features = [
    "Own White labeling",
    "Online / Offline Course Management",
    "Online / Offline Batch Management",
    "Pre-Recorded Content / Course Selling",
    "Course Progress Tracking",
    "Student's Assignment Management",
    "Online / Offline Class Management",
    "Student Attendance Management",
    "Online / Offline Exam Management",
    "Online / Offline Payment Management",
    "Teacher Management",
    "Assigning Teachers in a course",
    "Custom Announcement / Notice Board",
    "Custom Web & Mobile Notification Automation",
    "Online Assessment (by Quiz, Written)",
    "Device Log Management",
    "Individual (Admin, Teacher, Student) Profile",
  ];

  return (
    <section className="bg-white pt-10">
      <div className="container mx-auto p-4 shadow-md rounded">
      <div className="flex items-center justify-center mb-6">
        <div className="w-8 h-[1px] bg-[#4338CA]"></div>
        <h2 className="text-[#4338CA] text-2xl font-semibold mx-2">
          Features Summary
        </h2>
      </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
          {features.map((feature, index) => (
            <div
              key={index}
              className="flex items-start "
            >
              <div className="flex-shrink-0">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  className="w-6 h-6 text-green-600 mr-3"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="flex-grow">
                <p className="text-sm text-gray-700">{feature}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeatureSummary;
