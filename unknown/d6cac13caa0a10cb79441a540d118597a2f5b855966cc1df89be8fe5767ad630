/* Drag and drop styles for menu settings table */
.draggable-menu-item {
  cursor: move; /* Show move cursor */
  cursor: grab; /* Modern browsers */
}

.draggable-menu-item:active {
  cursor: grabbing; /* When actively dragging */
}

/* Add a subtle transition for hover effects */
.draggable-menu-item {
  transition: box-shadow 0.2s ease, border-color 0.2s ease, transform 0.1s ease;
}

.draggable-menu-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-color: #d1d5db;
}

/* Style for when item is being dragged */
.draggable-menu-item.dragging {
  opacity: 0.7;
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

/* Drag handle styles */
.drag-handle {
  color: #6b7280;
  transition: color 0.2s ease;
}

.draggable-menu-item:hover .drag-handle {
  color: #4b5563;
}
