import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
const Duplicate = ({showDuplicateModal, setShowDuplicateModal, data}) => {

    const [postApi, { isLoading, isError, error, isSuccess }] = usePostApiMutation();
    const onSubmit = async () => {
        const response = await postApi({end_point: '/admin/duplicate-course', body: {id: data.id}})
        console.log(response);
        setShowDuplicateModal(false);
    }
    return (

    <Modal
    activeModal={showDuplicateModal}
    onClose={() => setShowDuplicateModal(false)}
    title="Duplicate Menu"
    className="max-w-2xl"
    footer={
        <Button
            text="Close"
            btnClass="btn-primary"
            onClick={() => setShowDuplicateModal(false)}
        />
        }
    >        

    <h3 className="text-center">Are you sure?</h3>
    <p className="text-center text-slate-500 text-sm mt-4">You are going to copy the <b>"{data?.title}"</b> Course. 
    </p>

    <div className="ltr:text-right rtl:text-left mt-5 gap-4">
        <Button 
            // isLoading={isLoading}
            type="button"
            className="btn text-center btn-primary mr-4"
            onClick={() => setShowDuplicateModal(false)} 
        >
            Cancel
        </Button>
        <Button 
            isLoading={isLoading}
            type="button"
            className="btn text-center btn-danger"
            onClick={onSubmit} 
        >
            Duplicate Course Now
        </Button>
    </div>
    {/* <Button text="Duplicate" btnClass="btn btn-danger" /> */}
    </Modal>
    );
}

export default Duplicate;
