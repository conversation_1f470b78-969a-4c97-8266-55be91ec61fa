
import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/master/commonSlice";

import Select from "@/components/ui/Select";
import avatar from "@/assets/images/avatar/av-1.svg";

const Filter = ({ setApiParam }) => {

  const { data: courseList, isLoading, error } =  useGetApiQuery(`admin/course-list-for-filter`);
  return (
    <div className="flex gap-2">
      <Select

        className="w-72"
        defaultValue=""
        placeholder="Select Course"
        options={courseList?.map((item) => {
          return { label: item.title, value: item.id };
        })}
        name="class_level"
        onChange={(e) => { 
          setApiParam("?course_id=" + e.target.value);
        }}
      />
    </div>
  );
};

const index = () => {
  const [apiParam, setApiParam] = useState("");
  const { data: assignments, isLoading, error } =  useGetApiQuery(`admin/enrollemnt-list${apiParam}`);
 
  const [filter, setFilter] = useState("");
  // console.log(assignments);
  const changePage = (val) => {
    setApiParam(val);
  };

  
  const columns = [
    {
      label: "SL",
      field: "index",
    },
    {
      label: "Course",
      field: "course_title",
    },
    {
      label: "Student",
      field: "student",
    },
    {
      label: "Enroll Date",
      field: "enroll_date",
    }
  ];
  const tableData = assignments?.data?.map((item, index) => {
    return {
      index: index + 1,
      course_title: item.course_title,
      enroll_date: new Date(item.created_at).toLocaleString("en-IN", {
        year: "numeric",
        month: "short",
        day: "numeric",
      }),
      student: (
        <div className="flex items-center" >
          <img
            src={
              item.image
                ? import.meta.env.VITE_ASSET_HOST_URL + item.image
                : avatar
            }
            className="rounded-full w-8 h-8 mr-2"
            alt="avatar"
          />
          <span className="hover:text-primary-500 hover:underline">
            {item.name}
          </span>
        </div>
      ),
      is_free: (
        <Badge
          className={
            item.is_free == "true"
              ? `bg-danger-500 text-white`
              : `bg-success-500 text-white`
          }
        >
          {item.is_free ? "Yes" : "No"}
        </Badge>
      )
    };
  });

  const actions = [
    {
      name: "view",
      icon: "heroicons-outline:eye",
      onClick: (val) => {
        console.log(val);
      },
    },
    // {
    //   name: "edit",
    //   icon: "heroicons:pencil-square",
    //   onClick: (val) => {
    //     console.log(val);
    //   },
    // },
    // {
    //   name: "delete",
    //   icon: "heroicons-outline:trash",
    //   onClick: (val) => {
    //     console.log(val);
    //   },
    // },
  ];
  //   const changePage = (item) => {
  //     console.log(item);
  //   };
  const handleSubmit = () => {
    setShowModal(false);
  };
  // const CourseFilter = <Filter setApiParam={setApiParam} />;
  // const createPage = <CreateAssignment />;
  return (
    <div>
      {/* {tableData?.length > 0 && ( */}
      <BasicTablePage
        tableHeaderExtra={<Filter setApiParam={setApiParam}/>}
        title="Enrollment List"
        // createButton="Create Assignment"
        // actions={actions}
        // createButton="Create Assignment"
        columns={columns}
        data={tableData}
        filter={filter}
        submitForm={handleSubmit}
        changePage={changePage}
        currentPage={assignments?.current_page}
        totalPages={Math.ceil(assignments?.total / assignments?.per_page)}
        setFilter={setApiParam}
      />
      {/* )} */}
    </div>
  );
};

export default index;
