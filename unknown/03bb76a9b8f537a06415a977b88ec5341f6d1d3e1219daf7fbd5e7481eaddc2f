
import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Fileinput from "@/components/ui/Fileinput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import Textarea from "@/components/ui/Textarea";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import Select from "@/components/ui/Select";
import { Formik, Form, Field } from 'formik';
import { initialValues, validationSchema} from "./formCourse";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal } from "@/features/commonSlice";
import { useGetMenuListQuery } from "@/store/api/master/menuSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
const EditCourse = () => {

    const [postApi, { isLoading, isError, error, isSuccess }] = usePostApiMutation();
    const menuList = useGetMenuListQuery("pagination=false")?.data;

    const dispatch = useDispatch()
    const { showEditModal } = useSelector((state) => state.commonReducer);
    const { editData } = useSelector((state) => state.commonReducer);
    console.log(editData);
    const [isFree, setIsFree] = useState(editData.is_free);
    const [isActive, setIsActive] = useState(editData.is_active);
    const [hasLifeCoach, setHasLifeCoach] = useState(editData.has_life_coach);

    const onSubmit = async (values, { resetForm }) => {
        let formData = new FormData();
        formData.append("id", editData.id);
        formData.append("title", values.title);
        formData.append("title_bn", values.title_bn);
        formData.append("category_id", values.category_id || '');
        formData.append("course_type_id", values.course_type_id || '');
        formData.append("gp_product_id", values.gp_product_id || '');
        formData.append("youtube_url", values.youtube_url);
        formData.append("description", values.description);
        formData.append("thumbnail", values.thumbnail);
        formData.append("icon", values.icon);
        formData.append("number_of_enrolled", values.number_of_enrolled);
        formData.append("regular_price", values.regular_price);
        formData.append("sale_price", values.sale_price);
        formData.append("discount_percentage", values.discount_percentage);
        formData.append("rating", values.rating);
        formData.append("sequence", values.sequence);
        formData.append("appeared_from", new Date(values.appeared_from).toISOString().split('T')[0] );
        formData.append("appeared_to", new Date(values.appeared_to).toISOString().split('T')[0] );
        formData.append("is_free", isFree ? 1 : 0);
        formData.append("is_active", isActive ? 1 : 0);
        formData.append("has_life_coach", hasLifeCoach ? 1 : 0);

        const response = await postApi({end_point: "admin/course-save-or-update", body: formData});
        dispatch(setEditShowModal(false));
    }
    return (

    <Modal
    activeModal={showEditModal}
    onClose={() => dispatch(setEditShowModal(false))}
    title="Update Course"
    className="max-w-5xl"
    footer={
        <Button
            text="Close"
            btnClass="btn-primary"
            onClick={() => dispatch(setEditShowModal(false))}
        />
        }
    >        
      <Formik
        validationSchema={validationSchema}
        initialValues={editData}
        onSubmit={onSubmit}
      >
        {({
          values,
          errors,
          setFieldValue,
        }) => (
          <Form>
            <>
              <div className="grid md:grid-cols-2 gap-4">
                <InputField
                  label="Title"
                  name="title"
                  type="text"
                  placeholder="Enter Title"
                  required
                />
                <InputField
                  label="Bangla Title"
                  name="title_bn"
                  type="text"
                  placeholder="Enter Bangla Title"
                />
              </div>
              <div className="grid md:grid-cols-3 gap-4 my-2">
                
              <Select
                    label={"Select Menu"}
                    defaultValue={editData.category_id}
                    placeholder="Select Menu"
                    options={menuList?.map((item) => {
                      return { label: item.name, value: item.id };
                    })}
                    name="category_id"
                    onChange={(e) => {
                      setFieldValue("category_id", e.target.value);
                    }}
                    error={errors.category_id}
                  />
                <InputField
                  label="Youtube URL"
                  defaultValue={editData.youtube_url || ''}
                  name="youtube_url"
                  type="text"
                  placeholder="Enter Youtube URL"
                />
                <InputField
                  label="Google Pay Code"
                  defaultValue={editData.gp_product_id || ''}
                  name="gp_product_id"
                  type="text"
                  placeholder="Enter Google Pay Code"
                />
                <InputField
                  label="Regular Price"
                  name="regular_price"
                  type="text"
                  placeholder="Enter Regular Price"
                  required
                />
                <InputField
                  label="Sale Price"
                  name="sale_price"
                  type="text"
                  placeholder="Enter Sale Price"
                  required
                />
                <InputField
                  label="Discount %"
                  name="discount_percentage"
                  type="text"
                  placeholder="Enter Discount"
                  required
                />
                <InputField
                  label="Number of Enrolled"
                  name="number_of_enrolled"
                  type="text"
                  placeholder="Enter Number of Enrolled"
                  required
                />
                <InputField
                  label="Rating"
                  name="rating"
                  type="text"
                  placeholder="Enter Rating"
                  required
                />
                <InputField
                  label="Sequence"
                  name="sequence"
                  type="text"
                  placeholder="Enter Sequence"
                  required
                />
              </div>
              <div className="grid md:grid-cols-2 gap-4 my-2">
                <div>
                  <label className="block text-[#1D1D1F] text-base font-medium mb-2">
                    Icon
                  </label>
                  <Fileinput
                    name="icon"
                    accept="image/*"
                    type="file"
                    placeholder="Icon"
                    preview={true}
                    selectedFile={values.icon}
                    onChange={(e) => {
                      setFieldValue("icon", e.currentTarget.files[0]);
                    }}
                  />
                </div>
                <div>
                  <label className="block text-[#1D1D1F] text-base font-medium mb-2">
                    Thumbnail
                  </label>
                  <Fileinput
                    name="thumbnail"
                    accept="image/*"
                    type="file"
                    placeholder="Thumbnail"
                    preview={true}
                    selectedFile={values.thumbnail}
                    onChange={(e) => {
                      setFieldValue("thumbnail", e.currentTarget.files[0]);
                    }}
                  />
                </div>

    
                <DatePicker 
                defaultValue={editData.appeared_from}
                label="Appeared From"
                placeholder="YYYY-MM-DD"
                format="YYYY/MM/DD"
                name="appeared_from"
                error={errors?.appeared_from} 
                onChange={(e) => {
                    setFieldValue("appeared_from", e);
                    }} />

                <DatePicker defaultValue={editData.appeared_to} label="Appeared To" placeholder="YYYY-MM-DD" format="YYYY/MM/DD" name="appeared_to" error={errors?.appeared_to} 
                onChange={(e) => {
                    setFieldValue("appeared_to", e);
                    }} />


              </div>
              <div className="grid md:grid-cols-1 gap-4 my-2">
                <label className="block text-[#1D1D1F] text-base font-medium">
                  Description
                </label>
                  <Textarea
                    defaultValue={editData.description}
                    placeholder="Description"
                    name="description"
                    onChange={(e) => {
                      setFieldValue("description", e.target.value);
                    }}
                  />
              </div>
              <div className="grid md:grid-cols-4  gap-4 py-5 mx-10">

                <Switch
                  label="Free"
                  activeClass="bg-success-500"
                  value={isFree}
                  name="is_free"
                  onChange={() => setIsFree(!isFree)}
                />
                <Switch
                  label="Active"
                  activeClass="bg-success-500"
                  value={isActive}
                  name="is_active"
                  onChange={() => setIsActive(!isActive)}
                />
                <Switch
                  label="Has Life Coach"
                  activeClass="bg-success-500"
                  value={hasLifeCoach}
                  name="has_life_coach"
                  onChange={() => setHasLifeCoach(!hasLifeCoach)}
                />
              </div>
            </>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
        </Modal>
    );
}

export default EditCourse;
