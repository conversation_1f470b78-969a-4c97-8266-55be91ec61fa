import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { useDispatch } from "react-redux";
import {
  useGetApiQuery,
  usePostApiMutation,
} from "@/store/api/master/commonSlice";
import { useState } from "react";
import avater from "@/assets/images/avatar/av-1.svg";

const AddToBatchModal = ({ showEditModal, setEditShowModal, data }) => {
  const dispatch = useDispatch();
  const [selectedStudents, setSelectedStudents] = useState([]);
  const {
    data: students,
    isLoading,
    error,
  } = useGetApiQuery(
    `admin/student-list-for-batch-by-course-id/${data?.course_id}`
  );
  const studentList = students?.student_list;

  const [postApi, { isLoading: isSubmitting }] = usePostApiMutation();

  const handleToggleStudent = (studentId) => {
    setSelectedStudents((prev) =>
      prev.includes(studentId)
        ? prev.filter((id) => id !== studentId)
        : [...prev, studentId]
    );
  };

  const handleSelectAll = () => {
    if (selectedStudents.length === studentList.length) {
      setSelectedStudents([]); // Deselect all
    } else {
      setSelectedStudents(studentList.map((student) => student.id)); // Select all
    }
  };

  const handleSubmit = async () => {
    try {
      const response = await postApi({
        end_point: "admin/batches-add-students",
        body: { student_ids: selectedStudents, batch_id: data?.batch_id },
      });

      if (response?.data?.status) {
        dispatch(setEditShowModal(false));
      }
    } catch (error) {
      console.error("Error adding students to batch:", error);
    }
  };

  return (
    <Modal
      activeModal={showEditModal}
      onClose={() => dispatch(setEditShowModal(false))}
      title="Add Students"
      className="max-w-3xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setEditShowModal(false))}
        />
      }
    >
      <div>
        <div className="flex justify-start mb-4">
          {studentList?.length > 0 && <button
            className="px-4 py-2 border border-indigo-300 text-indigo-500 rounded-md hover:border-indigo-400"
            onClick={handleSelectAll}
          >
            {selectedStudents?.length === studentList?.length
              ? "Deselect All"
              : "Select All"}
          </button>}
        </div>

        <div className="grid grid-cols-2 gap-4">
          {isLoading ? (
            <p className="text-start py-3 ">Loading...</p>
          ) : (
            studentList?.length > 0 ?
            studentList?.map((student) => (
              <div
                key={student.id}
                className={`flex items-center rounded-md border border-gray-200 p-2 cursor-pointer transition-colors ${
                  selectedStudents.includes(student.id)
                    ? "bg-blue-50"
                    : "hover:bg-gray-50"
                }`}
                onClick={() => handleToggleStudent(student.id)}
              >
                <input
                  type="checkbox"
                  checked={selectedStudents.includes(student.id)}
                  onChange={() => handleToggleStudent(student.id)}
                  className="form-checkbox h-4 w-4 text-blue-600 mr-3"
                  onClick={(e) => e.stopPropagation()}
                />
                <div className="flex items-center space-x-3">
                  <img
                    src={student?.image ? student.image : avater}
                    className="rounded-full w-8 h-8"
                    alt="student avatar"
                  />
                  <div className="flex flex-col">
                    <span className="font-medium text-gray-900 text-sm">
                      {student?.name}
                    </span>
                    {student?.email && (
                      <span className="text-gray-500 text-xs">
                        {student?.email}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            )) : <p>No student available</p>
          )}
        </div>
      </div>
      {studentList?.length > 0 && <div className="flex justify-end pt-8">
        <button
          onClick={handleSubmit}
          disabled={isSubmitting || selectedStudents.length === 0}
          className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-lg hover:bg-blue-700 transition-colors duration-200"
        >
          {isSubmitting ? "Submitting..." : "Submit"}
        </button>
      </div>}
    </Modal>
  );
};

export default AddToBatchModal;
