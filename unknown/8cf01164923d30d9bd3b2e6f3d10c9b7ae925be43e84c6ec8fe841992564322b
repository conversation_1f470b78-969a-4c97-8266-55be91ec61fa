import React, { useEffect, useState } from "react";
import Mo<PERSON> from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Button from "@/components/ui/Button";
import { Formik, Form, Field, ErrorMessage } from "formik";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal, setShowModal } from "@/features/commonSlice";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import {
  useGetApiQuery,
  useUpdateApiMutation,
} from "@/store/api/master/commonSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import Select from "react-select";
import Textarea from "@/components/ui/Textarea";
import * as yup from "yup";
import MultiSelectComponent from "@/components/ui/MultiSelectComponent";
import { toast } from "react-toastify";

const validationSchema = yup.object({
  title: yup
    .string()
    .max(50, "Should not be more than 50 characters")
    .min(3, "Should not be less than 3 characters")
    .required("Title is Required"),

  mcq_mark: yup.number().required("MCQ mark is Required"),
  course_id: yup.number().required("Course ID is Required"),
  written_mark: yup.number().required("Written mark is Required"),
  //   batch_ids: yup.array().required("Please select minimum one batch"),
});

const EditResult = () => {
  const dispatch = useDispatch();
  const { showEditModal, editData } = useSelector(
    (state) => state.commonReducer
  );
  const [courseId, setCourseId] = useState(editData?.course_id || "");
  const [errors, setErrors] = useState([]);
  const [batchList, setBatchList] = useState([]);
  const [updateApi, { isLoading }] = useUpdateApiMutation();
  const { data: courseList, isLoading: isCourseLoading } = useGetApiQuery(
    "admin/course-list?pagination=false"
  );

  const {
    data: batches,
    isLoading: isBatchLoading,
    isFetching: isMentorFetching,
  } = useGetApiQuery(
    courseId ? `admin/batches?pagination=false&course_id=${courseId}` : ""
  );

  useEffect(() => {
    if (batches) {
      setBatchList(batches);
    }
  }, [batches]);

  const onSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    // Format the values
    const formattedValues = {
      ...values,
      exam_date: new Date(values.exam_date).toISOString().split("T")[0],
      total_mark: Number(values.mcq_mark) + Number(values.written_mark),
      id: editData?.id,
    };

    console.log("Formatted Values:", formattedValues);

    // Append all fields to formData
    Object.entries(formattedValues).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        formData.append(key, JSON.stringify(value));
      } else {
        formData.append(key, value);
      }
    });

    const response = await updateApi({
      end_point: "admin/offline-exams/" + editData?.id,
      body: formData, // Use formData here
    });
    if(response.data.status){
        toast.success(response.data.message);
    }

    if (response.error) {
      setErrors(response.error?.data.errors || []);
    } else {
      resetForm();
      dispatch(setEditShowModal(false));
    }
  };

  // console.log(editData)
  return (
    <Modal
      activeModal={showEditModal}
      onClose={() => dispatch(setEditShowModal(false))}
      title="Edit Offline Exam"
      className="max-w-3xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setEditShowModal(false))}
        />
      }
    >
      {" "}
      <Formik
        validationSchema={validationSchema}
        initialValues={{
          course_id: editData?.course_id,
          title: editData?.title || "",
          mcq_mark: editData?.mcq_mark || "",
          written_mark: editData?.written_mark || "",
          total_mark: editData?.total_mark || "",
          //   duration: "",
          exam_date:
            editData?.exam_date || new Date().toISOString().split("T")[0],
          //   batch_ids: editData?.batch_ids || [],
        }}
        onSubmit={onSubmit}
      >
        {({ setFieldValue, values }) => (
          <Form>
            <div className="grid md:grid-cols-2 gap-4 my-3">
              <InputField
                label="Title"
                name="title"
                type="text"
                placeholder="Enter title"
                required
                error={errors?.title}
              />
              <div className="w-full mt-1.5">
                <label className="block text-gray-600 text-sm font-medium mb-2">
                  Select Course
                  <span className="text-red-500">*</span>
                </label>
                <Select
                  required
                  placeholder="Select Course"
                  value={
                    courseList?.find((course) => course.id === values.course_id)
                      ? {
                          value: values.course_id,
                          label: courseList.find(
                            (course) => course.id === values.course_id
                          )?.title,
                        }
                      : null
                  }
                  options={courseList?.map((course) => ({
                    value: course.id,
                    label: course.title,
                  }))}
                  name="course_id"
                  onChange={(selectedOption) => {
                    // Use setFieldValue to update the value correctly
                    setFieldValue("course_id", selectedOption.value);
                  }}
                />
                <ErrorMessage name="course_id">
                  {(msg) => (
                    <div className="text-red-500 text-sm mt-1">{msg}</div>
                  )}
                </ErrorMessage>
              </div>
              <NumberInput
                label="MCQ Mark"
                name="mcq_mark"
                placeholder="Enter mcq mark"
                error={errors?.mcq_mark}
                required
              />
              <NumberInput
                label="Written Mark"
                name="written_mark"
                placeholder="Enter written mark"
                error={errors?.written_mark}
                required
              />
              <NumberInput
                label="Total Mark"
                name="total_mark"
                placeholder="Enter total mark"
                value={Number(values.mcq_mark) + Number(values.written_mark)}
                error={errors?.total_mark}
                disabled
              />
              <DatePicker
                label="Exam Date"
                placeholder="YYYY-MM-DD"
                format="YYYY/MM/DD"
                name="exam_date"
                value={values.exam_date}
                onChange={(e) => {
                  const formattedDate = new Date().toISOString().split("T")[0];
                  setFieldValue("exam_date", formattedDate);
                }}
                error={errors?.exam_date}
              />
            </div>
            {/* <MultiSelectComponent
              label="Batch"
              name="batch_ids"
              placeholder="Select batch"
              options={batchList?.map((batch) => ({
                label: batch.name,
                value: batch.id,
              }))}
              valueKey="value"
              labelKey="label"
              onChange={(e) => console.log(e.target)}
              required
            /> */}
            <div className="mt-3">
              <Textarea
                label="Description"
                name="description"
                type="text"
                placeholder="Enter description"
                className=""
                error={errors?.duration}
              />
            </div>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default EditResult;
