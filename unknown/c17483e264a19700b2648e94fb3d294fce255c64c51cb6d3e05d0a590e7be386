import React, { useState } from "react";
import { Link } from "react-router-dom";
import LoginForm from "./common/login-form";
import Social from "./common/social";
import SideInfo from "./common/side-info";
import useDarkMode from "@/hooks/useDarkMode";

// image import
import LogoWhite from "@/assets/images/logo/logo-white.svg";
import Logo from "@/assets/images/logo/bb-logo.png";

const login = () => {
  const [isDark] = useDarkMode();
  const [error, setError] = useState(null);

  const handleResponse = (message) => {
    setError(message);
  }

  return (
    <div className="loginwrapper">
      <div className="lg-inner-column">
        <SideInfo />
        <div className="right-column relative">
          <div className="inner-content h-full flex flex-col bg-white dark:bg-slate-800">
            <div className="auth-box h-full flex flex-col justify-center">
              <div className="mobile-logo text-center mb-6 lg:hidden block">
                <Link to="/">
                  <img
                    src={isDark ? LogoWhite : Logo}
                    alt=""
                    className="mx-auto"
                  />
                </Link>
              </div>
              <div className="text-center 2xl:mb-10 mb-4">
                <h4 className="font-medium">Sign in</h4>
                <div className="text-slate-500 text-base">
                  Sign in to EduPack admin panel to access your LMS
                </div>
                {error && <p className="mt-2 text-green-500">{error}</p>}
              </div>
              <LoginForm handleResponse={handleResponse} setError={setError} />
            
              <div className="md:max-w-[345px] mx-auto font-normal text-slate-500 dark:text-slate-400 mt-12 text-sm">
                Don’t have an account? Contact With Admins
              
              </div>
            </div>
            <div className="auth-footer text-center">
              Copyright 2024, EduPack All Rights Reserved.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default login;
