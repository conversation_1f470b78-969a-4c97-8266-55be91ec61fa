import React, { useState, useEffect } from "react";
import AddBatch from "./AddBatch";
import EditBatch from "./EditBatch";
import BatchDetails from "./BatchDetails";
import Icons from "@/components/ui/Icon";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import { Link, useNavigate, useParams } from "react-router-dom";
import DeleteBatch from "./DeleteBatch";
import Card from "@/components/ui/Card";
import Pagination from "@/components/partials/common-table/pagination";
import Loading from "@/components/Loading";
import Select from "react-select";

const BatchList = ({ data }) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [isAdding, setIsAdding] = useState(false);
  const [selectedBatch, setSelectedBatch] = useState(null);
  const [selectedBatchDelete, setSelectedBatchDelete] = useState(null);
  const [isViewingDetails, setIsViewingDetails] = useState(false);
  const [isEditBatch, setIsEditBatch] = useState(false);
  // const [params, setParams] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [courseId, setCourseId] = useState("");
  
  const {
    data: courseList,
    isLoading,
    error,
  } = useGetApiQuery(`admin/course-list-for-filter`);
  const selectedCourse = courseList?.find((course) => course.id === courseId);
  // console.log(selectedCourse)
  
  const params = `?current_page=${currentPage}` + `${selectedCourse?.id ? ('&course_id=' + selectedCourse.id) : ""}`;
  const batch = useGetApiQuery(`/admin/batches${params}`);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handleAddBatchClick = () => {
    navigate("/batch-create");
  };
  const handleFormClose = () => setIsAdding(false);
  const handleEditFormClose = () => setIsEditBatch(false);
  const handleViewDetailsClose = () => setIsViewingDetails(false);
  const handleDeleteModalClose = () => setShowDeleteModal(false);

  const handleEditBatchClick = (batchId) => {
    const batchToEdit = batch?.data?.data?.find((item) => item.id === batchId);
    setSelectedBatch(batchToEdit);
    setIsEditBatch(true); // Open the form in edit mode
  };

  const handleViewBatchDetails = (batchId) => {
    navigate("/batch-details/" + batchId);
    // const batchToView = batch?.data?.data?.find((item) => item.id === batchId);
    // setSelectedBatch(batchToView);
    // setIsViewingDetails(true);
  };

  useEffect(() => {
    if (!isAdding && !isEditBatch && !isViewingDetails) {
      setSelectedBatch(null); // Reset selected batch when closing forms
    }
  }, [isAdding, isEditBatch, isViewingDetails]);

  const handleBatchDelete = (batchId) => {
    const batchToDelete = batch?.data?.data?.find(
      (item) => item.id === batchId
    );
    setSelectedBatchDelete(batchToDelete);
    setShowDeleteModal(true);
  };

  const handlePageChange = (value) => {
    setCurrentPage(value);
  };

  return (
    <Card>
      <div className="rounded-lg pb-3 pt-3">
        {!isAdding && !isViewingDetails && !isEditBatch && (
          <div className="border-b-2">
            <div className="flex justify-between p-3">
              <h1 className="text-3xl font-semibold text-gray-900">
                Batch List
              </h1>
              <span className="flex items-center gap-4">
                <Select
                  required
                  className="min-w-56"
                  placeholder="Select Course"
                  options={courseList?.map((course) => ({
                    value: course.id,
                    label: course.title,
                  }))}
                  name="course_id"
                  onChange={(e) => {
                    // console.log(e.value);
                    setCourseId(e.value);
                  }}
                />
                <Link
                  to={"/batch-create"}
                  // onClick={handleAddBatchClick}
                  className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg shadow-lg hover:bg-green-700 transition-colors duration-200 space-x-2"
                >
                  <span>Add Batch</span>
                </Link>
              </span>
            </div>
          </div>
        )}

        <div className="p-3">
          {isEditBatch ? (
            <EditBatch
              onClose={handleEditFormClose}
              selectedBatch={selectedBatch}
            />
          ) : isViewingDetails ? (
            <BatchDetails
              onClose={handleViewDetailsClose}
              batch={selectedBatch}
            />
          ) : (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 mt-3">
                {batch?.data?.data?.length > 0 ? (
                  batch.data.data.map((item) => (
                    <div
                      key={item.id}
                      onClick={() => handleViewBatchDetails(item.id)}
                      className="group bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:shadow-lg overflow-hidden cursor-pointer transition-transform duration-300 hover:scale-[1.02]"
                    >
                      <div className="relative w-full aspect-video">
                        <img
                          src={`${import.meta.env.VITE_ASSET_HOST_URL}${
                            item.image
                          }`}
                          alt={item.name}
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-gray-600 opacity-0 group-hover:opacity-40 transition-opacity duration-300" />
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditBatchClick(item.id); // Open the form with data for editing
                          }}
                          className="absolute top-2 right-2 p-2 bg-white dark:bg-gray-700 rounded-full text-gray-600 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-300 opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0"
                        >
                          <Icons
                            icon="line-md:edit-twotone"
                            width="24"
                            height="24"
                            className="text-blue-500"
                          />
                        </button>
                        <button
                          onClick={(e) => {
                            handleBatchDelete(item.id);
                            e.stopPropagation();
                          }}
                          className="absolute top-14 right-2 p-2 bg-white dark:bg-gray-700 rounded-full text-gray-600 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-300 opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0"
                        >
                          <Icons
                            icon="material-symbols:delete-outline"
                            width="24"
                            height="24"
                            className="text-red-500"
                          />
                        </button>
                      </div>
                      <div className="p-4">
                        <h6 className="text-lg font-semibold text-gray-900 dark:text-white overflow-hidden text-ellipsis whitespace-nowrap mb-2">
                        <b>Batch:</b> {item.name}
                        </h6>
                        <small><b>Course:</b> {item?.course_title}</small>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-900 dark:text-white font-medium">
                            Capacity: {item.capacity}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="col-span-full text-center py-8 text-gray-600 dark:text-gray-400">
                    No batches available
                  </div>
                )}
              </div>
              {/* {console.log(batch?.data)} */}
              {batch?.data?.total > 0 && (
                <div className="flex justify-end">
                  <Pagination
                    totalPages={Math.ceil(
                      batch?.data?.total / batch?.data?.per_page
                    )}
                    currentPage={batch?.data?.current_page + 1}
                    handlePageChange={handlePageChange}
                  />
                </div>
              )}
            </>
          )}
        </div>
        {
          <DeleteBatch
            data={selectedBatchDelete}
            setShowDeleteModal={setShowDeleteModal}
            showDeleteModal={showDeleteModal}
            onClose={handleDeleteModalClose}
          />
        }
      </div>
    </Card>
  );
};

export default BatchList;
