import React, { useState, useEffect } from "react";
import Textinput from "@/components/ui/Textinput";
import { debounce } from "lodash";

const GlobalFilter = ({ filter, setFilter }) => {
  const [value, setValue] = useState(filter);

  // Wrap debounced function in useCallback to maintain the same instance
  const debouncedFilter = React.useCallback(
    debounce((value) => {
      setFilter("?search=" + (value || ""));
    }, 500),
    [setFilter]
  );

  // Update the debounced value whenever input changes
  const onChange = (e) => {
    setValue(e.target.value);
    debouncedFilter(e.target.value);
  };

  // Cleanup debounce on component unmount
  useEffect(() => {
    return () => {
      debouncedFilter.cancel();
    };
  }, [debouncedFilter]);

  return (
    <div>
      <Textinput
        value={value || ""}
        onChange={onChange}
        placeholder="search..."
      />
    </div>
  );
};

export default GlobalFilter;
