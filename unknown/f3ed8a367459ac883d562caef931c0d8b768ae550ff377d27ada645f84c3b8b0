import React, { useState, useEffect } from "react";
import InputField from "@/components/ui/InputField";
import FileInput from "@/components/ui/Fileinput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import Textarea from "@/components/ui/Textarea";
import Radio from "@/components/ui/Radio";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import { Formik, Form, Field } from "formik";
import { initialValues, validationSchema } from "./formVideo";
import { useDispatch, useSelector } from "react-redux";
import { useVideoCreateOrUpdateMutation } from "@/store/api/master/rowContentVideoContentlistSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import SimpleBar from "simplebar-react";
import Icon from "@/components/ui/Icon";
// import ReactPlayer from 'react-player';

const editVideo = ({ isSidebarOpen, setIsSidebarOpen, category }) => {
  const [apiParam, setApiParam] = useState("");
  const [postApi, { isLoading, isError, error, isSuccess }] = usePostApiMutation();
  const dispatch = useDispatch();
  const [isActive, setIsActive] = useState(true);
  const [isFree, setIsFree] = useState(false);

  const [url, setUrl] = useState("");
  const [title, setTitle] = useState("");
  const [duration, setDuration] = useState("");
  const handleDuration = (duration, setFieldValue) => {
    setDuration(Math.floor(duration));
    setFieldValue("duration", Math.floor(duration));
  };

  const setTitleFromURL = (url, setFieldValue) => {
    const title = url.split('/').pop().replace(/-/g, ' ').replace(/\.[^/.]+$/, ""); // Basic example to extract and clean the title
    setFieldValue("title", title);
  };

  // const fetchYoutubeTitle = async (videoUrl, setFieldValue) => {
  //   try {
  //     // Use noembed to fetch video title
  //     const response = await fetch(`https://noembed.com/embed?url=${videoUrl}`);
  //     const data = await response.json();

  //     if (data && data.title) {
  //       setTitle(data.title);
  //       setFieldValue("title", data.title);
  //     } else {
  //       console.error("No title found for the given URL.");
  //     }
  //   } catch (error) {
  //     console.error("Error fetching YouTube title:", error);
  //   }
  // };

  // const handleUrlChange = (e, setFieldValue) => {
  //   const newUrl = e;
  //   setUrl(newUrl);
  //   fetchYoutubeTitle(newUrl, setFieldValue); // Fetch title when the URL is updated
  // };


  const onSubmit = async (values, { resetForm }) => {
    let formData = new FormData();
    formData.append("course_id", category.course_id);
    formData.append("course_category_id", category.id);
    formData.append("title", values.title);
    formData.append("title_bn", values.title);
    formData.append("author_name", values.author_name);
    formData.append("author_details", values.author_details);
    formData.append("description", values.description);
    formData.append("raw_url", values.raw_url);
    formData.append("s3_url", values.s3_url);
    formData.append("youtube_url", values.youtube_url);
    formData.append("thumbnail", values.thumbnail);
    formData.append("download_url", values.download_url);
    formData.append("duration", values.duration || duration);
    formData.append("sequence", 1);
    formData.append("price", 0);
    formData.append("rating", 0);
    formData.append("is_active", isActive ? 1 : 0);
    formData.append("is_free", isFree ? 1 : 0);

    const response = await postApi({
      end_point: "admin/chapter-video-save-or-update",
      body: formData,
    });

    if (response.error) {
      resetForm({ values, errors: response.error.data.errors });
    } else {
      resetForm();
      setIsSidebarOpen(false);
    }
    // setIsSidebarOpen(false);
  };

  return (
    <>
      {isSidebarOpen && (
        <div className={`fixed right-0 top-0 w-[450px] bg-white dark:bg-slate-800 h-screen z-[9999] shadow-base2 border border-slate-200 dark:border-slate-700 transition-all duration-150`}>
          <SimpleBar className="px-6 h-full">
            <header className="flex items-center justify-between border-b border-slate-100 dark:border-slate-700 px-6 py-[25px]">
              <div>
                <span className="block text-xl text-slate-900 font-medium dark:text-[#eee]">Add New Video</span>
              </div>
              <div className="cursor-pointer text-2xl text-slate-800 dark:text-slate-200" onClick={() => setIsSidebarOpen(false)}>
                <Icon icon="heroicons-outline:x" />
              </div>
            </header>
            <Formik
              validationSchema={validationSchema}
              initialValues={initialValues}
              onSubmit={onSubmit}
            >
              {({ values, setFieldValue }) => (
                <Form>
                  <div className="grid md:grid-cols-1 gap-4 my-3">
                    <label className="font-bold leading-6 block">Select Video Type:<span className="text-red-500 ml-1">*</span></label>
                    <div className="flex items-center ml-4 gap-8 mt-2">
                      <Radio label="Raw URL" name="video_type" value="raw_url" checked={values.video_type === "raw_url"} onChange={(e) => setFieldValue('video_type', e.target.value)} />
                      <Radio label="S3 URL" name="video_type" value="s3_url" checked={values.video_type === "s3_url"} onChange={(e) => setFieldValue('video_type', e.target.value)} />
                      <Radio label="Youtube URL" name="video_type" value="youtube_url" checked={values.video_type === "youtube_url"} onChange={(e) => setFieldValue('video_type', e.target.value)} />
                    </div>
                  </div>

                  {values.video_type === "raw_url" && <InputField className="mt-4" label="Raw URL" name="raw_url" type="text" placeholder="Enter Raw URL" required  />}
                  {values.video_type === "s3_url" && <InputField className="mt-4" label="S3 URL" name="s3_url" type="text" placeholder="Enter S3 URL" required  />}
                  {values.video_type === "youtube_url" && <InputField className="mt-4" label="Youtube URL" name="youtube_url" type="text" placeholder="Enter Youtube URL" required />}

                  {/* {values.s3_url && <ReactPlayer 
                    url={values.s3_url} 
                    controls={true}
                    width="100%" 
                    height="100%" 
                    onDuration={(duration) => handleDuration(duration, setFieldValue)}
                  />}
                  {values.youtube_url && <ReactPlayer 
                    url={values.youtube_url} 
                    controls={true}
                    width="100%" 
                    height="100%" 
                    onDuration={(duration) => handleDuration(duration, setFieldValue)}
                  />}
                  {values.raw_url && <ReactPlayer 
                    url={values.raw_url} 
                    controls={true}
                    width="100%" 
                    height="100%" 
                    onDuration={(duration) => handleDuration(duration, setFieldValue)}
                  />} */}
                  <InputField classLabel="mt-4" label="Title" name="title" type="text" placeholder="Enter Name" required />

                  <>
                    <label
                      className={`block capitalize mt-4 pb-2`}
                    >
                      Thumbnail
                      <span className="text-red-500">*</span>
                    </label>
                    <FileInput
                      className="py-2 "
                      name="thumbnail"
                      accept="image/*"
                      type="file"
                      placeholder="Thumbnail"
                      preview={true}
                      selectedFile={values.thumbnail}
                      onChange={(e) => {
                        setFieldValue("thumbnail", e.target.files[0]);
                      }}
                    />
                  </>
                  <Textarea classLabel="mt-4 pb-2" label="Description" name="description" placeholder="Enter Description" onChange={(e) => setFieldValue("description", e.target.value)} />

                  <div className="grid md:grid-cols-2 gap-4 my-3">
                    <Switch className="mt-4" label="Active" activeClass="bg-success-500" value={isActive} onChange={() => setIsActive(!isActive)} />
                    <Switch className="mt-4" label="Free" activeClass="bg-success-500" value={isFree} onChange={() => setIsFree(!isFree)} />
                  </div>

                  <div className="ltr:text-right rtl:text-left my-5">
                    <Button isLoading={isLoading} type="submit" className="btn text-center btn-primary">Submit</Button>
                  </div>
                </Form>
              )}
            </Formik>
          </SimpleBar>
        </div>
      )}
    </>
  );
};

export default editVideo;
