import React, { useState } from "react";
import InputField from "@/components/ui/InputField";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { Formik, Form } from "formik";
import SimpleBar from "simplebar-react";
import { initialValues, validationSchema } from "./formSettings";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { useParams } from "react-router-dom";
import Icon from "@/components/ui/Icon";

const Create = ({ isSidebarOpen, setIsSidebarOpen }) => {
  const [postApi, { isLoading }] = usePostApiMutation();
  const { id } = useParams();
  const [isActive, setIsActive] = useState(true);

  const onSubmit = async (values, { resetForm }) => {
    let formData = new FormData();
    formData.append("course_id", id);
    formData.append("name", values.name);
    formData.append("name_bn", values.name_bn);
    formData.append("sequence", values.sequence || 1);
    formData.append("is_active", isActive ? 1 : 0);

    await postApi({
      end_point: "admin/course-category",
      body: formData,
    });
    resetForm();
    setIsSidebarOpen(false);
  };

  return (
    <>
      {isSidebarOpen && (
        <div
          className={`fixed right-0 top-0 w-[450px] bg-white dark:bg-slate-800 h-screen z-[9999] shadow-base2 border border-slate-200 dark:border-slate-700 transition-all duration-150`}
        >
          <SimpleBar className="px-6 h-full">
            <header className="flex items-center justify-between border-b border-slate-100 dark:border-slate-700 px-6 py-[25px]">
              <div>
                <span className="block text-xl text-slate-900 font-medium dark:text-[#eee]">
                  Add New Module
                </span>
              </div>
              <div
                className="cursor-pointer text-2xl text-slate-800 dark:text-slate-200"
                onClick={() => setIsSidebarOpen(false)}
              >
                <Icon icon="heroicons-outline:x" />
              </div>
            </header>
            <Formik
              validationSchema={validationSchema}
              initialValues={initialValues}
              onSubmit={onSubmit}
            >
              {({ setFieldValue }) => (
                <Form>
                  <div className="grid md:grid-cols-1 gap-4">
                    <InputField
                      label="Module Name"
                      name="name"
                      type="text"
                      placeholder="Enter Module Name"
                      required
                    />
                    {/* <InputField
                      label="Bangla Category Name"
                      name="name_bn"
                      type="text"
                      placeholder="Enter Bangla Category Name"
                    /> */}

                    {/* <NumberInput
                      label="Sequence"
                      name="sequence"
                      placeholder="Sequence"
                      onChange={(e) =>
                        setFieldValue("sequence", e.target.value)
                      }
                    /> */}
                  </div>

                  <div className="mt-4">
                    <Switch
                      label="Active"
                      activeClass="bg-success-500"
                      value={isActive}
                      name="is_active"
                      onChange={() => setIsActive(!isActive)}
                    />
                  </div>

                  <div className="ltr:text-right rtl:text-left mt-5">
                    <Button
                      isLoading={isLoading}
                      type="submit"
                      className="btn text-center btn-primary"
                    >
                      Submit
                    </Button>
                  </div>
                </Form>
              )}
            </Formik>
          </SimpleBar>
        </div>
      )}
    </>
  );
};

export default Create;
