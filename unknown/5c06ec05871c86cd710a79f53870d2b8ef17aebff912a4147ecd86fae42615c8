import React from "react";
import * as yup from "yup";

export const initialValues = { 
    sequence: '',
    name: '',
    name_bn: '',
    is_active: '',
};

export const validationSchema =  yup.object({
    name: yup.string().max(50, "Should not be more than 50 characters").min(3, "Should not be less than 3 characters").required("Name is Required"),
    name_bn: yup.string().max(50, "Should not be more than 50 characters").min(3, "Should not be less than 3 characters")
})
