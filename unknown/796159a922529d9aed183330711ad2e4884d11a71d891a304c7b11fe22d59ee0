import React, { useState,useEffect } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Create from "./create";
import Edit from "./edit";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";
import { useParams } from "react-router-dom";
import Icon from "@/components/ui/Icon"; // Assuming Icon component is used for icons

const Index = ({ data: initialData }) => {
  const [data, setData] = useState(initialData);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const dispatch = useDispatch();

  const handleFeatureUpdate = (newFeatures) => {
    setData(newFeatures);
  };

  const openEditModal = (item) => {
    dispatch(setEditData(item));
    dispatch(setEditShowModal(true));
  };

  const handleDelete = (item) => {
    console.log("Delete item", item);
  };

  return (
    <div className="flex flex-col p-5">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">Features</h2>
        <Button
          onClick={() => setIsSidebarOpen(true)}
          className="btn-primary btn-sm"
        >
          Add Features
        </Button>
      </div>
      <div className="grid gap-2">
        {!data.length && (
          <p className="text-sm text-red-500">No Feature has been added</p>
        )}
        <ul className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
          {data?.map((item) => (
            <li key={item.id || item.title} className="flex items-center gap-4 p-2 bg-gray-100 rounded-md">
              {item.icon && (
                <img
                  src={`${import.meta.env.VITE_ASSET_HOST_URL}${item.icon}`}
                  className="w-8 h-8"
                  alt={item.title}
                />
              )}
              <p className="flex-1">{item.title}</p>
            </li>
          ))}
        </ul>
      </div>
      <Create
        isSidebarOpen={isSidebarOpen}
        setIsSidebarOpen={setIsSidebarOpen}
        data={data}
        onFeatureUpdate={handleFeatureUpdate}
      />
    </div>
  );
};
export default Index;
