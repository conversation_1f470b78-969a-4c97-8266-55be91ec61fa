import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Fileinput from "@/components/ui/Fileinput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { Formik, Form, Field } from 'formik';
import { initialValues, validationSchema} from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal } from "@/features/commonSlice";
import { useMenuCreateOrUpdateMutation } from "@/store/api/master/menuSlice";
import { useDeleteApiMutation } from "@/store/api/master/commonSlice";
const Delete = ({showDeleteModal, setShowDeleteModal, data}) => {

    const dispatch = useDispatch()


    const [deleteApi, { isLoading, isError, error, isSuccess }] = useDeleteApiMutation();
    const onSubmit = async () => {
        const response = await deleteA<PERSON>({end_point: '/admin/course-category/' + data?.id, body: {}})
        setShowDeleteModal(false);
    }
    return (

    <Modal
    activeModal={showDeleteModal}
    onClose={() => setShowDeleteModal(false)}
    title="Delete Menu"
    className="max-w-5xl"
    footer={
        <Button
            text="Close"
            btnClass="btn-primary"
            onClick={() => setShowDeleteModal(false)}
        />
        }
    >        

    <h3 className="text-center">Are you sure?</h3>
    <p className="text-center text-slate-500 text-sm mt-4">You are going delete <b>"{data?.name}"</b> menu. 
        Once you have done this, there is no going back. </p>

    <div className="ltr:text-right rtl:text-left mt-5 gap-4">
        <Button 
            // isLoading={isLoading}
            type="button"
            className="btn text-center btn-primary mr-4"
            onClick={onSubmit} 
        >
            Cancel
        </Button>
        <Button 
            isLoading={isLoading}
            type="button"
            className="btn text-center btn-danger"
            onClick={onSubmit} 
        >
            Delete
        </Button>
    </div>
    {/* <Button text="Delete" btnClass="btn btn-danger" /> */}
    </Modal>
    );
}

export default Delete;
