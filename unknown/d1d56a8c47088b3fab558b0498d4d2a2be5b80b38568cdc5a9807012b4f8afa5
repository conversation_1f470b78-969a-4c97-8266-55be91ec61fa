import { apiSlice } from "../apiSlice";
export const rowContentSubjectListApi = apiSlice.injectEndpoints({
  reducerPath: "rowContentSubjectListApi",
  tagTypes: ["SubjectList"],
  endpoints: (builder) => ({
    getSubjectList: builder.query({
      query: (params) => ({
        url: "admin/subject-list", 
        method: "GET",
        params:params
      }),
      providesTags: ["SubjectList"],
    }),
    subjectCreateOrUpdate: builder.mutation({
      query: (body) => {
        return {
          url: `admin/subject-save-or-update`,
          method: "POST",
          body: body,
        };
      },
      invalidatesTags: ["SubjectList"],
    }),
  }),
});

export const { useGetSubjectListQuery, useSubjectCreateOrUpdateMutation } = rowContentSubjectListApi;
