import React from "react";
import * as yup from "yup";

export const initialValues = { 
    title: '',
    url: '',
    description: '',
};

export const validationSchema = yup.object({
    title: yup.string().required("Title is required").max(255, "Title cannot exceed 255 characters"),
    url: yup.string().required("URL is required").url("Must be a valid URL").max(500, "URL cannot exceed 500 characters"),
    description: yup.string().nullable(),
});

