import React, { useState } from "react";
import { useGetApiQuery, usePostApiMutation } from "@/store/api/master/commonSlice";
import { useSelector } from "react-redux";

const PaymentCollection = () => {
  const { user } = useSelector((state) => state.auth);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [paymentAmounts, setPaymentAmounts] = useState({}); 
  const [studentPayments, setStudentPayments] = useState([]);
  const [loadingPayments, setLoadingPayments] = useState(false);
  const [postApi, { isLoading }] = usePostApiMutation();

  const {
    data: students,
    isLoading: studentsLoading,
  } = useGetApiQuery(
    searchTerm
      ? `admin/all-student-list-admin?pagination=false&search=${searchTerm}`
      : null,
    { skip: !searchTerm }
  );

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleStudentSelect = async (student) => {
    setSelectedStudent(student);
    setLoadingPayments(true);
    try {
      const response = await fetch(
        `${import.meta.env.VITE_HOST_URL}admin/student-payments?id=${student.id}`,
        {
          headers: {
            Authorization: `Bearer ${user?.token}`,
          },
        }
      );
      const data = await response.json();
      setStudentPayments(data.data.payments || []);
    } catch (error) {
      console.error("Error fetching student payments:", error);
      setStudentPayments([]);
    } finally {
      setLoadingPayments(false);
    }
  };

  const handlePaymentChange = (paymentId, amount) => {
    setPaymentAmounts({
      ...paymentAmounts,
      [paymentId]: amount,
    });
  };

  const handlePaymentSubmit = async (paymentId) => {
    const paymentAmount = paymentAmounts[paymentId];

    if (!paymentAmount || paymentAmount <= 0) {
      alert("Please enter a valid amount.");
      return;
    }

    const paymentData = {
      student_id: selectedStudent.id,
      amount: paymentAmount,
      payment_id: paymentId,
    };

    try {
      const response = await postApi({
        end_point: "admin/collect-payment",
        body: paymentData,
      });
      if (response.error) {
        alert("Error submitting payment.");
        console.error(response.error);
      } else {
        handleStudentSelect(selectedStudent);
      }
    } catch (error) {
      console.error("Error submitting payment:", error);
    }
  };

  return (
    <div className="px-4 py-6">
      <h1 className="text-2xl font-bold mb-6">Payment Collection</h1>

      <div className="bg-white shadow-md rounded-lg p-6">
        {/* Search Section */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">Search Student</label>
          <input
            type="text"
            className="border rounded-md px-4 py-2 w-full"
            placeholder="Enter student name or ID"
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>

        {/* Display Search Results */}
        {studentsLoading ? (
          <p>Loading students...</p>
        ) : students?.length > 0 ? (
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-4">Search Results</h2>
            <ul className="space-y-2">
              {students.map((student) => (
                <li
                  key={student.id}
                  className={`p-4 border rounded-lg cursor-pointer ${
                    selectedStudent?.id === student.id ? "bg-blue-100" : ""
                  }`}
                  onClick={() => handleStudentSelect(student)}
                >
                  <div className="flex items-center space-x-4">
                    {student.image ? (
                      <img
                        src={import.meta.env.VITE_ASSET_HOST_URL + student.image}
                        alt={student.name}
                        className="w-12 h-12 rounded-full border object-cover"
                      />
                    ) : (
                      <div className="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center text-gray-500 font-bold">
                        {student.name[0]}
                      </div>
                    )}
                    <div className="flex-1">
                      <h3 className="text-lg font-medium text-gray-800">
                        {student.name}
                      </h3>
                      <p className="text-sm text-gray-500">{student.student_id}</p>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <p className="text-gray-500">No students found.</p>
        )}

        {/* Payment Section */}
        {selectedStudent && (
          <div className="mt-6">
            <h2 className="text-lg font-semibold mb-4">Selected Student</h2>
            <div className="p-4 border rounded-lg bg-gray-50">
              <h3 className="text-xl font-bold">{selectedStudent.name}</h3>
              <p className="text-sm text-gray-500">ID: {selectedStudent.student_id}</p>
              <p className="text-sm text-gray-500">Contact: {selectedStudent.contact_no}</p>
            </div>

            {/* Display Payments */}
            <div className="mt-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-800">Course & Payment</h2>
              <div className="space-y-4">
                {studentPayments?.length > 0 ? (
                  studentPayments.map((payment) => (
                    <div
                      key={payment.id}
                      className="p-4 border rounded-lg shadow-sm bg-white flex flex-col sm:flex-row sm:justify-between sm:items-center"
                    >
                      {/* Payment Details */}
                      <div className="flex-1 space-y-2">
                        <h3 className="text-xl font-medium text-gray-800">
                          {payment.course_title}
                        </h3>
                        <p className="text-sm text-gray-600">
                          <span className="font-semibold">Paid Amount:</span> {payment.paid_amount}
                        </p>
                        <p className="text-sm text-gray-600">
                          <span className="font-semibold">Payable Amount:</span> {payment.payable_amount}
                        </p>
                        <p
                          className={`text-sm font-medium ${
                            payment.payable_amount - payment.paid_amount > 0
                              ? "text-red-500"
                              : "text-green-500"
                          }`}
                        >
                          {payment.payable_amount - payment.paid_amount > 0
                            ? `Due: ${payment.payable_amount - payment.paid_amount}`
                            : "Paid"}
                        </p>
                      </div>

                      {/* Payment Input Section */}
                      <div className="mt-4 sm:mt-0 sm:ml-6 text-center sm:text-right">
                        <input
                          type="number"
                          className="mt-1 border rounded-md px-4 py-2 w-full sm:w-32 text-sm"
                          placeholder="Enter amount"
                          value={paymentAmounts[payment.id] || ""}
                          onChange={(e) =>
                            handlePaymentChange(payment.id, e.target.value)
                          }
                        />
                        <button
                          onClick={() => handlePaymentSubmit(payment.id)}
                          className="mt-2 bg-blue-500 text-white px-4 py-2 rounded-md text-sm"
                          disabled={isLoading}
                        >
                          Submit
                        </button>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500">No payment history found.</p>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentCollection;
