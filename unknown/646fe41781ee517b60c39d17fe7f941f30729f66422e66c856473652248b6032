import React, { useEffect, useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Button from "@/components/ui/Button";
import { Formik, Form, Field, ErrorMessage } from "formik";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
// import Select from "@/components/ui/Select";
import Select from "react-select";
import Textarea from "@/components/ui/Textarea";
import * as yup from "yup";
import MultiSelectComponent from "@/components/ui/MultiSelectComponent";
import InputSelect from "@/components/ui/InputSelect";

const validationSchema = yup.object({
  title: yup
    .string()
    .max(50, "Should not be more than 50 characters")
    .min(3, "Should not be less than 3 characters")
    .required("Title is Required"),

  mcq_mark: yup.number().required("MCQ mark is Required"),
  course_id: yup.number().required("Please select a course"),
  // written_mark: yup.number().required("Written mark is Required"),
  // total_mark: yup.number().required("Total mark is Required"),
  //   duration: yup.number().required("Duration is Required"),
  //   exam_date: yup.date().required("Exam date is Required"),
  batch_ids: yup.array().required("Please select minimum one batch"),
});

const CreateExam = () => {
  const [courseId, setCourseId] = useState("");
  const [errors, setErrors] = useState([]);
  const [batchList, setBatchList] = useState([]);
  const [postApi, { isLoading }] = usePostApiMutation();
  const { data: courseList, isLoading: isCourseLoading } =
    useGetApiQuery("admin/course-list?pagination=false");

  const {
    data: batches,
    isLoading: isBatchLoading,
    isFetching: isMentorFetching,
  } = useGetApiQuery(
    courseId ? `admin/batches?pagination=false&course_id=${courseId}` : ""
  );

  useEffect(() => {
    if (batches) {
      setBatchList(batches);
    }
  }, [batches]);

  const dispatch = useDispatch();
  const { showModal } = useSelector((state) => state.commonReducer);

  const onSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    // Format the values
    const formattedValues = {
      ...values,
      exam_date: new Date(values.exam_date).toISOString().split("T")[0],
      total_mark: Number(values.mcq_mark)  + Number(values.written_mark)
    };

    console.log("Formatted Values:", formattedValues);

    // Append all fields to formData
    Object.entries(formattedValues).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        formData.append(key, JSON.stringify(value));
        // Append array values individually
        // value.forEach((item) => formData.append(`${key}[]`, item));
      } else {
        formData.append(key, value);
      }
    });

    const response = await postApi({
      end_point: "admin/offline-exams",
      body: formData, // Use formData here
    });

    if (response.error) {
      setErrors(response.error?.data.errors || []);
    } else {
      resetForm();
      dispatch(setShowModal(false));
    }
  };

  //   console.log(batches, batchList)
  return (
    <Modal
      activeModal={showModal}
      onClose={() => dispatch(setShowModal(false))}
      title="Add Offline Exam"
      className="max-w-3xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setShowModal(false))}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={{
          course_id: "",
          title: "",
          mcq_mark: "",
          written_mark: "",
          total_mark: "",
          //   duration: "",
          exam_date: new Date().toISOString().split("T")[0],
          batch_ids: [],
        }}
        onSubmit={onSubmit}
      >
        {({ setFieldValue, values }) => (
          <Form>
            <div className="grid md:grid-cols-2 gap-4 my-3">
              <InputField
                label="Title"
                name="title"
                type="text"
                placeholder="Enter title"
                required
                error={errors?.title}
              />
              <div className="w-full mt-1.5">
                <label className="block text-gray-600 text-sm font-medium mb-2">
                  Select Course
                  <span className="text-red-500">*</span>
                </label>
                <Select
                  required
                  placeholder="Select Course"
                  options={courseList?.map((course) => ({
                    value: course.id,
                    label: course.title,
                  }))}
                  name="course_id"
                  onChange={(e) => {
                    // console.log(e.value);
                    setCourseId(e.value);
                    setFieldValue("course_id", e.value);
                  }}
                />
                <ErrorMessage name="course_id">
                  {(msg) => (
                    <div className="text-red-500 text-sm mt-1">{msg}</div>
                  )}
                </ErrorMessage>
              </div>
              <NumberInput
                label="MCQ Mark"
                name="mcq_mark"
                placeholder="Enter mcq mark"
                error={errors?.mcq_mark}
                required
              />
              <NumberInput
                label="Written Mark"
                name="written_mark"
                placeholder="Enter written mark"
                error={errors?.written_mark}
                // required
              />
              <NumberInput
                label="Total Mark"
                name="total_mark"
                placeholder="Enter total mark"
                value={Number(values.mcq_mark) + Number(values.written_mark)}
                error={errors?.total_mark}
                disabled
              />
              
              {/* <NumberInput
                label="Duration"
                name="duration"
                placeholder="Enter exam duration"
                error={errors?.duration}
              /> */}
              <DatePicker
                label="Exam Date"
                placeholder="YYYY-MM-DD"
                format="YYYY/MM/DD"
                name="exam_date"
                onChange={(e) => {
                  const formattedDate = new Date()
                    .toISOString()
                    .split("T")[0];
                  setFieldValue("exam_date", formattedDate);
                }}
                error={errors?.exam_date}
              />
            </div>
            <MultiSelectComponent
              label="Batch"
              name="batch_ids"
              placeholder="Select batch"
              options={batchList?.map((batch) => ({
                label: batch.name,
                value: batch.id,
              }))}
              valueKey="value"
              labelKey="label"
              onChange={(e) => console.log(e.target)}
              required
            />
            <div className="mt-3">
              <Textarea
                label="Description"
                name="description"
                type="text"
                placeholder="Enter description"
                className=""
                error={errors?.duration}
              />
            </div>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default CreateExam;
