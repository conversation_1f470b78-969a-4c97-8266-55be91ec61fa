import Button from "@/components/ui/Button";
import Fileinput from "@/components/ui/Fileinput";
import InputField from "@/components/ui/InputField";
import Modal from "@/components/ui/Modal";
import {
  useGetApiQuery,
  usePostApiMutation,
} from "@/store/api/master/commonSlice";
import { Form, Formik, FieldArray } from "formik";
import * as Yup from "yup";
import { useState } from "react";
import Select from "react-select";

const validationSchema = Yup.object().shape({
  name: Yup.string().nullable().required("Type is required."),
  icon: Yup.string().nullable().required("Type is required."),
  items: Yup.array()
    .of(
      Yup.object()
        .shape({
          field_name: Yup.string().trim().required("Field name is required."),
          field_value: Yup.string().trim().nullable(),
          image: Yup.mixed().nullable(),
        })
        .test(
          "field-value-or-image",
          "Either field value or image is required, but not both.",
          function (value) {
            const { field_value, image } = value || {};
            // Ensure only one of the two is selected
            return (!!field_value && !image) || (!field_value && !!image);
          }
        )
    )
    .min(1, "At least one item is required."),
});

const CreateType = ({ showCreateModal, setShowCreateModal, handleClose }) => {
  const { data: types, isLoading } = useGetApiQuery("admin/payment-types");
  const [postApi, { isSubmmiting }] = usePostApiMutation();
  const [addItem, setAddItem] = useState(false);
  const [fieldName, setFieldName] = useState("");
  const [dataFieldValue, setDataFieldValue] = useState("");
  const [image, setImage] = useState("");
  // const newItemInputRef = useRef(null);

  // useEffect(() => {
  //   if (addItem && newItemInputRef.current) {
  //     newItemInputRef.current.focus();
  //   }
  // }, [addItem]);

  const handleSubmit = async (values) => {
    console.log("Form Submitted: ", values);

    const formData = new FormData();
    formData.append("name", values.name);
    formData.append("icon", values.icon);
    values.items.forEach((item, index) => {
      formData.append(`items[${index}][field_name]`, item.field_name);
      formData.append(`items[${index}][field_value]`, item.field_value || "");
      if (item.image) {
        formData.append(`items[${index}][image]`, item.image);
      }
    });

    const response = await postApi({
      end_point: "admin/organization-payment-types",
      body: formData,
    });

    if (response.data.status) {
      handleClose();
    }
  };

  return (
    <Modal
      activeModal={showCreateModal}
      onClose={handleClose}
      title="Create new Type"
      className="max-w-3xl"
      footer={
        <Button text="Close" btnClass="btn-primary" onClick={handleClose} />
      }
    >
      <Formik
        initialValues={{
          name: "",
          icon: "",
          items: [],
        }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ values, errors, touched, setFieldValue }) => (
          <Form className="space-y-5">
            {/* Select Type */}
            <div className="flex gap-4">
              <div className="w-full">
                <InputField
                  label="Name"
                  name="name"
                  placeholder="Payment type name."
                  required
                />
              </div>

              <div className="space-y-2 w-full">
                <label htmlFor="icon" className="text-sm font-semibold">
                  Icon <span className="text-red-400">*</span>
                </label>
                <Fileinput
                  name="icon"
                  accept="image/*"
                  selectedFile={values.icon.name}
                  onChange={(e) =>
                    setFieldValue("icon", e.currentTarget.files[0])
                  }
                />
              </div>
            </div>

            {/* Dynamic Items */}
            <FieldArray
              name="items"
              render={(arrayHelpers) => (
                <>
                  <p className="text-lg font-semibold">Items: </p>
                  <div className="flex flex-col">
                    {values.items.map((item, index) => (
                      <div
                        key={index}
                        className="item flex flex-col gap-2 border mb-3 p-3 rounded-lg"
                      >
                        {/* Field Name */}
                        <InputField
                          name={`items[${index}].field_name`}
                          placeholder="Field Name"
                        />
                        {console.log(values)}

                        {/* Field Value */}
                        <InputField
                          name={`items[${index}].field_value`}
                          placeholder="Field Value"
                        />
                        {errors.items?.[index]?.field_value && (
                          <div className="text-red-500 text-sm">
                            {errors.items[index].field_value}
                          </div>
                        )}

                        {/* Image */}
                        <Fileinput
                          name={`items[${index}].image`}
                          accept="image/*"
                          selectedFile={
                            values.items[index].image
                              ? values.items[index].image.name
                              : ""
                          }
                          onChange={(e) =>
                            setFieldValue(
                              `items[${index}].image`,
                              e.currentTarget.files[0]
                            )
                          }
                        />
                        {errors.items?.[index]?.image && (
                          <div className="text-red-500 text-sm">
                            {errors.items[index].image}
                          </div>
                        )}

                        {/* Mutual Validation Error */}
                        {errors.items?.[index] &&
                          typeof errors.items[index] === "string" && (
                            <div className="text-red-500 text-sm">
                              {errors.items[index]}
                            </div>
                          )}

                        {/* Remove Button */}
                        <button
                          type="button"
                          onClick={() => arrayHelpers.remove(index)}
                        >
                          Remove
                        </button>
                      </div>
                    ))}
                    <button
                      type="button"
                      className="border py-2 rounded-lg hover:border-gray-300"
                      onClick={() =>
                        arrayHelpers.push({
                          field_name: "",
                          field_value: "",
                          image: null,
                        })
                      }
                    >
                      Add Item
                    </button>
                  </div>

                  {/* Array-Level Validation */}
                  {touched.items && typeof errors.items === "string" && (
                    <div className="text-red-500 text-sm">{errors.items}</div>
                  )}
                </>
              )}
            />

            {/* Submit Button */}
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isSubmmiting}
                type="submit"
                className="btn text-center btn-primary"
              >
                {isSubmmiting ? "Submitting..." : "Submit"}
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default CreateType;
