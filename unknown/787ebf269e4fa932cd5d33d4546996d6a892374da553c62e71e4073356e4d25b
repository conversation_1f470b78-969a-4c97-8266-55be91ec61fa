import React, { useState, useEffect } from "react";
import Input<PERSON>ield from "@/components/ui/InputField";
import Button from "@/components/ui/Button";
import SimpleBar from "simplebar-react";
import Icon from "@/components/ui/Icon";
import { Formik, Form, Field } from "formik";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { useParams } from "react-router-dom";
import Tooltip from "@/components/ui/Tooltip";
import { useDispatch } from "react-redux";
import * as yup from "yup";

const CreateItem = ({ isSidebarOpen, setIsSidebarOpen, data, onFeatureUpdate }) => {
  const [postApi, { isLoading }] = usePostApiMutation();
  const { id } = useParams();
  const dispatch = useDispatch();
  const [isActive, setIsActive] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedIconIndex, setSelectedIconIndex] = useState(null);

  const [titles, setTitles] = useState(() => {
    if (data.length > 0) {
      return data.map((item) => ({
        icon: item.icon,
        title: item.title,
        selected: true,
      }));
    }
    return [
      { icon: "features/icon.png", title: "Experienced Instructors", selected: false },
      { icon: "features/Simplification.svg", title: "Pre-recorded class ", selected: false },
      { icon: "features/Simplification-1.svg", title: "Assessments after class", selected: false },
      { icon: "features/Simplification-2.svg", title: "Live class", selected: false },
      { icon: "features/Simplification-5.svg", title: "Supporting scripts", selected: false },
      { icon: "features/Simplification-4.svg", title: "Assignment and review", selected: false },
      { icon: "features/Simplification-6.svg", title: "Certification", selected: false },
    ];
  });

  const icons = [
    "features/icon.png",
    "features/Simplification.svg",
    "features/Simplification-1.svg",
    "features/Simplification-2.svg",
    "features/Simplification-3.svg",
    "features/Simplification-4.svg",
    "features/Simplification-5.svg",
    "features/Simplification-6.svg",
    "features/Simplification-7.svg",
  ];

  useEffect(() => {
    if (data.length > 0) {
      setTitles(
        data.map((item) => ({
          icon: item.icon,
          title: item.title,
          selected: true,
        }))
      );
    }
  }, [data]);

  const addFeature = () => {
    setTitles([
      ...titles,
      {
        icon: "features/icon.png",
        title: `Learning Item ${titles.length + 1}`,
        selected: false,
      },
    ]);
  };

  const openIconModal = (index) => {
    setSelectedIconIndex(index);
    setIsModalOpen(true);
  };

  const selectIcon = (icon) => {
    const updatedTitles = [...titles];
    updatedTitles[selectedIconIndex].icon = icon;
    setTitles(updatedTitles);
    setIsModalOpen(false);
  };

  const validationSchema = yup.object().shape(
    titles.reduce((acc, _, index) => {
      acc[`title${index + 1}`] = yup
        .string()
        .max(255, "Must be less than or equal to 255 characters");
      return acc;
    }, {})
  );

  const onSubmit = async (values, { resetForm }) => {
    const selectedFeatures = titles
      .map((item, index) => {
        if (values[`selected${index}`]) {
          return {
            title: values[`title${index + 1}`],
            title_bn: values[`title${index + 1}`],
            icon: item.icon,
            is_active: isActive,
            course_id: id,
          };
        }
        return null;
      })
      .filter((item) => item !== null);

    try {
      const response = await postApi({
        end_point: "admin/learning-items-save-or-update",
        body: { feature: JSON.stringify(selectedFeatures) },
      });
      onFeatureUpdate(selectedFeatures);
      resetForm();
      setIsSidebarOpen(false);
    } catch (error) {
      console.error("Error saving features:", error);
    }
  };

  return (
    <>
      {isSidebarOpen && (
        <div className="fixed right-0 top-0 w-[450px] bg-white dark:bg-slate-800 h-screen z-[9999] shadow-base2 border border-slate-200 dark:border-slate-700 transition-all duration-150">
          <SimpleBar className="px-6 h-full">
            <header className="flex items-center justify-between border-b border-slate-100 dark:border-slate-700 px-2 py-[25px]">
              <div>
                <span className="block text-xl text-slate-900 font-medium dark:text-[#eee]">
                  Add Learning Items
                </span>
              </div>
              <div
                className="cursor-pointer text-2xl text-slate-800 dark:text-slate-200"
                onClick={() => setIsSidebarOpen(false)}
              >
                <Icon icon="heroicons-outline:x" />
              </div>
            </header>
            <Formik
              enableReinitialize
              initialValues={{
                ...titles.reduce((acc, item, index) => {
                  acc[`selected${index}`] = item.selected;
                  acc[`title${index + 1}`] = item.title;
                  return acc;
                }, {}),
              }}
              validationSchema={validationSchema}
              onSubmit={onSubmit}
            >
              {({ values, handleSubmit, errors, touched }) => {
                const isAnyFeatureSelected = titles.some(
                  (_, index) => values[`selected${index}`]
                );

                return (
                  <Form onSubmit={handleSubmit}>
                    <div className="grid md:grid-cols-1 gap-4 mt-5">
                      {titles.map((item, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-2 bg-slate-100 dark:bg-slate-700 p-2"
                        >
                          <Field
                            type="checkbox"
                            name={`selected${index}`}
                            className="form-checkbox"
                          />
                          <input
                            type="hidden"
                            name={`icon${index + 1}`}
                            value={item.icon}
                          />
                          <img
                            className="w-8 h-8 cursor-pointer"
                            src={`${import.meta.env.VITE_ASSET_HOST_URL}${item.icon}`}
                            alt=""
                            onClick={() => openIconModal(index)}
                          />
                          <div className="flex-1">
                            <Field
                              name={`title${index + 1}`}
                              placeholder={item.title}
                              as={InputField}
                            />
                            {/* {touched[`title${index + 1}`] && errors[`title${index + 1}`] && (
                              <div className="text-red-500 text-sm mt-1">
                                {errors[`title${index + 1}`]}
                              </div>
                            )} */}
                          </div>
                        </div>
                      ))}
                    </div>

                    <Button
                      type="button"
                      onClick={addFeature}
                      className="btn flex gap-2 text-center btn-secondary mt-4 w-full"
                    >
                      <Icon icon="heroicons-outline:plus" className="text-xl" />
                      Add More Items
                    </Button>

                    <div className="ltr:text-right rtl:text-left mt-8">
                      {!isAnyFeatureSelected ? (
                        <Tooltip
                          content="Please select at least one feature"
                          placement="top"
                          className="text-left"
                        >
                          <div>
                            <Button
                              isLoading={isLoading}
                              type="submit"
                              className="btn text-center btn-primary"
                              disabled={!isAnyFeatureSelected}
                            >
                              Submit
                            </Button>
                          </div>
                        </Tooltip>
                      ) : (
                        <div>
                          <Button
                            isLoading={isLoading}
                            type="submit"
                            className="btn text-center btn-primary"
                            disabled={!isAnyFeatureSelected}
                          >
                            Submit
                          </Button>
                        </div>
                      )}
                    </div>
                  </Form>
                );
              }}
            </Formik>
          </SimpleBar>
        </div>
      )}

      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-[10000] flex items-center justify-center">
          <div className="bg-white p-4 rounded-lg shadow-lg max-w-md w-full">
            <h2 className="text-lg font-semibold mb-4">Select an Icon</h2>
            <div className="grid grid-cols-3 gap-4">
              {icons.map((icon, index) => (
                <img
                  key={index}
                  src={`${import.meta.env.VITE_ASSET_HOST_URL}${icon}`}
                  alt=""
                  className="w-12 h-12 cursor-pointer"
                  onClick={() => selectIcon(icon)}
                />
              ))}
            </div>
            <Button
              type="button"
              onClick={() => setIsModalOpen(false)}
              className="btn btn-secondary mt-4"
            >
              Close
            </Button>
          </div>
        </div>
      )}
    </>
  );
};

export default CreateItem;
