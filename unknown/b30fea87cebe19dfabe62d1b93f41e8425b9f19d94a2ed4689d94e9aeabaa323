import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import Modal from "@/components/ui/Modal";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery, useUpdateApiMutation } from "@/store/api/master/commonSlice";

const ChooseTemplate = ({setShowTemplateList, organization}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [template, setTemplate] = useState(null);
  const [isNotSelected, setIsNotSelected] = useState(false);
  const [templateIndex, setTemplateIndex] = useState(null);
  const [previewTemplate, setPreviewTemplate] = useState(null);

  const [updateApi, { isLoading: isUpdating }] = useUpdateApiMutation();
  
  console.log(organization);

  const ASSET_URL = import.meta.env.VITE_ASSET_HOST_URL;
  const { data: res, isLoading } = useGetApiQuery("website/templates");
  const templates = res || [];

  useEffect(() => {
    if (template) {
      const index = templates.findIndex(t => t.id === template.id);
      setTemplateIndex(index);
    }
  }, [template, templates]);

  const selectTemplate = (value, index) => {
    setTemplate(value);
    setTemplateIndex(index);
    setIsNotSelected(false);
  };

  const handleUpdate = async () => {
    if (template) {
      console.log("Selected Template ID:", template.id);
      const formData = new FormData();
      formData.append("template_id", template.id);
      await updateApi({
        end_point: `admin/organizations/${organization.id}`,
        body: formData,
      }).unwrap();

      setShowTemplateList(false);
     
    } else {
      setIsNotSelected(true);
    }
  };

  if (isLoading) return <p className="text-center text-lg">Loading...</p>;

  return (
    <div className={`w-full p-6 space-y-6 bg-white rounded-lg shadow-lg ${isNotSelected && "border border-red-500"}`}>
      <h1 className="text-3xl font-bold text-gray-800">Choose a Template</h1>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
  {templates.map((temp, index) => (
    <div key={temp.id} className="border rounded-lg shadow-md overflow-hidden hover:shadow-lg transition relative">
      
      {/* Activated Badge */}
      {organization.template_id === temp.id && (
        <span className="absolute top-2 left-2 bg-green-600 text-white text-xs font-semibold px-2 py-1 rounded shadow-md z-10">
          Activated
        </span>
      )}

      <div className="relative">
        {templateIndex === index && (
          <div className="absolute top-2 right-2 bg-green-500 rounded-full w-8 h-8 flex items-center justify-center text-white">
            <Icon icon="fa-solid:check" />
          </div>
        )}
        <img
          src={`${ASSET_URL}${temp.theme_image}`}
          alt={temp.title}
          className="w-full h-48 object-cover"
        />
      </div>

      <div className="p-4 text-center">
        <h5 className="text-lg font-semibold text-gray-800">{temp.title}</h5>
        <p className="text-gray-600">{temp.short_description}</p>
      </div>

      <div className="flex border-t divide-x">
      {organization.template_id != temp.id && (
        <button
          onClick={() => selectTemplate(temp, index)}
          className={`w-1/2 py-2 text-blue-500 hover:bg-blue-500 hover:text-white transition ${templateIndex === index && "bg-blue-500 text-white"}`}
        >
          {templateIndex === index ? "Selected" : "Select"}
        </button>        
      )}
        <button
          onClick={() => { setIsOpen(true); setPreviewTemplate(temp); }}
          className="w-1/2 py-2 text-blue-500 hover:bg-blue-500 hover:text-white transition"
        >
          Preview
        </button>
      </div>
    </div>
  ))}
</div>


      {/* Update Button */}
      <div className="flex justify-end mt-4">
        <button
          onClick={() => setShowTemplateList(false)}
          className="px-6 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition mr-2"
        >
          Cancel
        </button>

        <button
          onClick={handleUpdate}
          className="px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition"
        >
          Update Template
        </button>
      </div>

      {/* Preview Modal */}
      {isOpen && (
        <Modal activeModal={isOpen} onClose={() => setIsOpen(false)} className='max-w-5xl' title={previewTemplate?.title}>
          <div className="p-4">
            <div className="grid grid-cols-3 gap-4">
              {previewTemplate?.items.length === 0 ? (
                <p className='text-center text-lg text-gray-600'>No items added</p>
              ) : (
                previewTemplate.items.map((item, index) => (
                  <div key={index} className="flex flex-col items-center space-y-2">
                    <img src={`${ASSET_URL}${item.image}`} className="h-32 object-cover" alt={item.title} />
                    <p className="text-center text-lg font-semibold text-gray-800">{item.title}</p>
                  </div>
                ))
              )}
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default ChooseTemplate;
