// CreateVideo.jsx (Renamed to CreateVideo.jsx for clarity)

import React, { useState, useEffect } from "react";
import InputField from "@/components/ui/InputField";
import FileInput from "@/components/ui/Fileinput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import Textarea from "@/components/ui/Textarea";
import Radio from "@/components/ui/Radio";
import { Formik, Form } from "formik";
import { initialValues, validationSchema } from "./formVideo";
import { useDispatch } from "react-redux";
import { usePostApiMutation, useUpdateApiMutation } from "@/store/api/master/commonSlice";
import SimpleBar from "simplebar-react";
import Icon from "@/components/ui/Icon";
import UploadVideo from "./UploadVideo";

const CreateVideo = ({ isSidebarOpen, setIsSidebarOpen, category, video }) => {

  const [postApi, { isLoading }] = usePostApiMutation();
  const [videoUploadApi, { isUploading }] = usePostApiMutation();
  const [updateApi] = useUpdateApiMutation();
  const dispatch = useDispatch();
  const [isActive, setIsActive] = useState(video ? Boolean(video.is_active) : true);
  const [isFree, setIsFree] = useState(video ? Boolean(video.is_free) : false);
  const [duration, setDuration] = useState(video ? video.duration : "");

  // Function to infer video_type based on which URL is present
  const inferVideoType = (video) => {
    if (video.youtube_url) return "youtube_url";
    if (video.s3_url) return "s3_url";
    if (video.raw_url) return "raw_url";
    return "raw_url"; // Default
  };

  const formInitialValues = video
    ? {
        video_type: inferVideoType(video),
        raw_url: video.raw_url || "",
        s3_url: video.s3_url || "",
        youtube_url: video.youtube_url || "",
        title: video.title || "",
        author_name: video.author_name || "",
        author_details: video.author_details || "",
        description: video.description || "",
        thumbnail: video.thumbnail || "", // Handle file uploads separately
        download_url: video.download_url || "",
        duration: video.duration || "",
        is_active: video.is_active || "",
        is_free: video.is_free || "",
        id: video?.id,
        // ... other fields
      }
    : initialValues;

  const handleDuration = (durationValue, setFieldValue) => {
    const floorDuration = Math.floor(durationValue);
    setDuration(floorDuration);
    setFieldValue("duration", floorDuration);
  };

  const onSubmit = async (values, { resetForm }) => {
    let formData = new FormData();
    formData.append("id", video?.id ? video.id : "");
    formData.append("course_id", category.course_id);
    formData.append("course_category_id", category.id);
    formData.append("title", values.title);
    formData.append("title_bn", values.title);
    formData.append("author_name", values.author_name);
    formData.append("author_details", values.author_details);
    formData.append("description", values.description);
    formData.append("raw_url", values.raw_url);
    formData.append("s3_url", values.s3_url);
    formData.append("youtube_url", values.youtube_url);
    if (values.thumbnail && typeof values.thumbnail !== "string") {
      formData.append("thumbnail", values.thumbnail);
    }
    if (values.temp_video_id) {
      formData.append("temp_video_id", values.temp_video_id);
    }
    formData.append("download_url", values.download_url);
    formData.append("duration", values.duration || duration);
    formData.append("sequence", 1);
    formData.append("price", 0);
    formData.append("rating", 0);
    formData.append("is_active", isActive ? 1 : 0);
    formData.append("is_free", isFree ? 1 : 0);

    const endpoint = video
      ? `admin/chapter-video-save-or-update` // Adjust endpoint for update
      : "admin/chapter-video-save-or-update";

    const response = await postApi({
      end_point: endpoint,
      body: formData,
    });

    if (response.error) {
      resetForm({ values, errors: response.error.data.errors });
    } else {
      resetForm();
      setIsSidebarOpen(false);
      // Optionally, refresh video list
      // e.g., dispatch(fetchVideos(category.id));
    }
  };

  const uploadVideo = async (video, setFieldValue) => {
    let formData = new FormData();
    formData.append("video", video);
    const response = await videoUploadApi({
      end_point: "admin/upload-video",
      body: formData,
      notoast: true
    });

    console.log(response);
    if (response?.data) {
      setFieldValue("raw_url", response?.data?.data.raw_url);
    }
  }
  return (
    <>
      {isSidebarOpen && (
        <div
          className={`fixed right-0 top-0 w-[450px] bg-white dark:bg-slate-800 h-screen z-[9999] shadow-base2 border border-slate-200 dark:border-slate-700 transition-all duration-150`}
        >
          <SimpleBar className="px-6 h-full">
            <header className="flex items-center justify-between border-b border-slate-100 dark:border-slate-700 px-6 py-[25px]">
              <div>
                <span className="block text-xl text-slate-900 font-medium dark:text-[#eee]">
                  {video ? "Edit Video" : "Add New Video"}
                </span>
              </div>
              <div
                className="cursor-pointer text-2xl text-slate-800 dark:text-slate-200"
                onClick={() => setIsSidebarOpen(false)}
              >
                <Icon icon="heroicons-outline:x" />
              </div>
            </header>
            <Formik
              enableReinitialize
              validationSchema={video? null : validationSchema}
              initialValues={formInitialValues}
              onSubmit={onSubmit}
            >
              {({ values, setFieldValue, errors }) => (
                console.log(errors),
                <Form>
                  <div className="grid md:grid-cols-1 gap-4 my-3">
                    <label className="font-bold leading-6 block">
                      Select Video Type:
                      <span className="text-red-500 ml-1">*</span>
                    </label>
                    <div className="flex items-center ml-4 gap-8 mt-2">
                      <Radio
                        label="Raw URL"
                        name="video_type"
                        value="raw_url"
                        checked={values.video_type === "raw_url"}
                        onChange={(e) => setFieldValue("video_type", e.target.value)}
                      />
                      {/* <Radio
                        label="S3 URL"
                        name="video_type"
                        value="s3_url"
                        checked={values.video_type === "s3_url"}
                        onChange={(e) => setFieldValue("video_type", e.target.value)}
                      /> */}
                      <Radio
                        label="Youtube URL"
                        name="video_type"
                        value="youtube_url"
                        checked={values.video_type === "youtube_url"}
                        onChange={(e) => setFieldValue("video_type", e.target.value)}
                      />
                    </div>
                  </div>

                  {values.video_type === "raw_url" && (
                    

                    <UploadVideo video={video} setFieldValue={setFieldValue} />
    


                  )}
                  {values.video_type === "s3_url" && (
                    <InputField
                      className="mt-4"
                      label="S3 URL"
                      name="s3_url"
                      type="text"
                      placeholder="Enter S3 URL"
                      required
                    />
                  )}
                  {values.video_type === "youtube_url" && (
                    <InputField
                      className="mt-4"
                      label="Youtube URL"
                      name="youtube_url"
                      type="text"
                      placeholder="Enter Youtube URL"
                      required
                    />
                  )}

                  <InputField
                    classLabel="mt-4"
                    label="Title"
                    name="title"
                    type="text"
                    placeholder="Enter Name"
                    required
                  />

                  <>
                    <label className={`block capitalize mt-4 pb-2`}>
                      Thumbnail
                      <span className="text-red-500">*</span>
                    </label>
                    <FileInput
                      className="py-2"
                      name="thumbnail"
                      accept="image/*"
                      type="file"
                      placeholder="Thumbnail"
                      preview={true}
                      selectedFile={values.thumbnail}
                      onChange={(e) => {
                        setFieldValue("thumbnail", e.target.files[0]);
                      }}
                    />
                    {video && video.thumbnail && (
                      <div className="mt-2">
                        <img
                          src={`${import.meta.env.VITE_ASSET_HOST_URL}${video.thumbnail}`} // Adjust based on your asset URL
                          alt="Current Thumbnail"
                          className="w-32 h-32 object-cover"
                        />
                      </div>
                    )}
                  </>

                  <Textarea
                    classLabel="mt-4 pb-2"
                    label="Description"
                    name="description"
                    placeholder="Enter Description"
                    value={values.description}
                    onChange={(e) => setFieldValue("description", e.target.value)}
                  />

                  <div className="grid md:grid-cols-2 gap-4 my-3">
                    <Switch
                      className="mt-4"
                      label="Active"
                      activeClass="bg-success-500"
                      value={isActive}
                      onChange={() => setIsActive(!isActive)}
                    />
                    <Switch
                      className="mt-4"
                      label="Free"
                      activeClass="bg-success-500"
                      value={isFree}
                      onChange={() => setIsFree(!isFree)}
                    />
                  </div>

                  <div className="ltr:text-right rtl:text-left my-5">
                    <Button
                      isLoading={isLoading}
                      type="submit"
                      className="btn text-center btn-primary"
                    >
                      {video ? "Update" : "Submit"}
                    </Button>
                  </div>
                </Form>
              )}
            </Formik>
          </SimpleBar>
        </div>
      )}
    </>
  );
};

export default CreateVideo;
