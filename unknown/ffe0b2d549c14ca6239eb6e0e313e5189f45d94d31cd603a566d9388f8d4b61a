import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetClassListQuery } from "@/store/api/master/rowContentClassListSlice";
import { useGetSubjectChapterListQuery } from "@/store/api/master/rowContentChapterListSlice";
import { useGetChapterListBySubjectQuery } from "@/store/api/master/rowContentChapterListSlice";
import Select from "@/components/ui/Select";
import { useGetQuizListQuery } from "@/store/api/master/rowContentQuizListSlice";
import CreateQuiz from "./createQuiz"
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

const Filter = ({ setApiParam }) => {
  const [classId, setClassId] = useState(null);
  const [subjectId, setSubjectId] = useState(null);
  const classList = useGetClassListQuery("?pagination=false").data;
  // api
  const subjectList = useGetSubjectChapterListQuery(classId).data;
  const chapterList = useGetChapterListBySubjectQuery(subjectId).data;
  
  return (
    <div className="flex gap-2">
      <Select
        className="w-52"
        defaultValue=""
        placeholder="Select Class"
        options={classList?.map((item) => {
          return { label: item.name, value: item.id };
        })}
        name="class_level"
        onChange={(e) => {
          setClassId(e.target.value);
          setApiParam("?class_id=" + e.target.value);
        }}
      />

      <Select
        className="w-40"
        defaultValue=""
        placeholder="Select Subject"
        options={subjectList?.map((item) => {
          return { label: item.name, value: item.id };
        })}
        name="subject"
        onChange={(e) => {
          setSubjectId(e.target.value);
          setApiParam(`?class_id=${classId}&subject_id=${e.target.value}`);
        }}
      />
      <Select
        className="w-40"
        defaultValue=""
        placeholder="Select Chapter"
        options={chapterList?.map((item) => {
          return { label: item.name, value: item.id };
        })}
        name="chapter"
        onChange={(e) => {
          setApiParam(
            `?class_id=${classId}&subject_id=${subjectId}&chapter_id=${e.target.value}`
          );
        }}
      />
    </div>
  );
};

const index = () => {
  const navigate = useNavigate();
  const [apiParam, setApiParam] = useState("");
  const res = useGetQuizListQuery(apiParam);

  const { showModal, showEditModal } = useSelector(
    (state) => state.commonReducer
  );
  const changePage = (val) => {
    setApiParam(val);
  };
  const data = res.data;
  const columns = [
    {
      label: "SL",
      field: "id",
    },
    {
      label: "Quiz",
      field: "title",
    },
    {
      label: "Class-Subject-Chapter",
      field: "class_name",
    },
    {
      label: "Duration",
      field: "duration",
    },
    {
      label: "Marking",
      field: "positive_mark",
    },
    {
      label: "Total Mark",
      field: "total_mark",
    },
    {
      label: "Question",
      field: "number_of_question",
    },
    {
      label: "QTN Status",
      field: "sufficient_question",
    },
    {
      label: "Status",
      field: "is_active",
    },
    {
      label: "Action",
      field: "",
    },
  ];
  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      title: (
        <div>
          EN:{item.title} <br /> BN:{item.title_bn}
        </div>
      ),
      class_name: (
        <div>
          {item.class_name} <br /> {item.subject_name} <br />
          {item.chapter_name}
        </div>
      ),
      duration: item.duration,
      positive_mark: (
        <div>
          <div>
            {"Positive : "}
            <span className="text-green-600">{item.positive_mark}</span>
          </div>
          <br />{" "}
          <div>
            {"Negative : "}
            <span className="text-red-600">{item.negative_mark}</span>
          </div>
        </div>
      ),
      total_mark:item.total_mark,
      number_of_question:item.number_of_question,
      sufficient_question: (
        <Badge
          className={
            item.sufficient_question == true
              ? `bg-success-500 text-white rounded-full`
              : `bg-danger-500 text-white rounded-full`
          }
        >
          {item.sufficient_question ? "Enough Questions" : "Question Not Sufficient"}
        </Badge>
      ),
      is_active: (
        <Badge
          className={
            item.is_active == "true"
              ? `bg-danger-500 text-white`
              : `bg-success-500 text-white`
          }
        >
          {item.is_active ? "Yes" : "No"}
        </Badge>
      ),
    };
  });

  const actions = [
    {
      name: "Edit",
      icon: "heroicons-outline:eye",
      onClick: (val) => {
        console.log(val);
      },
    },
    {
      name: "Subject",
      icon: "heroicons:numbered-list",
      onClick: (val) => {
        navigate('/quiz-subject-list/' + tableData[val].id);
      },
    },
    {
      name: "Question",
      icon: "heroicons:numbered-list",
      onClick: (val) => {
        navigate('/question-list/' + tableData[val].id);
      },
    },
    {
      name: " W.Question",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        navigate('/quiz-written-question/' + tableData[val].id);
      },
    },
  ];

  
  const filter = <Filter setApiParam={setApiParam} />;



  return (
    <div>
      {/* {tableData?.length > 0 && ( */}
      <BasicTablePage
        title="Quiz List"
        createButton="Add New Quiz"
        actions={actions}
        columns={columns}
        data={tableData}
        filter={filter}
        changePage={changePage}
        currentPage={data?.current_page}
        totalPages={Math.ceil(data?.total / data?.per_page)}
      />
      
      {showModal && <CreateQuiz /> }
      
    </div>
  );
};

export default index;
