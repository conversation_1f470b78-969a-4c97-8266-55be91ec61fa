import React, { useState } from "react";
import InputField from "@/components/ui/InputField";
import FileInput from "@/components/ui/Fileinput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import Textarea from "@/components/ui/Textarea";
import { Formik, Form } from "formik";
import { initialValues, validationSchema } from "./formScript";
import SimpleBar from "simplebar-react";
import Icon from "@/components/ui/Icon";
import { usePostApiMutation } from "@/store/api/master/commonSlice";

const createScript = ({ isSidebarOpen, setIsSidebarOpen, category }) => {
  const [isActive, setIsActive] = useState(true);
  const [isFree, setIsFree] = useState(false);
  const [uploadType, setUploadType] = useState("pdf");

  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();

  const onSubmit = async (values, { resetForm }) => {
    let formData = new FormData();
    formData.append("course_id", category.course_id);
    formData.append("course_category_id", category.id);
    formData.append("title", values.title);
    formData.append("description", values.description);
    formData.append("is_active", isActive ? 1 : 0);
    formData.append("is_free", isFree ? 1 : 0);
    formData.append("price", 0);
    formData.append("sequence", 1);

    if (uploadType === "pdf") {
      formData.append("file", values.file);
    } else {
      formData.append("raw_url", values.raw_url);
    }

    // Call the API
    const response = await postApi({
      end_point: "admin/chapter-script-save-or-update",
      body: formData,
    });
    resetForm();
    setIsSidebarOpen(false);
  };

  return (
    <>
      {isSidebarOpen && (
        <div className="fixed right-0 top-0 w-[450px] bg-white dark:bg-slate-800 h-screen z-[9999] shadow-base2 border border-slate-200 dark:border-slate-700 transition-all duration-150">
          <SimpleBar className="px-6 h-full">
            <header className="flex items-center justify-between border-b border-slate-100 dark:border-slate-700 px-6 py-[25px]">
              <div>
                <span className="block text-xl text-slate-900 font-medium dark:text-[#eee]">
                  Create Learning Material
                </span>
              </div>
              <div
                className="cursor-pointer text-2xl text-slate-800 dark:text-slate-200"
                onClick={() => setIsSidebarOpen(false)}
              >
                <Icon icon="heroicons-outline:x" />
              </div>
            </header>
            <Formik
              validationSchema={validationSchema(uploadType)} // Pass the type to validation
              initialValues={initialValues}
              onSubmit={onSubmit}
            >
              {({ values, errors, touched, setFieldValue, handleSubmit }) => (
                <Form>
                  <div className="grid md:grid-cols-1 gap-4">
                    <InputField
                      label="Title"
                      name="title"
                      type="text"
                      placeholder="Enter Title"
                      required
                    />
                  </div>
                  <div className="grid md:grid-cols-1 gap-4 my-3">
                    <label className="block text-[#1D1D1F] text-base font-medium">
                      Description
                    </label>
                    <Textarea
                      placeholder="Enter Description"
                      name="description"
                      onChange={(e) =>
                        setFieldValue("description", e.target.value)
                      }
                    />
                  </div>

                  <div className="grid md:grid-cols-1 gap-4 my-3">
                    <label className="block text-base font-medium">
                      Upload Type
                    </label>
                    <div className="flex gap-4">
                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          name="uploadType"
                          value="pdf"
                          checked={uploadType === "pdf"}
                          onChange={() => setUploadType("pdf")}
                        />
                        File
                      </label>
                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          name="uploadType"
                          value="url"
                          checked={uploadType === "url"}
                          onChange={() => setUploadType("url")}
                        />
                        URL
                      </label>
                    </div>
                  </div>

                  {uploadType === "url" && (
                    <InputField
                      label="Script URL"
                      name="raw_url"
                      type="text"
                      placeholder="Enter Script URL"
                      required
                    />
                  )}

                  {uploadType === "pdf" && (
                    <>
                      <label className="block font-medium mb-2">
                        Upload File 
                        <span className="text-red-500 ml-1">*</span>
                        <small>.pdf,.docx,.txt,.ppt and image</small>
                      </label>

                      <FileInput
                        name="file"
                        accept=".pdf,.docx,.txt,.ppt,.png,.jpeg,.jpg,.gif,.bmp,.svg"
                        type="file"
                        placeholder="Upload PDF"
                        preview={true}
                        selectedFile={values.file}
                        onChange={(e) =>
                          setFieldValue("file", e.target.files[0])
                        }
                      />
                    </>
                  )}

                  {/* <div className="grid md:grid-cols-1 gap-4 my-3">
                    <>
                      <label className="block text-[#1D1D1F] text-base font-medium">
                        Thumbnail
                      </label>
                      <FileInput
                        name="thumbnail"
                        accept="image/*"
                        type="file"
                        placeholder="Thumbnail"
                        preview={true}
                        selectedFile={values.thumbnail}
                        onChange={(e) => {
                          setFieldValue("thumbnail", e.target.files[0]);
                        }}
                      />
                    </>
                  </div> */}
                  <div className="grid md:grid-cols-2 gap-4 my-3">
                    <Switch
                      label="Active"
                      activeClass="bg-success-500"
                      value={isActive}
                      name="is_active"
                      onChange={() => setIsActive(!isActive)}
                    />
                    <Switch
                      label="Free"
                      activeClass="bg-success-500"
                      value={isFree}
                      name="is_free"
                      onChange={() => setIsFree(!isFree)}
                    />
                  </div>
                  <div className="ltr:text-right rtl:text-left mt-5">
                    <Button
                      isLoading={isLoading}
                      type="submit"
                      className="btn btn-primary"
                    >
                      Submit
                    </Button>
                  </div>
                </Form>
              )}
            </Formik>
          </SimpleBar>
        </div>
      )}
    </>
  );
};

export default createScript;
