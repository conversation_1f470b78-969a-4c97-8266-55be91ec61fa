import React from "react";
import * as yup from "yup";

export const initialValues = {
  name: "",
  name_bn: "",
  price: "",
  color_code: "",
  sequence: "",
  // icon: "null",
  // is_Free: false,
  // is_Active: false,
};

export const validationSchema = yup.object({
  name: yup
    .string()
    .max(30, "Should not be more than 30 characters")
    .min(3, "Should not be less than 3 characters")
    .required("Name is Required"),
  name_bn: yup
    .string()
    .max(30, "Should not be more than 30 characters")
    .min(3, "Should not be less than 3 characters"),
});
