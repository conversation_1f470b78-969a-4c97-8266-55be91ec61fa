import React from "react";
import { ArrowRight, Globe, Youtube } from "lucide-react";
import worldGif from "@/assets/images/all-img/world.gif";
import ReactPlayer from "react-player";

const HeroSection = () => {
  return (
    <div
      className="relative min-h-[600px] overflow-hidden bg-cover bg-center bg-no-repeat"
      style={{
        backgroundImage:
          "url('https://images.unsplash.com/photo-1519681393784-d120267933ba?auto=format&fit=crop&q=80&w=2070')",
        backgroundBlendMode: "overlay",
      }}
    >
      {/* Overlay for better text readability */}
      <div className="absolute inset-0 bg-white/90 backdrop-blur-sm"></div>

      {/* Left side content */}
      <div className="relative max-w-screen-xl mx-auto px-4 py-16 grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="flex flex-col justify-center">
          <h1 className="text-4xl lg:text-5xl font-bold text-blue-600 mb-6">
            Uncover the boundless potential of learning
          </h1>
          <p className="text-gray-600 text-lg mb-8 leading-relaxed">
            Would you like to become more knowledgeable and proficient? You will
            increase your knowledge and learn new skills from our classes. This
            is the right moment to set off on your path to career success and
            personal development.
          </p>
          <div>
            <button className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors duration-200">
              Explore Courses
              <ArrowRight className="ml-2 w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Right side grid */}
        <div className="flex-1 grid grid-cols-12 gap-5">
          <div className="col-span-6 flex flex-col">
            <div className="flex-grow-[1] "></div>
            <div className="mb-5 w-full flex-grow-[3] flex-shrink-0 basis-auto">
            <img
                src="https://images.unsplash.com/photo-1633332755192-727a05c4013d?auto=format&fit=crop&w=800&q=80"
                alt="Professor"
                className="w-full object-cover rounded-lg mb-4"
              />
            </div>

            {/* Second item with 1/3 height */}
            <div className=" w-full rounded-lg relative border shadow-lg">
            <img
                src="https://images.unsplash.com/photo-1614036417651-efe5912149d8?auto=format&fit=crop&w=800&q=80"
                alt="Trophy"
                className=" object-contain"
              />
            </div>
          </div>

          <div className="col-span-6 flex flex-col h-full">
            {/* First item with space at the top */}
            <div className="flex-grow-[2]"></div>
            <div className="flex-grow-[3] flex-shrink-0 basis-auto w-full relative flex items-center justify-center border shadow-lg rounded-lg border-2 border-sky-50 hover:border-red-600 group cursor-pointer mb-5">
              <div className="w-full h-full rounded-lg overflow-hidden">
                <ReactPlayer
                  url={
                    
                    "https://www.youtube.com/watch?v=88jH_04zmIw"
                  }
                  playing={true}
                  muted={true}
                  controls={false}
                  loop={true}
                  width="100%"
                  height="100%"
                />
              </div>
            </div>

            {/* Second item */}
            <div className="flex-grow-[0.1] flex-shrink-0 basis-auto w-full rounded-lg relative border shadow-lg">
              <img
                src={worldGif}
                alt="Rotating Globe"
                className="w-full object-cover rounded-lg h-full z-0"
              />
              <div className="max-sm:text-xs text-lg font-semibold text-gray-500 rounded-r-lg h-full absolute top-0 right-0 flex items-center justify-center">
                <div className="absolute inset-0 bg-gray-200 opacity-75 rounded-r-lg"></div>
                <span className="relative z-10 text-center px-3">
                  <span className="text-2xl sm:text-4xl text-sky-700">
                    ---
                  </span>
                  <br />
                  Worldwide User
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
