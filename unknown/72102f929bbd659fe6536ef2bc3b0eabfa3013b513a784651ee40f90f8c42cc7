import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { useDeleteApiMutation } from "@/store/api/master/commonSlice";

const DeleteTestimonial = ({ showDeleteModal, setShowDeleteModal, data }) => {
  const [deleteApi, { isLoading }] = useDeleteApiMutation();

  const handleDelete = async () => {
    await deleteApi({
      end_point: `admin/testimonials/${data.id}`,
      body: {},
    });
    setShowDeleteModal(false);
  };

  return (
    <Modal
      title="Confirm Delete"
      activeModal={showDeleteModal}
      className="max-w-3xl"
      onClose={() => setShowDeleteModal(false)}
      footerContent={
        <div className="flex justify-end space-x-3">
          <Button
            text="Cancel"
            disabled={isLoading}
            onClick={() => setShowDeleteModal(false)}
            className="btn-outline-dark"
          />
          <Button
            text="Delete"
            onClick={handleDelete}
            isLoading={isLoading}
            className="btn-danger"
          />
        </div>
      }
    >
      <div className="text-center py-5">
        <div className="text-xl mb-2">Are you sure?</div>
        <div className="text-sm text-slate-600">
          Do you really want to delete this testimonial? This process cannot be undone.
        </div>
      </div>
    </Modal>
  );
};

export default DeleteTestimonial;
