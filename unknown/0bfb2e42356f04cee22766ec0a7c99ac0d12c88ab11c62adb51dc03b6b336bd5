import React, { useState } from "react";
import Icon from "@/components/ui/Icon";
import { Formik, Form, Field } from "formik";
import Button from "@/components/ui/Button";
import two from "@/assets/saas-lms/2.png";
import three from "@/assets/saas-lms/3.png";
import four from "@/assets/saas-lms/4.png";
import { validationSchema } from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import { setUser } from "@/store/api/auth/authSlice";
import {
  useGetApiQuery,
  useUpdateApiMutation,
} from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";

const EditHomePage = () => {
  const { user } = useSelector((state) => state.auth);
  const [updateApi, { isLoading, isError, error, isSuccess }] =
    useUpdateApiMutation();
  // const [organization, { isOrganizationLoading, isOrganizationError, errorOrganization, isOrganizationSuccess }] = useGetApiQuery('admin/organizations/' + user?.organization_id);
  const res = useGetApiQuery("admin/organizations/" + user?.organization_id);
  let organization = { ...res.data };
  const dispatch = useDispatch();
  const [isActive, setIsActive] = useState(false);

  const initialValues = {
    name: organization?.name,
    details: organization?.details,
    address: organization?.address,
    email: organization?.email,
    contact_no: organization?.contact_no,
    contact_person: organization?.contact_person,
    is_active: organization?.is_active,
    contact_number: organization?.contact_number,
    hotline_number: organization?.hotline_number,
  };

  const onSubmit = async (values, { resetForm }) => {
    const formData = new FormData();
    Object.keys(values).forEach((key) => {
      if (values[key] !== undefined) {
        formData.append(key, values[key]);
      }
    });

    const response = await updateApi({
      end_point: "admin/organizations/" + user?.organization_id,
      body: formData,
    });

    console.log(response);
    // dispatch(setUser(response.data.user));
  };
  return (
    <div className="w-full bg-white">
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
      >
        {({ values, setFieldValue, errors, touched }) => (
          <Form className="">
            <div className="w-full">
              <div className="bg-primary-500 text-white">
                <div className="mx-auto flex justify-between items-center px-4">
                  <div className="flex items-center space-x-1">
                    <Icon icon="mdi:email" className="w-5 h-5" />

                    <input
                      name="email"
                      type="text"
                      defaultValue={organization?.email}
                      onChange={(e) => setFieldValue("email", e.target.value)}
                      className="bg-primary-600 outline-none border-none pl-2 h-10"
                      placeholder="Contact Number"
                    />
                  </div>
                  <div className="flex space-x-4">
                    <div className="flex items-center space-x-1">
                      <Icon icon="mdi:phone" className="w-5 h-5" />
                      {/* <span>
                                    {organization?.contact_no}
                                </span> */}
                      <input
                        type="text"
                        defaultValue={organization?.contact_no}
                        onChange={(e) =>
                          setFieldValue("contact_no", e.target.value)
                        }
                        className="bg-primary-600 outline-none border-none pl-2 h-10"
                        placeholder="Contact Number"
                      />
                    </div>
                    <div className="border-l border-gray-300 h-6 mx-2"></div>
                    <div className="flex items-center space-x-1">
                      <Icon icon="mdi:phone" className="w-5 h-5" />
                      <input
                        type="text"
                        defaultValue={organization?.hotline_number}
                        onChange={(e) =>
                          setFieldValue("hotline_number", e.target.value)
                        }
                        className="bg-primary-600 outline-none border-none pl-2 h-10"
                        placeholder="Hotline Number"
                      />
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {/* <span>EN</span>
                            <Icon icon="mdi:chevron-down" className="w-5 h-5" /> */}
                  </div>
                </div>
              </div>
              {/* <nav className="bg-white dark:bg-gray-900 w-full border-b border-gray-200 dark:border-gray-600">
                    <div className="max-w-screen-xl flex flex-wrap items-center justify-between mx-auto py-4">
                        <a href="/" onClick={() => navigate('/')} className="flex items-center space-x-3 rtl:space-x-reverse">
                            <img src={import.meta.env.VITE_ASSET_HOST_URL + organization?.logo} className="h-8" alt="Flowbite Logo" />
                            <h4> {organization?.name} </h4>
                        </a>
                        <div className="flex md:order-2 space-x-3 md:space-x-0 rtl:space-x-reverse">
                            <div className="flex items-center justify-center space-x-2">

                            </div>

                        </div>
                    </div>
                </nav> */}
            </div>

            <div className="grid grid-cols-2 pt-6 px-4">
              <div className="flex items-center">
                <div>
                  <div className="flex mb-5 gap-2">
                    <div className="my-auto">
                      <input
                        type="file"
                        accept="image/*"
                        className="hidden"
                        id="lms-logo"
                        onChange={(e) => {
                          const file = e.target.files[0];
                          setFieldValue("logo", file);
                        }}
                      />
                      <label>
                        <div
                          className={`relative group h-[50px] w-[50px] rounded-full overflow-hidden cursor-pointer ${
                            errors.logo && touched.logo
                              ? "border-1 border-red-500"
                              : ""
                          }`}
                        >
                          <img
                            src={
                              values?.logo
                                ? URL.createObjectURL(values.logo)
                                : import.meta.env.VITE_ASSET_HOST_URL +
                                  organization?.logo
                            }
                            alt="BB Logo"
                            className="h-full w-full object-cover"
                          />

                          <div
                            className="absolute bottom-0 left-0 w-full h-full
                        items-center justify-center bg-black-900 bg-opacity-0
                          hover:bg-opacity-50 flex text-white font-bold
                        cursor-pointer transition-all duration-200"
                            onClick={() =>
                              document.getElementById("lms-logo").click()
                            }
                          >
                            <span className="hidden group-hover:block text-sm">
                              Upload
                            </span>
                          </div>
                        </div>
                      </label>
                      {errors.logo && touched.logo && (
                        <span className="text-red-500 text-xs">
                          {errors.logo}
                        </span>
                      )}
                    </div>
                    <div className="">
                      <input
                        type="text"
                        className="text-2xl font-bold text-pictonBlue-500 bg-transparent border-none outline-none bg-[#FBFBFB]"
                        defaultValue={organization?.name}
                        onChange={(e) => setFieldValue("name", e.target.value)}
                        // onChange={(e) => {
                        //   dispatch(setOrganization({ ...organization, name: e.target.value }));
                        // }}
                      />

                      <p className="text-xs ">Learning Management System</p>
                    </div>
                  </div>
                  <div className="">
                    <textarea
                      name="headline"
                      className="top-0 left-0 w-full h-[150px] text-5xl text-primary-500 bg-[#FBFBFB] border-none p-0 outline-none p-2"
                      spellCheck="false"
                      rows={1}
                      defaultValue={organization?.headline}
                      style={{ resize: "none" }}
                      placeholder="Type your headline here"
                      onChange={(e) =>
                        setFieldValue("headline", e.target.value)
                      }
                    />

                    {errors.headline && touched.headline && (
                      <div className="text-red-500 text-xs">
                        {errors.headline}
                      </div>
                    )}
                  </div>
                  <textarea
                    defaultValue={organization?.sub_headline}
                    name="sub_headline"
                    className={`top-0 left-0  h-[150px] w-full text-xl p-0 bg-transparent outline-none  bg-[#FBFBFB] p-2 ${
                      errors.sub_headline ? "border-1 border-red-500" : ""
                    }`}
                    spellCheck="false"
                    rows={1}
                    style={{ resize: "none" }}
                    placeholder="Write your sub headline here..."
                    onChange={(e) =>
                      setFieldValue("sub_headline", e.target.value)
                    }
                  />
                  {errors.sub_headline && touched.sub_headline && (
                    <div className="text-red-500 text-xs">
                      {errors.sub_headline}
                    </div>
                  )}
                </div>
              </div>
              <div>
                <div className="grid grid-cols-3 gap-4">
                  <input
                    type="file"
                    accept="image/*"
                    className="hidden"
                    id="lms-banner"
                    onChange={(e) => {
                      const file = e.target.files[0];
                      setFieldValue("banner", file);
                    }}
                  />
                  <div
                    className="relative col-span-2"
                    onMouseEnter={() =>
                      document
                        .getElementById("upload-icon")
                        .classList.add("show")
                    }
                    onMouseLeave={() =>
                      document
                        .getElementById("upload-icon")
                        .classList.remove("show")
                    }
                  >
                    <div className="relative w-full h-[250px] bg-gray-200 rounded shadow group">
                      <img
                        src={
                          values?.banner
                            ? URL.createObjectURL(values.banner)
                            : import.meta.env.VITE_ASSET_HOST_URL +
                              organization?.banner
                        }
                        alt="Hero Background"
                        className="w-full h-full object-cover rounded"
                      />
                      <div
                        className="absolute bottom-0 left-0 w-full h-[60px] group-hover:h-full
                     items-center justify-center bg-black-900 bg-opacity-50
                    flex text-white text-2xl font-bold
                   cursor-pointer transition-all duration-200"
                        onClick={() =>
                          document.getElementById("lms-banner").click()
                        }
                      >
                        <span className="">Upload</span>
                      </div>
                    </div>
                    {errors.banner && touched.banner && (
                      <div className="text-red-500 text-xs">
                        {errors.banner}
                      </div>
                    )}
                    <div
                      id="upload-icon"
                      className="absolute top-2 right-2 hidden"
                    >
                      <Icon
                        icon="heroicons:arrow-up-tray"
                        className="w-6 h-6 text-gray-500"
                      />
                    </div>
                  </div>
                  <div className="flex flex-col items-center justify-center bg-[#FFDCBA] p-4 rounded-lg">
                    <img
                      src={two}
                      alt="Hero Background"
                      className="w-[100px] object-cover rounded "
                    />
                    <h4 className="mt-4">0</h4>
                    <p>Verified Mentors </p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4 mt-4 ">
                  <div className="flex bg-[#D3EAFF] rounded-lg p-4 items-center justify-center gap-4 ">
                    <img
                      src={three}
                      alt="Hero Background"
                      className="w-[100px] object-cover"
                    />
                    <svg
                      width="10"
                      height="66"
                      viewBox="0 0 10 66"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M1 1V65.3479"
                        stroke="#5C6777"
                        strokeLinecap="round"
                      />
                      <path
                        d="M5.05933 14V52.6087"
                        stroke="#5C6777"
                        strokeLinecap="round"
                      />
                      <path
                        d="M8.72168 17V49.1739"
                        stroke="#5C6777"
                        strokeLinecap="round"
                      />
                    </svg>

                    <div>
                      <h4 className="mt-4">0</h4>
                      <p>Enrolled Student </p>
                    </div>
                  </div>
                  <div className="flex bg-[#DEFDF3] rounded-lg p-4 items-center justify-center gap-4 ">
                    <img
                      src={four}
                      alt="Hero Background"
                      className="w-[100px] object-cover"
                    />
                    <svg
                      width="10"
                      height="66"
                      viewBox="0 0 10 66"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M1 1V65.3479"
                        stroke="#5C6777"
                        strokeLinecap="round"
                      />
                      <path
                        d="M5.05933 14V52.6087"
                        stroke="#5C6777"
                        strokeLinecap="round"
                      />
                      <path
                        d="M8.72168 17V49.1739"
                        stroke="#5C6777"
                        strokeLinecap="round"
                        standard="true"
                      />
                    </svg>

                    <div>
                      <h4 className="mt-4">0</h4>
                      <p>Course Available </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="p-4 bg-white rounded-lg ">
                <h5 className="font-semibold text-sm">Address</h5>
                {/* <p className="mt-2">{organization?.address}</p> */}

                <textarea
                  defaultValue={organization?.address}
                  name="address"
                  className={`top-0 left-0  h-[80px] w-full text-sm p-0 bg-transparent outline-none  bg-[#FBFBFB] p-2 ${
                    errors.sub_headline ? "border-1 border-red-500" : ""
                  }`}
                  spellCheck="false"
                  rows={1}
                  style={{ resize: "none" }}
                  placeholder="Write your organization address here..."
                  onChange={(e) => setFieldValue("address", e.target.value)}
                />
                {errors.address && touched.address && (
                  <div className="text-red-500 text-xs">{errors.address}</div>
                )}
              </div>
              <div className="p-4 bg-white rounded-lg ">
                <h5 className="font-semibold text-sm">
                  Organization Description
                </h5>
                <textarea
                  defaultValue={organization?.details}
                  name="details"
                  className={`top-0 left-0  h-[80px] w-full text-sm p-0 bg-transparent outline-none  bg-[#FBFBFB] p-2 ${
                    errors.sub_headline ? "border-1 border-red-500" : ""
                  }`}
                  spellCheck="false"
                  rows={1}
                  style={{ resize: "none" }}
                  placeholder="Write your short description here..."
                  onChange={(e) => setFieldValue("details", e.target.value)}
                />
                {errors.details && touched.details && (
                  <div className="text-red-500 text-xs">{errors.details}</div>
                )}
              </div>
              <div className="p-4 bg-white rounded-lg ">
                <h5 className="font-semibold text-sm">Contact Person</h5>
                {/* <p className="mt-2">{organization?.contact_person}</p> */}
                <input
                  type="text"
                  defaultValue={organization?.contact_person}
                  onChange={(e) =>
                    setFieldValue("contact_person", e.target.value)
                  }
                  className="bg-[#FBFBFB] outline-none border-none p-2 w-full"
                  placeholder="Contact Person"
                />
              </div>
            </div>
            <div className="w-full p-4 flex justify-end">
              <Button
                type="submit"
                text="Update"
                className="btn btn-primary bg-blue-500 hover:bg-blue-600 text-center shadow-md"
                isLoading={isLoading}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default EditHomePage;
