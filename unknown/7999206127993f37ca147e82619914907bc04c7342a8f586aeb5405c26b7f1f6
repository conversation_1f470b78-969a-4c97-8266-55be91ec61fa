import React from "react";
import Card from "@/components/ui/Card";

const CategorySkeleton = () => {
  const skeletonItems = Array(4).fill(null);

  return (
    <div className="space-y-6">
      <Card>
        <div className="md:flex justify-between items-center mb-5 border-b pb-3">
          <div className="h-7 w-40 bg-gray-200 rounded animate-pulse"></div>
          <div className="flex gap-2">
            <div className="h-10 w-32 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-10 w-36 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>

        <div className="flex justify-end mb-4">
          <div className="flex items-center divide-x rounded border">
            <div className="p-2 px-3.5 rounded-l bg-gray-100">
              <div className="h-5 w-5 bg-gray-300 rounded animate-pulse"></div>
            </div>
            <div className="p-2 px-3.5 rounded-r bg-gray-300">
              <div className="h-5 w-5 bg-gray-200 rounded animate-pulse"></div>
            </div>
          </div>
        </div>

        {skeletonItems.map((_, index) => (
          <div 
            key={index} 
            className="border rounded-lg bg-gray-50 border-gray-200 flex justify-between items-center mb-3 p-4"
          >
            <div className="flex items-start gap-4 flex-1">
              <div className="w-12 h-12 bg-gray-200 rounded-full animate-pulse"></div>
              <div className="flex-1">
                <div className="h-6 w-48 bg-gray-200 rounded animate-pulse mb-3"></div>
                <div className="h-5 w-32 bg-gray-200 rounded animate-pulse mb-2"></div>
                <div className="flex flex-wrap gap-3 mt-3">
                  <div className="flex items-center justify-between px-4 py-2 rounded-lg border shadow-sm w-full md:w-auto bg-gray-100">
                    <div className="h-5 w-24 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                  <div className="flex items-center justify-between px-4 py-2 rounded-lg border shadow-sm w-full md:w-auto bg-gray-100">
                    <div className="h-5 w-32 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                </div>
              </div>
            </div>
            <div className="table-td">
              <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
            </div>
          </div>
        ))}
        <div className="flex justify-center mt-5">
          <div className="flex items-center gap-2">
            <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-8 w-8 bg-blue-200 rounded animate-pulse"></div>
            <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default CategorySkeleton;
