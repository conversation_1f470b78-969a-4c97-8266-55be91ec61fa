import React, { useState } from "react";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import ChangePassword from "./ChangePassword"; 

const ProfilePage = () => {
  const { user } = useSelector((state) => state.auth);
  const { data, isLoading, isError } = useGetApiQuery("admin/get-profile");

  const [showChangePassword, setShowChangePassword] = useState(false); 

  if (isLoading) return <div className="text-center mt-10 text-lg">Loading profile...</div>;
  if (isError) return <div className="text-center text-red-500 mt-10">Failed to load profile.</div>;

  const profileImage = data?.image || "/src/assets/images/all-img/user.png";
  const organization = data?.organization;

  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      {/* Profile Info */}
      <div className="flex flex-col sm:flex-row items-center gap-6">
        <img
          src={profileImage}
          alt="Profile"
          className="w-32 h-32 rounded-full border-4 border-blue-500 object-cover"
        />
        <div className="relative flex-1 text-center sm:text-left">
          <div className="absolute top-0 right-0">
            {data?.is_active ? (
              <span className="inline-block px-3 py-1 text-sm bg-green-100 text-green-800 rounded-full">Active</span>
            ) : (
              <span className="inline-block px-3 py-1 text-sm bg-red-100 text-red-800 rounded-full">Inactive</span>
            )}
          </div>
          <h2 className="text-2xl font-semibold">{data?.name}</h2>
          <p className="text-gray-600">{data?.email}</p>
          <p className="text-sm text-gray-500 mt-1">Username: {data?.username}</p>
          
          <button
            onClick={() => setShowChangePassword(true)}
            className="mt-4 inline-block text-sm px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition"
          >
            Change Password
          </button>
        </div>
      </div>

      {/* Organization Info */}
      {organization && (
        <div className="mt-8 border-t pt-6">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Organization Info</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <p className="font-semibold">Name:</p>
              <p className="text-gray-700">{organization.name}</p>
            </div>
            <div>
              <p className="font-semibold">Email:</p>
              <p className="text-gray-700">{organization.email}</p>
            </div>
            <div>
              <p className="font-semibold">Contact No:</p>
              <p className="text-gray-700">{organization.contact_no || "N/A"}</p>
            </div>
            <div>
              <p className="font-semibold">Website:</p>
              <p className="text-gray-700">{organization.host_url || "N/A"}</p>
            </div>
            <div className="sm:col-span-2">
              <p className="font-semibold">Address:</p>
              <p className="text-gray-700">{organization.address}</p>
            </div>
          </div>
        </div>
      )}

      {/* Password Change Modal */}
      {showChangePassword && (
        <ChangePassword
          showModal={showChangePassword}
          setShowModal={setShowChangePassword}
        />
      )}
    </div>
  );
};

export default ProfilePage;
