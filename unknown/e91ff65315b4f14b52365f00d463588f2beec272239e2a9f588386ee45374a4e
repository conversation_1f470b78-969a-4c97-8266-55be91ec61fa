import React, { useState, useRef } from "react";
import { useSelector } from "react-redux";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import heroBooksIcon from "@/assets/images/all-img/heroBooksIcon.png";

const BookCounter = ({ organization }) => {
  const { user } = useSelector((state) => state.auth);
  const [updateApi] = useUpdateApiMutation();

  // Book count editing states
  const [isEditingBookCount, setIsEditingBookCount] = useState(false);
  const [bookCountValue, setBookCountValue] = useState(
    organization?.custom_book_number ? String(organization?.custom_book_number) : "2000"
  );
  const [isSavingBookCount, setIsSavingBookCount] = useState(false);
  const [bookCountError, setBookCountError] = useState("");

  const bookCountInputRef = useRef(null);

  return (
    <div className="flex items-center justify-center flex-col gap-2 relative group">
          <img className="max-sm:w-10 mx-auto" src={heroBooksIcon} alt="" />
          <div className="relative">
            {isEditingBookCount ? (
              <div className="flex items-center">
                <input
                  ref={bookCountInputRef}
                  type="text"
                  min="1"
                  value={bookCountValue}
                  onChange={(e) => {
                    // Remove non-numeric characters and leading zeros
                    const value = e.target.value.replace(/^0+|[^0-9]/g, '');
                    setBookCountValue(value);
                    if (bookCountError) setBookCountError("");
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      // Validate input
                      if (bookCountValue === '' || bookCountValue === '0') {
                        setBookCountError("Please enter a valid number greater than 0");
                        return;
                      }

                      // Check if the value is a valid number
                      if (isNaN(Number(bookCountValue))) {
                        setBookCountError("Please enter a valid number");
                        return;
                      }

                      const submitBookCount = async () => {
                        setIsSavingBookCount(true);
                        try {
                          const formData = new FormData();
                          formData.append("custom_book_number", bookCountValue);

                          await updateApi({
                            end_point: `admin/organizations/${user?.organization_id}`,
                            body: formData
                          }).unwrap();

                          setIsEditingBookCount(false);
                          setBookCountError("");
                        } catch (err) {
                          console.error("Failed to update book count:", err);
                          setBookCountError("Failed to update. Please try again.");
                        } finally {
                          setIsSavingBookCount(false);
                        }
                      };

                      submitBookCount();
                    } else if (e.key === 'Escape') {
                      setIsEditingBookCount(false);
                      setBookCountValue(organization?.custom_book_number ? String(organization?.custom_book_number) : "2000");
                      setBookCountError("");
                    }
                  }}
                  className="w-24 text-center text-2xl max-sm:text-xl font-semibold text-blue-600 bg-transparent outline-none border-b border-gray-400 py-1 transition-all duration-200 ease-in-out focus:border-blue-500"
                  autoFocus
                />
                <span className="text-2xl max-sm:text-xl font-semibold text-blue-600">+</span>
                <span className="text-blue-600 ml-1">Books</span>

                {/* Check icon */}
                <button
                  onClick={async () => {
                    // Validate input
                    if (bookCountValue === '' || bookCountValue === '0') {
                      setBookCountError("Please enter a valid number greater than 0");
                      return;
                    }

                    // Check if the value is a valid number
                    if (isNaN(Number(bookCountValue))) {
                      setBookCountError("Please enter a valid number");
                      return;
                    }

                    setIsSavingBookCount(true);
                    try {
                      const formData = new FormData();
                      formData.append("custom_book_number", bookCountValue);

                      await updateApi({
                        end_point: `admin/organizations/${user?.organization_id}`,
                        body: formData
                      }).unwrap();

                      setIsEditingBookCount(false);
                      setBookCountError("");
                    } catch (err) {
                      console.error("Failed to update book count:", err);
                      setBookCountError("Failed to update. Please try again.");
                    } finally {
                      setIsSavingBookCount(false);
                    }
                  }}
                  className="ml-2 text-green-500 hover:text-green-700"
                  disabled={bookCountValue === '' || bookCountValue === '0' || isSavingBookCount}
                  aria-label="Submit Book Count"
                >
                  <Icon icon="heroicons-outline:check" className="w-5 h-5" />
                </button>
              </div>
            ) : (
              <div className="flex items-center">
                <p
                  className="text-blue-600 cursor-pointer"
                  onClick={() => {
                    setIsEditingBookCount(true);
                    setBookCountValue(organization?.custom_book_number ? String(organization?.custom_book_number) : "2000");
                  }}
                >
                  <span className="text-2xl max-sm:text-xl font-semibold">
                    {organization?.custom_book_number ? organization.custom_book_number : "2000"}+
                  </span>{" "}
                  Books
                </p>

                {/* Pencil icon */}
                <button
                  onClick={() => {
                    setIsEditingBookCount(true);
                    setBookCountValue(organization?.custom_book_number ? String(organization?.custom_book_number) : "2000");
                  }}
                  className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 bg-blue-50 hover:bg-blue-100 rounded-full"
                  aria-label="Edit Book Count"
                >
                  <Icon icon="heroicons-outline:pencil" className="text-blue-600 w-4 h-4" />
                </button>
              </div>
            )}

            {bookCountError && (
              <div className="text-red-500 text-xs mt-1">{bookCountError}</div>
            )}
          </div>
    </div>
  );
};

export default BookCounter;
