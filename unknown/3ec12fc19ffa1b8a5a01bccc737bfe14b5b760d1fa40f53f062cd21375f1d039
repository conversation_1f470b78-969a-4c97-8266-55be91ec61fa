import React, { useState } from "react";
import { Icon } from "@iconify/react";
import logo from "../../assets/images/promotion/logo.png";
import { Link } from "react-router-dom";
import { Popover } from "@headlessui/react";

import { useSelector } from "react-redux";
const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user } = useSelector((state) => state.auth);

  return (
    <header className="bg-white py-4 px-6 fixed w-full top-0 z-50 shadow-sm">
      <div className="max-w-7xl mx-auto">
        <nav className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <div className="">
              <img
                src={logo}
                alt="Logo"
                className="h-10 w-auto" // Adjust height and width as needed
              />
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <a href="/" className="text-gray-900 hover:text-indigo-600">
              Home
            </a>

            <Popover>
              <Popover.Button className="block text-sm font-semibold text-gray-900 hover:text-indigo-600">
                Solutions
              </Popover.Button>
              <Popover.Panel className="absolute mt-2 w-56 bg-white border border-gray-200 rounded-lg shadow-lg">
                <div className="p-3">
                  <a
                    className="block rounded-lg py-2 px-3 transition hover:bg-gray-100"
                    href="#"
                  >
                    <p className="font-semibold text-gray-900">Insights</p>
                    <p className="text-gray-500">
                      Measure actions your users take
                    </p>
                  </a>
                  <a
                    className="block rounded-lg py-2 px-3 transition hover:bg-gray-100"
                    href="#"
                  >
                    <p className="font-semibold text-gray-900">Automations</p>
                    <p className="text-gray-500">
                      Create your own targeted content
                    </p>
                  </a>
                  <a
                    className="block rounded-lg py-2 px-3 transition hover:bg-gray-100"
                    href="#"
                  >
                    <p className="font-semibold text-gray-900">Reports</p>
                    <p className="text-gray-500">Keep track of your growth</p>
                  </a>
                </div>
                <div className="p-3">
                  <a
                    className="block rounded-lg py-2 px-3 transition hover:bg-gray-100"
                    href="#"
                  >
                    <p className="font-semibold text-gray-900">Documentation</p>
                    <p className="text-gray-500">
                      Start integrating products and tools
                    </p>
                  </a>
                </div>
              </Popover.Panel>
            </Popover>
            {/* <div className="relative group">
              <button className="text-gray-900 hover:text-indigo-600 flex items-center gap-1">
                Products
                <Icon icon="mdi:chevron-down" className="w-5 h-5" />
              </button>
            </div> */}
            <a href="/demo" className="text-gray-900 hover:text-indigo-600">
              Demo
            </a>
            {/* <div className="relative group">
              <button className="text-gray-900 hover:text-indigo-600 flex items-center gap-1">
                Resources
                <Icon icon="mdi:chevron-down" className="w-5 h-5" />
              </button>
            </div> */}
            <a href="/blog" className="text-gray-900 hover:text-indigo-600">
              Blog
            </a>
            <a href="/pricing" className="text-gray-900 hover:text-indigo-600">
              Pricing
            </a>
          </div>

          {/* CTA Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            {user ? (
              <Link
                to="/dashboard"
                className="text-gray-900 hover:text-[#4C1D95] "
              >
                Dashboard
              </Link>
            ) : (
              <Link to="/login" className="text-gray-900 hover:text-[#4C1D95] ">
                Sign in
              </Link>
            )}
            <button className="bg-gradient-to-r from-indigo-500 to-purple-900 text-white px-4 py-2 rounded-md shadow-[-2px_4px_8px_0px_rgba(0,0,0,0.15)]">
              See Demo
            </button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? (
              <Icon icon="mdi:close" className="w-6 h-6" />
            ) : (
              <Icon icon="mdi:menu" className="w-6 h-6" />
            )}
          </button>
        </nav>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden mt-4 pb-4">
            <div className="flex flex-col space-y-4">
              <a href="/" className="text-gray-900 hover:text-indigo-600">
                Home
              </a>
              <a
                href="/products"
                className="text-gray-900 hover:text-indigo-600"
              >
                Products
              </a>
              <a href="/demo" className="text-gray-900 hover:text-indigo-600">
                Demo
              </a>
              <a
                href="/resources"
                className="text-gray-900 hover:text-indigo-600"
              >
                Resources
              </a>
              <a href="/blog" className="text-gray-900 hover:text-indigo-600">
                Blog
              </a>
              <a
                href="/pricing"
                className="text-gray-900 hover:text-indigo-600"
              >
                Pricing
              </a>
              <div className="pt-4 space-y-4">
                <button className="w-full text-center text-gray-900 hover:text-[#4C1D95]">
                  Sign in
                </button>
                <button className="w-full bg-gradient-to-r from-indigo-500 to-purple-900 text-white px-4 py-2 rounded-md">
                  See Demo
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
