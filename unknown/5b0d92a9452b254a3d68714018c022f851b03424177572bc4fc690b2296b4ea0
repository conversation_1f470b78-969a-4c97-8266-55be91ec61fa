import React, { useState } from "react";
import CourseIcon from "@/assets/MentorDashboard/coursesIcon.svg";
import LiveClassIcon from "@/assets/MentorDashboard/onlineClass.svg";
import StudentIcon from "@/assets/MentorDashboard/student.svg";
import AssignmentIcon from "@/assets/MentorDashboard/assignment.svg";
import { useGetApiQuery } from "@/store/api/master/commonSlice";

const StatsCard = () => {
  const [isLoading, setLoading] = useState(false);
  const { data: mentorDashboard, isError } = useGetApiQuery("mentor-dashboard");

  console.log("mentor", mentorDashboard?.data);

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error fetching data</div>;

  // Extract data from the mentorDashboard  response
  const topCardData = mentorDashboard?.data;
  // console.log(topCardData)

  const stats = [
    {
      title: "Assigned Courses",
      titleColor: "text-[#582D00]",
      value: topCardData?.course_count ?? "N/A",
      icon: "mdi:graduation-cap",
      color: "from-[#FFBE9880] to-[#FEECE280]",
      textColor: "text-[#773D00]",
      borderColor:
        "border border-[#EB8317] rounded bg-[#EB831726] text-[#EB8317]",
      iconImage: CourseIcon,
    },
    {
      title: "Upcoming Live Class",
      titleColor: "text-[#003237]",
      value: topCardData?.live_class_count ?? "N/A",
      icon: "mdi:video-outline",
      color: "from-[#AAD4D080] to-[#DEFEF680]",
      textColor: "text-[#003C43]",
      borderColor:
        "border border-[#135D66] rounded bg-[#135D6626] text-[#135D66]",
      iconImage: StudentIcon,
    },
    {
      title: "Assigned Students",
      titleColor: "text-[#000461]",
      value: topCardData?.student_count ?? "N/A",
      icon: "mdi:account-multiple-outline",
      color: "from-[#F6D2FF80] to-[#FFEDFF80]",
      textColor: "text-[#050C9C]",
      borderColor:
        "border border-[#3572EF] rounded bg-[#3572EF26] text-[#3572EF]",
      iconImage: LiveClassIcon,
    },
    {
      title: "Ongoing Assignment",
      titleColor: "text-[#3E004D]",
      value: topCardData?.upcoming_assignment_count ?? "N/A",
      icon: "mdi:clipboard-text-outline",
      color: "from-purple-100 to-purple-50",
      textColor: "text-[#6B0484]",
      borderColor:
        "border border-[#9933B1] rounded bg-[#C36ED826] text-[#9933B1]",
      iconImage: AssignmentIcon,
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
      {stats.map((stat, index) => (
        <div
          key={index}
          className={`p-2 rounded-lg shadow-md bg-gradient-to-r ${stat.color}`}
        >
          {/* Icon Section */}
          <div className="flex justify-between items-center my-auto px-3 ">
            <div className={`w-8 h-8 rounded-full ${stat.borderColor}`}>
              <img src={stat.iconImage} className="w-6 h-6 m-1" alt="" />
            </div>
            <span className={`text-md font-bold ${stat.textColor}`}>
              {stat.value}
            </span>
          </div>

          {/* Content Section */}
          <div className="mt-3 mx-2">
            <p className={`text-xs font-semibold ${stat.titleColor} `}>
              {stat.title}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
};

export default StatsCard;
