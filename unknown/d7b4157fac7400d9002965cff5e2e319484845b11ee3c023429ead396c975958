import React, { useState } from "react";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import { Formik, Form, Field } from "formik";
import * as Yup from "yup";
import { usePostApiMutation } from "@/store/api/master/commonSlice";

const ImportSubject = ({ course }) => {

  const [isOpen, setIsOpen] = useState(false);

  const openModal = () => setIsOpen(true);
  const closeModal = () => setIsOpen(false);

  const [postApi, { isLoading }] = usePostApiMutation();

  // Validation schema using Yup
  const validationSchema = Yup.object().shape({
    subjectUrl: Yup.string()
      .url("Enter a valid URL")
      .required("Subject URL is required"),
  });

  const handleSubmit = async (values, { setSubmitting, resetForm }) => {
    const urlSplit = values.subjectUrl.split("/lesson-list/");
    if (urlSplit.length !== 2) {
      alert("Invalid URL");
      return;
    }
    const [_, courseIdAndSubjectId] = urlSplit;
    const [courseId, subjectId] = courseIdAndSubjectId.split("/").map(Number);

    console.log("course id:", courseId);
    console.log("subject id:", subjectId);
    console.log("Submitted values:", values);

    const response = await postApi({
      end_point: "admin/clone-course-content",
      body: {
        external_course_id: courseId,
        external_subject_id: subjectId,
        course_id: course.id,
      }
    })

    if (response.data.status) {
        setSubmitting(false);
        resetForm();
        closeModal();
    }
  };

  return (
    <>
      <Button onClick={openModal} className="btn-primary btn-sm">Import Subject</Button>
      <Modal
        activeModal={isOpen}
        onClose={() => setIsOpen(false)}
        title="Import Subject"
        className="max-w-2xl"
      >
        <Formik
          initialValues={{
            subjectUrl: "",
          }}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ errors, touched, isSubmitting }) => (
            <Form>
              {/* URL Input Field */}
              <div className="mb-4">
                <label htmlFor="subjectUrl" className="block text-sm font-medium text-gray-700">
                  Subject URL
                </label>
                <Field
                  id="subjectUrl"
                  name="subjectUrl"
                  type="url"
                  placeholder="Enter subject URL"
                  autoComplete="off"
                  className={`mt-1 block w-full px-4 py-2 border ${
                    errors.subjectUrl && touched.subjectUrl
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500`}
                />
                {errors.subjectUrl && touched.subjectUrl && (
                  <p className="text-red-500 text-sm mt-1">{errors.subjectUrl}</p>
                )}
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                btnClass="btn-primary"
                isLoading={isLoading}
              >
                {isLoading ? "Submitting..." : "Submit"}
              </Button>
            </Form>
          )}
        </Formik>
      </Modal>
    </>
  );
};

export default ImportSubject;
