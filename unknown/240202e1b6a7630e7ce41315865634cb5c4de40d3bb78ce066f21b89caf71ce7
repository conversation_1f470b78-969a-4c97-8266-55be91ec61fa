import React from 'react';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';

const assetBaseURL = import.meta.env.VITE_ASSET_HOST_URL || '';
const placeholderImage = 'https://via.placeholder.com/300x400?text=No+Image';

const EbookViewModal = ({ ebook, isOpen, onClose }) => {
  if (!ebook) return null;

  return (
    <Modal
      title="Ebook Details"
      activeModal={isOpen}
      onClose={onClose}
      className="max-w-4xl"
    >
      <div className="p-5">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left column - Details */}
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">{ebook.title}</h2>

            <div className="grid grid-cols-2 gap-4 mb-6">
              <div>
                <p className="text-sm text-gray-500">Price</p>
                <p className="text-lg font-medium">${typeof ebook.price === 'number' ? ebook.price.toFixed(2) : ebook.price}</p>
              </div>

              <div>
                <p className="text-sm text-gray-500">Status</p>
                <p className="text-lg font-medium">
                  <span className={`inline-block px-2 py-1 rounded-full text-xs ${
                    ebook.status === 'Published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {ebook.status || 'N/A'}
                  </span>
                </p>
              </div>

              {ebook.author && (
                <div>
                  <p className="text-sm text-gray-500">Author</p>
                  <p className="text-lg font-medium">{ebook.author}</p>
                </div>
              )}

              {ebook.category && (
                <div>
                  <p className="text-sm text-gray-500">Category</p>
                  <p className="text-lg font-medium">{ebook.category}</p>
                </div>
              )}

              {ebook.published_date && (
                <div>
                  <p className="text-sm text-gray-500">Published Date</p>
                  <p className="text-lg font-medium">{ebook.published_date}</p>
                </div>
              )}
            </div>

            {ebook.description && (
              <div className="mb-6">
                <p className="text-sm text-gray-500 mb-1">Description</p>
                <p className="text-gray-700">{ebook.description}</p>
              </div>
            )}

            {/* Cover Image */}
            <div className="mb-6">
              <p className="text-sm text-gray-500 mb-2">Cover Image</p>
              <img
                src={ebook.image_url ? `${assetBaseURL}${ebook.image_url}` : placeholderImage}
                alt={ebook.title}
                className="w-full max-w-[200px] h-auto rounded-lg shadow-md object-cover"
                onError={(e) => { e.target.src = placeholderImage }}
              />
            </div>
          </div>

          {/* Right column - PDF Preview */}
          <div>
            <p className="text-sm text-gray-500 mb-2">PDF Preview</p>
            {ebook.pdf_url || ebook.pdf ? (
              <div className="relative p-2 bg-gray-100 rounded mt-2 h-[500px]">
                <embed
                  src={`${assetBaseURL}${ebook.pdf_url}`}
                  type="application/pdf"
                  width="100%"
                  height="100%"
                  className="rounded"
                />
              </div>
            ) : (
              <div className="w-full h-[500px] bg-gray-200 rounded-lg flex items-center justify-center">
                <span className="text-gray-500">No PDF available</span>
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-end mt-6">
          <Button
            text="Close"
            className="btn-outline-dark"
            onClick={onClose}
            type="button"
          />
        </div>
      </div>
    </Modal>
  );
};

export default EbookViewModal;
