import React, { useState } from "react";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Create from "./create";
import avatar from "@/assets/images/avatar/av-1.svg";

const index = ({ course }) => {
  const [showModal, setShowModal] = useState(false);

  return (
    <div className="grid grid-cols-1 p-5">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-semibold">Mentors</h2>
        <Button
          onClick={() => setShowModal(true)}
          className="btn-primary btn-sm"
        >
          Assign And Remove Mentor
        </Button>
      </div>
      <div className="">
        {!course?.course_mentor.length && (
          <p className="text-sm text-red-500">No Teacher has been assigned</p>
        )}

        <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:p-4 lg:p-6">
          {course?.course_mentor?.map((item, index) => (
            <div
              className="flex items-center p-4 border border-gray-200 rounded-lg shadow-sm bg-white"
              key={index}
            >
              <img
                src={
                  item?.mentor?.image
                    ? import.meta.env.VITE_ASSET_HOST_URL + item?.mentor?.image
                    : avatar
                }
                className="rounded-full w-12 h-12 mr-3"
                alt="avatar"
              />
              <div className="flex flex-col overflow-hidden">
                <span className="font-semibold text-sm text-gray-800 truncate">
                  {item?.mentor?.name}
                </span>
                <span className="text-xs text-gray-600 break-words">
                  {item?.mentor?.email}
                </span>
              </div>
            </div>
          ))}
        </div> 
      </div>
      <Create
        showModal={showModal}
        setShowModal={setShowModal}
        course={course}
      />
    </div>
  );
};

export default index;
