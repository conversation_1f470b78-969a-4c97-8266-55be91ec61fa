import React from "react";
import Select from "react-select";
import { useGetApiQuery } from "@/store/api/master/commonSlice";

const SelectBatch = ({courseId, onChange, ErrorMessage}) => {

    const { data: batchList, isLoading: batchLoading } = useGetApiQuery(
        `admin/batches?pagination=false${courseId ? `&course_id=${courseId}` : ""}`
    );

    return (
        <div className="col-12 col-md-6">
            <div className="row">
                <div className="col-12 mb-3">  
                    <label className="form-label">Select Batch</label>
                    <Select
                        
                        placeholder="Select Batch"
                        options={batchList?.map((student) => ({
                            label: student.name,
                            value: student.id,
                        }))}
                        onChange={(option) =>
                            onChange(option?.value)
                        }
                        className="react-select-container"
                        classNamePrefix="react-select"
                    />
                    <ErrorMessage
                        name="student_id"
                        component="div"
                        className="text-danger mt-1"
                    />
                </div>
            </div>
        </div>
    );
};

export default SelectBatch;