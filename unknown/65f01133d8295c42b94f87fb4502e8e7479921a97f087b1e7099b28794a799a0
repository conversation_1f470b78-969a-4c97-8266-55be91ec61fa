import React from "react";
import { useF<PERSON><PERSON>, <PERSON><PERSON>, FormikProvider } from "formik";
import * as Yup from "yup";
import { useNavigate } from "react-router-dom";
import Button from "@/components/ui/Button";
import {
  usePostApiMutation,
} from "@/store/api/master/commonSlice";

const validationSchema = Yup.object({
  plan_name: Yup.string().required("Plan name is required."),
  price: Yup.number().typeError("Price must be a number").required("Price is required."),
  sale_price: Yup.number().nullable(),
  dollar_price: Yup.number().nullable(),
  dollar_sale_price: Yup.number().nullable(),
  yen_price: Yup.number().nullable(),
  yen_sale_price: Yup.number().nullable(),
  krw_price: Yup.number().nullable(),
  krw_sale_price: Yup.number().nullable(),
  description: Yup.string(),
  features: Yup.array().of(Yup.string()),
  disabled_features: Yup.array().of(Yup.string()),
  is_active: Yup.boolean(),
});

const Create = () => {
  const navigate = useNavigate();

  const [postApi, { isLoading }] = usePostApiMutation();

  const formik = useFormik({
    initialValues: {
      plan_name: "",
      description: "",
      trial_period_days: 30,
      price: "",
      sale_price: "",
      dollar_price: "",
      dollar_sale_price: "",
      yen_price: "",
      yen_sale_price: "",
      krw_price: "",
      krw_sale_price: "",
      features: [""],
      disabled_features: [""],
      is_active: true,
    },
    validationSchema,
    onSubmit: async (values, { setSubmitting }) => {
      const payload = {
        ...values,
      };
      let formData = new FormData();

      Object.keys(payload).forEach((key) => {
        formData.append(key, payload[key]);
        if (key === "is_active") {
          formData.append("is_active", values.is_active ? 1 : 0);
        }
      });

      const response = await postApi({ end_point: "admin/super-admin-create-package", body: formData });

      if (response.error) {
        console.error("Error creating package:", response.error);
      }
      setSubmitting(false);

      navigate("/pricing-plan");
  
    },
  });

  const {
    values,
    handleChange,
    handleSubmit,
    isSubmitting,
    errors,
    touched,
    setFieldValue,
  } = formik;

  return (
    <FormikProvider value={formik}>
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-semibold mb-4">Create New Package</h2>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Plan Name */}
          <div>
            <label className="font-medium">Plan Name</label>
            <input
              name="plan_name"
              value={values.plan_name}
              onChange={handleChange}
              className="w-full border px-3 py-2 rounded mt-1 focus:outline-none"
            />
            {touched.plan_name && errors.plan_name && (
              <p className="text-red-500 text-sm">{errors.plan_name}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="font-medium">Description</label>
            <textarea
              name="description"
              value={values.description}
              onChange={handleChange}
              className="w-full border px-3 py-2 rounded mt-1 focus:outline-none"
              rows={3}
            />
          </div>

          {/* Prices */}
          <div className="grid grid-cols-4 gap-4">
            {["BDT", "DOLLAR", "YEN", "KRW"].map((currency) => {
              const key = currency.toLowerCase();
              return (
                <div key={currency}>
                  <label className="font-medium">{currency} Price</label>
                  <input
                    type="number"
                    name={key === "bdt" ? "price" : `${key}_price`}
                    value={key === "bdt" ? values.price : values[`${key}_price`]}
                    onChange={handleChange}
                    className="w-full border px-2 py-1 rounded mt-1 focus:outline-none"
                    placeholder="Regular Price"
                  />
                  <input
                    type="number"
                    name={key === "bdt" ? "sale_price" : `${key}_sale_price`}
                    value={key === "bdt" ? values.sale_price : values[`${key}_sale_price`]}
                    onChange={handleChange}
                    className="w-full border px-2 py-1 rounded mt-2 focus:outline-none"
                    placeholder="Sale Price"
                  />
                  {key === "bdt" && touched.price && errors.price && (
                    <p className="text-red-500 text-sm">{errors.price}</p>
                  )}
                </div>
              );
            })}
          </div>

          {/* Features */}
          <div>
            <label className="font-medium">Features</label>
            <FieldArray
              name="features"
              render={(arrayHelpers) => (
                <div className="space-y-2 mt-2">
                  {values.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <input
                        value={feature}
                        onChange={(e) => arrayHelpers.replace(index, e.target.value)}
                        className="w-full border px-2 py-1 rounded focus:outline-none"
                      />
                      <Button
                        type="button"
                        onClick={() => arrayHelpers.remove(index)}
                        className="btn-sm btn-danger"
                      >
                        X
                      </Button>
                    </div>
                  ))}
                  <Button
                    type="button"
                    className="btn-sm btn-primary"
                    onClick={() => arrayHelpers.push("")}
                  >
                    + Add Feature
                  </Button>
                </div>
              )}
            />
          </div>

          {/* Disabled Features */}
          <div>
            <label className="font-medium">Disabled Features</label>
            <FieldArray
              name="disabled_features"
              render={(arrayHelpers) => (
                <div className="space-y-2 mt-2">
                  {values.disabled_features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <input
                        value={feature}
                        onChange={(e) => arrayHelpers.replace(index, e.target.value)}
                        className="w-full border px-2 py-1 rounded focus:outline-none"
                      />
                      <Button
                        type="button"
                        onClick={() => arrayHelpers.remove(index)}
                        className="btn-sm btn-danger"
                      >
                        X
                      </Button>
                    </div>
                  ))}
                  <Button
                    type="button"
                    className="btn-sm btn-primary"
                    onClick={() => arrayHelpers.push("")}
                  >
                    + Add Disabled Feature
                  </Button>
                </div>
              )}
            />
          </div>

          {/* Active Status */}
          <div>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                name="is_active"
                checked={values.is_active}
                onChange={(e) => setFieldValue("is_active", e.target.checked)}
              />
              Active
            </label>
          </div>

          {/* Submit */}
          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-indigo-600 text-white hover:bg-indigo-700"
            >
              {isSubmitting ? "Saving..." : "Save Package"}
            </Button>
            <Button type="button" variant="outline" onClick={() => navigate(-1)}>
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </FormikProvider>
  );
};

export default Create;

