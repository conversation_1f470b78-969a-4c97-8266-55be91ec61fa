import React, { useState } from "react";
import { useSelector } from "react-redux";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import { ASSET_URL } from "@/config";
import heroImg2 from "@/assets/images/all-img/heroImg2.png";
import heroShape from "@/assets/images/all-img/heroShape.png";

const BannerEditor = ({ organization }) => {
  const { user } = useSelector((state) => state.auth);
  const [bannerFile, setBannerFile] = useState(null);
  const [isSavingBanner, setIsSavingBanner] = useState(false);
  
  const [updateApi] = useUpdateApiMutation();

  return (
    <div className="relative">
      <div className="relative group">
        <input
          type="file"
          accept="image/*"
          className="hidden"
          id="banner-image"
          onChange={async (e) => {
            if (e.target.files && e.target.files[0]) {
              const file = e.target.files[0];
              setBannerFile(file);

              // Automatically upload the image when selected
              setIsSavingBanner(true);
              try {
                const formData = new FormData();
                formData.append("banner", file);

                await updateApi({
                  end_point: `admin/organizations/${user?.organization_id}`,
                  body: formData
                }).unwrap();
              } catch (err) {
                console.error("Failed to update banner:", err);
              } finally {
                setIsSavingBanner(false);
                setBannerFile(null);
              }
            }
          }}
        />
        <label htmlFor="banner-image" className="cursor-pointer">
          <div className="relative">
            <img
              className="xl:max-w-2xl md:max-w-xl relative z-10 rounded-lg"
              src={bannerFile ? URL.createObjectURL(bannerFile) : (organization?.banner ? ASSET_URL + organization?.banner : heroImg2)}
              alt="Banner"
            />
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center rounded-lg z-20">
              {isSavingBanner ? (
                <div className="p-3 bg-blue-500 rounded-full">
                  <Icon icon="eos-icons:loading" className="w-6 h-6 text-white animate-spin" />
                </div>
              ) : (
                <Icon
                  icon="mdi:camera"
                  className="w-10 h-10 text-white opacity-0 group-hover:opacity-100"
                />
              )}
            </div>
          </div>
        </label>
      </div>
      <img
        className="absolute -right-20 bottom-0 z-0"
        src={heroShape}
        alt=""
      />
    </div>
  );
};

export default BannerEditor;
