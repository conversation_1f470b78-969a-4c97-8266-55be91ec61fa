import React, { useRef, useEffect } from 'react';
import { Draggable } from 'react-beautiful-dnd';
import '../../assets/css/strict-vertical-drag.css';

/**
 * StrictVerticalDraggable - A component that strictly enforces vertical-only dragging
 */
const StrictVerticalDraggable = ({ id, index, children, className = '', ...props }) => {
  const nodeRef = useRef(null);
  
  // Function to enforce vertical-only movement during drag
  const enforceVerticalOnly = (style) => {
    if (!style || !style.transform) return style;
    
    // Extract the y-transform value
    const match = style.transform.match(/translate\(.+?px,\s*(.+?)px\)/);
    if (!match || match.length < 2) return style;
    
    const y = match[1];
    
    // Set a CSS variable for the y position that our CSS can use
    if (nodeRef.current) {
      nodeRef.current.style.setProperty('--y', `${y}px`);
    }
    
    // Return style without the transform (our CSS will handle it)
    return {
      ...style,
      transform: 'none', // Remove the transform, our CSS will apply it
    };
  };
  
  // Add/remove body class for dragging state
  useEffect(() => {
    const handleDragStart = () => {
      document.body.classList.add('dragging-vertical');
      document.body.style.userSelect = 'none';
    };
    
    const handleDragEnd = () => {
      document.body.classList.remove('dragging-vertical');
      document.body.style.userSelect = '';
    };
    
    window.addEventListener('dragstart', handleDragStart);
    window.addEventListener('dragend', handleDragEnd);
    
    return () => {
      window.removeEventListener('dragstart', handleDragStart);
      window.removeEventListener('dragend', handleDragEnd);
      document.body.classList.remove('dragging-vertical');
      document.body.style.userSelect = '';
    };
  }, []);
  
  return (
    <Draggable
      draggableId={id.toString()}
      index={index}
      {...props}
    >
      {(provided, snapshot) => {
        // Apply our vertical-only style modifications
        const style = enforceVerticalOnly(provided.draggableProps.style);
        
        return (
          <div
            ref={(el) => {
              nodeRef.current = el;
              provided.innerRef(el);
            }}
            {...provided.draggableProps}
            style={style}
            className={`strict-vertical-item ${className} ${snapshot.isDragging ? 'dragging' : ''}`}
            data-is-dragging={snapshot.isDragging}
          >
            {typeof children === 'function' 
              ? children(provided, snapshot, nodeRef) 
              : children}
          </div>
        );
      }}
    </Draggable>
  );
};

export default StrictVerticalDraggable;
