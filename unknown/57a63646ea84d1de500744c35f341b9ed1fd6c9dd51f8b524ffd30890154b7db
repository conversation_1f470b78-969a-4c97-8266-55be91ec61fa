import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Create from "./create";
import Edit from "./edit";
import Delete from "./Delete";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";

const index = () => {
  const dispatch = useDispatch();

  // const [showModal, setShowModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);

  const { showModal, showEditModal } = useSelector((state) => state.commonReducer);
  
  const [apiParam, setApiParam] = useState("");

  const { data, isLoading, error } = useGetApiQuery(`admin/announcements${apiParam ? `?${apiParam}` : ''}`);

  const changePage = (val) => {
    setApiParam(val);
  };

  
  const columns = [
    {
      label: "SL",
      field: "id",
    },
    {
      label: "Name",
      field: "name",
    },
    {
      label: "Image",
      field: "image",
    },
    {
      label: "Available On",
      field: "available_on",
    },
    {
      label: "Action",
      field: "",
    },
  ];
  console.log(data);

  const tableData = data?.map((item, index) => {
    return {
      id: index + 1,
      name: item.title,
      available_on: item.start_date && item.end_date
        ? `${new Intl.DateTimeFormat("en-GB", {
            day: "numeric",
            month: "short",
            year: "numeric",
          }).format(new Date(item.start_date))} - ${new Intl.DateTimeFormat("en-GB", {
            day: "numeric",
            month: "short",
            year: "numeric",
          }).format(new Date(item.end_date))}`.replace(
            /-/g,
            " - "
          )
        : "",
      available_on_color: item.start_date && item.end_date
        ? new Date() > new Date(item.start_date) &&
          new Date() < new Date(item.end_date)
          ? "bg-success-500"
          : "bg-danger-500"
        : "",
      is_free: (
        <Badge
          className={
            item.is_free
              ? "bg-success-500 text-white"
              : "bg-danger-500 text-white"
          }
        >
          {item.is_free ? "YES" : "NO"}
        </Badge>
      ),
      image : (
        item.image ?  <img src={ import.meta.env.VITE_ASSET_HOST_URL +  item.image } alt={item.title} className="w-auto h-10" /> : ""
      ),
    };
  });

  const actions = [
    {
      name: "edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        console.log(val);
        console.log(data[val]);
        dispatch(setEditData(data[val]));
        dispatch(setEditShowModal(true));
      },
    },    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data[val]);
        setShowDeleteModal(true);
      },
    },
  ];
  // const changePage = (item) => {
  //   console.log(item);
  // };
  const handleSubmit = () => {
    // setShowModal(false);
  };


  return (
    <div>
      <BasicTablePage
        title="Announcement List"
        createButton="Create an Announcement"
        actions={actions}
        columns={columns}
        data={tableData}
        changePage={changePage}
        submitForm={handleSubmit}
        currentPage={data?.current_page}
        totalPages={Math.ceil(data?.total / data?.per_page)}
        filter={false}
      />
      { showModal && <Create /> }
      { showEditModal && <Edit /> }

      { showDeleteModal && 
        <Delete
          showDeleteModal={showDeleteModal}
          setShowDeleteModal={setShowDeleteModal}
          data={deleteData}
        />
      }
    </div>
  );
};

export default index;
