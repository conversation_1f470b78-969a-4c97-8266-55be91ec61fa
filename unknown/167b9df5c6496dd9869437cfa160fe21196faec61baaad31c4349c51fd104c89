import React, { useState } from "react";
import Modal from "@/components/ui/Modal";

const ActionButton = ({ selectedDate }) => {


 
  return (
    <Modal
      activeModal={showModal}
      onClose={() => dispatch(setShowModal(false))}
      title="Add New Class"
      className="max-w-4xl p-6"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setShowModal(false))}
        />
      }
    >
   
    </Modal>
  );
};

export default ActionButton;

