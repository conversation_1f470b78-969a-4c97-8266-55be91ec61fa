import React, { useState } from "react";

import Card from "@/components/ui/Card";
import { useGetSubjectListQuery } from "@/store/api/master/rowContentSubjectListSlice";
import Badge from "@/components/ui/Badge";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import CreateSubject from "./createSubject";
import { useGetApiQuery } from "@/store/api/master/commonSlice";

const index = () => {
  const [showModal, setShowModal] = useState(false);
  const dispatch = useDispatch();
  const { id } = useParams();
  const subject = useGetApiQuery(`admin/content-subject-list/${id}`)?.data;
  console.log(subject);

  return (
    <>
      <Card>
        <div className="flex justify-end my-4">
          <button
            className="btn btn-primary btn-sm mt-4"
            onClick={() => setShowModal(true)}
          >
            Add New Subject
          </button>
        </div>

        <div className="flex mt-2 pb-2 bg-white p-4 rounded-lg shadow border dark:border-gray-700">
          <div className="w-full">
            <table className="table w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="px-4 py-2">SL</th>
                  <th className="px-4 py-2">Class</th>
                  <th className="px-4 py-2">Subject</th>
                  <th className="px-4 py-2">Action</th>
                </tr>
              </thead>
              <tbody>
                {subject?.map((item, index) => (
                  <tr key={index} className="border-b">
                    <td className="px-4 py-2 text-center">{item.id}</td>
                    <td className="px-4 py-2 text-center">{item.class_name}</td>
                    <td className="px-4 py-2 text-center">
                      {item.subject_name}
                    </td>
                    <td className="px-4 py-2 text-center">
                      <button className="btn btn-sm btn-secondary">Edit</button>
                      {/* <button className="btn btn-sm btn-danger">Delete</button> */}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </Card>
      <CreateSubject showModal={showModal} setShowModal={setShowModal} />
    </>
  );
};

export default index;
