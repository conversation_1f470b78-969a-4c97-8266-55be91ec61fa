import { apiSlice } from "../apiSlice";
export const mentorSettingApi = apiSlice.injectEndpoints({
  reducerPath: "mentorSettingApi",
  tagTypes: ["Mentor"],
  endpoints: (builder) => ({
    getMentorList: builder.query({
      query: (params) => ({
        url: "admin/all-mentor-list-admin",
        method: "GET",
        params: params
      }),
      providesTags: ["Mentor"],
    }),
    mentorCreate: builder.mutation({
      query: (body) => {
        return {
          url: `admin/mentor-create`,
          method: "POST",
          body: body,
        };
      },
      invalidatesTags: ["Mentor"],
    }),
    mentorUpdate: builder.mutation({
      query: (body) => {
        return {
          url: `admin/mentor-update/${body.id}`,
          method: "POST",
          body: body.formData,
        };
      },
      invalidatesTags: ["Mentor"],
    }),
  }),
});

export const { useGetMentorListQuery, 
  useMentorCreateMutation,
  useMentorUpdateMutation,
 } = mentorSettingApi;
