# Course Duration Fields Implementation

## Overview
Added support for displaying and editing `course_duration` and `duration_per_day` fields from the main course object. Both fields are shown in hours and can be updated through the course edit form.

## Changes Made

### 1. CourseStats Component (`src/pages/CourseSetup/Course/CourseDetailsComponents/CourseStats.jsx`)

**Added:**
- Import for `Clock` icon from lucide-react
- New "Duration Stats" section displaying both duration fields
- Two new StatCard components for:
  - **Total Course Duration**: Shows `course_duration` in hours
  - **Duration Per Day**: Shows `duration_per_day` in hours
- Fallback text "Not specified" when values are 0 or null

**Display Format:**
```javascript
// Total Course Duration
value={course?.course_duration ? `${course.course_duration} hours` : 'Not specified'}

// Duration Per Day  
value={course?.duration_per_day ? `${course.duration_per_day} hours` : 'Not specified'}
```

### 2. CourseEditForm Component (`src/pages/CourseSetup/Course/CourseDetailsComponents/CourseEditForm.jsx`)

**Added:**
- Duration fields to `initialValues`:
  ```javascript
  course_duration: course?.course_duration || 0,
  duration_per_day: course?.duration_per_day || 0,
  ```

- New form section with two input fields:
  ```javascript
  {/* Duration Fields */}
  <div className="grid md:grid-cols-2 gap-2 my-4">
    <InputField
      label="Total Course Duration (Hours)"
      name="course_duration"
      type="number"
      placeholder="Enter total course duration in hours"
      value={values.course_duration}
      onChange={(e) => setFieldValue('course_duration', e.target.value)}
      error={errors?.course_duration}
    />
    <InputField
      label="Duration Per Day (Hours)"
      name="duration_per_day"
      type="number"
      placeholder="Enter daily duration in hours"
      value={values.duration_per_day}
      onChange={(e) => setFieldValue('duration_per_day', e.target.value)}
      error={errors?.duration_per_day}
    />
  </div>
  ```

### 3. Details Component (`src/pages/CourseSetup/Course/Details.jsx`)

**Added to handleSubmit function:**
```javascript
// Add duration fields
formData.append('course_duration', values.course_duration || '0');
formData.append('duration_per_day', values.duration_per_day || '0');
```

### 4. Form Validation (`src/pages/CourseSetup/Course/formCourse.js`)

**Added to initialValues:**
```javascript
course_duration: 0,
duration_per_day: 0,
```

**Added to validationSchema:**
```javascript
course_duration: yup.number().min(0, "Course duration should be 0 or positive").nullable(),
duration_per_day: yup.number().min(0, "Duration per day should be 0 or positive").nullable(),
```

## Features

### Display Features
- ✅ Shows duration fields in course details view
- ✅ Displays values in hours format (`X hours`)
- ✅ Shows "Not specified" when value is 0 or null
- ✅ Uses appropriate icons (Clock) for duration fields
- ✅ Responsive grid layout (2 columns on desktop, 1 on mobile)

### Edit Features
- ✅ Two separate input fields for each duration type
- ✅ Number input type for proper validation
- ✅ Clear labels indicating hours unit
- ✅ Placeholder text for guidance
- ✅ Form validation (non-negative numbers)
- ✅ Proper error handling and display

### Data Handling
- ✅ Reads existing values from course object
- ✅ Defaults to 0 when no value exists
- ✅ Sends data to backend via FormData
- ✅ Handles null/undefined values gracefully

## Usage

### Viewing Duration Information
Duration information is automatically displayed in the course details page in the "Duration Stats" section, showing:
- **Total Course Duration**: The complete duration of the course in hours
- **Duration Per Day**: The expected daily study/class duration in hours

### Editing Duration Information
1. Navigate to course details page
2. Click the edit button to open the course edit form
3. Scroll to the "Duration Fields" section
4. Enter values in hours for:
   - **Total Course Duration (Hours)**: Complete course duration
   - **Duration Per Day (Hours)**: Daily duration
5. Click Submit to save changes

### Validation Rules
- Both fields accept numbers only
- Values must be 0 or positive
- Fields are optional (can be left empty/0)
- Invalid values will show error messages

## Technical Notes
- Fields are stored as numeric values in the database
- Frontend displays values with "hours" suffix for clarity
- Form validation prevents negative values
- Null/undefined values are handled gracefully
- Uses FormData for backend submission
- Responsive design works on all screen sizes
