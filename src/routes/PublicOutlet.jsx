import { Outlet, useNavigate } from 'react-router-dom';
import { useEffect } from 'react';

const PublicOutlet = () => {
  const navigate = useNavigate();
  
  useEffect(() => { 
    const isAuthenticated = () => {
       return localStorage.getItem("lms_token") !== null;
   };
    const localAuth = isAuthenticated();
    if (localAuth) {navigate('/dashboard', {replace: true})}
  }, [navigate])

  return <Outlet />
};

export default PublicOutlet;