export const superAdminMenuItems = [
  {
    title: "Dashboard",
    isHide: true,
    icon: "heroicons-outline:home",
    link: "/dashboard",
  },
  {
    title: "Organization List",
    icon: "heroicons:list-bullet",
    link: "/organizations",
    isHide: false,

  },
  // {
  //   title: "User List",
  //   icon: "heroicons:list-bullet",
  //   link: "/sample-page",
  //   isHide: false,
  // },
  // {
  //   title: "Payments",
  //   icon: "heroicons-outline:currency-dollar",
  //   link: "/announcement",
  //   isHide: false,
  // },
  {
    title: "Price Plan",
    icon: "heroicons-outline:credit-card",
    link: "/pricing-plan",
    isHide: false,
  },
  {
    title: "User Story",
    icon: "heroicons-outline:cog",
    link: "/user-stories",
    isHide: false,
  },
  {
    title: "Traffics",
    icon: "heroicons:chart-bar",
    link: "/traffics",
    isHide: false,
  },
  {
    title: "Currencies",
    icon: "heroicons:currency-dollar",
    link: "/currencies",
    isHide: false,
  },


];


export const menuItems = [
  {
    title: "Dashboard",
    isHide: true,
    icon: "heroicons-outline:home",
    link: "/dashboard",
  },
  {
    title: "Menu Settings",
    icon: "heroicons:cog",
    link: "/course-category",
    isHide: false,
    // badge: "1",
  },
  {
    title: "Course",
    icon: "line-md:text-box-multiple-to-text-box-transition",
    link: "/course-setup",
    isHide: false,
    child: [
      {
        childtitle: "Categories",
        childlink: "/categories",
        isOnlyBacBon: true,
      },
      {
        childtitle: "Create Course",
        childlink: "/course-create",
        isOnlyBacBon: true,
      },
      {
        childtitle: "Course List",
        childlink: "/course-list",
        isOnlyBacBon: true,
      },

    ],
  },
  {
    title: "Batch",
    icon: "hugeicons:teaching",
    isHide: false,
    child: [
      {
        childtitle: "Create Batch",
        childlink: "/batch-create",
        isOnlyBacBon: true,
      },
      {
        childtitle: "Batch List",
        childlink: "/batch-list",
        isOnlyBacBon: true,
      },
    ]
  },
  {
    title: "Teachers",
    icon: "hugeicons:user-edit-01",
    link: "/mentor-list",
    isHide: false,
    // badge: "1",
  },
  {
    title: "Students",
    icon: "ph:student-bold",
    link: "/student-list",
    isHide: false,
    // badge: "1",
  },
  {
    title: "Ebooks",
    icon: "mdi:book-open-page-variant",
    link: "/ebooks",
    isHide: false,
  },
  {
    title: "Routine",
    icon: "carbon:task-complete",
    link: "/class-list",
    isHide: false,
    // badge: "1",
  },
  {
    title: "Attendance",
    icon: "formkit:people",
    link: "/attendance",
    isHide: false,
    // badge: "1",
  },
  {
    title: "Exam Results",
    icon: "icon-park-outline:buy",
    isHide: false,
    child: [
      {
        childtitle: "Online Exam",
        childlink: "/exam-result-list",
        isOnlyBacBon: true,
      },
      {
        childtitle: "Offline Exam",
        childlink: "/offline-exam-results",
        isOnlyBacBon: true,
      },
    ],
  },

  {
    title: "Enrollment",
    icon: "icon-park-outline:buy",
    isHide: false,
    child: [
      {
        childtitle: "Enroll",
        childlink: "/enroll",
        isOnlyBacBon: true,
      },
      {
        childtitle: "Enrollment List",
        childlink: "/enrollment-list",
        isOnlyBacBon: true,
      },
    ],
    // badge: "1",
  },

  {
    title: "Assignment",
    icon: "material-symbols:assignment-add-outline",
    isHide: false,
    child: [
      {
        childtitle: "Create Assignment",
        childlink: "/create-assignment",
        isOnlyBacBon: true,
      },
      {
        childtitle: "Assignment List",
        childlink: "/assignment-list",
        isOnlyBacBon: true,
      },
    ],
  },
  {
    title: "Payment",
    icon: "heroicons-outline:currency-bangladeshi",
    isHide: false,
    child: [
      {
        childtitle: "Settings",
        childlink: "/payment-settings",
        isOnlyBacBon: true,
      },
      {
        childtitle: "Payment Collection",
        childlink: "/payment-collection",
        isOnlyBacBon: true,
      },
      {
        childtitle: "All Payments",
        childlink: "/all-payments",
        isOnlyBacBon: true,
      },
      {
        childtitle: "Pending Payment List",
        childlink: "/pending-payment-list",
        isOnlyBacBon: true,
      },
    ],
    // badge: "1",
  },
  {
    title: "Promotional Items",
    icon: "mdi:teach",
    link: "/promotional-items",
    isHide: false,
    // badge: "1",
  },
  {
    title: "All Users",
    icon: "hugeicons:user",
    link: "/user-list",
    isHide: false,
  },
  {
    title: "Announcement",
    icon: "mi:notification",
    link: "/announcement",
    isHide: false,
  },
  {
    title: "Testimonials",
    icon: "mdi:comment-quote-outline",
    link: "/testimonials",
    isHide: false,
  },
  // {
  //   title: "User Stories",
  //   icon: "heroicons:document-text",
  //   link: "/user-stories",
  //   isHide: false,
  // },
  {
    title: "Discussions",
    icon: "heroicons:chat-bubble-left-right",
    link: "/discussions",
    isHide: false,
  },
  {
    title: "Master Settings",
    icon: "mi:settings",
    link: "/#",
    isHide: false,
    child: [
      {
        childtitle: "Website Settings",
        childlink: "/configure-organization",
        isOnlyBacBon: false,
      },
      {
        childtitle: "Payment Gateways Settings",
        childlink: "/payment-gateways",
        isOnlyBacBon: false,
      },
      {
        childtitle: "Coupon Settings",
        childlink: "/coupons",
        isOnlyBacBon: false,
      },
      {
        childtitle: "Translation Settings",
        childlink: "/translations",
        isOnlyBacBon: false,
      },

    ],
  },
];

export const topMenu = [
  {
    title: "Dashboard",
    icon: "heroicons-outline:home",
    link: "//app/home",
    child: [
      {
        childtitle: "Analytics Dashboard",
        childlink: "/dashboard",
        childicon: "heroicons:presentation-chart-line",
      },
    ],
  },
];


export const colors = {
  primary: "#4669FA",
  secondary: "#A0AEC0",
  danger: "#F1595C",
  black: "#111112",
  warning: "#FA916B",
  info: "#0CE7FA",
  light: "#425466",
  success: "#50C793",
  "gray-f7": "#F7F8FC",
  dark: "#1E293B",
  "dark-gray": "#0F172A",
  gray: "#68768A",
  gray2: "#EEF1F9",
  "dark-light": "#CBD5E1",
};

