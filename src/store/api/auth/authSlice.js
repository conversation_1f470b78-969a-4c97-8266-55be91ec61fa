import { createSlice } from "@reduxjs/toolkit";

const storedUser = JSON.parse(localStorage.getItem("user"));
const storedOrganization = storedUser?.organization;

export const authSlice = createSlice({
  name: "auth",
  initialState: {
    user: storedUser || null,
    organization: storedOrganization || null,
    isAuth: !!storedUser,
  },
  reducers: {
    setUser: (state, action) => {
      state.user = action.payload;
      state.isAuth = true;
    },
    setOrganization: (state, action) => {
      state.organization = action.payload;
    },
    logOut: (state, action) => {
      state.user = null;
      state.isAuth = false;
    },
  },
});

export const { setUser, setOrganization, logOut } = authSlice.actions;
export default authSlice.reducer;
