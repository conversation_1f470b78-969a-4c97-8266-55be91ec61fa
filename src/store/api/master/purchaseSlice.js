import { apiSlice } from "../apiSlice";
export const purchaseListApi = apiSlice.injectEndpoints({
  reducerPath: "purchaseListApi",
  tagTypes: ["Purchase"],
  endpoints: (builder) => ({
    getPurchaseList: builder.query({
      query: (params) => ({
        url: "admin/course-payment-list-by-course-id/" + params,
        method: "GET",
      }),
      providesTags: ["Purchase"],
    }),
  }),
});

export const { useGetPurchaseListQuery } = purchaseListApi;
