import { apiSlice } from "../apiSlice";

export const currencyApi = apiSlice.injectEndpoints({
  reducerPath: "currencyApi",
  tagTypes: ["Currency"],
  endpoints: (builder) => ({
    getCurrencies: builder.query({
      query: () => ({
        url: "admin/currencies",
        method: "GET",
      }),
      providesTags: ["Currency"],
    }),
    getCurrency: builder.query({
      query: (id) => ({
        url: `admin/currencies/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Currency", id }],
    }),
    createCurrency: builder.mutation({
      query: (body) => ({
        url: "admin/currencies",
        method: "POST",
        body: {
          ...body,
          exchange_rate: body.exchange_rate || "0.00000"
        },
      }),
      invalidatesTags: ["Currency"],
    }),
    updateCurrency: builder.mutation({
      query: ({ id, ...body }) => ({
        url: `admin/currencies/${id}`,
        method: "PUT",
        body: {
          ...body,
          exchange_rate: body.exchange_rate || "0.00000"
        },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Currency", id },
        "Currency",
      ],
    }),
    partialUpdateCurrency: builder.mutation({
      query: ({ id, ...body }) => ({
        url: `admin/currencies/${id}`,
        method: "PATCH",
        body,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Currency", id },
        "Currency",
      ],
    }),
    deleteCurrency: builder.mutation({
      query: (id) => ({
        url: `admin/currencies/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Currency"],
    }),
  }),
});

export const {
  useGetCurrenciesQuery,
  useGetCurrencyQuery,
  useCreateCurrencyMutation,
  useUpdateCurrencyMutation,
  usePartialUpdateCurrencyMutation,
  useDeleteCurrencyMutation,
} = currencyApi;
