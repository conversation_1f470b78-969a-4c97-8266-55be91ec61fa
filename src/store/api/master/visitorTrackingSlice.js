import { apiSlice } from "../apiSlice";

export const visitorTrackingApi = apiSlice.injectEndpoints({
  reducerPath: "visitorTrackingApi",
  tagTypes: ["VisitorTracking"],
  endpoints: (builder) => ({
    trackVisitor: builder.mutation({
      query: (body) => ({
        url: "website/track-visitor",
        method: "POST",
        body: body,
      }),
      invalidatesTags: ["VisitorTracking"],
    }),
    getVisitorStats: builder.query({
      query: (params = {}) => ({
        url: "admin/visitor-stats",
        method: "GET",
        params: {
          date_range: params.date_range || 7,
          ...params
        },
      }),
      providesTags: ["VisitorTracking"],
    }),
    getVisitorList: builder.query({
      query: (params = {}) => ({
        url: "admin/visitor-list",
        method: "GET",
        params: {
          per_page: params.per_page || 50,
          page: params.page || 1,
          date_range: params.date_range || 7,
          ...params
        },
      }),
      providesTags: ["VisitorTracking"],
    }),
  }),
});

export const { 
  useTrackVisitorMutation,
  useGetVisitorStatsQuery,
  useGetVisitorListQuery
} = visitorTrackingApi;
