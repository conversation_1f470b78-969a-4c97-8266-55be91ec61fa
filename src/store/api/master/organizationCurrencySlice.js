import { apiSlice } from "../apiSlice";

export const organizationCurrencyApi = apiSlice.injectEndpoints({
  reducerPath: "organizationCurrencyApi",
  tagTypes: ["OrganizationCurrency", "AvailableCurrency"],
  endpoints: (builder) => ({
    getOrganizationCurrencies: builder.query({
      query: () => ({
        url: "admin/org-currencies",
        method: "GET",
      }),
      providesTags: ["OrganizationCurrency"],
    }),
    getAvailableCurrencies: builder.query({
      query: () => ({
        url: "admin/org-currencies/available",
        method: "GET",
      }),
      providesTags: ["AvailableCurrency"],
    }),
    getAllCurrencies: builder.query({
      query: () => ({
        url: "admin/currencies",
        method: "GET",
      }),
      providesTags: ["Currency"],
    }),
    assignCurrency: builder.mutation({
      query: (body) => ({
        url: "admin/org-currencies/assign",
        method: "POST",
        body,
      }),
      invalidatesTags: ["OrganizationCurrency", "AvailableCurrency"],
    }),
    updateOrganizationCurrency: builder.mutation({
      query: ({ id, ...body }) => ({
        url: `admin/org-currencies/${id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["OrganizationCurrency"],
    }),
    removeOrganizationCurrency: builder.mutation({
      query: (id) => ({
        url: `admin/org-currencies/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["OrganizationCurrency", "AvailableCurrency"],
    }),
  }),
});

export const {
  useGetOrganizationCurrenciesQuery,
  useGetAvailableCurrenciesQuery,
  useGetAllCurrenciesQuery,
  useAssignCurrencyMutation,
  useUpdateOrganizationCurrencyMutation,
  useRemoveOrganizationCurrencyMutation,
} = organizationCurrencyApi;
