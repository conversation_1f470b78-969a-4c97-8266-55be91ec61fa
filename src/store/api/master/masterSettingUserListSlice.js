import { apiSlice } from "../apiSlice";
export const masterSettingUserListApi = apiSlice.injectEndpoints({
  reducerPath: "masterSettingUserListApi",
  tagTypes: ["User"],
  endpoints: (builder) => ({
    getUserList: builder.query({
      query: () => ({
        url: "admin/all-student-list-admin", 
        method: "GET",
      }),
      providesTags: ["User"],
    }),
  }),
});

export const { useGetUserListQuery } = masterSettingUserListApi;
