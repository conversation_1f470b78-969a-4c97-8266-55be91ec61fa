import { apiSlice } from "../apiSlice";

export const allPaymentsApi = apiSlice.injectEndpoints({
  reducerPath: "allPaymentsApi",
  tagTypes: ["AllPayments"],
  endpoints: (builder) => ({
    getAllPayments: builder.query({
      query: (params) => ({
        url: "admin/all-payments",
        method: "GET",
        params: params
      }),
      providesTags: ["AllPayments"],
    }),
    getPaymentDetails: builder.query({
      query: (id) => ({
        url: `admin/payments/${id}`,
        method: "GET",
      }),
      providesTags: ["AllPayments"],
    }),
  }),
});

export const { 
  useGetAllPaymentsQuery,
  useGetPaymentDetailsQuery
} = allPaymentsApi;
