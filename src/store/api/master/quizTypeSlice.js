import { apiSlice } from "../apiSlice";
export const quizTypeListApi = apiSlice.injectEndpoints({
  reducerPath: "quizTypeListApi",
  tagTypes: ["QuizTypeList"],
  endpoints: (builder) => ({
    getQuizTypeList: builder.query({
      query: () => ({
        url: "admin/quiz-type-list", 
        method: "GET",
      }),
      providesTags: ["QuizTypeList"],
    }),
  }),
});

export const { useGetQuizTypeListQuery } = quizTypeListApi;