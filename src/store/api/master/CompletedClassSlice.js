import { apiSlice } from "../apiSlice";
export const completedClassApi = apiSlice.injectEndpoints({
  reducerPath: "completedClassApi",
  tagTypes: ["CompletedClass"],
  endpoints: (builder) => ({
    getCourseListFilter: builder.query({
      query: () => ({
        url: "admin/course-list-for-filter",
        method: "GET",
      }),
      providesTags: ["CompletedClass","CourseList"],
    }),
    getMentorListFilter: builder.query({
        query: () => ({
          url: "admin/mentor-list-for-filter",
          method: "GET",
        }),
        providesTags: ["CompletedClass"],
      }),
  }),
});

export const { useGetCourseListFilterQuery , useGetMentorListFilterQuery} = completedClassApi;
