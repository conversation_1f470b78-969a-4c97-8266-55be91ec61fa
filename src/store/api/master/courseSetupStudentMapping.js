import { apiSlice } from "../apiSlice";
export const courseSetupStudentMappingListApi = apiSlice.injectEndpoints({
  reducerPath: "courseSetupStudentMappingListApi",
  tagTypes: ["StudentMapping"],
  endpoints: (builder) => ({
    getStudentMappingList: builder.query({
      query: () => ({
        url: "admin/student-mapping-list", 
        method: "GET",
      }),
      providesTags: ["StudentMapping"],
    }),
  }),
});

export const { useGetStudentMappingListQuery} = courseSetupStudentMappingListApi;
