import { apiSlice } from "../apiSlice";
export const enrollmentSettingApi = apiSlice.injectEndpoints({
  reducerPath: "enrollmentSettingApi",
  tagTypes: ["Enrollment"],
  endpoints: (builder) => ({
    getEnrollmentList: builder.query({
      query: (params) => ({
        url: "admin/enrollment-list/"+params,
        method: "GET",
      }),
      providesTags: ["Enrollment"],
    }),
  }),
});

export const { useGetEnrollmentListQuery } = enrollmentSettingApi;
