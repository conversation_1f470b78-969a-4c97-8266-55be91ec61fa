import { apiSlice } from "../apiSlice";
export const rowContentQuizListApi = apiSlice.injectEndpoints({
  reducerPath: "rowContentQuizListApi",
  tagTypes: ["QuizList"],
  endpoints: (builder) => ({
    getQuizList: builder.query({
      query: (params) => ({
        url:"admin/chapter-quiz-list" + params,
        method: "GET",
      }),
      providesTags: ["QuizList"],
    }),
    getQuizDetails: builder.query({
      query: (params) => ({
        url:"admin/quiz-details-by-id/" + params,
        method: "GET",
      }),
      providesTags: ["QuizDetails"],
    }),
    getQuestionList: builder.query({
      query: (id) => ({
        url:"admin/question-list-by-quiz/" + id,
        method: "GET",
      }),
      providesTags: ["QuestionList"],
    }),
    getSubjectByQuizList: builder.query({
      query: (id) => ({
        url:"admin/chapter-quiz-subject-list/" + id,
        method: "GET",
      }),
      providesTags: ["QuizSubjectList"],
    }),
    getCoreSubjectList: builder.query({
      query: (id) => ({
        url:"admin/core-subject-list",
        method: "GET",
      }),
      providesTags: ["CoreSubjectList"],
    }),
    getQuestionSetList: builder.query({
      query: (id) => ({
        url:"admin/question-set-list",
        method: "GET",
      }),
      providesTags: ["QuestionSetList"],
    }),



    quizCreateOrUpdate: builder.mutation({
      query: (body) => ({
        url:"admin/chapter-quiz-save-or-update",
        method: "POST",
        body: body,
      }),
      invalidatesTags: ["QuizList"],
    }), 
    quizSubjectCreateOrUpdate: builder.mutation({
      query: (body) => ({
        url:"admin/quiz-subject-save-or-update",
        method: "POST",
        body: body,
      }),
      invalidatesTags: ["QuizSubjectList"],
    }),
    questionCreateOrUpdate: builder.mutation({
      query: (body) => ({
        url:"admin/chapter-quiz-question-save-or-update",
        method: "POST",
        body: body,
      }),
      invalidatesTags: ["QuestionList"],
    }),
  }),
});

export const { 
  useGetQuizListQuery, 
  useGetQuizDetailsQuery, 
  useGetQuestionListQuery, 
  useGetSubjectByQuizListQuery, 
  useGetCoreSubjectListQuery,
  useGetQuestionSetListQuery,
  useQuizCreateOrUpdateMutation,
  useQuizSubjectCreateOrUpdateMutation,
  useQuestionCreateOrUpdateMutation
} = rowContentQuizListApi;
