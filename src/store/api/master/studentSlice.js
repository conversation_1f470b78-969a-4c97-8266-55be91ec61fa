import { apiSlice } from "../apiSlice";
export const studentSettingApi = apiSlice.injectEndpoints({
  reducerPath: "studentSettingApi",
  tagTypes: ["Student"],
  endpoints: (builder) => ({
    getStudentList: builder.query({
      query: (params) => ({
        url: "admin/all-student-list-admin", 
        method: "GET",
        params: params
      }),
      providesTags: ["Student"],
    }),
    studentCreate: builder.mutation({
      query: (body) => {
        return {
          url: `admin/student-create`,
          method: "POST",
          body: body,
        };
      },
      invalidatesTags: ["Student"],
    }),
    studentUpdate: builder.mutation({
      query: (body) => {
        return {
          url: `admin/student-update/${body.id}`,
          method: "POST",
          body: body.formData,
        };
      },
      invalidatesTags: ["Student"],
    }),
  }),
});

export const { useGetStudentListQuery, useStudentCreateMutation, useStudentUpdateMutation } = studentSettingApi;
