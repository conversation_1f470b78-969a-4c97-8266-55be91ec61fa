import { apiSlice } from "../apiSlice";
export const courseSetupCourseListApi = apiSlice.injectEndpoints({
  reducerPath: "courseSetupCourseListApi",
  tagTypes: ["Course"],
  endpoints: (builder) => ({
    getCourseList: builder.query({
      query: () => ({
        url: "admin/course-list",
        method: "GET",
      }),
      providesTags: ["Course"],
    }),

    courseCreateOrUpdate: builder.mutation({
      query: (body) => {
        return {
          url: `admin/course-save-or-update`,
          method: "POST",
          body: body,
        };
      },
      invalidatesTags: ["Course"],
    }),
  }),
});

export const { useGetCourseListQuery } = courseSetupCourseListApi;
