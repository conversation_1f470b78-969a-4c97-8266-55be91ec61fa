// import { Pagination } from "swiper";
import { apiSlice } from "../apiSlice";
export const rowContentClassListApi = apiSlice.injectEndpoints({
  reducerPath: "rowContentClassListApi",
  tagTypes: ["ClassList"],
  endpoints: (builder) => ({
    getClassList: builder.query({
      query: ( pagination ) => {
        return {
          url: 'admin/class-list',
          method: 'GET',
          params: pagination,
        };
      },
      providesTags: ['ClassList'],
    }),
    classCreateOrUpdate: builder.mutation({
      query: (body) => {
        return {
          url: `admin/class-save-or-update`,
          method: "POST",
          body: body,
        };
      },
      invalidatesTags: ["ClassList"],
    }),
  }),
});

export const { useGetClassListQuery, useClassCreateOrUpdateMutation } = rowContentClassListApi;
