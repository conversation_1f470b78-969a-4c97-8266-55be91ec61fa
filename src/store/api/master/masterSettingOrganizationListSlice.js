import { apiSlice } from "../apiSlice";
export const masterSettingOrganizationListApi = apiSlice.injectEndpoints({
  reducerPath: "masterSettingOrganizationListApi",
  tagTypes: ["Organization"],
  endpoints: (builder) => ({
    getOrganizationList: builder.query({
      query: () => ({
        url: "admin/organizations",
        method: "GET",
      }),
      providesTags: ["Organization"],
    }),
    
    organizationCreateOrUpdate: builder.mutation({
      query: (body) => {
        return {
          url: `admin/organizations`,
          method: "POST",
          body: body,
        };
      },
      invalidatesTags: ["Organization"],
    }),
    
    configureOrganization: builder.mutation({
      query: (body) => {
        return {
          url: `admin/configure-organization`,
          method: "POST",
          body: body,
        };
      },
      invalidatesTags: ["Organization"],
    }),
  }),
});

export const { useGetOrganizationListQuery,
  useOrganizationCreateOrUpdateMutation,
  useConfigureOrganizationMutation
 } = masterSettingOrganizationListApi;
