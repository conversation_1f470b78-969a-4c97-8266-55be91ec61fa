import { apiSlice } from "../apiSlice";
export const contentSetupContentListApi = apiSlice.injectEndpoints({
  reducerPath: "contentSetupContentListApi",
  tagTypes: ["ContentList"],
  endpoints: (builder) => ({
    getContentList: builder.query({
      query: () => ({
        url: "admin/content-list",
        method: "GET",
      }),
      providesTags: ["ContentList"],
    }),
    contentCreateOrUpdate: builder.mutation({
      query: (body) => {
        return {
          url: `admin/content-save-or-update`,
          method: "POST",
          body: body,
        };
      },
      invalidatesTags: ["ContentList"],
    }),
    getContentSubject: builder.query({
      query: () => ({
        url: "admin/content-subject-list",
        //api/admin/subject-by-class-id
        method: "GET",
      }),
      providesTags: ["ContentList"],
    }),
    contentCreateOrUpdateSubject: builder.mutation({
      query: (body) => {
        return {
          url: `admin/content-subject-assign-save-or-update`,
          method: "POST",
          body: body,
        };
      },
      invalidatesTags: ["ContentList"],
    }),

    getContentSubjectByClassId: builder.query({
      query: () => ({
        url: `api/admin/subject-by-class-id/${id}`,
        method: "GET",
      }),
      providesTags: ["ContentList"],
    }),
  }),
});

export const {
  useGetContentListQuery,
  useContentCreateOrUpdateMutation,
  useGetContentSubjectQuery,
  useContentCreateOrUpdateSubjectMutation,
  useGetContentSubjectByClassIdQuery,
} = contentSetupContentListApi;
