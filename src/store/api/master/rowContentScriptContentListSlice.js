import { apiSlice } from "../apiSlice";
export const rowContentScriptContentListApi = apiSlice.injectEndpoints({
  reducerPath: "rowContentScriptContentListApi",
  tagTypes: ["ScriptContentList"],
  endpoints: (builder) => ({
    getScriptContentList: builder.query({
      query: (params) => ({
        // url: "admin/chapter-script-list?class_id=0&subject_id=0&chapter_id=0",
        url:"admin/chapter-script-list"+ params,
        method: "GET",
      }),
      providesTags: ["ScriptContentList"],
    }),

    scriptCreateOrUpdate: builder.mutation({
      query: (body) => {
        return {
          url: `admin/chapter-script-save-or-update`,
          method: "POST",
          body: body,
        };
      },
      invalidatesTags: ["ScriptContentList"],
    }),

  }),
});

export const { useGetScriptContentListQuery ,useScriptCreateOrUpdateMutation,} = rowContentScriptContentListApi;
