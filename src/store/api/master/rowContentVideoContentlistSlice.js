import { apiSlice } from "../apiSlice";
export const rowContentVideoContentListApi = apiSlice.injectEndpoints({
  reducerPath: "rowContentVideoContentListApi",
  tagTypes: ["VideoContentList"],
  endpoints: (builder) => ({
    getVideoContentList: builder.query({
      query: (params) => ({
        url: "admin/video-chapter-list" + params, 
        method: "GET",
      }),
      providesTags: ["VideoContentList"],
    }),
    videoCreateOrUpdate: builder.mutation({
      query: (body) => {
        return {
          url: `admin/chapter-video-save-or-update`,
          method: "POST",
          body: body,
        };
      },
      invalidatesTags: ["VideoContentList"],
    }),
  }),
  
});

export const { useGetVideoContentListQuery, useVideoCreateOrUpdateMutation } = rowContentVideoContentListApi;