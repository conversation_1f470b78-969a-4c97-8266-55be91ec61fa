import { apiSlice } from "../apiSlice";

export const masterSettingTagListApi = apiSlice.injectEndpoints({
  reducerPath: "masterSettingTagListApi",
  tagTypes: ["Tags"],
  endpoints: (builder) => ({
    getTagsList: builder.query({
      query: () => ({
        url: "admin/tag-list-admin",
        method: "GET",
      }),
      providesTags: ["Tags"],
    }),
    tagsCreateOrUpdate: builder.mutation({
      query: (body) => ({
        url: `admin/tag-save-or-update-admin`,
        method: "POST",
        body: body,
      }),
      invalidatesTags: ["Tags"],
    }),
    tagsDelete: builder.mutation({
      query: (id) => ({
        url: `admin/delete-tag/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Tags"],
    }),
  }),
});

export const {
  useGetTagsListQuery,
  useTagsCreateOrUpdateMutation,
  useTagsDeleteMutation,
} = masterSettingTagListApi;
