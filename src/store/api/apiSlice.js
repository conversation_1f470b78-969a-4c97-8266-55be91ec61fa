import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { toast } from "react-toastify";
// import { logout } from "../../features/authSlice";
const baseQuery = fetchBaseQuery({
  baseUrl: import.meta.env.VITE_HOST_URL,
  // credentials: "include",
  prepareHeaders: (headers, { getState }) => {
    headers.set("Accept", `Application/json`);
    const token = getState().auth?.user?.token;
    if (token) {
      headers.set("Authorization", `Bearer ${token}`);
    }

    return headers;
  },
});


function capitalizeWords(str) {
  return str
    .split(" ") // Split the string into an array of words
    .map(word => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize the first letter of each word
    .join(" "); // Join the words back into a single string
}

const baseQueryWithReauth = async (args, api, extraOptions, toaster) => {
  let result = await baseQuery(args, api, extraOptions);

  if (result?.error?.status === 401) {
    toast.error("Session Expired. Please login again");

    
    localStorage.clear();
    window.location.href = "/";


    setTimeout(() => {
      window.location.reload(false);
    }, 3000);
    return result;
  }

  if (result?.error) {
    const errorMessage = result.error?.data?.message || "Something went wrong. Please try again";
    if (args.showToast && result.error.status !== 422) {
      toast.error(errorMessage);
    }
    return result;
  } else {
    if (api.type === "mutation" && args.showToast) {
      toast.success(capitalizeWords(result.data?.message) || "Operation successful");
    }
    return api.type === "mutation" ? result : result.data;
  }
};

export const apiSlice = createApi({
  baseQuery: baseQueryWithReauth,
  tagTypes: ["Admin"],
  reducerPath: "apiSliceAdmin",

  endpoints: (builder) => ({

  }),
});
