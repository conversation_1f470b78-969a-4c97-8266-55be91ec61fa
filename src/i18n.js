import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import en from "@/locale/en.json";
import bn from "@/locale/bn.json";
import jp from "@/locale/jp.json";
import kr from "@/locale/kr.json";
import ar from "@/locale/ar.json";

i18n.use(initReactI18next).init({
  lng: localStorage.getItem("lang") || "en",
  fallbackLng: "en",
  interpolation: {
    escapeValue: true,
  },
  resources: {
    en: { translation: en },
    bn: { translation: bn },
    jp: { translation: jp },
    kr: { translation: kr },
    ar: { translation: ar },
  },
});

export default i18n;
localStorage.getItem("lang");