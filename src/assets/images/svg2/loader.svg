<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="margin: auto; background: trannsparent; display: block; shape-rendering: auto;"  viewBox="0 0 100 100" preserveAspectRatio="xMidYMid">
<g transform="translate(50 50)">
  <g transform="scale(0.32)">
    <g transform="translate(-50 -50)">
      <g>
        <animateTransform attributeName="transform" type="rotate" repeatCount="indefinite" values="0 50 50;360 50 50" keyTimes="0;1" dur="0.5434782608695652s"></animateTransform>
        <path fill-opacity="0.8" fill="#4669fa" d="M50 50L50 0A50 50 0 0 1 100 50Z"></path>
      </g>
      <g>
        <animateTransform attributeName="transform" type="rotate" repeatCount="indefinite" values="0 50 50;360 50 50" keyTimes="0;1" dur="0.7246376811594202s"></animateTransform>
        <path fill-opacity="0.8" fill="#50c793" d="M50 50L50 0A50 50 0 0 1 100 50Z" transform="rotate(90 50 50)"></path>
      </g>
      <g>
        <animateTransform attributeName="transform" type="rotate" repeatCount="indefinite" values="0 50 50;360 50 50" keyTimes="0;1" dur="1.0869565217391304s"></animateTransform>
        <path fill-opacity="0.8" fill="#a0aec0" d="M50 50L50 0A50 50 0 0 1 100 50Z" transform="rotate(180 50 50)"></path>
      </g>
      <g>
        <animateTransform attributeName="transform" type="rotate" repeatCount="indefinite" values="0 50 50;360 50 50" keyTimes="0;1" dur="2.1739130434782608s"></animateTransform>
        <path fill-opacity="0.8" fill="#f1595c" d="M50 50L50 0A50 50 0 0 1 100 50Z" transform="rotate(270 50 50)"></path>
      </g>
    </g>
  </g>
</g>
<!-- [ldio] generated by https://loading.io/ --></svg>