// Active class styling for Navmenu component

// Define CSS variables for consistent colors
:root {
  --navmenu-active-color: #4669FA;
  --navmenu-active-color-rgb: 70, 105, 250;
  --navmenu-active-bg-light: rgba(70, 105, 250, 0.1);
  --navmenu-active-bg-dark: rgba(70, 105, 250, 0.15);
}

// Active state for main menu items
.single-sidebar-menu {
  &.menu-item-active {
    border-left: 4px solid var(--navmenu-active-color);

    .menu-link {
      background-color: var(--navmenu-active-color);
      transition: all 0.3s ease;

      .dark & {
        background-color: var(--navmenu-active-color);
      }

      .menu-icon,
      .text-box {
        color: white;
        font-weight: 500;
      }

      .menu-badge {
        background-color: white;
        color: var(--navmenu-active-color);
      }
    }
  }

  // Hover effect for menu items
&:hover:not(.menu-item-active) {
  .menu-link {
    background-color: #f1f5f9;
    transition: all 0.3s ease;

    .dark & {
      background-color: #334155;
    }

    .menu-icon,
    .text-box {
      color: black;
    }
  }
}

  // Style for active NavLink
  .menu-link.active {
    background-color: var(--navmenu-active-color);
    transition: all 0.3s ease;

    .dark & {
      background-color: var(--navmenu-active-color);
    }

    .menu-icon,
    .text-box {
      color: white;
      font-weight: 500;
    }

    .menu-badge {
      background-color: white;
      color: var(--navmenu-active-color);
    }
  }
}

// Active state for parent items with children
.item-has-children {
  &.open {
    // When submenu is open
    border-left: 4px solid var(--navmenu-active-color);

    .parent_active {
      background-color: var(--navmenu-active-bg-light);

      .dark & {
        background-color: var(--navmenu-active-bg-dark);
      }

      .menu-icon,
      .text-box {
        color: var(--navmenu-active-color);
        font-weight: 500;
      }

      .menu-arrow {
        background-color: var(--navmenu-active-color);
        color: white;
      }
    }
  }

  // When a child item is active, highlight the parent
  &.has-active-child {
    border-left: 4px solid var(--navmenu-active-color);

    > .menu-link {
      background-color: var(--navmenu-active-bg-light);

      .dark & {
        background-color: var(--navmenu-active-bg-dark);
      }

      .menu-icon,
      .text-box {
        color: var(--navmenu-active-color);
        font-weight: 500;
      }
    }
  }

  // Style for active parent when child is active
  .active-parent {
    background-color: var(--navmenu-active-bg-light);

    .dark & {
      background-color: var(--navmenu-active-bg-dark);
    }

    .menu-icon,
    .text-box {
      color: var(--navmenu-active-color);
      font-weight: 500;
    }

    .menu-arrow {
      background-color: var(--navmenu-active-color);
      color: white;
    }
  }
}

// Active state for submenu items
.sub-menu {
  li {
    a, .nav-link {
      &.active {
        span:first-child {
          // background-color: var(--navmenu-active-color);
          // border-color: var(--navmenu-active-color);
          // box-shadow: 0 0 0 4px rgba(var(--navmenu-active-color-rgb), 0.2);
        }

        span:last-child {
          color: var(--navmenu-active-color);
          font-weight: 500;
          padding: 5px 0px 5px 0;
        }
      }
    }
  }
}

// Animation for active menu items
.menu-item-active, .item-has-children.open, .item-has-children.has-active-child {
  transition: all 0.3s ease;
}

// Add smooth transition to all menu items
.single-sidebar-menu, .menu-link, .sub-menu li a, .sub-menu li .nav-link {
  transition: all 0.3s ease;
}

// Add a subtle pulse animation to the active indicator
@keyframes activePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--navmenu-active-color-rgb), 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(var(--navmenu-active-color-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--navmenu-active-color-rgb), 0);
  }
}

.single-sidebar-menu.menu-item-active,
.item-has-children.open,
.item-has-children.has-active-child {
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background-color: var(--navmenu-active-color);
    border-radius: 0 2px 2px 0;
    animation: activePulse 2s infinite;
  }
}
