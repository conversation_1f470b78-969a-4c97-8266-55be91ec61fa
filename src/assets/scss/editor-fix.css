/* Fix for RTL text direction in CKEditor */

/* Force LTR mode for editor content */
.ck-editor__editable {
  direction: ltr !important;
  text-align: left !important;
}

/* Ensure proper text flow */
.ck-editor__editable p {
  direction: ltr !important;
  text-align: left !important;
  unicode-bidi: normal !important;
}

/* Fix for cursor position */
.ck-editor__editable .ck-widget {
  direction: ltr !important;
}

/* Fix for lists */
.ck-editor__editable ul,
.ck-editor__editable ol {
  direction: ltr !important;
  padding-left: 40px !important;
  padding-right: 0 !important;
}

/* Fix for tables */
.ck-editor__editable table {
  direction: ltr !important;
}

/* Fix for image captions */
.ck-editor__editable .image > figcaption {
  direction: ltr !important;
  text-align: left !important;
}

/* Fix for quotes */
.ck-editor__editable blockquote {
  border-left: 5px solid #ccc !important;
  border-right: none !important;
  padding-left: 1.5em !important;
  padding-right: 0 !important;
  margin-left: 1.5em !important;
  margin-right: 0 !important;
}
