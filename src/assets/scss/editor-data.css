/* EditorData component styles for proper CKEditor content display */
/* Higher specificity to override Tailwind CSS resets and other global styles */

/* Ensure proper list styling for bullet points and numbered lists */
.ck-content ul,
.ck-content ul:not(.custom-list),
.editor-data-content ul,
.editor-data-content ul:not(.custom-list) {
  list-style-type: disc !important;
  padding-left: 40px !important;
  margin: 1em 0 !important;
  list-style-position: outside !important;
}

.ck-content ol,
.ck-content ol:not(.custom-list),
.editor-data-content ol,
.editor-data-content ol:not(.custom-list) {
  list-style-type: decimal !important;
  padding-left: 40px !important;
  margin: 1em 0 !important;
  list-style-position: outside !important;
}

.ck-content li,
.ck-content li:not(.custom-list li),
.editor-data-content li,
.editor-data-content li:not(.custom-list li) {
  display: list-item !important;
  margin: 0.5em 0 !important;
  padding-left: 0 !important;
  list-style: inherit !important;
}

/* Nested lists */
.ck-content ul ul,
.ck-content ol ol,
.ck-content ul ol,
.ck-content ol ul {
  margin: 0.5em 0 !important;
  padding-left: 30px !important;
}

.ck-content ul ul {
  list-style-type: circle !important;
}

.ck-content ul ul ul {
  list-style-type: square !important;
}

/* Headings */
.ck-content h1 {
  font-size: 2em !important;
  font-weight: bold !important;
  margin: 0.67em 0 !important;
}

.ck-content h2 {
  font-size: 1.5em !important;
  font-weight: bold !important;
  margin: 0.83em 0 !important;
}

.ck-content h3 {
  font-size: 1.17em !important;
  font-weight: bold !important;
  margin: 1em 0 !important;
}

.ck-content h4 {
  font-size: 1em !important;
  font-weight: bold !important;
  margin: 1.33em 0 !important;
}

.ck-content h5 {
  font-size: 0.83em !important;
  font-weight: bold !important;
  margin: 1.67em 0 !important;
}

.ck-content h6 {
  font-size: 0.67em !important;
  font-weight: bold !important;
  margin: 2.33em 0 !important;
}

/* Paragraphs */
.ck-content p {
  margin: 1em 0 !important;
  line-height: 1.6 !important;
}

/* Bold and italic */
.ck-content strong {
  font-weight: bold !important;
}

.ck-content em {
  font-style: italic !important;
}

/* Links */
.ck-content a {
  color: #0066cc !important;
  text-decoration: underline !important;
}

.ck-content a:hover {
  color: #004499 !important;
}

/* Tables */
.ck-content table {
  border-collapse: collapse !important;
  width: 100% !important;
  margin: 1em 0 !important;
}

.ck-content table td,
.ck-content table th {
  border: 1px solid #ccc !important;
  padding: 8px !important;
  text-align: left !important;
}

.ck-content table th {
  background-color: #f5f5f5 !important;
  font-weight: bold !important;
}

/* Blockquotes */
.ck-content blockquote {
  border-left: 5px solid #ccc !important;
  margin: 1em 0 !important;
  padding-left: 1.5em !important;
  font-style: italic !important;
  color: #666 !important;
}

/* Images */
.ck-content img {
  max-width: 100% !important;
  height: auto !important;
  display: block !important;
  margin: 1em 0 !important;
}

.ck-content figure {
  margin: 1em 0 !important;
  text-align: center !important;
}

.ck-content figure img {
  margin: 0 auto !important;
}

.ck-content figcaption {
  font-style: italic !important;
  color: #666 !important;
  margin-top: 0.5em !important;
  text-align: center !important;
}

/* Code blocks */
.ck-content pre {
  background-color: #f5f5f5 !important;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  padding: 1em !important;
  overflow-x: auto !important;
  font-family: monospace !important;
  margin: 1em 0 !important;
}

.ck-content code {
  background-color: #f5f5f5 !important;
  padding: 0.2em 0.4em !important;
  border-radius: 3px !important;
  font-family: monospace !important;
}

/* Horizontal rules */
.ck-content hr {
  border: none !important;
  border-top: 1px solid #ccc !important;
  margin: 2em 0 !important;
}

/* Media embeds */
.ck-content .media {
  margin: 1em 0 !important;
  text-align: center !important;
}

/* Ensure proper text direction and alignment */
.ck-content {
  direction: ltr !important;
  text-align: left !important;
  unicode-bidi: normal !important;
}

/* Fix for any potential CSS conflicts */
.ck-content * {
  box-sizing: border-box !important;
}

/* Dark mode support */
.dark .ck-content {
  color: #e2e8f0 !important;
}

.dark .ck-content a {
  color: #60a5fa !important;
}

.dark .ck-content blockquote {
  color: #94a3b8 !important;
  border-left-color: #475569 !important;
}

.dark .ck-content table td,
.dark .ck-content table th {
  border-color: #475569 !important;
}

.dark .ck-content table th {
  background-color: #374151 !important;
}

.dark .ck-content pre {
  background-color: #374151 !important;
  border-color: #475569 !important;
}

.dark .ck-content code {
  background-color: #374151 !important;
}

.dark .ck-content hr {
  border-top-color: #475569 !important;
}
