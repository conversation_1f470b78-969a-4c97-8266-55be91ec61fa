/* Fixed Vertical-Only Drag CSS */

/* Prevent any transitions that might cause jumping */
[data-rbd-draggable-id] {
  transition: none !important;
  left: 0 !important;
  right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  width: 100% !important;
  position: relative !important;
  transform: translate(0, 0) !important;
}

/* Only apply transform when actually dragging */
[data-rbd-draggable-id][data-rbd-dragging="true"] {
  transform: translate(0, var(--y, 0)) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
  background-color: #eff6ff !important; /* blue-50 */
  border-color: #93c5fd !important; /* blue-300 */
  z-index: 10 !important;
}

/* Fix for Firefox */
@-moz-document url-prefix() {
  [data-rbd-draggable-id][data-rbd-dragging="true"] {
    transform: translate(0, var(--y, 0)) !important;
  }
}

/* Ensure the drag handle indicates vertical-only movement */
.drag-handle {
  cursor: ns-resize !important;
  background-color: #f9fafb;
  border-right: 1px solid #e5e7eb;
  transition: background-color 0.2s ease;
}

.drag-handle:hover {
  background-color: #f3f4f6;
  color: #3b82f6;
}

/* Style for the dragging state - moved to the data-rbd-dragging selector above */

/* Ensure the body has the right cursor during dragging */
body.dragging-vertical * {
  cursor: ns-resize !important;
}
