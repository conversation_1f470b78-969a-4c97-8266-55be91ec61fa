/* Strict Vertical-Only Dragging CSS */

/* Force vertical-only movement for all draggable elements */
[data-rbd-draggable-id] {
  transform: translate3d(0, var(--y, 0), 0) !important;
}

/* Prevent any horizontal movement during drag */
[data-rbd-draggable-context-id] [data-rbd-draggable-id] {
  left: 0 !important;
  right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* Ensure the drag handle indicates vertical-only movement */
.strict-vertical-handle {
  cursor: ns-resize !important;
  touch-action: pan-y !important;
  user-select: none !important;
}

/* Style for the dragging state */
.strict-vertical-item {
  transition: transform 0.1s ease, box-shadow 0.1s ease;
  touch-action: pan-y !important;
  user-select: none !important;
}

.strict-vertical-item.dragging {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-color: #eff6ff !important; /* blue-50 */
  border-color: #93c5fd !important; /* blue-300 */
  z-index: 10;
}

/* Ensure the body has the right cursor during dragging */
body.dragging-vertical {
  cursor: ns-resize !important;
}

body.dragging-vertical * {
  cursor: ns-resize !important;
}

/* Fix for Firefox */
@-moz-document url-prefix() {
  [data-rbd-draggable-id] {
    transform: translate(0, var(--y, 0)) !important;
  }
}
