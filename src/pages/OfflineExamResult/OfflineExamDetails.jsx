import React, { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import moment from "moment";
import Icon from "@/components/ui/Icon";
import Tooltip from "@/components/ui/Tooltip";
import { useDispatch } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";
import avatar from "@/assets/images/avatar/av-1.svg";
import EditResult from "./EditResult";
import {
  useGetApiQuery,
  usePostApiMutation,
} from "@/store/api/master/commonSlice";
import Loading from "@/components/Loading";
import InputField from "@/components/ui/InputField";
import { Form, Formik } from "formik";
import * as yup from "yup";
import Button from "@/components/ui/Button";

const SinglePart = ({ detail }) => {
  return (
    <div className="border shadow-sm hover:shadow-md hover:border-gray-300 transition-all duration-200 p-5 rounded">
      <p className="text-gray-700 text-sm flex items-center justify-center">
        <Icon
          icon={detail.icon}
          className="inline-block text-blue-500 mr-2 text-2xl"
        />
        <>
          <span className="text-base font-semibold  text-gray-600">
            {detail.label}:
          </span>
          <span className="text-gray-700 mt-0.5 text-lg">{detail.value}</span>
        </>
      </p>
    </div>
  );
};

const OfflineExamDetails = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  const data = location.state?.exam;
  const [selectedBatch, setSelectedBatch] = useState("");
  const [isMark, setIsMark] = useState(false);
  const [postApi, { isLoading }] = usePostApiMutation();

  const { id } = useParams();
  const {
    data: exam,
    isLoading: isExamLoading,
    isFetching: isExamFetching,
    refetch,
  } = useGetApiQuery(
    selectedBatch
      ? `admin/offline-exams/${id}?batch_id=${selectedBatch}`
      : `admin/offline-exams/${id}`
  );

  const batches = exam?.batches;

  useEffect(() => {
    if (batches && !selectedBatch) {
      setSelectedBatch(batches[0]?.batch_id); // Set initial batch
    }
  }, [batches]);

  const details = [
    {
      label: "Exam Date",
      value: new Date(data.exam_date).toLocaleDateString(),
      icon: "uiw:date",
    },
    {
      label: "Duration",
      value: `${data.duration} minutes`,
      icon: "mingcute:time-duration-line",
    },
    {
      label: "Pass Mark",
      value: data.pass_mark,
      icon: "ix:certificate-success",
    },
    {
      label: "MCQ Mark",
      value: data.mcq_mark,
      icon: "mdi:format-list-bulleted",
    },
    {
      label: "Written Mark",
      value: data.written_mark,
      icon: "material-symbols-light:edit-note-outline",
    },
    {
      label: "Total Mark",
      value: data.total_mark,
      icon: "mdi:chart-bar",
    },
  ];
  console.log(data)

  // Refetch data when the selected batch changes
  useEffect(() => {
    if (selectedBatch) {
      refetch();
    }
  }, [selectedBatch, refetch]);

  const handleEditMentorClick = () => {
    dispatch(setEditData(exam));
    dispatch(setEditShowModal(true));
  };

  if (!exam) {
    return <div>No Exam details available</div>;
  }

  const mcqMark = exam?.mcq_mark;
  const writtenMark = exam?.written_mark;

  const validationSchema = yup.object({
    students: yup.array().of(
      yup.object({
        mcq_mark: yup
          .number()
          .typeError("MCQ Mark must be a number")
          .max(mcqMark, `Marks should not be more than ${mcqMark}`),
        // .required("MCQ Mark is Required"),
        written_mark: yup
          .number()
          .typeError("Written Mark must be a number")
          .max(writtenMark, `Marks should not be more than  ${writtenMark}`),
        // .required("Written Mark is Required"),
      })
    ),
  });

  const handleSubmit = async (values) => {
    values.id = id;
    const response = await postApi({
      end_point: "admin/mark-offline-exam",
      body: values,
    });
    if(response.data.status){
      setIsMark(false);
    };
  };

  return (
    <>
      <div className="profile-page space-y-5">
        <div className="profiel-wrap rounded-lg bg-white dark:bg-slate-800 relative z-[1]">
          {/* Back and Edit Buttons */}

          <div className="w-full mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
            <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-4 text-center">
              <h2 className="text-white text-2xl font-bold">{data.title}</h2>
              <p className="text-white text-sm mt-1">{data.course_title}</p>
            </div>
            <div className="p-4 grid grid-cols-2 md:grid-cols-3 gap-5">
              {details.map((item, idx) => (
                <SinglePart key={idx} detail={item} />
              ))}
            </div>

            <div className="w-full flex justify-between px-5 mt-6">
              <select
                className="border px-3 py-1.5 rounded"
                name="batch_filter"
                onChange={(e) => setSelectedBatch(e.target.value)}
                value={selectedBatch}
              >
                {batches?.map((item, idx) => (
                  <option key={idx} value={item?.batch_id}>
                    {item.batch_name}
                  </option>
                ))}
              </select>
              <button
                onClick={() => setIsMark(!isMark)}
                className="bg-gray-200 px-3 py-1.5 rounded border border-transparent hover:border-gray-300"
              >
                {isMark ? "Cancel" : "Mark"}
              </button>
            </div>
            <div className="p-4">
              <div className="flex justify-around p-4 rounded bg-gray-100">
                <h2 className="text-xl font-semibold text-gray-600">
                  Students
                </h2>

                {/* {isMark && ( */}
                <span className="text-base font-semibold">MCQ Mark</span>
                {/* )} */}
                {/* {isMark && ( */}
                <span className="text-base font-semibold">Written Mark</span>
                {/* )} */}
              </div>
              <div className="max-h-96 overflow-y-auto pt-7">
                <Formik
                  validationSchema={validationSchema}
                  initialValues={{
                    students:
                      exam?.exam_students?.map((item) => ({
                        id: item?.id,
                        mcq_mark: item?.mcq_mark !== null ? item.mcq_mark : "",
                        written_mark: item?.written_mark !== null
                          ? item.written_mark
                          : "",
                      })) || [],
                  }}
                  onSubmit={handleSubmit}
                >
                  {({ values, setFieldError }) => (
                    <Form>
                      {isExamLoading && isExamFetching ? (
                        <Loading />
                      ) : (
                        exam?.exam_students?.map((item, idx) => (
                          <div
                            key={idx}
                            className="flex justify-around items-center hover:bg-gray-100 p-3 rounded gap-8"
                          >
                            <div className="flex items-center gap-3 flex-1">
                              <img
                                src={
                                  item?.image
                                    ? import.meta.env.VITE_ASSET_HOST_URL +
                                      item?.image
                                    : avatar
                                }
                                className="w-12 h-12 rounded-full"
                                alt="Student"
                              />
                              <p className="font-semibold">
                                {item?.student_name}
                              </p>
                            </div>
                            
                            {isMark ? (
                              <span className="flex-1">
                                <InputField
                                  name={`students[${idx}].mcq_mark`}
                                  type="text"
                                  placeholder=""
                                  className="focus:outline focus:ring-gray-400"
                                />
                              </span>
                            ) : (
                              <p className="flex-1 text-center">{item?.mcq_mark !== null ? item.mcq_mark : "--"}</p>
                            )}


                            {isMark ? (
                              <span className="flex-1">
                                <InputField
                                  name={`students[${idx}].written_mark`}
                                  type="text"
                                  placeholder=""
                                />
                              </span>
                            ) : (
                              <p className="flex-1 text-center">
                                {item?.written_mark !== null ? item.written_mark : "--"}
                              </p>
                            )}
                          </div>
                        ))
                      )}

                      {isMark && (
                        <div className="text-right px-4 pb-3">
                          <Button
                            isLoading={isLoading}
                            type="submit"
                            className="btn text-center btn-primary py-1.5"
                          >
                            Submit
                          </Button>
                        </div>
                      )}
                    </Form>
                  )}
                </Formik>
              </div>
            </div>

            {/* <div className="bg-gray-100 p-4 border-t">
              <p className="text-gray-600 text-xs">
                Created At: {new Date(data.created_at).toLocaleString()}
              </p>
            </div> */}
          </div>
        </div>
      </div>
      <EditResult
        mentor={exam}
        onClose={() => dispatch(setEditShowModal(false))}
      />
    </>
  );
};

export default OfflineExamDetails;
