import BasicTablePage from "@/components/partials/common-table/table-basic";
import React, { useState } from "react";
import EditResult from "./EditResult";
import { useDispatch } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import { useMentorUpdateMutation } from "@/store/api/master/mentorSlice";
import Badge from "@/components/ui/Badge";
import { setEditData, setEditShowModal } from "@/features/commonSlice";
import avatar from "@/assets/images/avatar/av-1.svg";
import CreateExam from "./CreateExam";
import DeleteOfflineExam from "./DeleteOfflineExam";

const OfflineExamResult = () => {
  const [showModal, setShowModal] = useState(false);
  const [filter, setFilter] = useState("");
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [deleteData, setDeleteData] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showBulkModal, setShowBulkModal] = useState(false);
  // console.log(filter);
  const [apiParam, setApiParam] = useState("");

  const {
    data,
    isLoading: isMentorLoading,
    isFetching: isMentorFetching,
  } = useGetApiQuery("admin/offline-exams");

  const changePage = (val) => {
    setApiParam(val);
  };

  const [mentorUpdate, { isLoading, isError, error, isSuccess }] =
    useMentorUpdateMutation();

  const columns = [
    {
      label: "Course",
      field: "title",
    },
    {
      label: "Exam Date",
      field: "exam_date",
    },
    {
      label: "MCQ Mark",
      field: "mcq_mark",
    },
    {
      label: "Written Mark",
      field: "written_mark",
    },
    {
      label: "Total Mark",
      field: "total_mark",
    },
    {
      label: "Action",
      field: "",
    },
  ];

  const handleIndexImageNameClick = (id) => {
    const selectedMentor = data.find((item) => item.id === id);
    navigate(`/mentor-details/${id}`, { state: { mentor: selectedMentor } });
    // navigate(`/mentor-dashboard/${id}`, { state: { mentor: selectedMentor } });
  };

  const tableData = data?.map((item, index) => {
    return {
      title: (
        <Link state={{ exam: item }} className="hover:text-blue-600 hover:underline" to={`/offline-exam-details/${item?.id}`}>
          {item.title}
        </Link>
      ),
      exam_date: item?.exam_date ? item.exam_date.slice(0, 10) : "--",
      mcq_mark: item.mcq_mark,
      written_mark: <div>{item.written_mark}</div>,
      total_mark: item.total_mark,
    };
  });

  // const makeFeature = async (val) => {
  //   console.log(val);
  //   let formData = new FormData();
  //   formData.append("name", val.name);
  //   formData.append("is_featured", val.is_featured ? 0 : 1);
  //   formData.append("_method", "PUT");
  //   let data = { id: val.id, formData: formData };
  //   const response = await mentorUpdate(data);
  //   console.log(response);
  // };
  const actions = [
    {
      name: "Details",
      icon: "heroicons-outline:eye",
      onClick: (val) => {
        const selectedItem = data[val];
        navigate(`/offline-exam-details/${selectedItem.id}`, {
          state: { exam: selectedItem },
        });
      },
    },
    // {
    //   name: "Make Featured",
    //   icon: "heroicons-solid:star",
    //   onClick: (val) => {
    //     makeFeature(data.data[val]);
    //   },
    // },
    {
      name: "edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        dispatch(setEditData(data[val]));
        dispatch(setEditShowModal(true));
      },
    },
    {
      name: "delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data[val]);
        setShowDeleteModal(true);
      },
    },
  ];
  // const handleSubmit = () => {
  //   setShowModal(false);
  // };
  const createPage = <CreateExam />;
  const editPage = <EditResult />;

  return (
    <div>
      {/* {tableData?.length > 0 && ( */}
      <BasicTablePage
        title="Offline Exam Results"
        createButton="Create Offline Exam"
        actions={actions}
        columns={columns}
        data={tableData}
        filter={filter}
        createPage={createPage}
        editPage={editPage}
        changePage={changePage}
        currentPage={data?.current_page}
        totalPages={Math.ceil(data?.total / data?.per_page)}
      />
      {/* )} */}

      {showDeleteModal && (
        <DeleteOfflineExam
          setShowDeleteModal={setShowDeleteModal}
          showDeleteModal={showDeleteModal}
          data={deleteData}
        />
      )}
    </div>
  );
};

export default OfflineExamResult;
