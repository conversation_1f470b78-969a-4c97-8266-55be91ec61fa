import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import Select from "@/components/ui/Select";
import { useGetCourseListQuery } from "@/store/api/master/courseSetupCourseListSlice";
import { useGetPurchaseListQuery } from "@/store/api/master/purchaseSlice";
import { useDispatch, useSelector } from "react-redux";
import View from "./view";


const Filter = ({ setApiParam }) => {

  

  const [courseId, setCourseId] = useState(null);

  const courseList = useGetCourseListQuery().data?.data;

  return (
    <div className="flex gap-2">
      <Select
        className="w-80"
        defaultValue=""
        placeholder="Select Course"
        options={courseList?.map((item) => {
          return { label: item.title, value: item.id };
        })}
        name="category_id"
        onChange={(e) => {
          setCourseId(e.target.value);
          setApiParam(e.target.value);
        }}
      />
    </div>
  );
};

const index = () => {
  const dispatch = useDispatch();

  const [showModal, setShowModal] = useState(false);
  const [purchaseData, setPurchaseData] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [apiParam, setApiParam] = useState(0);

  const res = useGetPurchaseListQuery(apiParam);

  const changePage = (val) => {
    setApiParam(val);
  };

  // console.log(res.data);
  
  const data = res.data;
  const columns = [
    {
      label: "SL",
      field: "id",
    },
    {
      label: "Course",
      field: "course_name",
    },
    {
      label: "Student",
      field: "name",
    },
    {
      label: "Price",
      field: "item_price",
    },
    {
      label: "Discount",
      field: "discount",
    },
    {
      label: "Action",
      field: "",
    },
  ];
  const tableData = data?.map((item, index) => {
    return {
      id: item.id,
      course_name: item.course_name,
      name: item.name,
      discount: item.discount,
      item_price: item.item_price,
    };
  });

  const actions = [
    {
      name: "Details",
      icon: "heroicons-outline:eye",
      onClick: (val) => {
        setPurchaseData(data[val]);
        setShowPurchaseModal(true);
        // console.log(data.data[val]);
        
      }
    },
  ];
  

  const handleSubmit = () => {
    setShowModal(false);
  };

  const filter = <Filter setApiParam={setApiParam} />;

 
  return (
    <div>
      {/* {tableData?.length > 0 && ( */}
      <BasicTablePage
        title="Payment List"
        actions={actions}
        columns={columns}
        data={tableData}
        filter={filter}
        submitForm={handleSubmit}
        changePage={changePage}
        currentPage={data?.current_page}
        totalPages={Math.ceil(data?.total / data?.per_page)}
      />
      {/* )} */}
      <View showPurchaseModal={showPurchaseModal} setShowPurchaseModal={setShowPurchaseModal} data={purchaseData} />
    </div>
  );
};

export default index;
