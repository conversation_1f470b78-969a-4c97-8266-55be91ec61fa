import React, { useState, useEffect } from "react";
import { useCreateCurrencyMutation, useUpdateCurrencyMutation } from "@/store/api/master/currencySlice";
import Card from "@/components/ui/Card";
import { Icon } from "@iconify/react";
import { toast } from "react-toastify";

const CurrencyForm = ({ currency, onCancel, onSuccess }) => {
  const [createCurrency, { isLoading: isCreating }] = useCreateCurrencyMutation();
  const [updateCurrency, { isLoading: isUpdating }] = useUpdateCurrencyMutation();

  const [formData, setFormData] = useState({
    name: "",
    symbol: "",
    code: "",
    status: "active",
    is_default: false,
  });

  const [errors, setErrors] = useState({});
  const isEditing = !!currency;
  const isLoading = isCreating || isUpdating;

  useEffect(() => {
    if (currency) {
      setFormData({
        name: currency.name || "",
        symbol: currency.symbol || "",
        code: currency.code || "",
        status: currency.status || "active",
        is_default: currency.is_default || false,
      });
    }
  }, [currency]);

  const validateForm = () => {
    const newErrors = {};
    const trimmedCode = formData.code.trim().toUpperCase(); // Normalize code here

    if (!formData.name.trim()) {
      newErrors.name = "Currency name is required";
    } else if (formData.name.length > 255) {
      newErrors.name = "Currency name must not exceed 255 characters";
    }

    if (!formData.symbol.trim()) {
      newErrors.symbol = "Currency symbol is required";
    } else if (formData.symbol.length > 10) {
      newErrors.symbol = "Currency symbol must not exceed 10 characters";
    }

    if (!formData.code.trim()) {
      newErrors.code = "Currency code is required";
    } else if (trimmedCode.length !== 3) {
      newErrors.code = "Currency code must be exactly 3 characters";
    } else if (!/^[A-Z]{3}$/.test(trimmedCode)) {
      newErrors.code = "Currency code must be 3 uppercase letters";
    }

    if (!["active", "inactive"].includes(formData.status)) {
      newErrors.status = "Status must be either active or inactive";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      const payload = {
        ...formData,
        code: formData.code.toUpperCase(),
      };

      if (isEditing) {
        await updateCurrency({ id: currency.id, ...payload }).unwrap();
        toast.success("Currency updated successfully");
      } else {
        await createCurrency(payload).unwrap();
        toast.success("Currency created successfully");
      }

      onSuccess();
    } catch (error) {
      if (error?.data?.errors) {
        setErrors(error.data.errors);
        Object.values(error.data.errors)
          .flat()
          .forEach((msg) => toast.error(msg));
      } else {
        toast.error(error?.data?.message || "An error occurred");
      }
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));

    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            {isEditing ? "Edit Currency" : "Create New Currency"}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {isEditing ? "Update your currency details" : "Add a new currency to your system"}
          </p>
        </div>
        <button
          onClick={onCancel}
          className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          aria-label="Close form"
        >
          <Icon icon="heroicons:x-mark-20-solid" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Currency Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Currency Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className={`w-full px-4 py-2.5 border rounded-lg focus:outline-none focus:ring-2 ${
                errors.name
                  ? "border-red-500 focus:ring-red-500 bg-red-50 dark:bg-red-900/20"
                  : "border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring-primary-500"
              } dark:bg-gray-800 dark:text-white transition-colors`}
              placeholder="e.g., US Dollar"
              maxLength={255}
            />
            {errors.name && (
              <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.name}</p>
            )}
          </div>

          {/* Currency Symbol */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Currency Symbol <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="symbol"
              value={formData.symbol}
              onChange={handleChange}
              className={`w-full px-4 py-2.5 border rounded-lg focus:outline-none focus:ring-2 ${
                errors.symbol
                  ? "border-red-500 focus:ring-red-500 bg-red-50 dark:bg-red-900/20"
                  : "border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring-primary-500"
              } dark:bg-gray-800 dark:text-white transition-colors`}
              placeholder="e.g., $"
              maxLength={10}
            />
            {errors.symbol && (
              <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.symbol}</p>
            )}
          </div>

          {/* Currency Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Currency Code <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <input
                type="text"
                name="code"
                value={formData.code}
                onChange={handleChange}
                className={`w-full px-4 py-2.5 border rounded-lg focus:outline-none focus:ring-2 uppercase ${
                  errors.code
                    ? "border-red-500 focus:ring-red-500 bg-red-50 dark:bg-red-900/20"
                    : "border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring-primary-500"
                } dark:bg-gray-800 dark:text-white transition-colors`}
                placeholder="e.g., USD"
                maxLength={3}
                style={{ textTransform: "uppercase" }}
              />
              {!errors.code && formData.code.length === 3 && (
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <Icon icon="heroicons:check-circle" className="w-5 h-5 text-green-500" />
                </div>
              )}
            </div>
            {errors.code ? (
              <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.code}</p>
            ) : (
              <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                3-letter ISO currency code (e.g., USD, EUR, GBP)
              </p>
            )}
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Status <span className="text-red-500">*</span>
            </label>
            <select
              name="status"
              value={formData.status}
              onChange={handleChange}
              className={`w-full px-4 py-2.5 border rounded-lg focus:outline-none focus:ring-2 ${
                errors.status
                  ? "border-red-500 focus:ring-red-500 bg-red-50 dark:bg-red-900/20"
                  : "border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring-primary-500"
              } dark:bg-gray-800 dark:text-white transition-colors`}
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
            {errors.status && (
              <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.status}</p>
            )}
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={onCancel}
            className="px-5 py-2.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="px-5 py-2.5 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center min-w-[120px]"
          >
            {isLoading ? (
              <>
                <Icon icon="heroicons:arrow-path" className="w-4 h-4 mr-2 animate-spin" />
                Processing...
              </>
            ) : isEditing ? (
              "Update Currency"
            ) : (
              "Create Currency"
            )}
          </button>
        </div>
      </form>
    </Card>
  );
};

export default CurrencyForm;
