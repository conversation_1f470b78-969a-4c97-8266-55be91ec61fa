import React, { useState } from "react";
import { Icon } from "@iconify/react";
import CurrencyList from "./CurrencyList";
import CurrencyForm from "./CurrencyForm";

const Currencies = () => {
  const [showForm, setShowForm] = useState(false);
  const [editingCurrency, setEditingCurrency] = useState(null);

  const handleAdd = () => {
    setEditingCurrency(null);
    setShowForm(true);
  };

  const handleEdit = (currency) => {
    setEditingCurrency(currency);
    setShowForm(true);
  };

  const handleCancel = () => {
    setShowForm(false);
    setEditingCurrency(null);
  };

  const handleSuccess = () => {
    setShowForm(false);
    setEditingCurrency(null);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
            Currency Management
          </h1>
          <p className="text-slate-600 dark:text-slate-400 mt-1">
            Manage currencies for your platform
          </p>
        </div>
        <div className="flex items-center space-x-2 text-slate-600 dark:text-slate-400">
          <Icon icon="heroicons:currency-dollar" className="w-5 h-5" />
          <span className="text-sm">Super Admin Only</span>
        </div>
      </div>

      {/* Content */}
      {showForm ? (
        <CurrencyForm
          currency={editingCurrency}
          onCancel={handleCancel}
          onSuccess={handleSuccess}
        />
      ) : (
        <CurrencyList
          onEdit={handleEdit}
          onAdd={handleAdd}
        />
      )}
    </div>
  );
};

export default Currencies;
