import BasicTablePage from "@/components/partials/common-table/table-basic";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import React, { useState } from "react";
import avatar from "@/assets/images/avatar/av-1.svg";
import Badge from "@/components/ui/Badge";
import CreateType from "./CreateType";
import EditType from "./EditType";
import { useSelector } from "react-redux";
import {
  setEditData,
  setEditShowModal,
  setShowModal,
} from "@/features/commonSlice";
import { useDispatch } from "react-redux";
import DeleteType from "./DeleteType";

const TypeList = () => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const { showModal, showEditModal, editData } = useSelector(
    (state) => state.commonReducer
  );
  const [deleteData, setDeleteData] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const dispatch = useDispatch();
  const { data, isLoading } = useGetApiQuery(
    "admin/organization-payment-types"
  );
  //   console.log(res)
  //   const data = res?.data;

  //   console.log(data);

  const changePage = (val) => {
    console.log(val);
    setApiParam(val);
  };

  //   console.log(res.data?.data);

  const columns = [
    {
      label: "Name",
      field: "name",
    },
    // {
    //   label: "Name",
    //   field: "name",
    // },
    {
      label: "Total Items",
      field: "items",
    },
    {
      label: "Action",
      field: "",
    },
  ];

  const tableData = data?.map((item, index) => {
    return {
      name: (
        <button
          type="button"
          className="relative flex items-center bg-transparent border-none cursor-pointer p-0 rounded-lg p-2"
        >
          <img
            src={
              item.icon
                ? import.meta.env.VITE_ASSET_HOST_URL + item.icon
                : avatar
            }
            className="rounded-full w-20 h-14 mr-3"
            alt="avatar"
          />
          <div className="flex flex-col">
            <span className="text-sm font-medium text-gray-800 hover:text-primary-500 flex items-center">
              {item.name}
            </span>
            {/* {item.is_featured && (
              <span className="transform mt-1 px-3 py-1 text-xs font-bold text-white bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full shadow-md">
                Featured
              </span>
            )} */}
          </div>
        </button>
      ),
      items: item?.items?.length > 0 ? item.items.length : "--",
    };
  });

  const actions = [
    {
      name: "edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        dispatch(setEditData(data[val]));
        dispatch(setEditShowModal(true));
      },
    },
    {
      name: "delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  const handleClose = () => {
    dispatch(setShowModal(false));
    dispatch(setEditShowModal(false));
  };


  return (
    <div>
      <BasicTablePage
        loading={isLoading}
        title="Payment List"
        createButton="Add New Type"
        // setShowBulkModal={setShowBulkModal}
        // createPage={createPage}
        // editPage={editPage}
        actions={actions}
        columns={columns}
        data={tableData}
        changePage={changePage}
        currentPage={data?.current_page}
        // totalPages={Math.ceil(data?.total / data?.per_page)}
        // filter={filter}
        // setFilter={setApiParam}
      />

      {showModal && (
        <CreateType
          showCreateModal={showModal}
          setShowCreateModal={setShowModal}
          handleClose={handleClose}
        />
      )}

      {showEditModal && (
        <EditType
          showEditModal={showEditModal}
          editData={editData}
          handleClose={handleClose}
        />
      )}

      {showDeleteModal && (
        <DeleteType
          showDeleteModal={showDeleteModal}
          setShowDeleteModal={setShowDeleteModal}
          data={deleteData}
        />
      )}
    </div>
  );
};

export default TypeList;
