
import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import Header from "./Header";
import demo from "../../assets/images/promotion/demo.png";
import PlatformSection from "./Components/PlatformSection";
import WhyUseEduPack from "./Components/WhyUseEduPack";
import KeyAdvantages from "./Components/KeyAdvantages";
import Features from "./Components/Features";
import AppDemo from "./Components/AppDemo";
import TrialBanner from "./Components/TrialBanner";
import Overview from "./Components/OverviewData";
import { Link } from "react-router-dom";
import EdupakLoader from "@/components/EdupakLoader";
import Learning from "./Components/Learning";
import StoriesCarousel from "./Components/StoriesCarousel";
import useVisitorTracking from "@/hooks/useVisitorTracking";

const Home = () => {
  // State to track loading status
  const [loading, setLoading] = useState(true);

  // Track visitor for home page
  useVisitorTracking('home', {
    page_type: 'landing',
    section: 'promotional'
  });

  // Simulate a loading delay (replace this with real data fetching logic)
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false); // Set loading to false after 2 seconds (simulate loading)
    }, 2000);

    return () => clearTimeout(timer); // Clean up the timer
  }, []);

  return (
    <div className="min-h-screen relative">
      {/* Loader is shown when loading is true */}

      {/* Main content is shown after loading is complete */}
        <main className="bg-[#EEF2FF]">
          {/* Learning Section */}
          <Learning />
          {/* Platform Section */}
          <PlatformSection />
          {/* Overview Section */}
          <Overview />
          {/* Why Use EduPack Section */}
          <WhyUseEduPack />
          {/* Key Advantages Section */}
          <KeyAdvantages />
           {/* Features Section */}
          <Features />
          {/* App Demo Section */}
          <AppDemo />
           {/* Trial Banner */}
          <TrialBanner />
           {/* Trial Banner */}
          <StoriesCarousel />
        </main>
    </div>
  );
};

export default Home;
