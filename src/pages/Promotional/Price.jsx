import React, { useState, useEffect } from "react";
import PriceSectionInfo from "./Components/PriceSectionInfo";
import TrialBanner from "./Components/TrialBanner";
import { features } from "./Components/stories";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
// import FeatureSummary from "./FeatureSummary";
import PricingPage from "./PricingPage";
import { useTranslation } from 'react-i18next';
import useVisitorTracking from "@/hooks/useVisitorTracking";


const plans = {
  monthly: ["$0", "$29", "$59", "$99"],
  annual: ["$0", "$299", "$599", "$999"],
};

const Price = () => {
  // State to track loading status
  const [loading, setLoading] = useState(true);
  const [isAnnual, setIsAnnual] = useState(true);
  const currentPrices = isAnnual ? plans.annual : plans.monthly;
  const { t } = useTranslation();

  const { isAuth } = useSelector((state) => state.auth);

  // Track visitor for pricing page
  useVisitorTracking('pricing', {
    page_type: 'pricing',
    section: 'promotional',
    is_authenticated: isAuth
  });
  // Simulate a loading delay (replace this with real data fetching logic)
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false); // Set loading to false after 2 seconds (simulate loading)
    }, 2000);

    return () => clearTimeout(timer); // Clean up the timer
  }, []);
  const [error, setError] = useState("");
  const navigate = useNavigate();
  const [billingCycle, setBillingCycle] = useState("monthly");
  const [email, setEmail] = useState("");
  const Price = useGetApiQuery("package-list");

  const handleStartTrail = (e) => {
    e.preventDefault();

    // Email validation regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!email) {
      setError("Please enter your email.");
      return;
    }

    if (!emailRegex.test(email)) {
      setError("Please enter a valid email address.");
      return;
    }

    setError(""); // Clear error if email is valid

    // Navigate to '/try-new-lms' with email in state
    navigate("/try-new-lms", { state: { email } });
  };

  return (
    <div className="min-h-screen bg-white">
      <main className="bg-white py-20 ">


      {/* <FeatureSummary /> */}
        <div className="px-6 max-w-7xl mx-auto flex flex-col items-center gap-4 pb-12 mt-12">

          {/* Purple subtitle */}
          <div className="flex items-center justify-center mb-2">
            <div className="w-8 h-[1px] bg-[#4338CA]"></div>
            <h2 className="text-[#4338CA] text-2xl font-semibold mx-2">
              {t("pricing.fits_budget")}
            </h2>
          </div>
          { !isAuth &&
          <div>
          {/* Main heading */}
          <h1 className="text-3xl md:text-3xl font-semibold text-gray-900 text-center mb-4">
            {t("pricing.plans_grow")}
          </h1>

          {/* Email input and button */}
          <form
            onSubmit={handleStartTrail}
            className="w-full max-w-xl flex flex-col sm:flex-row gap-4"
          >
            <input
              type="email"
              placeholder={t("pricing.email_placeholder")}
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className={`flex-1 px-4 py-[5px] border ${
                error ? "border-red-500" : "border-[#C7D2FE]"
              } focus:outline-none focus:ring-2 focus:ring-[#C7D2FE] rounded-full`}
            />
            <button
              type="submit"
              className="px-6 py-3 bg-[#312E81] text-white rounded-full font-semibold hover:bg-[#312E81] transition-colors"
            >
              {t("pricing.start_free")}
            </button>


          </form>
          {error && (
              <p className="text-red-500 text-sm mt-1 sm:mt-0">{error}</p>
            )}
          </div>
          }
          {/* Billing toggle */}
          <div className="inline-flex items-center rounded-lg border border-[#4F46E5] px-3 py-2 bg-gray-100 mt-3 space-x-3">
            <button
              onClick={() => setBillingCycle("annual")}
              className={`px-6 py-2 rounded-lg text-sm p-3 font-medium transition-colors ${
                billingCycle === "annual"
                  ? "bg-[#312E81] text-white shadow-[0_4px_8px_0_rgba(0,0,0,0.4)]"
                  : "text-[#6B7280] border border-[#6B7280]"
              }`}
            >
              {t("pricing.annual_save")}
            </button>
            <button
              onClick={() => setBillingCycle("monthly")}
              className={`px-6 py-2 rounded-lg text-sm font-medium transition-colors ${
                billingCycle === "monthly"
                  ? "bg-[#312E81] text-white shadow-[0_4px_8px_0_rgba(0,0,0,0.4)]"
                  : "text-[#6B7280] border border-[#6B7280]"
              }`}
            >
              {t("pricing.monthly")}
            </button>
          </div>
        </div>

        <PriceSectionInfo PriceData={Price} billingCycle={billingCycle} />

        <PricingPage PriceData={Price?.data} billingCycle={billingCycle} />

        <TrialBanner />
      </main>
    </div>
  );
};

export default Price;
