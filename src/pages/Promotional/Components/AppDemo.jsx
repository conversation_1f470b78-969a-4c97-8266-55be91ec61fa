import React, { useEffect, useRef, useState } from "react";
import { Icon } from "@iconify/react";
import AndoidDevice from "../../../assets/images/promotion/androiddevice.png";
import IosDevice from "../../../assets/images/promotion/iosDevice.png";
import { useOnScreen } from "./Features";
import { useTranslation } from "react-i18next";

const AppDemo = () => {
  const { t } = useTranslation();

  const features = [
    {
      title: t('promotional.cross_functional'),
      description: t('promotional.sync_courses'),
      icon: "mdi:sync",
    },
    {
      title: t('promotional.course_access'),
      description: t('promotional.manage_anywhere'),
      icon: "mdi:book-open-page-variant",
    },
    {
      title: t('promotional.progress_tracking'),
      description: t('promotional.monitor_progress'),
      icon: "mdi:chart-line",
    },
    {
      title: t('promotional.offline_learning'),
      description: t('promotional.download_learn'),
      icon: "mdi:download",
    },
    {
      title: t('promotional.push_notification'),
      description: t('promotional.timely_updates'),
      icon: "mdi:bell",
    },
  ];

  const androidScreens = [
    "https://images.unsplash.com/photo-1551650975-87deedd944c3?auto=format&fit=crop&q=80&w=1974",
    "https://images.unsplash.com/photo-1555774698-0b77e0d5fac6?auto=format&fit=crop&q=80&w=1974",
    "https://images.unsplash.com/photo-1551650975-87deedd944c3?auto=format&fit=crop&q=80&w=1974",
  ];

  const iosScreens = [
    "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?auto=format&fit=crop&q=80&w=1974",
    "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?auto=format&fit=crop&q=80&w=1974",
    "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?auto=format&fit=crop&q=80&w=1974",
  ];
  const useOnScreen = (ref, threshold = 0.1) => {
    const [isIntersecting, setIntersecting] = useState(false);

    useEffect(() => {
      if (!ref.current) return;

      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setIntersecting(true);
            observer.unobserve(ref.current); // Trigger only once
          }
        },
        {
          threshold
        }
      );

      observer.observe(ref.current);

      // Cleanup the observer on unmount
      return () => {
        if (ref.current) observer.unobserve(ref.current);
      };
    }, [ref, threshold]);

    return isIntersecting;
  };
  const headerRef = useRef(null);
  const headerInView = useOnScreen(headerRef, 0.1);
  const [activeApp, setActiveApp] = useState("android");
  const sectionRef = useRef(null);;
  const isVisible = useOnScreen(sectionRef, 0.2);

  // useEffect(() => {
  //   setIsVisible(true);
  // }, []);


  return (
    <section ref={sectionRef} className="pt-20 pb-24 px-6 bg-[#F8F8FC]">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div
          ref={headerRef}
          className={`
            text-center mb-12 transform transition-all duration-1000
            ${headerInView ? 'translate-y-0 opacity-100' : 'translate-y-20 opacity-0'}
          `}
        >
          <div className="flex items-center justify-center mb-2">
            <div className="w-8 h-[1px] bg-[#4338CA]"></div>
            <h2 className="text-[#4338CA] text-2xl font-semibold mx-2">
              {t('promotional.app_demo')}
            </h2>
          </div>
          <h3 className="text-3xl font-medium text-gray-900">
            {t('promotional.integrate_mobile')}
          </h3>
        </div>

        <div className="flex flex-col lg:flex-row items-center gap-16">
          {/* Left Content */}
          <div className={`lg:w-1/2 space-y-6 transition-all duration-1000 transform
            ${isVisible ? 'translate-x-0 opacity-100' : '-translate-x-[70%] opacity-0'}`}>
            <p className="text-gray-600 text-lg leading-relaxed">
              {t('promotional.mobile_app_desc')}
            </p>

            {/* Features List */}
            <div className="space-y-6">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center gap-3">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="flex-shrink-0"
                  >
                    <mask
                      id="mask0_362_302"
                      maskUnits="userSpaceOnUse"
                      x="0"
                      y="0"
                      width="16"
                      height="16"
                    >
                      <rect width="16" height="16" fill="#D9D9D9" />
                    </mask>
                    <g mask="url(#mask0_362_302)">
                      <path
                        d="M4.46686 12L0.700195 8.23334L1.6502 7.3L5.41686 11.0667L4.46686 12ZM8.23353 12L4.46686 8.23334L5.4002 7.28334L8.23353 10.1167L14.3669 3.98334L15.3002 4.93334L8.23353 12ZM8.23353 8.23334L7.28353 7.3L10.5835 4L11.5335 4.93334L8.23353 8.23334Z"
                        fill="#4C1D95"
                      />
                    </g>
                  </svg>
                  <div>
                    <span className="font-semibold text-gray-900">
                      {feature.title}
                    </span>
                    <span className="text-gray-600 ml-2">
                      - {feature.description}
                    </span>
                  </div>
                </div>
              ))}
            </div>

            {/* Store Badges */}
            <div className="flex gap-4 items-center mt-6">
              <a
                href="https://play.google.com/store/apps" // Replace with your app's Play Store URL
                target="_blank"
                rel="noopener noreferrer"
              >
                <button
                  className={`flex items-center gap-3 max-sm:gap-1 px-6 py-3 max-sm:px-2 max-sm:py-3 rounded-xl transition-all duration-300 shadow-md ${
                    activeApp === "android"
                      ? "bg-indigo-600 text-white"
                      : "bg-white border border-gray-300 text-gray-600"
                  }`}
                >
                  <Icon icon="mdi:google-play" className="w-6 h-6" />
                  <div>
                    <p className="text-xs">{t('promotional.get_it_on')}</p>
                    <p className="font-bold">{t('promotional.google_play')}</p>
                    <p className={`text-sm ${activeApp === "android" ? "text-gray-300" : "text-gray-600" }`}>{t('promotional.upcoming')}</p>
                  </div>
                </button>
              </a>
              <a
                href="https://www.apple.com/app-store/" // Replace with your app's App Store URL
                target="_blank"
                rel="noopener noreferrer"
              >
              <button
                className={`flex items-center gap-3 px-6 py-3 rounded-xl transition-all duration-300 shadow-md ${
                  activeApp === "ios"
                    ? "bg-indigo-600 text-white"
                    : "bg-white border border-gray-300 text-gray-600"
                }`}
              >
                <Icon icon="mdi:apple" className="w-6 h-6" />
                <div>
                  <p className="text-xs">{t('promotional.download_on')}</p>
                  <p className="font-bold">{t('promotional.apple_store')}</p>
                  <p className={`text-sm ${activeApp === "ios" ? "text-gray-300" : "text-gray-600" }`}>{t('promotional.upcoming')}</p>
                </div>
              </button>
              </a>
            </div>
          </div>

          {/* Right App Preview */}
          <div className={`lg:w-1/2 transition-all duration-1000 transform
            ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-[70%] opacity-0'}`}>
            <div className=" relative">
              <div className="flex justify-center items-center h-[300px]">
                {activeApp === "android" ? (
                  <img
                    src={AndoidDevice}
                    alt="Android Device"
                    className="rounded-xl shadow-lg transform hover:scale-105 transition-transform duration-300"
                  />
                ) : (
                  <img
                    src={IosDevice}
                    alt="iOS Device"
                    className="rounded-xl shadow-lg transform hover:scale-105 transition-transform duration-300"
                  />
                )}
              </div>

              {/* Platform Toggle */}
              <div className="absolute -bottom-[120px] max-sm:-bottom-[60px] left-1/2 transform -translate-x-1/2 bg-white rounded-full shadow-md p-1">
                <div className="flex items-center gap-1">
                  <button
                    onClick={() => setActiveApp("android")}
                    className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                      activeApp === "android"
                        ? "bg-gray-900 text-white"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    {t('promotional.android_app')}
                  </button>
                  <button
                    onClick={() => setActiveApp("ios")}
                    className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                      activeApp === "ios"
                        ? "bg-gray-900 text-white"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    {t('promotional.ios_app')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AppDemo;
