import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from 'react-i18next';
import feature1 from "../../../assets/images/promotion/feature1.png";
import feature2 from "../../../assets/images/promotion/feature2.png";
import feature3 from "../../../assets/images/promotion/feature3.png";
import feature4 from "../../../assets/images/promotion/feature4.png";
import feature5 from "../../../assets/images/promotion/feature5.png";
import feature6 from "../../../assets/images/promotion/feature6.png";
import feature7 from "../../../assets/images/promotion/feature7.png";
import feature8 from "../../../assets/images/promotion/feature8.png";
const getFeatures = (t) => [
  {
    title: t('promotional.student_management'),
    description: t('promotional.student_management_desc'),
    image: feature1,
    gridArea: "span 1 / span 2",
  },
  {
    title: t('promotional.course_management'),
    description: t('promotional.course_management_desc'),
    image: feature2,
    gridArea: "span 1 / span 2",
  },
  {
    title: t('promotional.forum_support'),
    description: t('promotional.forum_support_desc'),
    image: feature8,
    gridArea: "span 2 / span 2",
  },
  {
    title: t('promotional.affiliate_course'),
    description: t('promotional.affiliate_course_desc'),
    image: feature3,
    gridArea: "span 1 / span 2",
  },
  {
    title: t('promotional.role_management'),
    description: t('promotional.role_management_desc'),
    image: feature4,
    gridArea: "span 1 / span 2",
  },
  {
    title: t('promotional.live_chat'),
    description: t('promotional.live_chat_desc'),
    image: feature5,
    gridArea: "span 1 / span 2",
  },
  {
    title: t('promotional.schedule_setup'),
    description: t('promotional.schedule_setup_desc'),
    image: feature6,
    gridArea: "span 1 / span 2",
  },
  {
    title: t('promotional.payment_management'),
    description: t('promotional.payment_management_desc'),
    image: feature7,
    gridArea: "span 1 / span 2",
  },
];

export const useOnScreen = (ref, threshold = 0.1) => {
  const [isIntersecting, setIntersecting] = useState(false);

  useEffect(() => {
    if (!ref.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIntersecting(true);
          observer.unobserve(ref.current); // Trigger only once
        }
      },
      { threshold }
    );

    observer.observe(ref.current);

    return () => {
      if (ref.current) observer.unobserve(ref.current);
    };
  }, [ref, threshold]);

  return isIntersecting;
};


const Features = () => {
  const { t } = useTranslation();
  const features = getFeatures(t);
  const sectionRef = useRef(null);
  const isVisible = useOnScreen(sectionRef, 0.2);
  const headerRef = useRef(null);
  const headerInView = useOnScreen(headerRef, 0.1);
  return (
    <section
      ref={sectionRef}
      className="py-20 px-6 bg-white"
    >
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div
          ref={headerRef}
          className={`
            text-center mb-12 transform transition-all duration-1000
            ${headerInView ? 'translate-y-0 opacity-100' : 'translate-y-20 opacity-0'}
          `}
        >
          <div className="flex items-center justify-center mb-2">
            <div className="w-8 h-[1px] bg-[#4338CA]"></div>
            <h2 className="text-[#4338CA] text-2xl font-semibold mx-2">
              {t('promotional.features')}
            </h2>
          </div>
          <h3 className="text-3xl font-medium text-gray-900">
            {t('promotional.empower_growth')}
          </h3>
        </div>

        {/* Bento Box Grid */}
        <div className="grid grid-cols-2 md:grid-cols-6 auto-rows-[200px] gap-6">
          {features.map((feature, index) => (
            <div
              key={index}
              className={`group relative rounded-2xl overflow-hidden cursor-pointer transform transition-all duration-1000 ${
                isVisible ? "translate-y-0 opacity-100" : "translate-y-10 opacity-0"
              }`}
              style={{
                gridArea: feature.gridArea,
                transitionDelay: isVisible ? `${index * 200}ms` : "0ms", // Staggered delay
              }}
            >
              {/* Background Image */}
              <img
                src={feature.image}
                alt={feature.title}
                className="absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
              />

              {/* Dark Overlay */}
              <div className="absolute inset-0 bg-gradient-to-b from-transparent via-black/50 to-black/80" />

              {/* Default Content */}
              <div className="absolute inset-0 p-6 flex flex-col justify-end group-hover:opacity-0 transition-opacity duration-300">
                <h4 className="text-2xl font-semibold text-white mb-2">
                  {feature.title}
                </h4>
                <p className="text-white/90 text-sm leading-relaxed">
                  {feature.description.split(" ").slice(0, 8).join(" ")}...
                </p>
              </div>

              {/* Hover Overlay */}
              <div className="absolute inset-0 bg-gray-900/90 opacity-0 group-hover:opacity-70 transition-opacity duration-300">
                <div className="absolute inset-0 p-6 flex flex-col justify-end">
                  <h4 className="text-2xl font-semibold text-white mb-3">
                    {feature.title}
                  </h4>
                  <p className="text-white/90 text-sm leading-relaxed transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
                    {feature.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;
