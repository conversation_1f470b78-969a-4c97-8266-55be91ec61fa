import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from 'react-i18next';
import feature1 from "../../../assets/images/promotion/feature1.png";
import feature2 from "../../../assets/images/promotion/feature2.png";
import feature3 from "../../../assets/images/promotion/feature3.png";
import feature4 from "../../../assets/images/promotion/feature4.png";
import feature5 from "../../../assets/images/promotion/feature5.png";
import feature6 from "../../../assets/images/promotion/feature6.png";
import feature7 from "../../../assets/images/promotion/feature7.png";
import feature8 from "../../../assets/images/promotion/feature8.png";

const getFeatures = (t) => [
  {
    title: t('promotional.student_management'),
    description: t('promotional.student_management_desc'),
    image: feature1,
    gridArea: "span 1 / span 2",
    icon: "👥",
  },
  {
    title: t('promotional.course_management'),
    description: t('promotional.course_management_desc'),
    image: feature2,
    gridArea: "span 1 / span 2",
    icon: "📚",
  },
  {
    title: t('promotional.forum_support'),
    description: t('promotional.forum_support_desc'),
    image: feature8,
    gridArea: "span 2 / span 2",
    icon: "💬",
  },
  {
    title: t('promotional.affiliate_course'),
    description: t('promotional.affiliate_course_desc'),
    image: feature3,
    gridArea: "span 1 / span 2",
    icon: "🤝",
  },
  {
    title: t('promotional.role_management'),
    description: t('promotional.role_management_desc'),
    image: feature4,
    gridArea: "span 1 / span 2",
    icon: "🛡️",
  },
  {
    title: t('promotional.live_chat'),
    description: t('promotional.live_chat_desc'),
    image: feature5,
    gridArea: "span 1 / span 2",
    icon: "💭",
  },
  {
    title: t('promotional.schedule_setup'),
    description: t('promotional.schedule_setup_desc'),
    image: feature6,
    gridArea: "span 1 / span 2",
    icon: "⏰",
  },
  {
    title: t('promotional.payment_management'),
    description: t('promotional.payment_management_desc'),
    image: feature7,
    gridArea: "span 1 / span 2",
    icon: "💳",
  },
];

export const useOnScreen = (ref, threshold = 0.1) => {
  const [isIntersecting, setIntersecting] = useState(false);

  useEffect(() => {
    if (!ref.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIntersecting(true);
          observer.unobserve(ref.current);
        }
      },
      { threshold }
    );

    observer.observe(ref.current);

    return () => {
      if (ref.current) observer.unobserve(ref.current);
    };
  }, [ref, threshold]);

  return isIntersecting;
};

const Features = () => {
  const { t } = useTranslation();
  const features = getFeatures(t);
  const sectionRef = useRef(null);
  const isVisible = useOnScreen(sectionRef, 0.1);
  const headerRef = useRef(null);
  const headerInView = useOnScreen(headerRef, 0.1);

  return (
    <section
      ref={sectionRef}
      className="py-24 px-4 sm:px-6 bg-gradient-to-b from-white to-gray-50"
    >
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div
          ref={headerRef}
          className={`
            text-center mb-16 transform transition-all duration-700 ease-out
            ${headerInView ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}
          `}
        >
          <span className="inline-block px-3 py-1 text-sm font-medium text-indigo-700 bg-indigo-50 rounded-full mb-4">
            {t('promotional.features')}
          </span>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {t('promotional.empower_growth')}
          </h2>
          <p className="max-w-2xl mx-auto text-xl text-gray-600">
            { t('promotional.features_subtitle') || "Comprehensive tools designed to streamline your educational operations"}
          </p>
        </div>

        {/* Bento Box Grid */}
        <div className="grid grid-cols-2 md:grid-cols-6 auto-rows-[240px] gap-5">
          {features.map((feature, index) => (
            <div
              key={index}
              className={`
                group relative rounded-xl overflow-hidden cursor-pointer border border-gray-100
                transform transition-all duration-700 ease-out shadow-sm hover:shadow-md
                ${isVisible ? "translate-y-0 opacity-100" : "translate-y-8 opacity-0"}
              `}
              style={{
                gridArea: feature.gridArea,
                transitionDelay: isVisible ? `${index * 100}ms` : "0ms",
              }}
            >
              {/* Background Image */}
              <img
                src={feature.image}
                alt={feature.title}
                className="absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                loading="lazy"
              />

              {/* Gradient Overlay */}
              <div className="absolute inset-0 bg-gradient-to-b from-transparent via-black/30 to-black/70" />

              {/* Content Container */}
              <div className="relative h-full flex flex-col justify-between p-6">
                {/* Icon/Top Content */}
                <div className="flex justify-between items-start">
                  <span className="text-3xl opacity-90">{feature.icon}</span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-black/30 text-white backdrop-blur-sm">
                    Feature
                  </span>
                </div>

                {/* Bottom Content */}
                <div>
                  {/* Default State */}
                  <div className="group-hover:opacity-0 transition-opacity duration-300">
                    <h3 className="text-xl font-bold text-white mb-2 leading-tight">
                      {feature.title}
                    </h3>
                    <p className="text-white/90 text-sm line-clamp-2">
                      {feature.description}
                    </p>
                  </div>

                  {/* Hover State */}
                  <div className="absolute inset-0 p-6 flex flex-col justify-end opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-b from-transparent to-black/90">
                    <h3 className="text-xl font-bold text-white mb-3">
                      {feature.title}
                    </h3>
                    <p className="text-white/90 text-sm mb-4">
                      {feature.description}
                    </p>
                    <button className="self-start px-4 py-2 text-sm font-medium text-indigo-600 bg-white rounded-md hover:bg-indigo-50 transition-colors">
                      Learn more →
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <button className="px-8 py-3 bg-indigo-600 text-white font-medium rounded-lg hover:bg-indigo-700 transition-colors shadow-md hover:shadow-lg">
            {t('promotional.get_started') || "Get Started Today"}
          </button>
          <p className="mt-4 text-gray-500">
            {t('promotional.trusted_by') || "Trusted by 500+ educational institutions worldwide"}
          </p>
        </div>
      </div>
    </section>
  );
};

export default Features;