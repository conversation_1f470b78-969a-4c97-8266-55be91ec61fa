import React from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import image2 from "../../../assets/images/promotion/pi2.png";
import image3 from "../../../assets/images/promotion/pi3.png";
import image4 from "../../../assets/images/promotion/pi4.png";
// Get overview data with translations
const getOverviewData = (t) => [
  {
    number: t('promotional.one_third'),
    title: t('promotional.simplified_learning'),
    features: [
      t('promotional.user_friendly_tools'),
      t('promotional.automate_tasks'),
      t('promotional.real_time_tracking'),
    ],
    image: image2
  },
  {
    number: t('promotional.two_third'),
    title: t('promotional.engaging_learning'),
    features: [
      t('promotional.interactive_content'),
      t('promotional.mobile_first'),
      t('promotional.multi_language'),
    ],
    image: image3
  },
  {
    number: t('promotional.three_third'),
    title: t('promotional.scalable_flexible'),
    features: [
      t('promotional.adaptable_classes'),
      t('promotional.cloud_infrastructure'),
      t('promotional.integration_platforms'),
    ],
    image: image4
  },
];

// Animation variants for text content
const textVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: { opacity: 1, y: 0 },
};

// Animation variants for images
const imageVariants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { opacity: 1, scale: 1 },
};

// OverviewCard Component with Animations
const OverviewCard = ({ number, title, features, image, reverse }) => {
  const contentOrder = reverse ? "md:order-2" : "";
  const imageOrder = reverse ? "md:order-1" : "";

  return (
    <div className="flex flex-col md:flex-row gap-8 items-center py-8">
      {/* Text Content with Animation */}
      <motion.div
        className={`w-full md:w-1/2 space-y-2 ${contentOrder}`}
        variants={textVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <span className="text-gray-900 font-medium mb-5">{number}</span>
        <h3 className="text-xl font-medium bg-gradient-to-r from-indigo-500 to-purple-900 bg-clip-text text-transparent">
          {title}
        </h3>

        <ul className="space-y-4">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start gap-3">
              <div className="w-2 h-2 mt-2 rounded-full bg-gray-900" />
              <span className="text-gray-600">{feature}</span>
            </li>
          ))}
        </ul>
      </motion.div>

      {/* Image with Animation */}
      <motion.div
        className={`w-full md:w-1/2 ${imageOrder}`}
        variants={imageVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
      >
        <img
          src={image}
          alt={title}
          className="w-full h-[300px] object-cover rounded-lg shadow-lg"
        />
      </motion.div>
    </div>
  );
};

// Overview Component
const Overview = () => {
  const { t } = useTranslation();
  const overviewData = getOverviewData(t);

  return (
    <section className="py-20 px-6 bg-gradient-to-b from-blue-100 to-purple-100">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}

        <motion.div
          // className={`w-full md:w-1/2 space-y-2 ${contentOrder}`}
          variants={textVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
        >
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-2">
              <div className="w-8 h-[1px] bg-[#4338CA]"></div>
              <h2 className="text-[#4338CA] text-2xl font-semibold mx-2">
                {t('promotional.overview')}
              </h2>
            </div>
            <h2 className="text-3xl font-medium text-gray-900">
              {t('promotional.turn_visions_reality')}
            </h2>
          </div>
        </motion.div>

        {/* Overview Cards */}
        <div className="space-y-16">
          {overviewData.map((feature, index) => (
            <OverviewCard key={index} {...feature} reverse={index % 2 !== 0} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Overview;
