import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";

const FeatureBlock = ({
  tagline,
  title,
  description,
  features,
  image,
  imageAlt,
  reverse = false,
}) => {
  const [activeTab, setActiveTab] = useState(0);

  // Array of colors for the line indicator
  const lineColors = [
    "bg-gradient-to-b from-blue-600 to-indigo-600",
    "bg-gradient-to-b from-emerald-600 to-teal-600",
    "bg-gradient-to-b from-purple-600 to-indigo-600",
  ];

  const animationVariants = {
    hidden: (direction) => ({
      opacity: 0,
      x: direction === "left" ? -100 : 100,
    }),
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.8,
        ease: "easeInOut",
      },
    },
  };

  return (
    <div
      className={`flex flex-col ${
        reverse ? "lg:flex-row-reverse" : "lg:flex-row"
      } items-center gap-12 py-8`}
    >
      <motion.div
        className="lg:w-1/2 space-y-3 space-x-4"
        initial={{ opacity: 0, x: reverse ? 100 : -100 }}
        whileInView={{ opacity: 1, x: 0 }}
        viewport={{ once: true, amount: 0.2 }}
        transition={{ duration: 0.8, ease: "easeInOut" }}
      >
        {/* <span className="text-[#4338CA] font-medium uppercase tracking-wider text-sm">
          {tagline}
        </span> */}
        <h3 className="text-xl font-medium text-[#065F46]">{title}</h3>
        {/* <p className="text-[#4B5563] leading-relaxed">{description}</p> */}
        <div className="space-y-4 relative">
          <div className="absolute left-0 top-0 w-1 h-full bg-gray-200 rounded-full"></div>
          <div
            className={`absolute left-0 -top-4 w-1 rounded-full transition-all duration-300 ease-in-out ${lineColors[activeTab]}`}
            style={{
              height: `${100 / features.length}%`,
              transform: `translateY(${activeTab * 100}%)`,
            }}
          ></div>

          {features.map((feature, index) => (
            <button
              key={index}
              onClick={() => setActiveTab(index)}
              className={`w-full text-left flex items-start gap-4 transition-colors duration-200 ${
                activeTab === index
                  ? "opacity-100"
                  : "opacity-70 hover:opacity-90"
              }`}
            >
              <div className="w-1 h-full invisible flex-shrink-0"></div>
              <div>
                <h4
                  className={`font-medium text-base mb-1 ${
                    activeTab === index ? "text-indigo-600" : "text-[#164E63]"
                  }`}
                >
                  {feature.title}
                </h4>
                <p className="text-[#4B5563] text-sm">{feature.description}</p>
              </div>
            </button>
          ))}
        </div>
      </motion.div>

      <motion.div
        className="lg:w-1/2 relative"
        initial={{ opacity: 0, x: reverse ? -100 : 100 }}
        whileInView={{ opacity: 1, x: 0 }}
        viewport={{ once: true, amount: 0.2 }}
        transition={{ duration: 0.8, ease: "easeInOut" }}
      >
        <img src={image} alt={imageAlt} className="w-full h-auto " />
        {features[activeTab].overlayImage && (
          <motion.div
            className={`absolute ${
              reverse
                ? "right-[0px] lg:right-[-40px] "
                : "left-[0px] lg:left-[-40px]"
            } inset-y-0 flex items-center`}
            initial={{ scale: 0.8, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            viewport={{ once: true, amount: 0.2 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <img
              src={features[activeTab].overlayImage}
              alt={`${features[activeTab].title} illustration`}
              className="w-full max-w-[300px] h-auto object-cover rounded-lg shadow-lg transition-transform duration-300 ease-in-out hover:scale-105"
            />
          </motion.div>
        )}
      </motion.div>
    </div>
  );
};

export default FeatureBlock;
