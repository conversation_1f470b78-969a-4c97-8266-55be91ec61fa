import React, { useRef, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import FeatureBlock from "./FeatureBlock";
import Streamlined from '@/assets/images/promotion/why1.png';
import Engaging from '@/assets/images/promotion/why3.png';
import Scalable from '@/assets/images/promotion/why5.png';
import Mobile from '@/assets/images/promotion/why4.png';
import Interactive from '@/assets/images/promotion/why9.png';
import AI from '@/assets/images/promotion/why10.png';
import Cloud from '@/assets/images/promotion/why6.png';
import DataSecurity from '@/assets/images/promotion/why11.png';
import Payment from '@/assets/images/promotion/why12.png';
import image5 from "@/assets/images/promotion/pi5.png";
import image6 from "@/assets/images/promotion/pi6.png";
import image7 from "@/assets/images/promotion/pi7.png";
import Setup from '@/assets/images/promotion/why2.png';
import psi1 from '@/assets/images/promotion/psi1.png';
import psi2 from '@/assets/images/promotion/psi2.png';
import psi3 from '@/assets/images/promotion/psi3.png';
import Tracking from '@/assets/images/promotion/why7.png';
import Content from '@/assets/images/promotion/why8.png';

const WhyUseEduPack = () => {
  const { t } = useTranslation();

  const features = [
    {
      tagline: "TAGLINE",
      title: t('promotional.streamlined_course'),
      description: "Lorem ipsum dolor sit amet consectetur. A lobortis turpis quis aliquet condimentum ac consequat diam. Mi dictum quis.",
      image: image5,
      imageAlt: "Course management dashboard",
      features: [
        {
          title: t('promotional.easy_course'),
          description: t('promotional.create_organize'),
          overlayImage: psi1
        },
        {
          title: t('promotional.automated_tracking'),
          description: t('promotional.track_progress'),
          overlayImage: Tracking
        },
        {
          title: t('promotional.customizable_content'),
          description: t('promotional.add_interactive'),
          overlayImage: Content
        },
      ],
    },
    {
      tagline: "TAGLINE",
      title: t('promotional.engaging_student'),
      description: "Lorem ipsum dolor sit amet consectetur. A lobortis turpis quis aliquet condimentum ac consequat diam. Mi dictum quis.",
      image: image6,
      imageAlt: "Students using mobile app",
      features: [
        {
          title: t('promotional.mobile_accessibility'),
          description: t('promotional.access_anywhere'),
          overlayImage: psi2
        },
        {
          title: t('promotional.interactive_learning'),
          description: t('promotional.foster_engagement'),
          overlayImage: Interactive
        },
        {
          title: t('promotional.ai_assistant'),
          description: t('promotional.instant_support'),
          overlayImage: AI
        },
      ],
    },
    {
      tagline: "TAGLINE",
      title: t('promotional.scalable_secure'),
      description: "Lorem ipsum dolor sit amet consectetur. A lobortis turpis quis aliquet condimentum ac consequat diam. Mi dictum quis.",
      image: image7,
      imageAlt: "Security and infrastructure",
      features: [
        {
          title: t('promotional.cloud_based'),
          description: t('promotional.scale_effortlessly'),
          overlayImage: psi3
        },
        {
          title: t('promotional.data_security'),
          description: t('promotional.ensure_privacy'),
          overlayImage: DataSecurity
        },
        {
          title: t('promotional.payment_gateway'),
          description: t('promotional.hassle_free'),
          overlayImage: Payment
        },
      ],
    },
  ];
  const useOnScreen = (ref, threshold = 0.1) => {
    const [isIntersecting, setIntersecting] = useState(false);

    useEffect(() => {
      if (!ref.current) return;

      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setIntersecting(true);
            observer.unobserve(ref.current); // Trigger only once
          }
        },
        {
          threshold
        }
      );

      observer.observe(ref.current);

      // Cleanup the observer on unmount
      return () => {
        if (ref.current) observer.unobserve(ref.current);
      };
    }, [ref, threshold]);

    return isIntersecting;
  };
  const headerRef = useRef(null);
  const headerInView = useOnScreen(headerRef, 0.1);

  return (
    <section className="py-10 px-6 bg-white">
      <div className="max-w-7xl mx-auto">
      <div
          ref={headerRef}
          className={`
            text-center mb-12 transform transition-all duration-1000
            ${headerInView ? 'translate-y-0 opacity-100' : 'translate-y-20 opacity-0'}
          `}
        >
          <div className="flex items-center justify-center mb-2">
            <div className="w-8 h-[1px] bg-[#4338CA]"></div>
            <h2 className="text-[#4338CA] text-2xl font-semibold mx-2">
              {t('promotional.why_use')}
            </h2>
          </div>
          <h3 className="text-3xl font-medium text-gray-900">
            {t('promotional.unlock_potential')}
          </h3>
        </div>

        <div className="space-y-20">
          {features.map((feature, index) => (
            <FeatureBlock key={index} {...feature} reverse={index % 2 !== 0} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyUseEduPack;