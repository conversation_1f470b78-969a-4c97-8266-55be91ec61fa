import React from "react";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";

const PriceCard = ({ plan, isPopular, handleStartTrial, billingCycle }) => {
  const { plan_name, price, description, features, disabledFeatures, salePrice, discountPercentage, trialPeriod } = plan;
  const { t, i18n } = useTranslation();
  const { isAuth } = useSelector((state) => state.auth);

  // Currency mapping
  const currencyMap = {
    bn: { symbol: "৳" },
    en: { symbol: "$" },
    jp: { symbol: "¥" },
    kr: { symbol: "₩" },
  };

  const selectedLang = i18n?.language || "en";
  const currencySymbol = currencyMap[selectedLang]?.symbol || "$";

  return (
    <div
      className={`rounded-2xl p-8 relative flex flex-col gap-6 border transition-all duration-300
      ${isPopular ? "bg-gradient-to-t from-[#0E0023] to-[#0F0C68] text-white" : "bg-white hover:shadow-[4px_8px_0px_0px_#6366F1]"}
      shadow-[4px_8px_8px_0px_rgba(0,0,0,0.1)] border-[#C7D2FE] hover:border-[#818CF8]`}
    >
      {isPopular && (
        <span className="absolute -top-3 right-8 bg-indigo-600 text-white px-4 py-1 rounded-full text-sm">
          {t("pricing.most_popular")}
        </span>
      )}

      <div>
        <h3 className={`text-2xl font-bold mb-2 ${isPopular ? "text-white" : "text-gray-900"}`}>
          {plan_name}

        </h3>

        {/* Display currency symbol before the amount */}
        <div className="text-4xl font-bold mb-4">
          <span className={isPopular ? "text-white" : "text-[#4C1D95]"}>
            {currencySymbol} {Number(salePrice || price).toFixed(2)}
          </span>
          {price > salePrice && (
            <span className="ml-2 text-sm line-through text-gray-400">
              {currencySymbol} {Number(price).toFixed(2)}
            </span>
          )}



              { billingCycle != 'monthly' && <p className="text-sm text-red-500 font-normal">
                <small>{t("pricing.annual_save")}</small>
              </p>
              }
        </div>

        <p className={`text-sm ${isPopular ? "text-gray-300" : "text-gray-600"}`}>{description}</p>

        {trialPeriod > 0 && (
          <p className={`text-sm mt-2 ${isPopular ? "text-gray-300" : "text-gray-600"}`}>
            {trialPeriod} days free trial
          </p>
        )}
      </div>

      {!isAuth && (
        <div className="text-center">
          <button
            onClick={() => handleStartTrial(plan.id)}
            className={`w-48 py-3 rounded-lg font-medium transition-colors
            ${isPopular ? "bg-white text-[#4C1D95] hover:bg-gray-100" : "bg-gradient-to-r from-[#6366F1] to-[#4C1D95] text-white"}`}
          >
            {t("pricing.start_free_trial")}
          </button>
          <div className="mt-3 border-b-2 border-[#C7D2FE]"></div>
        </div>
      )}

      <ul className="space-y-4">
        {features.map((feature, index) => (
          <FeatureItem key={index} text={feature} isDisabled={false} isPopular={isPopular} />
        ))}
        {disabledFeatures.map((feature, index) => (
          <FeatureItem key={`disabled-${index}`} text={feature} isDisabled={true} />
        ))}
      </ul>
    </div>
  );
};

const FeatureItem = ({ text, isDisabled, isPopular }) => (
  <li className="flex items-center gap-3">
    {isDisabled ? <CloseIcon /> : <CheckIcon />}
    <span className={isDisabled ? "text-gray-400" : isPopular ? "text-gray-300" : "text-gray-600"}>
      {text}
    </span>
  </li>
);

const CheckIcon = () => (
  <svg className="w-5 h-5 text-indigo-600 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
  </svg>
);

const CloseIcon = () => (
  <svg className="w-5 h-5 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
  </svg>
);

export default PriceCard;
