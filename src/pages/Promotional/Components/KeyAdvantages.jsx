import React, { useRef, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Icon } from "@iconify/react";
import Advantage1 from "@/assets/images/promotion/advantage1.png";
import Advantage2 from "@/assets/images/promotion/advantage2.png";
import Advantage3 from "@/assets/images/promotion/advantage3.png";
import Advantage4 from "@/assets/images/promotion/advantage4.png";
import Advantage5 from "@/assets/images/promotion/advantage5.png";
import Advantage6 from "@/assets/images/promotion/advantage6.png";
import Advantage7 from "@/assets/images/promotion/advantage7.png";
import image8 from "@/assets/images/promotion/pi8.png";
import { motion } from "framer-motion";

const getAdvantages = (t) => [
  {
    id: "course-creation",
    title: t('promotional.easy_creation'),
    icon: "mdi:file-document-edit-outline",
    image: image8,
  },
  {
    id: "student-tracking",
    title: t('promotional.automated_student'),
    icon: "mdi:chart-line",
    image: Advantage2,
  },
  {
    id: "scalable",
    title: t('promotional.scalable_growth'),
    icon: "mdi:trending-up",
    image: Advantage3,
  },
  {
    id: "integrations",
    title: t('promotional.seamless_integrations'),
    icon: "mdi:puzzle",
    image: Advantage4,
  },
  {
    id: "mobile",
    title: t('promotional.mobile_friendly'),
    icon: "mdi:cellphone",
    image: Advantage5,
  },
  {
    id: "interactive",
    title: t('promotional.interactive_tools'),
    icon: "mdi:school",
    image: Advantage6,
  },
  {
    id: "multilingual",
    title: t('promotional.multilingual'),
    icon: "mdi:translate",
    image: Advantage7,
  },
];

const KeyAdvantages = () => {
  const { t } = useTranslation();
  const advantages = getAdvantages(t);
  const [activeTab, setActiveTab] = useState(advantages[0].id);

  const activeAdvantage = advantages.find((adv) => adv.id === activeTab);

  // Animation variants for text content
  const textVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0 },
  };

  // Animation variants for images
  const imageVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { opacity: 1, scale: 1 },
  };
  const useOnScreen = (ref, threshold = 0.1) => {
    const [isIntersecting, setIntersecting] = useState(false);

    useEffect(() => {
      if (!ref.current) return;

      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setIntersecting(true);
            observer.unobserve(ref.current); // Trigger only once
          }
        },
        {
          threshold
        }
      );

      observer.observe(ref.current);

      // Cleanup the observer on unmount
      return () => {
        if (ref.current) observer.unobserve(ref.current);
      };
    }, [ref, threshold]);

    return isIntersecting;
  };
  const headerRef = useRef(null);
  const headerInView = useOnScreen(headerRef, 0.1);
  return (
    <section className="py-20 px-6 bg-[#F5F3FF]">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div
          ref={headerRef}
          className={`
            text-center mb-12 transform transition-all duration-1000
            ${headerInView ? 'translate-y-0 opacity-100' : 'translate-y-20 opacity-0'}
          `}
        >
          <div className="flex items-center justify-center mb-2">
            <div className="w-8 h-[1px] bg-[#4338CA]"></div>
            <h2 className="text-[#4338CA] text-2xl font-semibold mx-2">
              {t('promotional.key_advantages')}
            </h2>
          </div>
          <h3 className="text-3xl font-medium text-gray-900">
            {t('promotional.explore_advantages')}
          </h3>
        </div>

        <div className="flex flex-col lg:flex-row gap-16">
          {/* Left Menu */}
          <motion.div
            className={`lg:w-1/3`}
            variants={textVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <div className="flex flex-col gap-4">
              {advantages.map((advantage) => (
                <button
                  key={advantage.id}
                  onClick={() => setActiveTab(advantage.id)}
                  className={`w-full flex items-center gap-4 p-4 rounded-lg transition-all duration-200 relative ${activeTab === advantage.id
                      ? "bg-[#C7D2FE] text-[#111827] font-medium shadow-lg"
                      : "bg-[#EEF2FF] hover:bg-gray-100 font-medium text-[#4B5563] border border-[#C7D2FE]"
                    }`}
                >
                  <Icon
                    icon={advantage.icon}
                    className={`w-6 h-6 ${activeTab === advantage.id
                        ? "text-[#111827]"
                        : "text-[#111827]"
                      }`}
                  />
                  <span className="font-medium">{advantage.title}</span>

                  {/* Active Tab Arrow (Triangle Shape) */}
                  {activeTab === advantage.id && (
                    <div
                      className="absolute top-1/2 -right-4 -translate-y-1/2 w-0 h-0
            border-y-[12px] border-y-transparent
            border-l-[16px] border-l-[#C7D2FE]"
                    />
                  )}
                </button>
              ))}
            </div>
          </motion.div>

          {/* Right Image */}
          <motion.div
            className={`lg:w-2/3`}
            variants={imageVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
          >
            {activeAdvantage && (
              <div className=" h-[550px]  overflow-hidden  transition-all duration-500">
                <img
                  src={activeAdvantage.image}
                  alt={activeAdvantage.title}
                  className="w-full h-full object-contained"
                />
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default KeyAdvantages;
