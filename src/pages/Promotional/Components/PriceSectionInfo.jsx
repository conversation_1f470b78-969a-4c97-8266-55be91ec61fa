import React from "react";
import PriceCard from "./PriceCard";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

const PriceSectionInfo = ({ PriceData, billingCycle }) => {
  const { i18n } = useTranslation();
  const navigate = useNavigate();


  const currencyMap = {
    bn: { price: "price", salePrice: "sale_price", symbol: "৳" },
    en: { price: "dollar_price", salePrice: "dollar_sale_price", symbol: "$" },
    jp: { price: "yen_price", salePrice: "yen_sale_price", symbol: "¥" },
    kr: { price: "krw_price", salePrice: "krw_sale_price", symbol: "₩" },
  };
  
  const selectedLang = i18n?.language || "en";
  const currency = currencyMap[selectedLang] || currencyMap["en"];

  const processPlanData = (plan) => {
    const basePrice = plan[currency.price];
    const baseSalePrice = plan[currency.salePrice] * 12;
    const discount = baseSalePrice * 0.10;

    return {
      ...plan,
      features: JSON.parse(plan.features || "[]"),
      disabledFeatures: JSON.parse(plan.disabled_features || "[]"),
      price: billingCycle === "monthly" ? basePrice : basePrice * 12,
      salePrice: billingCycle === "monthly" ? plan[currency.salePrice] : baseSalePrice - discount,
    };
  };

  const processedPlans = PriceData?.data?.map(processPlanData) || [];

  const handleStartTrial = (id) => {
    navigate("/try-new-lms", {
      state: {
        package_id: id,
        package_month: billingCycle === "monthly" ? 1 : 12,
        is_trial: 1,
      },
    });
  };

  return (
    <section className="px-6">
      <div className="max-w-7xl mx-auto grid md:grid-cols-3 gap-8 mt-8">
        {processedPlans.map((plan) => (
          <PriceCard
            key={plan.id}
            plan={plan}
            isPopular={plan.is_popular === 1}
            handleStartTrial={handleStartTrial}
            billingCycle={billingCycle}
          />
        ))}
      </div>
    </section>
  );
};

export default PriceSectionInfo;
