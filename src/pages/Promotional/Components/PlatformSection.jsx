import React, { useRef, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import platform1 from '../../../assets/images/promotion/Platform1.png';
import platform2 from '../../../assets/images/promotion/Platform2.png';
import platform3 from '../../../assets/images/promotion/Platform3.png';
import platform4 from '../../../assets/images/promotion/Platform4.png';
import platform5 from '../../../assets/images/promotion/Platform5.png';

const getPlatforms = (t) => [
  {
    title: t('promotional.coaching'),
    image: platform1,
    features: [
      // 'Custom Coaching Pages',
      // '1-on-1 Sessions',
      // 'Progress Tracking'
    ],
    alt: 'People in a coaching session'
  },
  {
    title: t('promotional.individuals'),
    image: platform2,
    features: [
      // 'Personalized Learning',
      // 'Self-Paced Courses',
      // 'Skills Assessment'
    ],
    alt: 'Individual learning'
  },
  {
    title: t('promotional.training_for_banks'),
    image: platform3,
    features: [
      // 'Compliance Training',
      // 'Risk Management',
      // 'Financial Modules'
    ],
    alt: 'Corporate training session'
  },
  {
    title: t('promotional.marketing_training'),
    image: platform4,
    features: [
      // 'Digital Marketing',
      // 'Analytics Tools',
      // 'Campaign Planning'
    ],
    alt: 'Marketing professional presenting'
  },
  {
    title: t('promotional.health'),
    image: platform5,
    features: [
      // 'Medical Training',
      // 'Patient Care',
      // 'Health Records'
    ],
    alt: 'Healthcare professional'
  }
];

// Custom Hook to detect if an element is in view
const useOnScreen = (ref, threshold = 0.1) => {
  const [isIntersecting, setIntersecting] = useState(false);

  useEffect(() => {
    if (!ref.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIntersecting(true);
          observer.unobserve(ref.current); // Trigger only once
        }
      },
      {
        threshold
      }
    );

    observer.observe(ref.current);

    // Cleanup the observer on unmount
    return () => {
      if (ref.current) observer.unobserve(ref.current);
    };
  }, [ref, threshold]);

  return isIntersecting;
};

const PlatformSection = () => {
  const headerRef = useRef(null);
  const gridRef = useRef(null);
  const { t } = useTranslation();

  const headerInView = useOnScreen(headerRef, 0.1);
  const gridInView = useOnScreen(gridRef, 0.1);

  const platforms = getPlatforms(t);

  return (
    <section className="py-20 px-6 bg-white overflow-hidden">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div
          ref={headerRef}
          className={`
            text-center mb-12 transform transition-all duration-1000
            ${headerInView ? 'translate-y-0 opacity-100' : 'translate-y-20 opacity-0'}
          `}
        >
          <div className="flex items-center justify-center mb-2">
            <div className="w-8 h-[1px] bg-[#4338CA]"></div>
            <h2 className="text-[#4338CA] text-2xl font-semibold mx-2">
              {t('promotional.platform')}
            </h2>
          </div>
          <h3 className="text-3xl font-medium text-gray-900">
            {t('promotional.platform_type')}
          </h3>
        </div>

        {/* Grid Section */}
        <div
          ref={gridRef}
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6"
        >
          {platforms.map((platform, index) => (
            <div
              key={index}
              className={`
                group relative rounded-xl overflow-hidden cursor-pointer
                transform transition-all duration-700
                ${gridInView ? 'translate-y-0 opacity-100' : 'translate-y-28 opacity-0'}
              `}
              style={{
                transitionDelay: gridInView ? `${index * 100}ms` : '0ms'
              }}
            >
              <div className="aspect-[3/4] relative">
                <img
                  src={platform.image}
                  alt={platform.alt}
                  className="absolute inset-0 w-full h-full object-cover"
                />
                {/* Default State */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-black/0" />
                <div className="absolute top-0 left-0 right-0 p-4">
                  <h4 className="text-white font-semibold text-lg">
                    {platform.title}
                  </h4>
                </div>

                {/* Hover State */}
                <div className="absolute inset-0 bg-black/80 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="flex flex-col h-full p-4">
                    <h4 className="text-white font-semibold text-lg mb-4">
                      {platform.title}
                    </h4>
                    <ul className="space-y-3 mb-auto">
                      {platform.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-white">
                          <span className="w-2 h-2 bg-white rounded-full mr-3" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <button className="mt-4 text-white border-2 border-white rounded-md py-2 px-6 hover:text-black transition-colors duration-300">
                      {t('layout.start_free_trial')}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PlatformSection;
