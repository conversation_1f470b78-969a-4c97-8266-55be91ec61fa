import Mynul from '@/assets/Teams/mynul.jpg'
import Rubai from '@/assets/Teams/rubai.jpg'
import Rueen from '@/assets/Teams/rueen.jpeg'
import Tushar from '@/assets/Teams/tushar.jpg'
import Dipto from '@/assets/Teams/dipto.jpeg'
import Zinat from '@/assets/Teams/zinat.jpeg'
import Rakib from '@/assets/Teams/rakib.jpeg'
import Mamun from '@/assets/Teams/mamun.jpg'
import Eashen from '@/assets/Teams/eashen_mahmud.jpeg'
import ridi from '@/assets/Teams/ridi.jpeg'
import { useTranslation } from 'react-i18next'

export const stories = [
  {
    id: 1,
    name: "Md Mynul Islam",
    nameKey: "promotional.mynul_islam",
    position: "Chief Technology Officer (CTO)",
    positionKey: "promotional.cto",
    description: "Leading the company’s technology strategy and driving innovation forward.",
    descriptionKey: "promotional.leading_technology",
    image: Mynul
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON>",
    nameKey: "promotional.hosne_mubarak",
    position: "Software Architecht",
    positionKey: "promotional.software_architect",
    description: "Designing robust software architectures to ensure scalable solutions.",
    descriptionKey: "promotional.designing_architectures",
    image: Rubai
  },
  {
    id: 3,
    name: "Md Mehedi Hasan",
    nameKey: "promotional.mehedi_hasan",
    position: "Sr. Software Engineer",
    positionKey: "promotional.sr_software",
    description: "Building and maintaining high-quality software systems with best practices.",
    descriptionKey: "promotional.building_maintaining",
    image: Rueen
  },
  {
    id: 4,
    name: "Tushar Imran",
    nameKey: "promotional.tushar_imran",
    position: "Sr. Mobile Developer",
    positionKey: "promotional.sr_mobile",
    description: "Creating seamless mobile experiences and optimizing app performance.",
    descriptionKey: "promotional.creating_mobile",
    image: Tushar
  },
  {
    id: 5,
    name: "Dipto Shom",
    nameKey: "promotional.dipto_shom",
    position: "Business Analyst",
    positionKey: "promotional.business_analyst",
    description: "Analyzing business needs and translating them into actionable insights.",
    descriptionKey: "promotional.analyzing_business",
    image: Dipto
  },
  {
    id: 6,
    name: "Zinat Sultana",
    nameKey: "promotional.zinat_sultana",
    position: "UI/UX Designer (Lead)",
    positionKey: "promotional.ui_ux_designer",
    description: "Leading the design team to craft intuitive, user-centered experiences and design strategies.",
    descriptionKey: "promotional.leading_design",
    image: Zinat
  },
  {
    id: 7,
    name: "Rakib Hasan",
    nameKey: "promotional.rakib_hasan",
    position: "Frontend Engineer",
    positionKey: "promotional.frontend_engineer",
    description: "Developing engaging and responsive user interfaces for the web.",
    descriptionKey: "promotional.developing_interfaces",
    image: Rakib
  },
  {
    id: 8,
    name: "Mamunur Rashid",
    nameKey: "promotional.mamunur_rashid",
    position: "DevOps",
    positionKey: "promotional.devops",
    description: "Ensuring smooth deployment, automation, and system reliability across environments.",
    descriptionKey: "promotional.ensuring_deployment",
    image: Mamun
  },
  {
    id:9,
    name: "Eashen Mahmud",
    nameKey: "promotional.eashen_mahmud",
    position: "Frontend Engineer",
    positionKey: "promotional.frontend_engineer",
    description: "Building and maintaining high-quality software systems with best practices.",
    descriptionKey: "promotional.building_maintaining",
    image: Eashen
  },
  {
    id:10,
    name: "Nowrin Hoque Ridi",
    nameKey: "promotional.nowrin_hoque",
    position: "Senior AI Engineer",
    positionKey: "promotional.senior_ai",
    description: "Building and maintaining high-quality software systems with AI enabled high quality features.",
    descriptionKey: "promotional.ai_features",
    image: ridi
  }
];





export const features = [
  {
    title: "File Storage",
    free: "5 GB",
    basic: "50 GB",
    professional: "500 GB",
    expert: "Unlimited",
  },
  {
    title: "User Accounts",
    free: "Single User",
    basic: "Up to 5 Users",
    professional: "Up to 50 Users",
    expert: "Unlimited Users",
  },
  {
    title: "Task Management",
    free: "Basic Tasks",
    basic: "Priority Tasks",
    professional: "Advanced Task Automation",
    expert: "Full Workflow Automation",
  },
  {
    title: "Collaboration Tools",
    free: "Chat Support",
    basic: "Team Chat",
    professional: "Team Chat + File Sharing",
    expert: "Full Collaboration Suite",
  },
  {
    title: "Analytics Reports",
    free: "Basic Activity Stats",
    basic: "Monthly Reports",
    professional: "Weekly Insights",
    expert: "Real-time Advanced Analytics",
  },
  {
    title: "API Access",
    free: "Limited Access",
    basic: "Basic APIs",
    professional: "Full API Access",
    expert: "Full API + Custom Integrations",
  },
  {
    title: "Custom Branding",
    free: "Not Available",
    basic: "Basic Branding",
    professional: "Full Branding",
    expert: "Full Branding + White Label",
  },
  {
    title: "Customer Support",
    free: "Community Support",
    basic: "Email Support",
    professional: "Email + Live Chat Support",
    expert: "Priority Support",
  }
];
