import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, FreeMode } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/free-mode';
import StoryCard from './StoryCard';
import { stories } from './stories';
import { useTranslation } from 'react-i18next';
import { useGetApiQuery } from '@/store/api/master/commonSlice';
const StoriesCarousel = () => {
  const { t } = useTranslation();

  const { data, isLoading, isFetching } = useGetApiQuery('open/user-stories/landing');

  if (isLoading || isFetching) {
    return <div>Loading...</div>;
  }
console.log(data);
  return (
    <div className='w-full pt-20 pb-24 px-6 bg-white'>
      <div
          // ref={headerRef}
          className={`
            text-center mb-12 transform transition-all duration-1000
            `}
            // ${headerInView ? 'translate-y-0 opacity-100' : 'translate-y-20 opacity-0'}
        >
          <div className="flex items-center justify-center mb-2">
            <div className="w-8 h-[1px] bg-[#4338CA]"></div>
            <h2 className="text-[#4338CA] text-2xl font-semibold mx-2">
              { data?.title || t('promotional.user_stories')}
            </h2>
          </div>
          <h3 className="text-3xl font-medium text-gray-900">

          { data?.short_description || t('promotional.creators_talking') } 
          
          </h3>
        </div>
      <Swiper
        modules={[Autoplay, FreeMode]}
        spaceBetween={30}
        slidesPerView={5}
        loop={true}
        freeMode={true}
        speed={5000}
        autoplay={{
          delay: 0,
          disableOnInteraction: false,
        }}
        breakpoints={{
          320: { slidesPerView: 1 },
          640: { slidesPerView: 2 },
          1024: { slidesPerView: 3.8 },
        }}
        style={styles.swiper}
      >
        {data?.active_testimonials.map((story) => (
          <SwiperSlide key={story.id}>
            <StoryCard story={story} />
          </SwiperSlide>
        ))}
      </Swiper>

      {/* <Swiper
        modules={[Autoplay, FreeMode]}
        spaceBetween={30}
        slidesPerView={5}
        loop={true}
        freeMode={true}
        speed={5000}
        autoplay={{
          delay: 0,
          disableOnInteraction: false,
          reverseDirection: true
        }}
        breakpoints={{
          320: { slidesPerView: 1 },
          640: { slidesPerView: 2 },
          1024: { slidesPerView: 4 },
        }}
        style={styles.swiper}
      >
        {stories.map((story) => (
          <SwiperSlide key={story.id}>
            <StoryCard story={story} />
          </SwiperSlide>
        ))}
      </Swiper> */}
    </div>
  );
};

const styles = {
  container: {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '20px'
  },
  swiper: {
    padding: '20px 0'
  }
};

export default StoriesCarousel;