import { motion } from "framer-motion";
import bannerImg from "../../../../assets/images/all-img/resource.jpg";

export default function PageHeader() {
  return (
    <div className="relative overflow-hidden">
      <div
        className="absolute inset-0 z-0"
        style={{
          backgroundImage: `url(${bannerImg})`, // Correct usage of backgroundImage
          backgroundPosition: "center",
          backgroundSize: "cover",
          filter: "brightness(0.5)",
        }}
      />
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/70 via-black/70 to-indigo-600/50 z-1" />
      <div className="relative z-10 py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <p className="text-xl text-white">Coaching</p>
            <h1 className="text-3xl tracking-tight text-white sm:text-4xl mb-6">
              The support you need to succeed
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-200 max-w-3xl mx-auto">
              When you build your business with EduPack, we’ll be right by your
              side. Explore all the resources we’ve developed to help you grow
              your business.
            </p>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
