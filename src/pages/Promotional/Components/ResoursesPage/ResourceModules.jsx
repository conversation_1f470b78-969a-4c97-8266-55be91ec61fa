import React, { useState } from "react";
import ReactPlayer from "react-player";
import thumbnail from "../../../../assets/images/all-img/card-4.png";

const commingModules = [
  {
    thumbnail: thumbnail,
    title: "Next Video Title",
    duration: "5:00 min",
  },
  {
    thumbnail: thumbnail,
    title: "Second Next Video Title",
    duration: "4:00 min",
  },
  {
    thumbnail: thumbnail,
    title: "Third Next Video Title",
    duration: "5:00 min",
  },
  {
    thumbnail: thumbnail,
    title: "Fourth Next Video Title",
    duration: "10:00 min",
  },
  {
    thumbnail: thumbnail,
    title: "Fifth Next Video Title",
    duration: "11:00 min",
  },
  {
    thumbnail: thumbnail,
    title: "Sixth Next Video Title",
    duration: "5:00 min",
  },
  {
    thumbnail: thumbnail,
    title: "Seventh Next Video Title",
    duration: "15:00 min",
  },
  {
    thumbnail: thumbnail,
    title: "Next Video Title",
    duration: "14:00 min",
  },
];

const ResourceModules = () => {
  const [seeMore, setSeeMore] = useState(false);
  const [selected, setSelected] = useState([]);

  const categories = [
    { id: 1, title: "Coaching" },
    { id: 2, title: "Individual" },
    { id: 3, title: "Training for Banks" },
    { id: 4, title: "Marketing Training" },
    { id: 5, title: "Health" },
  ];

  const handleSelect = (id) => {
    if (selected.includes(id)) {
      setSelected(selected.filter((item) => item !== id));
    } else {
      setSelected([...selected, id]);
    }
  };

//   console.log(selected)

  return (
    <section className="bg-white py-12 px-5">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center flex-wrap justify-center gap-4">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => handleSelect(category.id)}
              className={`${
                selected.includes(category.id)
                  ? "bg-indigo-400 text-white"
                  : "bg-white text-black"
              } border rounded-lg border-indigo-400 px-4 py-1.5`}
            >
              {category.title}
            </button>
          ))}
        </div>

        <div className="grid grid-cols-12 my-10 gap-5">
          <div className="col-span-12 md:col-span-8">
            <div className="h-[450px] rounded-xl overflow-hidden">
             
              <iframe
              width="100%"
              height="100%"
              src={`https://www.youtube.com/embed/88jH_04zmIw`}
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              className="rounded-lg"
            ></iframe>
            </div>

            {/* overview  */}
            <div className="space-y-4 py-5">
              <h2 className="text-xl font-semibold">Video Title: Overview</h2>
              <p className={`${seeMore ? "block" : "hidden"} md:block`}>
                Lorem ipsum dolor sit amet consectetur adipisicing elit. Facere
                cumque esse id optio exercitationem maxime laboriosam facilis
                odit pariatur veniam?
              </p>

              <div className={`${seeMore ? "block" : "hidden"} md:block`}>
                <p>Timestamps:</p>
                <h3 className="text-lg">Video Intro</h3>
                <span className="flex gap-5">
                  <span className="text-indigo-400">00:00</span>
                  <p>Section 1 - Introduction</p>
                </span>
                <span className="flex gap-5">
                  <span className="text-indigo-400">02:00</span>
                  <p>Section 1 - Introduction</p>
                </span>
                <span className="flex gap-5">
                  <span className="text-indigo-400">03:00</span>
                  <p>Section 1 - Introduction</p>
                </span>
                <span className="flex gap-5">
                  <span className="text-indigo-400">04:00</span>
                  <p>Section 1 - Introduction</p>
                </span>
                <span className="flex gap-5">
                  <span className="text-indigo-400">06:00</span>
                  <p>Section 1 - Introduction</p>
                </span>
              </div>

              <span
                className="text-blue-600 md:hidden"
                onClick={() => setSeeMore(!seeMore)}
              >
                {seeMore ? "See Less" : "See More"}
              </span>
            </div>
          </div>
          <div className="col-span-12 md:col-span-4">
            <div className="border rounded-lg shadow-[6px_10px_0px_0px_#A5B4FC] pb-4">
              <h2 className="text-xl font-semibold p-4">Comming Up Next</h2>
              <div className="space-y-3 max-h-[500px] overflow-y-auto p-4">
                {commingModules.map((module, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-3 group border rounded hover:border-indigo-400 transition-color cursor-pointer duration-300"
                  >
                    <img
                      className="rounded-lg w-[35%]"
                      src={module.thumbnail}
                      alt=""
                    />
                    <div className="py-2">
                      <h3 className="text-lg mb-2 group-hover:text-indigo-600">
                        {module.title}
                      </h3>
                      <span className="border rounded-lg px-3 py-1 group-hover:shadow group-hover:text-indigo-600">
                        {module.duration}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ResourceModules;
