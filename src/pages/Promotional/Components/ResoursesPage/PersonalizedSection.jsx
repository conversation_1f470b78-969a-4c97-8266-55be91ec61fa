import React, { useRef } from "react";
import { useOnScreen } from "../Features";
import image1 from "../../../../assets/images/all-img/image-3.png";
import image2 from "../../../../assets/images/all-img/card-3.png";

const PersonalizedSection = () => {
  const headerRef = useRef(null);
  const headerInView = useOnScreen(headerRef, 0.1);

  return (
    <div className="max-w-7xl mx-auto py-12 px-5">
      <div
        ref={headerRef}
        className={`
            text-center mb-12 transform transition-all duration-1000
            ${
              headerInView
                ? "translate-y-0 opacity-100"
                : "translate-y-20 opacity-0"
            }
          `}
      >
        <div className="flex items-center justify-center mb-2">
          <div className="w-8 h-[1px] bg-[#4338CA]"></div>
          <h2 className="text-[#4338CA] text-2xl font-semibold mx-2">
            Personalized Coaching
          </h2>
        </div>
        <h3 className="text-3xl font-medium text-gray-900">
          Tailored guidance for your success
        </h3>
      </div>

      <div>
        <h2 className="text-xl font-semibold my-3">
          Offer World Class Personalized Coaching
        </h2>
        <p>
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Suscipit
          illum ullam cumque placeat voluptatem quia commodi, ratione impedit
          amet possimus accusantium laboriosam assumenda delectus. Nostrum optio
          assumenda officia dolores blanditiis doloribus consequatur quasi animi
          quisquam, sint at unde dicta enim, ad labore deleniti eaque quo vero
          et voluptas ratione. Dolores autem repellat error tenetur blanditiis,{" "}
          <br /> expedita at animi asperiores ducimus natus ipsum tempore sequi
          dolorum, debitis rem quis distinctio officia qui beatae nostrum
          reprehenderit neque ad enim? Nostrum quia cumque cupiditate quaerat
          quis reprehenderit blanditiis vel, in quo. Nulla quibusdam velit
          dolorem odio quos doloribus laudantium eaque necessitatibus dolores
          quae.
        </p>
      </div>

      <div className="my-5">
        <h2 className="text-xl font-semibold my-3">
          Offer World Class Personalized Coaching
        </h2>
        <img src={image1} className="mx-auto my-4" alt="" />
        <p>
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Suscipit
          illum ullam cumque placeat voluptatem quia commodi, ratione impedit
          amet possimus accusantium laboriosam assumenda delectus. Nostrum optio
          assumenda officia dolores blanditiis doloribus consequatur quasi animi
          quisquam, sint at unde dicta enim, ad labore deleniti eaque quo vero
          et voluptas ratione. Dolores autem repellat error tenetur blanditiis,{" "}
          <br /> expedita at animi asperiores ducimus natus ipsum tempore sequi
          dolorum, debitis rem quis distinctio officia qui beatae nostrum
          reprehenderit neque ad enim? Nostrum quia cumque cupiditate quaerat
          quis reprehenderit blanditiis vel, in quo. Nulla quibusdam velit
          dolorem odio quos doloribus laudantium eaque necessitatibus dolores
          quae.
        </p>
      </div>

      <div className="my-5">
        <h2 className="text-xl font-semibold my-3">
          Offer World Class Personalized Coaching
        </h2>
        <div className=" lg:flex gap-5 items-center">
          <img src={image2} className="mx-auto my-4" alt="" />
          <p>
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Suscipit
            illum ullam cumque placeat voluptatem quia commodi, ratione impedit
            amet possimus accusantium laboriosam assumenda delectus. Nostrum
            optio assumenda officia dolores blanditiis doloribus consequatur
            quasi animi quisquam, sint at unde dicta enim, ad labore deleniti
            eaque quo vero et voluptas ratione. Dolores autem repellat error
            tenetur blanditiis, <br /> expedita at animi asperiores ducimus
            natus ipsum tempore sequi dolorum, debitis rem quis distinctio
            officia qui beatae nostrum reprehenderit neque ad enim? Nostrum quia
            cumque cupiditate quaerat quis reprehenderit blanditiis vel, in quo.
            Nulla quibusdam velit dolorem odio quos doloribus laudantium eaque
            necessitatibus dolores quae.
          </p>
        </div>
      </div>
    </div>
  );
};

export default PersonalizedSection;
