import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import demo from "../../../assets/images/promotion/pi1.png";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";

const Learning = () => {
  const [isVisible, setIsVisible] = useState(false);
  const { isAuth } = useSelector((state) => state.auth);
  const { t } = useTranslation();


  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <section className="py-16 md:py-20 px-6 bg-[#EEF2FF] overflow-hidden">
      <div className="max-w-7xl mx-auto flex flex-col lg:flex-row items-center gap-12 py-8 md:py-12">
        {/* Left Content */}
        <div
          className={`
            lg:w-1/2 space-y-8
            transition-all duration-1000 transform
            ${isVisible ? 'translate-x-0 opacity-100' : '-translate-x-full opacity-0'}
          `}
        >
          <h1 className="text-3xl md:text-4xl font-bold leading-tight bg-gradient-to-r from-indigo-500 to-purple-600 text-transparent bg-clip-text">
            {t('promotional.empower_learning')}
          </h1>

          <p className="text-base md:text-lg text-[#4B5563] leading-relaxed max-w-xl">
            {t('promotional.edupack_description')}
          </p>

          {/* Feature List */}
          <div className="space-y-3">
            <div className="flex items-center mb-3 gap-3">
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="flex-shrink-0"
              >
                <mask
                  id="mask0_362_302"
                  maskUnits="userSpaceOnUse"
                  x="0"
                  y="0"
                  width="16"
                  height="16"
                >
                  <rect width="16" height="16" fill="#D9D9D9" />
                </mask>
                <g mask="url(#mask0_362_302)">
                  <path
                    d="M4.46686 12L0.700195 8.23334L1.6502 7.3L5.41686 11.0667L4.46686 12ZM8.23353 12L4.46686 8.23334L5.4002 7.28334L8.23353 10.1167L14.3669 3.98334L15.3002 4.93334L8.23353 12ZM8.23353 8.23334L7.28353 7.3L10.5835 4L11.5335 4.93334L8.23353 8.23334Z"
                    fill="#4C1D95"
                  />
                </g>
              </svg>
              <p className="text-[#4B5563] text-base">
                {t('promotional.design_courses')}
              </p>
            </div>

            <div className="flex items-center mb-3 gap-3">
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="flex-shrink-0"
              >
                <mask
                  id="mask0_362_302"
                  maskUnits="userSpaceOnUse"
                  x="0"
                  y="0"
                  width="16"
                  height="16"
                >
                  <rect width="16" height="16" fill="#D9D9D9" />
                </mask>
                <g mask="url(#mask0_362_302)">
                  <path
                    d="M4.46686 12L0.700195 8.23334L1.6502 7.3L5.41686 11.0667L4.46686 12ZM8.23353 12L4.46686 8.23334L5.4002 7.28334L8.23353 10.1167L14.3669 3.98334L15.3002 4.93334L8.23353 12ZM8.23353 8.23334L7.28353 7.3L10.5835 4L11.5335 4.93334L8.23353 8.23334Z"
                    fill="#4C1D95"
                  />
                </g>
              </svg>
              <p className="text-[#4B5563] text-base">
                {t('promotional.monitor_performance')}
              </p>
            </div>
            <div className="flex items-center mb-3 gap-3">
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="flex-shrink-0"
              >
                <mask
                  id="mask0_362_302"
                  maskUnits="userSpaceOnUse"
                  x="0"
                  y="0"
                  width="16"
                  height="16"
                >
                  <rect width="16" height="16" fill="#D9D9D9" />
                </mask>
                <g mask="url(#mask0_362_302)">
                  <path
                    d="M4.46686 12L0.700195 8.23334L1.6502 7.3L5.41686 11.0667L4.46686 12ZM8.23353 12L4.46686 8.23334L5.4002 7.28334L8.23353 10.1167L14.3669 3.98334L15.3002 4.93334L8.23353 12ZM8.23353 8.23334L7.28353 7.3L10.5835 4L11.5335 4.93334L8.23353 8.23334Z"
                    fill="#4C1D95"
                  />
                </g>
              </svg>
              <p className="text-[#4B5563] text-base">
                {t('promotional.connect_tools')}
              </p>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 mt-6">
            {!isAuth && <Link
              to="/try-new-lms"
              className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-medium px-8 py-3 rounded-md hover:opacity-90 transition-opacity text-center"
            >
              {t('layout.start_free_trial')}
            </Link>}
            <button className="border-2 border-[#A78BFA] text-[#4C1D95] font-medium px-8 py-3 rounded-md transition-all duration-300 hover:text-white hover:bg-[#4C1D95] hover:border-[#4C1D95]">
              {t('promotional.see_demo')}
            </button>
          </div>
        </div>

        {/* Right Image */}
        <div
          className={`
            lg:w-1/2
            transition-all duration-1000 transform
            ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
          `}
        >
          <img
            src={demo}
            alt="Student learning online"
            className="w-full h-auto"
          />
        </div>
      </div>
    </section>
  );
};

export default Learning;