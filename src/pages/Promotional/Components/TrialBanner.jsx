import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";

const TrialBanner = () => {
  const [showModal, setShowModal] = useState(false);
  const { t } = useTranslation();

  // Prevent scrolling when modal is open
  useEffect(() => {
    if (showModal) {
      document.body.style.overflow = "hidden"; // Disable scrolling
    } else {
      document.body.style.overflow = ""; // Enable scrolling
    }
    return () => (document.body.style.overflow = ""); // Cleanup on unmount
  }, [showModal]);

  return (
    <div className="bg-gradient-to-t from-[#0E0023] to-[#0F0C68] flex items-center justify-center px-4 py-20">
      <div className="max-w-7xl mx-auto text-center">
        <h1 className="text-3xl sm:text-5xl md:text-3xl font-medium text-white mb-3 leading-tight">
          {t('promotional.start_monetizing')}
        </h1>
        <p className="text-base sm:text-lg text-[#94A3B8] mb-8 mx-auto">
          {t('promotional.users_rely')}
        </p>

        <div className="flex flex-wrap justify-center gap-8 sm:gap-12 mb-12">
          <div className="flex items-center gap-2 text-white">
            <Icon icon="ph:gear-fine-duotone" className="w-6 h-6" />
            <span className="text-sm sm:text-base">{t('promotional.create_anything')}</span>
          </div>
          <div className="flex items-center gap-2 text-white">
            <Icon icon="ph:coin-vertical-duotone" className="w-6 h-6" />
            <span className="text-sm sm:text-base">{t('promotional.free_to_start')}</span>
          </div>
          <div className="flex items-center gap-2 text-white">
            <Icon icon="ph:credit-card-duotone" className="w-6 h-6" />
            <span className="text-sm sm:text-base">{t('promotional.easy_payments')}</span>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Link
            to={"/try-new-lms"}
            className="px-8 py-3 bg-white text-[#0F0628] rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors"
          >
            {t('layout.start_free_trial')}
          </Link>
          <button
            onClick={() => setShowModal(true)}
            className="px-8 py-3 bg-transparent border border-white text-white rounded-lg font-semibold text-lg hover:bg-white/5 transition-colors"
          >
            {t('promotional.see_demo')}
          </button>
        </div>
      </div>

      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-800/40 backdrop-blur-sm" onClick={() => setShowModal(false)}>
          <div className="relative bg-white rounded-lg shadow-lg w-[90%] max-w-3xl">
            <button
              onClick={() => setShowModal(false)}
              className="absolute -top-2 -right-2 p-1 bg-gray-200 rounded-full text-gray-700 hover:bg-gray-300"
            >
              <Icon icon="bitcoin-icons:cross-filled" className="text-xl" />
            </button>
            <iframe
              width="100%"
              height="400"
              src={`https://www.youtube.com/embed/0vGRHgvjPqM?si=NSRzfWxzoZEpnYq1`}
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              className="rounded-lg"
            ></iframe>
          </div>
        </div>
      )}
    </div>
  );
};

export default TrialBanner;





// {showModal && (
//   <Modal
//     activeModal={showModal}
//     onClose={() => setShowModal(false)}
//     className="max-w-2xl "
//   >
//     <div className="flex items-center p-5 h-96 w-96 md:min-w-[600px] z-[999] fixed  left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 ">
//       <span
//         onClick={() => setShowModal(false)}
//         className="bg-gray-100 rounded-full cursor-pointer p-1 absolute top-4 right-4 text-center"
//       >
//         <Icon
//           className="text-red-400 text-2xl"
//           icon="bitcoin-icons:cross-filled"
//           width="24"
//           height="24"
//         />
//       </span>
//       <iframe
//         width="100%"
//         height="100%"
//         src={`https://www.youtube.com/embed/88jH_04zmIw`}
//         frameBorder="0"
//         allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
//         allowFullScreen
//         className="rounded-lg"
//       ></iframe>
//     </div>
//   </Modal>
// )}