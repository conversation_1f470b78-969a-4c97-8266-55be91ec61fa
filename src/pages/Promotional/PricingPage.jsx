import React from "react";
import { useTranslation } from 'react-i18next';

export default function PricingPage( { PriceData, billingCycle }) {

  const { t, i18n } = useTranslation();
  const isBengali = i18n?.language === 'bn';

  console.log(billingCycle);
  const currencyMap = {
    bn: { price: "price", salePrice: "sale_price", symbol: "৳" },
    en: { price: "dollar_price", salePrice: "dollar_sale_price", symbol: "$" },
    jp: { price: "yen_price", salePrice: "yen_sale_price", symbol: "¥" },
    kr: { price: "krw_price", salePrice: "krw_sale_price", symbol: "₩" },
  };

  const selectedLang = i18n?.language || "en";
  const currency = currencyMap[selectedLang] || currencyMap["en"];

    const plans = [
      {
        name: t("pricing.basic"),
        price: "৳ 1490.00",
        storage: t("pricing.app_storage_5gb"),
        users: t("pricing.users_200"),
        yearlySupport: t("pricing.yearly_support_2"),
        appUpdate: t("pricing.app_update_6month"),
      },
      {
        name: t("pricing.standard"),
        price: "৳ 3490.00",
        storage: t("pricing.app_storage_15gb"),
        users: t("pricing.users_500"),
        yearlySupport: t("pricing.yearly_support_5"),
        appUpdate: t("pricing.app_update_1year"),
      },
      {
        name: t("pricing.premium"),
        price: "৳ 4990.00",
        storage: t("pricing.app_storage_30gb"),
        users: t("pricing.users_1000"),
        yearlySupport: t("pricing.yearly_support_10"),
        appUpdate: t("pricing.app_update_1year"),
      },
    ];

    const featureKeys = [
      "pricing.feature.white_labeling",
      "pricing.feature.course_management",
      "pricing.feature.batch_management",
      "pricing.feature.recorded_content",
      "pricing.feature.progress_tracking",
      "pricing.feature.assignment_management",
      "pricing.feature.class_management",
      "pricing.feature.attendance_management",
      "pricing.feature.exam_management",
      "pricing.feature.payment_management",
      "pricing.feature.teacher_management",
      "pricing.feature.assigning_teachers",
      "pricing.feature.announcement",
      "pricing.feature.notification",
      "pricing.feature.assessment",
      "pricing.feature.device_log",
      "pricing.feature.profile",
      "pricing.feature.chatbot",
    ];
    const videoStorage = [
        { storage: t("pricing.five_gb"), traffic: t("pricing.unlimited"), monthlyFee: t("pricing.fee_990"), yearlyFee: t("pricing.fee_10690") },
        { storage: t("pricing.fifteen_gb"), traffic: t("pricing.unlimited"), monthlyFee: t("pricing.fee_1490"), yearlyFee: t("pricing.fee_16090") },
        { storage: t("pricing.thirty_gb"), traffic: t("pricing.unlimited"), monthlyFee: t("pricing.fee_1990"), yearlyFee: t("pricing.fee_21490") },
        { storage: t("pricing.custom_enterprise"), traffic: t("pricing.unlimited"), monthlyFee: t("pricing.contact_us"), yearlyFee: t("pricing.contact_us") },
      ];

    return (
      <div className="container mx-auto py-6">
      <div className="flex items-center justify-center mb-6">
        <div className="w-8 h-[1px] bg-[#4338CA]"></div>
        <h2 className="text-[#4338CA] text-2xl font-semibold mx-2">
        {t("pricing.packages_details")}
        </h2>
      </div>
        <div className="overflow-x-auto">
          <table className="w-full border border-gray-300">
            <thead>
              <tr className="bg-gray-100">
                <th className="px-4 py-2 border">{t("pricing.feature")}</th>
                {PriceData?.map((plan, index) => (
                  <th key={index} className="px-4 py-2 border text-center">{plan.plan_name} (  { currencyMap[selectedLang].symbol }
                   { billingCycle === 'monthly' ? plan[currency.salePrice] :
                   (plan[currency.salePrice] * 12 * 0.90).toFixed(2)
                   })
                   </th>
                ))}
              </tr>
            </thead>
            <tbody>
              <tr className="bg-gray-50">
                <td className="px-4 py-2 border">{t("pricing.app_storage")}</td>
                {plans.map((plan, index) => (
                  <td key={index} className="px-4 py-2 border text-center">{plan.storage}</td>
                ))}
              </tr>
              <tr>
                <td className="px-4 py-2 border">{t("pricing.users")}</td>
                {plans.map((plan, index) => (
                  <td key={index} className="px-4 py-2 border text-center">{plan.users}</td>
                ))}
              </tr>
              {featureKeys.map((featureKey, index) => (
                <tr key={index} className="bg-gray-{index % 2 === 0 ? '50' : '100'}">
                  <td className="px-4 py-2 border">{t(featureKey)}</td>
                  {plans.map((_, i) => (
                    <td key={i} className="px-4 py-2 border text-center">✔</td>
                  ))}
                </tr>
              ))}
              <tr>
                <td className="px-4 py-2 border">{t("pricing.yearly_support")}</td>
                {plans.map((plan, index) => (
                  <td key={index} className="px-4 py-2 border text-center">{plan.yearlySupport}</td>
                ))}
              </tr>
              <tr className="bg-gray-50">
                <td className="px-4 py-2 border">{t("pricing.app_update")}</td>
                {plans.map((plan, index) => (
                  <td key={index} className="px-4 py-2 border text-center">{plan.appUpdate}</td>
                ))}
              </tr>
              <tr className="bg-gray-50">
                <td className="px-4 py-2 border">{t("pricing.custom_domain")}</td>
                {plans.map((plan, index) => (
                  <td key={index} className="px-4 py-2 border text-center">{t("pricing.one_time_charge")}</td>
                ))}
              </tr>
            </tbody>
          </table>
        </div>


        <div className="flex items-center justify-center mt-8">
            <div className="flex items-center">
                <div className="w-8 h-[1px] bg-[#4338CA]"></div>
                <h2 className="text-[#4338CA] text-2xl font-semibold mx-2">{t("pricing.video_storage_plans")}</h2>
            </div>
      </div>
        <p className="text-gray-600  text-sm text-center">
        {t("pricing.dynamic_storage_info")}
        </p>
      <table className="w-full border border-gray-300 mt-4">
        <thead>
          <tr className="bg-gray-100">
            <th className="px-4 py-2 border">{t("pricing.video_storage")}</th>
            <th className="px-4 py-2 border">{t("pricing.traffic")}</th>
            <th className="px-4 py-2 border">{t("pricing.monthly_fee")}</th>
            <th className="px-4 py-2 border">{t("pricing.yearly_fee")}</th>
          </tr>
        </thead>
        <tbody>
          {videoStorage.map((plan, index) => (
            <tr key={index} className="bg-gray-{index % 2 === 0 ? '50' : '100'}">
              <td className="px-4 py-2 border text-center">{plan.storage}</td>
              <td className="px-4 py-2 border text-center">{plan.traffic}</td>
              <td className="px-4 py-2 border text-center">{plan.monthlyFee}</td>
              <td className="px-4 py-2 border text-center">{plan.yearlyFee}</td>
            </tr>
          ))}
        </tbody>
      </table>
      </div>
    );
  }