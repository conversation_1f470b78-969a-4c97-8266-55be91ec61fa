import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Create from "./create";
import Edit from "./edit";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";
// import { useParams } from "react-router-dom";

const index = () => {
  const [showModal, setShowModal] = useState(false);
  const dispatch = useDispatch();
  // const { id } = useParams();
  // const res = useGetApiQuery('admin/student-mapping-list');
  const [apiParam, setApiParam] = useState('');

  // const res = useGetMenuListQuery(apiParam);
  const res = useGetApiQuery(`admin/student-mapping-list${apiParam}`);
  const changePage = (val) => {
    setApiParam(val);
  };
  const data = res.data;
  const columns = [
    {
      label: "SL",
      field: "serial",
    },
    {
      label: "Coruse Title",
      field: "course_title",
    },
    {
      label: "Mentor",
      field: "mentor_name",
    },

    {
      label: "Student",
      field: "student_name",
    },
    {
      label: "Status",
      field: "status",
    },

    {
      label: "Action",
      field: "",
    },
  ];

  const tableData = data?.data?.map((item, index) => {
    return {
      serial: index + 1,
      course_title: item.course_title,
      mentor_name: item.mentor_name,
      student_name: item.student_name,
      status: <Badge className={item.is_active ? `bg-success-500 text-white` : `bg-danger-500 text-white` }> {item.is_active ? 'Active' : 'Inactive'} </Badge> 
    };
  });

  const actions = [
    {
      name: "Delete",
      icon: "uil:eye",
      onClick: (val) => {
        console.log(val);
      },
    },
  ];

  const createPage = <Create />;
  const editPage = <Edit />;

  return (
    <div>
      
        <BasicTablePage
          title="Course Teacher Student Mapping"
          createButton="Map New Teacher Student"
          createPage={createPage}
          editPage={editPage}
          actions={actions}
          columns={columns}
          data={tableData}
          changePage={changePage}
          currentPage={data?.current_page}
          totalPages={Math.ceil(data?.total / data?.per_page)}
        />
    
    </div>
  );
};

export default index;
