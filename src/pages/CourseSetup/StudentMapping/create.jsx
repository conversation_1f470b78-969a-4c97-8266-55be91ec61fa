import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Textarea from "@/components/ui/Textarea";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { Formik, Form, Field } from "formik";
import { initialValues, validationSchema } from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import { useGetApiQuery, usePostApiMutation } from "@/store/api/master/commonSlice";
import Select from "@/components/ui/Select";

const Create = () => {
  const [postApi, { isLoading, isError, error, isSuccess }] = usePostApiMutation();
  const dispatch = useDispatch();
  const [courseId, setCourseId] = useState(0);

  const courseList = useGetApiQuery(`admin/course-list-for-mapping`)?.data;
  const mentorList = useGetApiQuery(`admin/course-mentor-assign-list/${courseId}`)?.data;
  const studentList = useGetApiQuery(`admin/student-Participant-list-by-course-id/${courseId}`)?.data;
  console.log(studentList?.student_list);
  const [isActive, setIsActive] = useState(false);
  const { showModal } = useSelector((state) => state.commonReducer);

  const onSubmit = async (values, { resetForm }) => {
    let list = [];
    values.is_active = isActive;
    list.push(values);
    
    let obj = JSON.stringify(list);
    const response = await postApi({end_point: "admin/student-mapping-save-or-update", body: {mapping: obj}});
    dispatch(setShowModal(false));
  };
  return (
    <Modal
      activeModal={showModal}
      onClose={() => dispatch(setShowModal(false))}
      title="Add New Feature"
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setShowModal(false))}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={initialValues}
        onSubmit={onSubmit}
      >
        {({
          values,
          errors,
          setFieldValue,
        }) => (
          <Form>
            <>
              <div className="grid md:grid-cols-1 gap-4">
              
             
                  <Select
                    defaultValue=""
                    label="Course"
                    placeholder="Select Course"
                    options={courseList?.map((item) => {
                      return { label: item.title, value: item.id };
                    })}
                    name="course_id"
                    onChange={(e) => {
                      setFieldValue("course_id", e.target.value);
                      setCourseId(e.target.value);
                    }}
                  />
                  <Select
                    defaultValue=""
                    label="Mentor"
                    placeholder="Select Mentor"
                    options={mentorList?.map((item) => {
                      return { label: item.mentor_name, value: item.mentor_id };
                    })}
                    name="mentor_id"
                    onChange={(e) => {
                      setFieldValue("mentor_id", e.target.value);
                    }}
                  />
                
                
                  <Select
                      defaultValue=""
                      label="Student"
                      placeholder="Select Student"
                      options={studentList?.student_list?.map((item) => {
                        return { label: item.name + " - " + item.email, value: item.id };
                      })}
                      name="student_id"
                      onChange={(e) => {
                        setFieldValue("student_id", e.target.value);
                      }}
                  />
                
                <div className="mt-4 pt-4">
                <Switch
                    label="Active"
                    activeClass="bg-success-500"
                    value={isActive}
                    name="is_note"
                    onChange={() => setIsActive(!isActive)}
                  />
                </div>
              </div>

            
              
            </>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default Create;
