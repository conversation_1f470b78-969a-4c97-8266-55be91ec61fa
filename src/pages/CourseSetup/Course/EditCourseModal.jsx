import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Button from "@/components/ui/Button";
import { Formik, Form, Field } from "formik";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal } from "@/features/commonSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";

const EditCourseModal = () => {
  const dispatch = useDispatch();
  const { showEditModal, editData } = useSelector(
    (state) => state.commonReducer
  );
  const [postApi, { isLoading }] = usePostApiMutation();

  const onSubmit = async (values) => {
    const response = await postApi({
      end_point: "admin/course-save-or-update",
      body: values,
    });
    dispatch(setEditShowModal(false));
  };

  return (
    <Modal
      activeModal={showEditModal}
      onClose={() => dispatch(setEditShowModal(false))}
      title="Edit Course"
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setEditShowModal(false))}
        />
      }
    >
      <Formik initialValues={editData} onSubmit={onSubmit}>
        {({ values, handleChange, handleSubmit }) => (
          <Form>
            <div className="mb-4">
              <InputField
                label="Course Title"
                name="title"
                type="text"
                placeholder="Course Title"
                value={values.title}
                onChange={handleChange}
              />
            </div>
            <div className="mb-4">
              <InputField
                label="Course Description"
                name="description"
                type="text"
                placeholder="Course Description"
                value={values.description}
                onChange={handleChange}
              />
            </div>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default EditCourseModal;
