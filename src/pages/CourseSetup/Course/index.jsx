import React, { useState, useEffect, useRef } from "react";
import Course from "./Course";
import CourseListView from "./CourseListView";
import Textinput from "@/components/ui/Textinput";
import Button from "@/components/ui/Button";
import Select from "@/components/ui/Select";
import Pagination from "@/components/partials/common-table/pagination";
import { useNavigate } from "react-router-dom";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import { useGetMenuListQuery } from "@/store/api/master/menuSlice";
import Loading from "@/components/Loading";
const Index = () => {
  const [showCreateCourse, setShowCreateCourse] = useState(false);
  const [params, setParams] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [viewMode, setViewMode] = useState("grid");
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();

  const { data: menuData } = useGetMenuListQuery("pagination=false");
  const { data: res, isLoading, isError } = useGetApiQuery("admin/course-list" + params);
  const data = res;

  const [filterState, setFilterState] = useState({
    categoryId: "",
    subCategoryId: "",
    typeId: "",
    statusId: "",
    search: ""
  });
  const [subMenuList, setSubMenuList] = useState([]);

  const filterRef = useRef(null);

  const toggleDropdown = () => setIsOpen(!isOpen);

  const handlePageChange = (value) => {
    setCurrentPage(value);
    setParams(`?page=${value}`);
  };

  const debounce = (fn, delay) => {
    let timer;
    return (...args) => {
      if (timer) clearTimeout(timer);
      timer = setTimeout(() => fn(...args), delay);
    };
  };

  const handleSearch = debounce((e) => {
    setFilterState((prev) => ({ ...prev, search: e.target.value }));
  }, 500);

  useEffect(() => {
    if (filterState.categoryId) {
      const selectedCat = menuData?.find((item) => item.id == filterState.categoryId);
      setSubMenuList(selectedCat?.sub_categories || []);
    } else {
      setSubMenuList([]);
    }
  }, [filterState.categoryId, menuData]);

  const handleCreateClick = () => {
    navigate("/course-create");
    setShowCreateCourse(true);
  };

  const buildQueryParams = () => {
    const params = new URLSearchParams();
    if (filterState.search) params.append("search", filterState.search);
    if (filterState.categoryId) params.append("category_id", filterState.categoryId);
    if (filterState.subCategoryId) params.append("sub_category_id", filterState.subCategoryId);
    if (filterState.typeId) params.append("is_free", filterState.typeId);
    if (filterState.statusId) params.append("is_active", filterState.statusId);
    return params.toString() ? `?${params.toString()}` : "";
  };

  const applyFilters = () => {
    const query = buildQueryParams();
    setParams(query);
    setIsOpen(false);
  };

  const resetFilters = () => {
    setFilterState({
      categoryId: "",
      subCategoryId: "",
      typeId: "",
      statusId: "",
      search: ""
    });
    setSubMenuList([]);
    setParams("");
    setIsOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (e) => {
      if (filterRef.current && !filterRef.current.contains(e.target)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden min-h-[460px]">
        <div className="flex justify-between items-center p-4">
          <h1 className="text-3xl font-bold">Course List</h1>
          <div className="flex gap-2">
            <div className="flex items-center bg-slate-200 dark:bg-slate-800">
              <Button
                icon="heroicons-outline:view-grid"
                className={`${viewMode === "grid" ? "bg-slate-900 dark:bg-slate-700 text-white" : "bg-transparent text-gray-500 dark:text-slate-300"} h-8 w-8 flex items-center justify-center transition-all duration-200`}
                iconClass="text-lg"
                onClick={() => setViewMode("grid")}
              />
              <Button
                icon="heroicons:list-bullet"
                className={`${viewMode === "list" ? "bg-slate-900 dark:bg-slate-700 text-white" : "bg-transparent text-gray-500 dark:text-slate-300"} h-8 w-8 flex items-center justify-center transition-all duration-200`}
                iconClass="text-lg"
                onClick={() => setViewMode("list")}
              />
            </div>
            <Textinput
              value={filterState.search}
              onChange={handleSearch}
              placeholder="search..."
            />
            <div className="relative" ref={filterRef}>
              <Button
                onClick={toggleDropdown}
                className="btn btn-sm flex items-center bg-gray-100 border rounded-md shadow-sm hover:bg-gray-200"
              >
                <span>Filter</span>
              </Button>
              {isOpen && (
                <div className="absolute right-0 mt-2 w-fit bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-10">
                  <div className="flex justify-between mb-2">
                  <h3 className="text-lg font-medium ">Filter</h3>
                  <button
                        onClick={() =>
                          setFilterState((prev) => ({
                            ...prev,
                            categoryId: "",
                            subCategoryId: "",
                            typeId: "",
                            statusId: ""
                          }))
                        }
                        className="text-sm py-1 px-2 font-medium text-gray-600 bg-gray-200 rounded-md hover:bg-gray-100"
                      >
                        Clear
                      </button>
                  </div>
                  <div className="mb-3 py-2 border-t border-b border-gray-200">
                    <div className="flex justify-between gap-4">
                      <label className="text-sm font-medium">Category</label>
                      
                    </div>
                    <div className="flex gap-4 py-2">
                      <Select
                        className="w-60"
                        defaultValue=""
                        placeholder="Select Menu"
                        options={menuData?.map((item) => ({
                          label: item.name,
                          value: item.id
                        }))}
                        value={filterState.categoryId}
                        name="category_id"
                        onChange={(e) =>
                          setFilterState((prev) => ({
                            ...prev,
                            categoryId: e.target.value,
                            subCategoryId: ""
                          }))
                        }
                      />
                      <Select
                        className="w-60"
                        placeholder="Select Sub Menu"
                        options={subMenuList?.map((item) => ({
                          label: item.name,
                          value: item.id
                        }))}
                        value={filterState.subCategoryId}
                        name="sub_category_id"
                        onChange={(e) =>
                          setFilterState((prev) => ({
                            ...prev,
                            subCategoryId: e.target.value
                          }))
                        }
                      />
                    </div>
                  </div>
                  <div className="mb-3">
                    <label className="block text-sm font-medium mb-2">Type</label>
                    <Select
                      className="w-full"
                      placeholder="Select Type"
                      options={[
                        { label: "Free", value: "1" },
                        { label: "Paid", value: "0" }
                      ]}
                      value={filterState.typeId}
                      name="type_id"
                      onChange={(e) =>
                        setFilterState((prev) => ({ ...prev, typeId: e.target.value }))
                      }
                    />
                  </div>
                  <div className="mb-3">
                    <label className="block text-sm font-medium mb-2">Status</label>
                    <Select
                      className="w-full"
                      placeholder="Select Status"
                      options={[
                        { label: "Active", value: "1" },
                        { label: "Inactive", value: "0" }
                      ]}
                      value={filterState.statusId}
                      name="is_active"
                      onChange={(e) =>
                        setFilterState((prev) => ({ ...prev, statusId: e.target.value }))
                      }
                    />
                  </div>
                  <div className="flex justify-between mt-4">
                  <button
                      onClick={resetFilters}
                      className="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 bg-gray-200 rounded-md"
                    >
                      Reset all
                    </button>
                    <button
                      onClick={applyFilters}
                      className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                    >
                      Apply now
                    </button>
                  </div>
                </div>
              )}
            </div>
            <button
              onClick={handleCreateClick}
              className="btn text-center btn-primary btn-sm"
            >
              Create New Course
            </button>
          </div>
        </div>
        <hr className="my-4 mx-4" />
        {isLoading ? 
          <Loading />
        :
        <div>
        {!data?.data?.length ? (
          <div className="p-4 text-center text-gray-600 text-sm">No course found</div>
        ) : viewMode === "grid" ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-5 gap-4 p-4">
            {data?.data.map((course) => (
              <Course key={course.id} course={course} />
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-4 p-4">
            {data?.data.map((course) => (
              <CourseListView key={course.id} course={course} />
            ))}
          </div>
        )}
        {data?.data?.length ? (
          <div className="p-4 flex justify-end">
            <Pagination
              totalPages={Math.ceil(data?.total / data?.per_page)}
              currentPage={currentPage}
              handlePageChange={handlePageChange}
            />
          </div>
        ) : null}
        </div> 
        }
      </div>
    </div>
  );
};

export default Index;
