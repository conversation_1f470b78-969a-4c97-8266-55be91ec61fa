import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import {
  usePostApiMutation,
  useGetApiQuery,
} from '@/store/api/master/commonSlice';
import { useGetMenuListQuery } from '@/store/api/master/menuSlice';
import Loading from '@/components/Loading';
import CourseHeader from './CourseDetailsComponents/CourseHeader';
import CourseStats from './CourseDetailsComponents/CourseStats';
import CourseDescription from './CourseDetailsComponents/CourseDescription';
import CourseMedia from './CourseDetailsComponents/CourseMedia';
import CourseTabs from './CourseDetailsComponents/CourseTabs';
import CourseEditForm from './CourseDetailsComponents/CourseEditForm';
import { validationSchema } from './formCourse';
import CourseCategory from "./CourseCategory";
import Feature from "./Feature";
import FAQ from "./FAQ";
import MentorAssign from "./MentorAssign";
import Batch from "./Batch/Index";
import Students from "./Students";
import LearningItems from './LearningItems';
import ExternalLibrary from './ExternalLibrary';

const CourseDetails = () => {
  const [postApi, { isLoading }] = usePostApiMutation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams();

  const [isEditing, setIsEditing] = useState(false);
  const [isFree, setIsFree] = useState(false);
  const [activeTab, setActiveTab] = useState('category');
  const [subMenuList, setSubMenuList] = useState([]);

  const course = useGetApiQuery('admin/course-details/' + id)?.data;
  const menuList = useGetMenuListQuery('pagination=false&course_category=1')?.data;

  useEffect(() => {
    if (course) {
      setIsFree(course?.is_free);
      setSubMenuList(
        menuList?.find((item) => item.id == course?.category_id)?.sub_categories
      );
    }
  }, [course, menuList]);

  if (!course) {
    return <Loading />;
  }

  const tabs = [
    {
      id: 'category',
      label: 'Outline',
      icon: 'material-symbols:category',
      count: course?.module_count,
      countLabel: 'Modules',
      component: (
        <CourseCategory
          data={course?.course_category}
          subjects={course?.subjects}
          course={course}
        />
      ),
    },
    {
      id: 'batch',
      label: 'Batch',
      icon: 'clarity:group-solid',
      count: course?.batch_number,
      countLabel: 'Batches',
      component: (
        <Batch
          data={course?.course_category}
          subjects={course?.subjects}
          course={course}
        />
      ),
    },
    {
      id: 'mentor',
      label: 'Mentor',
      icon: 'ph:student-bold',
      count: course?.course_mentor?.length,
      countLabel: 'Mentors',
      component: <MentorAssign course={course} />,
    },
    {
      id: 'students',
      label: 'Students',
      icon: 'heroicons-outline:user-group',
      count: course?.number_of_enrolled || 0,
      countLabel: 'Students',
      component: <Students course={course} />,
    },
    {
      id: 'features',
      label: 'Features',
      icon: 'solar:star-bold',
      count: course?.course_feature?.length,
      countLabel: 'Features',
      component: <Feature data={course?.course_feature} />,
    },
    {
      id: 'learning_items',
      label: 'Learning Items',
      icon: 'carbon:machine-learning-model',
      count: course?.course_learning_items?.length,
      countLabel: 'Learning Items',
      component: <LearningItems data={course?.course_learning_items} />,
    },
    {
      id: 'faq',
      label: 'FAQ',
      icon: 'material-symbols:quiz',
      count: course?.course_faq?.length,
      countLabel: 'FAQ',
      component: <FAQ data={course?.course_faq} />,
    },
    {
      id: 'external_library',
      label: 'External Library',
      icon: 'material-symbols:quiz',
      count: course?.external_library_number,
      countLabel: 'External Library',
      component: <ExternalLibrary course={course} />,
    },
  ];

  const formatDateToSql = (date) => {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };


  const handleSubmit = async (values, { resetForm }) => {
    let formData = new FormData();
    formData.append('id', course?.id);
    formData.append('title', values.title);
    formData.append('category_id', values.category_id || '');
    formData.append('sub_category_id', values.sub_category_id || '');
    formData.append('course_type_id', values.course_type_id || '');
    formData.append('regular_price', values.regular_price || '0');
    formData.append('sale_price', values.sale_price || '0');
    formData.append('discount_percentage', values.discount_percentage || '0');
    formData.append('currency', values.currency || 'BDT');
    formData.append('is_free', isFree ? 1 : 0);
    formData.append('is_featured', course.is_featured ? 1 : 0);
    formData.append('is_active', course.is_active ? 1 : 0);
    formData.append('course_type_id', 1);

    formData.append('appeared_from', formatDateToSql(values.appeared_from));
    formData.append('appeared_to', formatDateToSql(values.appeared_to));

    formData.append('installment_type', values.installment_type || '0');
    formData.append('monthly_amount', values.monthly_amount || '0');
    formData.append('minimum_enroll_amount', values.minimum_enroll_amount || '0');
    formData.append('max_installment_qty', values.max_installment_qty || '0');


    const response = await postApi({
      end_point: 'admin/course-save-or-update',
      body: formData,
    });

    if (response?.data) {
      resetForm();
      setIsEditing(false);
    }
  };

  return (
    <>
      <div className="mt-4">
        <section className="space-y-6 bg-white rounded-lg shadow-md">
          <CourseHeader
            course={course}
            postApi={postApi}
            onEdit={() => setIsEditing(true)}
            isLoading={isLoading}
          />

          <div className="grid grid-cols-1 lg:grid-cols-3 pb-2 gap-4">
            <div className="px-2 border-r-0 lg:border-r-2 lg:col-span-2">
              <CourseStats course={course} />
              <CourseDescription description={course.description} course={course} />
            </div>
            <CourseMedia course={course} />
          </div>
        </section>
      </div>

      <div className="grid grid-cols-12 gap-6 mt-5">
        <div className="md:col-span-3 col-span-12 bg-white rounded-lg shadow-lg p-4 space-y-3">
          <CourseTabs
            tabs={tabs}
            activeTab={activeTab}
            onTabClick={setActiveTab}
          />
        </div>
        <div className="md:col-span-9 col-span-12 bg-white rounded-lg shadow-lg">
          {tabs.find((tab) => tab.id === activeTab)?.component}
        </div>
      </div>

      {isEditing && (
        <CourseEditForm
          course={course}
          menuList={menuList}
          subMenuList={subMenuList}
          isFree={isFree}
          setIsFree={setIsFree}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          handleBackClick={() => setIsEditing(false)}
          setSubMenuList={setSubMenuList}
          isLoading={isLoading}
        />
      )}
    </>
  );
};

export default CourseDetails;