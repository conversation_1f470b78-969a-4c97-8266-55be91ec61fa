
import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Fileinput from "@/components/ui/Fileinput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { Formik, Form, Field } from 'formik';
import { initialValues, validationSchema} from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal } from "@/features/commonSlice";
import { useMenuCreateOrUpdateMutation } from "@/store/api/master/menuSlice";
const Edit = () => {

    const [menuCreateOrUpdate, { isLoading, isError, error, isSuccess }] = useMenuCreateOrUpdateMutation();
    const dispatch = useDispatch()
    const { showEditModal } = useSelector((state) => state.commonReducer);
    const { editData } = useSelector((state) => state.commonReducer);
    const [checkedAuthentication, setIsAuthentication] = useState(editData.is_authentication_needed);
    const [checkedContnent, setIsContnent] = useState(editData.is_course);

    const onSubmit = async (values, { resetForm }) => {
        values.is_authentication_needed = checkedAuthentication;
        values.is_content = checkedContnent;
        let formData = new FormData();
        formData.append("id", values.id);
        formData.append("name", values.name);
        formData.append("link", values.link);
        formData.append("sequence", 1);
        formData.append("is_authentication_needed", values.is_authentication_needed ? 1 : 0);
        // formData.append("has_submenu", values.has_submenu ? 1 : 0);
        formData.append("is_course", values.is_content ? 0 : 1);
        formData.append("is_content", values.is_content ? 1 : 0);
        formData.append("icon", values.icon);
        // formData.append("is_active", values.is_active ? 1 : 0);

        const response = await menuCreateOrUpdate(formData);
        dispatch(setEditShowModal(false));
    }
    return (

    <Modal
    activeModal={showEditModal}
    onClose={() => dispatch(setEditShowModal(false))}
    title="Update Course"
    className="max-w-5xl"
    footer={
        <Button
            text="Close"
            btnClass="btn-primary"
            onClick={() => dispatch(setEditShowModal(false))}
        />
        }
    >        
    <Formik 
    validationSchema={validationSchema}
    initialValues={editData}
    onSubmit={onSubmit}>
    {({ values,
        errors,
        touched,
        handleChange,
        handleBlur,
        handleSubmit,
        setFieldValue,
        isSubmitting, }) => (
        <Form>
        <div>
            <div className="mb-4">
                <InputField
                label="Menu Name"
                name="name"
                type="text"
                placeholder="Menu Name"
                />
            </div>
            
            <div className="mb-4">
                <InputField
                label="Link"
                name="link"
                type="text"
                placeholder="http://example.com or /example"
                />
            </div>
      
            <div className="mb-4">
                <label className="block text-[#1D1D1F] text-base font-medium mb-2">Icon</label>
                <Fileinput
                name="icon"
                accept="image/*"
                type="file"
                placeholder="Icon"
                preview={true}
                selectedFile={values.icon}
                onChange={(e) => {
                setFieldValue("icon", e.currentTarget.files[0]);
                    
                }}
                />
            </div>
            <div className="mb-4">
                <div className="grid grid-4-md gap-4">
                <Switch
                  label="Authentication"
                  activeClass="bg-success-500"
                  value={checkedAuthentication}
                  name="is_authentication_needed"
                  onChange={() => setIsAuthentication(!checkedAuthentication)}
                />
                <Switch
                  label="Course"
                  activeClass="bg-success-500"
                  value={checkedContnent}
                  name="is_content"
                  onChange={() => setIsContnent(!checkedContnent)}
                />
                </div>
            </div>
            <div className="mb-4">
            </div>
            </div>
            <div className="ltr:text-right rtl:text-left mt-5">
                <Button
                    isLoading={isLoading}
                    type="submit"
                    className="btn text-center btn-primary"
                >
                    Submit
                </Button>
                </div>
          
          </Form>
        )}
        </Formik>
        </Modal>
    );
}

export default Edit;
