import React, { useState, useEffect } from "react";
import Button from "@/components/ui/Button";
import Create from "./create";
import Delete from "./Delete";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
const index = ({ course }) => {
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectItemToDelete, setSelectItemToDelete] = useState(null);
  const { data } = useGetApiQuery('admin/course-external-libraries-list?course_id='+course?.id) || [];

  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const dispatch = useDispatch();
  const { id } = useParams();

  useEffect(() => {
    navigator.mediaDevices.ondevicechange = async () => {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const screenRecording = devices.some(device =>
        device.kind === 'videoinput' && device.label.toLowerCase().includes('screen')
      );
  
      if (screenRecording) {
        document.body.classList.add('blackout-sensitive');
      } else {
        document.body.classList.remove('blackout-sensitive');
      }
    };
  }, []);

  return (
    <div className="flex flex-col p-5">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-semibold">External Library</h2>
        <Button
          onClick={() => setIsSidebarOpen(true)}
          className="btn-primary btn-sm"
        >
          Add New Library
        </Button>
      </div>
      <div className="flex">
        {!data?.length && (
          <p className="text-sm text-red-500">No External Libray has been added</p>
        )}
        <ol className="list-decimal grid gap-2 px-4">
          {data?.map((item) => (
            <li className="py-2 flex justify-between items-center" key={item.id}>
              <div>
                <p className="flex-1">{item.title}</p>
                <p className="mt-1">
                  <a href={item.url} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-500 underline">
                    {item.url}
                  </a>
                </p>
              </div>
              <Button
                onClick={() => {
                  setSelectItemToDelete(item);
                  setShowDeleteModal(true);
                }}
                className="btn-danger btn-sm"
              >
                Delete
              </Button>
            </li>
          ))}
        </ol>
      </div>
      <Create
        isSidebarOpen={isSidebarOpen}
        setIsSidebarOpen={setIsSidebarOpen}
      />
      {showDeleteModal && <Delete showDeleteModal={showDeleteModal} setShowDeleteModal={setShowDeleteModal} data={selectItemToDelete} />}
    </div>
  );
};

export default index;
