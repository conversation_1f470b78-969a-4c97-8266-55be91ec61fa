import React from "react";
import avatar from "@/assets/images/avatar/av-1.svg";

const SelectionModal = ({
  title,
  items,
  selectedItems,
  onChange,
  onSelectAll,
  onClose,
  onSubmit,
}) => {
//   console.log(selectedItems, "selected data");
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white rounded-lg w-1/2 md:w-2/2 lg:w-1/2 shadow-lg">
        <div className="border-b p-4">
        <h3 className="text-xl font-semibold ">{title}</h3>
        </div>
        <div className="mb-4 px-4 py-2 flex items-center space-x-2">
          <input
            type="checkbox"
            checked={selectedItems.length === items.length && items.length > 0}
            onChange={onSelectAll}
            className="form-checkbox h-4 w-4 text-blue-600"
          />
          <span className="text-sm font-medium text-gray-700">Select All</span>
        </div>
        <div className="max-h-60 overflow-y-auto space-y-2 grid md:grid-cols-2 gap-4 px-4 ">
          {items.map((item) => {
            const isSelected = selectedItems.includes(item.value);
            return (
              <div
                key={item.value}
                className={`flex items-center rounded-md border border-gray-200 p-2 cursor-pointer transition-colors ${
                  isSelected ? "bg-blue-50" : "hover:bg-gray-50"
                }`}
                onClick={() => onChange(item.value)}
              >
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={() => onChange(item.value)}
                  className="form-checkbox h-4 w-4 text-blue-600 mr-3"
                  onClick={(e) => e.stopPropagation()}
                />
                <div className="flex items-center space-x-3">
                  {item.image && (
                    <img
                      src={
                        item.image
                          ? import.meta.env.VITE_ASSET_HOST_URL + item.image
                          : avatar
                      }
                      className=" md:block hidden rounded-full w-8 h-8 md:mr-2 md:mb-0 mb-2"
                      alt="avatar"
                    />
                  )}
                  <div className="flex flex-col">
                    <span className="font-medium text-gray-900 text-sm">
                      {item.label}
                    </span>
                    {item.email && (
                      <span className="text-gray-500 text-xs">
                        {item.email}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        <div className="flex justify-end space-x-2 mt-4 p-4">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200 text-sm font-medium"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={onSubmit}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors duration-200 text-sm font-medium"
          >
            Submit
          </button>
        </div>
      </div>
    </div>
  );
};

export default SelectionModal;
