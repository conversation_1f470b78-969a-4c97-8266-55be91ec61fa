// src/components/Batch/Batch.jsx

import React, { useState, useEffect } from "react";
import AddBatch from "./AddBatch";
import EditBatch from "./EditBatch";
import BatchDetails from "./BatchDetails";
import Icons from "@/components/ui/Icon";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import { useNavigate, useParams } from "react-router-dom";
import BatchDelete from "./BatchDelete";

const Batch = ({ data }) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [isAdding, setIsAdding] = useState(false);
  const [selectedBatch, setSelectedBatch] = useState(null);
  const [selectedBatchDelete, setSelectedBatchDelete] = useState(null);
  const [isViewingDetails, setIsViewingDetails] = useState(false);
  const [isEditBatch, setIsEditBatch] = useState(false);

  const batch = useGetApiQuery(`/admin/batches?course_id=${id}`);
  const course = useGetApiQuery(`admin/course-details/${id}`)?.data;
  const Student = useGetApiQuery(`/admin/student-Participant-list-by-course-id/${id}`);
  const Mentors = course?.course_mentor;
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handleAddBatchClick = () => setIsAdding(true);
  const handleFormClose = () => setIsAdding(false);
  const handleEditFormClose = () => setIsEditBatch(false);
  const handleViewDetailsClose = () => setIsViewingDetails(false);
  const handleDeleteModalClose = () => setShowDeleteModal(false);

  const handleEditBatchClick = (batchId) => {
    const batchToEdit = batch?.data?.data?.find(item => item.id === batchId);
    setSelectedBatch(batchToEdit);
    setIsEditBatch(true); // Open the form in edit mode
  };

  const handleViewBatchDetails = (batchId) => {
    const batchToView = batch?.data?.data?.find(item => item.id === batchId);
    setSelectedBatch(batchToView);
    setIsViewingDetails(true);
  };

  useEffect(() => {
    if (!isAdding && !isEditBatch && !isViewingDetails) {
      setSelectedBatch(null); // Reset selected batch when closing forms
    }
  }, [isAdding, isEditBatch, isViewingDetails]);

  const handleBatchDelete = (batchId) => {
    const batchToDelete = batch?.data?.data?.find(item => item.id === batchId);
    setSelectedBatchDelete(batchToDelete);
    setShowDeleteModal(true);
  };

  return (
    <>
      <div className="rounded-lg pb-6 pt-3">
        {!isAdding && !isViewingDetails && !isEditBatch && (
          <div className="border-b-2">
            <div className="flex justify-between p-3">
              <h1 className="text-3xl font-semibold text-gray-900">Batch</h1>
              <button
                onClick={handleAddBatchClick}
                className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg shadow-lg hover:bg-green-700 transition-colors duration-200 space-x-2"
              >
                <span>Add Batch</span>
              </button>
            </div>
          </div>
        )}

        <div className="p-3">
          {isAdding ? (
            <AddBatch
              onClose={handleFormClose}
              Mentors={Mentors}
              Student={Student}
            />
          ) : isEditBatch ? (
            <EditBatch
              onClose={handleEditFormClose}
              Mentors={Mentors}
              Student={Student}
              selectedBatch={selectedBatch}
            />
          ) : isViewingDetails ? (
            <BatchDetails
              onClose={handleViewDetailsClose}
              batch={selectedBatch}
            />
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {batch?.data?.data?.length > 0 ? (
                batch.data.data.map((item) => (
                  <div
                    key={item.id}
                    onClick={() => navigate(`/batch-details/${item.id}`)}
                    className="group bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:shadow-lg overflow-hidden cursor-pointer transition-transform duration-300 hover:scale-[1.02]"
                  >
                    <div className="relative w-full aspect-video">
                      {item.image ? (
                      <img
                      src={`${import.meta.env.VITE_ASSET_HOST_URL}${item.image}`}
                      alt={item.name}
                      className="w-full h-full object-cover"
                    />
                      ): (
                        <div
                          className="w-full h-full flex items-center justify-center bg-gray-200 text-lg text-gray-800"
                        >
                          {item.name}
                        </div>
                      )}
                      <div className="absolute inset-0 bg-gray-600 opacity-0 group-hover:opacity-40 transition-opacity duration-300" />
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditBatchClick(item.id); // Open the form with data for editing
                        }}
                        className="absolute top-2 right-2 p-2 bg-white dark:bg-gray-700 rounded-full text-gray-600 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-300 opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0"
                      >
                        <Icons icon="line-md:edit-twotone" width="24" height="24" className="text-blue-500" />
                      </button>
                      <button
                        onClick={(e) => {
                          handleBatchDelete(item.id);
                          e.stopPropagation();
                        }}
                        className="absolute top-14 right-2 p-2 bg-white dark:bg-gray-700 rounded-full text-gray-600 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-300 opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0"
                      >
                        <Icons icon="material-symbols:delete-outline" width="24" height="24" className="text-red-500" />
                      </button>
                    </div>
                    <div className="p-4">
                      <h6 className="text-lg font-semibold text-gray-900 dark:text-white overflow-hidden text-ellipsis whitespace-nowrap mb-2">
                        {item.name}
                      </h6>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-900 dark:text-white font-medium">
                          Capacity: {item.capacity}
                        </span>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="col-span-full text-center py-8 text-gray-600 dark:text-gray-400">
                  No batches available
                </div>
              )}
            </div>
          )}
        </div>
        {
          <BatchDelete
            data={selectedBatchDelete}
            setShowDeleteModal={setShowDeleteModal}
            showDeleteModal={showDeleteModal}
            onClose={handleDeleteModalClose}
          />
        }
      </div>
    </>
  );
};

export default Batch;
