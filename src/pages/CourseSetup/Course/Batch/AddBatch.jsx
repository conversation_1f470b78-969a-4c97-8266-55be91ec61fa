import React, { useState } from "react";
import { Formik, Form } from "formik";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { useParams } from "react-router-dom";
import InputField from "@/components/ui/InputField";
import Textarea from "@/components/ui/Textarea";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import Fileinput from "@/components/ui/Fileinput";
import * as yup from "yup";
import SelectionModal from "./SelectionModal";

const AddBatch = ({ onClose, Mentors, Student }) => {
  const { id } = useParams();
  const [postApi] = usePostApiMutation();
  const Students = Student?.data?.student_list || [];

  const createUrl = "admin/batches";

  const validationSchema = yup.object({
    name: yup.string().required("Batch name is required"),
    capacity: yup
      .number()
      .required("Capacity is required")
      .min(1, "Capacity must be at least 1")
      .typeError("Capacity must be a number"),
  });

  const createBatch = async (formData) => {
    try {
      await postApi({ end_point: createUrl, body: formData });
    } catch (error) {
      console.error("Error creating batch:", error);
    }
  };

  const onSubmit = async (values, { resetForm }) => {
    const mentorIds = values.mentor_ids || [];
    const studentIds = values.student_ids || [];
    const formData = new FormData();
    formData.append("course_id", id);
    formData.append("name", values.name);
    formData.append("description", values.description || "");
    formData.append("capacity", values.capacity);
    if (values.image && typeof values.image !== "string") {
      formData.append("image", values.image);
    }
    formData.append("mentor_ids", JSON.stringify(mentorIds));
    formData.append("student_ids", JSON.stringify(studentIds));
    await createBatch(formData);
    resetForm();
    if (onClose) onClose();
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-2xl mb-4">Add New Batch</h2>
      <Formik
        initialValues={{
          course_id: id || "",
          name: "",
          description: "",
          capacity: "",
          image: "",
          student_ids: [],
          mentor_ids: [],
        }}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
      >
        {({ isSubmitting, setFieldValue, values }) => {
          const [isMentorModalOpen, setMentorModalOpen] = useState(false);
          const [tempSelectedMentors, setTempSelectedMentors] = useState(values.mentor_ids || []);
          const handleMentorCheckboxChange = (mentorId) => {
            if (tempSelectedMentors.includes(mentorId)) {
              setTempSelectedMentors(tempSelectedMentors.filter((id) => id !== mentorId));
            } else {
              setTempSelectedMentors([...tempSelectedMentors, mentorId]);
            }
          };
          const handleSelectAllMentors = () => {
            if (tempSelectedMentors.length === Mentors.length) {
              setTempSelectedMentors([]);
            } else {
              const allMentorIds = Mentors.map((mentor) => mentor.mentor_id);
              setTempSelectedMentors(allMentorIds);
            }
          };
          const handleMentorModalSubmit = () => {
            setFieldValue("mentor_ids", tempSelectedMentors);
            setMentorModalOpen(false);
          };

          const [isStudentModalOpen, setStudentModalOpen] = useState(false);
          const [tempSelectedStudents, setTempSelectedStudents] = useState(values.student_ids || []);
          const handleStudentCheckboxChange = (studentId) => {
            if (tempSelectedStudents.includes(studentId)) {
              setTempSelectedStudents(tempSelectedStudents.filter((id) => id !== studentId));
            } else {
              setTempSelectedStudents([...tempSelectedStudents, studentId]);
            }
          };
          const handleSelectAllStudents = () => {
            if (tempSelectedStudents.length === Students.length) {
              setTempSelectedStudents([]);
            } else {
              const allStudentIds = Students.map((student) => student.id);
              setTempSelectedStudents(allStudentIds);
            }
          };
          const handleStudentModalSubmit = () => {
            setFieldValue("student_ids", tempSelectedStudents);
            setStudentModalOpen(false);
          };

          return (
            <>
              <Form>
                <div>
                  <InputField
                    label="Name"
                    name="name"
                    type="text"
                    placeholder="Enter Batch Name"
                    required
                  />
                </div>
                <div className="mt-2">
                  <Textarea
                    label="Description"
                    placeholder="Enter Description"
                    name="description"
                    value={values.description}
                    onChange={(e) => setFieldValue("description", e.target.value)}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4 mt-2">
                  <div>
                    <NumberInput
                      label="Batch Capacity"
                      type="number"
                      name="capacity"
                      id="capacity"
                      placeholder="Batch capacity"
                      required
                    />
                  </div>
                  <div className="">
                    <label className="block capitalize pb-2">Batch Image</label>
                    <Fileinput
                      label="Choose Image"
                      className="py-2"
                      name="image"
                      accept="image/*"
                      type="file"
                      placeholder="Batch Image"
                      preview={true}
                      selectedFile={values.image}
                      onChange={(e) => {
                        setFieldValue("image", e.target.files[0]);
                      }}
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-2 mt-6">
                  {onClose && (
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200"
                    >
                      Cancel
                    </button>
                  )}
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-lg hover:bg-blue-700 transition-colors duration-200"
                  >
                    {isSubmitting ? "Submitting..." : "Submit"}
                  </button>
                </div>
              </Form>

            </>
          );
        }}
      </Formik>
    </div>
  );
};

export default AddBatch;
