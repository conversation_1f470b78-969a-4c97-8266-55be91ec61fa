import React from 'react';
import { Users, UserCheck, GraduationCap, Calendar, BookOpen, Activity, ArrowLeft } from 'lucide-react';
import { useGetApiQuery } from "@/store/api/master/commonSlice";

const BatchDetails = ({ batch, onClose }) => {
  console.log(batch?.id,'Batch Details');
  const batch_id = batch.id;
  const { data: batchDetails, isLoading, error } = useGetApiQuery(`/admin/batches/${batch_id}`, {
    skip: !batch_id, // Ensures the query doesn't run if `batch_id` is not available
  });
  console.log(batchDetails,'new batch details');
  const stats = [
    { label: 'Capacity', value: batch.capacity, icon: Users },
    { label: 'Total Mentors', value: batch.total_mentors, icon: UserCheck },
    { label: 'Total Students', value: batch.total_students, icon: GraduationCap },
  ];

  const imageSrc = batch.image
    ? `${import.meta.env.VITE_ASSET_HOST_URL}${batch.image}`
    : '/placeholder-image.png'; // Provide a fallback image URL

  return (
    <div className=" mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl">
      <div className="relative group">
        
      {batch.image ? (
        <img
          src={`${import.meta.env.VITE_ASSET_HOST_URL}${batch.image}`}
          alt={batch.name || 'Batch Image'}
          className="w-full h-72 object-cover transition-transform duration-300 group-hover:scale-105"
        />
      ): (
        <div
          className="w-full h-72 flex items-center justify-center bg-gray-200 text-lg text-gray-800"
        >
          {batch.name}
        </div>
        )}


        <div className="absolute inset-0 bg-gradient-to-t from-gray-500/60 to-transparent" />
        <div className="absolute bottom-4 left-4 right-4">
          <h1 className="text-3xl font-bold text-white mb-2">{batch.name}</h1>
          <div className="flex items-center space-x-2 text-white/80">
            <Calendar className="w-4 h-4" />
            <span className="text-sm">
              Created: {new Date(batch.created_at).toLocaleDateString()}
            </span>
          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg transition-all duration-300 hover:shadow-md hover:scale-105"
            >
              <stat.icon className="w-8 h-8 text-indigo-500 dark:text-indigo-400" />
              <div className="ml-4">
                <p className="text-sm text-gray-500 dark:text-gray-400">{stat.label}</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{stat.value}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="space-y-6">
          <div className='p-2 shadow-md rounded-md'>
              <p>{batch.description}</p>
          </div>
          <div className="flex items-center space-x-3 text-gray-700 dark:text-gray-300">
            <BookOpen className="w-5 h-5 text-indigo-500" />
            <span className="font-medium">Course ID:</span>
            <span>{batch.course_id}</span>
          </div>

          <div className="flex items-center space-x-3 text-gray-700 dark:text-gray-300">
            <Activity className="w-5 h-5 text-indigo-500" />
            <span className="font-medium">Status:</span>
            <span className={`px-3 py-1 rounded-full text-sm ${
              batch.is_active
                ? 'bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-400'
                : 'bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-400'
            }`}>
              {batch.is_active ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>

        {onClose && (
          <button
            onClick={onClose}
            className="mt-8 flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Go Back</span>
          </button>
        )}
      </div>
    </div>
  );
};

export default BatchDetails;