import React, { useState } from "react";
import Icon from "@/components/ui/Icon";
import InputField from "@/components/ui/InputField";
import { Formik, Form } from "formik";
import { initialValues, validationSchema } from "./formCourse";
import { useDispatch, useSelector } from "react-redux";
// import { setShowModal } from "@/features/commonSlice";
import SimpleBar from "simplebar-react";
import Fileinput from "@/components/ui/Fileinput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import Textarea from "@/components/ui/Textarea";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import Select from "@/components/ui/Select";
import { useGetMenuListQuery } from "@/store/api/master/menuSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { useNavigate } from "react-router-dom";

const Sidebar = () => {
  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const menuList = useGetMenuListQuery("pagination=false")?.data;

  const [isFree, setIsFree] = useState(true);
  const [isActive, setIsActive] = useState(false);
  const [hasLifeCoach, setHasLifeCoach] = useState(false);

  // State for storing the title
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [regularPrice, setRegularPrice] = useState("");
  const [salePrice, setSalePrice] = useState("");

  const [uploadedThumbnailUrl, setUploadedThumbnailUrl] = useState("");
  const [discountPercentage, setDiscountPercentage] = useState("");

  //Truncate Text function
  const truncateText = (text, maxLength) => {
    return text.length > maxLength
      ? `${text.substring(0, maxLength)}...`
      : text;
  };

  const onSubmit = async (values, { resetForm }) => {
    let formData = new FormData();
    formData.append("title", values.title);

    formData.append("category_id", values.category_id || "");
    formData.append("course_type_id", values.course_type_id || "");

    formData.append("description", values.description);
    formData.append("thumbnail", values.thumbnail);

    formData.append("regular_price", values.regular_price || "0");
    formData.append("sale_price", values.sale_price || "0");
    formData.append("discount_percentage", values.discount_percentage || "0");

    formData.append("is_free", isFree ? 0 : 1);

    formData.append("is_active", 0);
    formData.append("course_type_id", 1);

    const response = await postApi({
      end_point: "admin/course-save-or-update",
      body: formData,
    });

    if (response?.data) {
      // dispatch(setShowModal(false));
      resetForm();
      navigate("/course-list");
    }
  };
  //image upload function
  // const handleImageChange = (e, setFieldValue) => {
  //   const file = e.currentTarget.files[0];
  //   if (file) {
  //     const fileUrl = URL.createObjectURL(file);
  //     setUploadedImageUrl(fileUrl);
  //     setFieldValue("icon", file);
  //   }
  // };

  const handleThumbnailChange = (e, setFieldValue) => {
    const file = e.currentTarget.files[0];
    if (file) {
      const fileUrl = URL.createObjectURL(file);
      setUploadedThumbnailUrl(fileUrl);
      setFieldValue("thumbnail", file);
    }
  };

  const handleBackClick = () => {
    navigate("/course-list");
  };

  return (
    <>
      <div
        className={`fixed right-0 top-0 w-[450px] bg-white dark:bg-slate-800 h-screen z-[9999] shadow-base2 border border-slate-200 dark:border-slate-700 transition-all duration-150`}
      >
        <SimpleBar className="px-6 h-full">
          <header className="flex items-center justify-between border-b border-slate-100 dark:border-slate-700 -mx-6 px-6 py-[25px] mb-">
            <div>
              <span className="block text-xl text-slate-900 font-medium dark:text-[#eee]">
                Add New Course
              </span>
            </div>
            <div
              className="cursor-pointer text-2xl text-slate-800 dark:text-slate-200"
              onClick={handleBackClick}
            >
              <Icon icon="heroicons-outline:x" />
            </div>
          </header>

          <Formik
            validationSchema={validationSchema}
            initialValues={initialValues}
            onSubmit={onSubmit}
          >
            {({ values, errors, setFieldValue }) => (
              <Form>
                <div className="grid md:grid-cols-1 gap-2">
                  <InputField
                    label="Title"
                    name="title"
                    type="text"
                    placeholder="Enter Title"
                    required
                    onChange={(e) => {
                      setFieldValue("title", e.target.value);
                      setTitle(e.target.value);
                    }}
                  />
                </div>

                <div className="grid md:grid-cols-2 gap-2 my-2">
                  <Select
                    label="Select Menu"
                    defaultValue=""
                    placeholder="Select Menu"
                    options={menuList?.map((item) => ({
                      label: item.name,
                      value: item.id,
                    }))}
                    name="category_id"
                    onChange={(e) =>
                      setFieldValue("category_id", e.target.value)
                    }
                    error={errors.category_id}
                  />
                  <Select
                    label="Select Sub Menu"
                    defaultValue=""
                    placeholder="Select Menu"
                  />

                  <div className="my-auto">
                    <Switch
                      label="Free"
                      activeClass="bg-success-500 my-5"
                      value={isFree}
                      name="is_free"
                      onChange={() => setIsFree(!isFree)}
                    />
                  </div>

                  {!isFree && (
                    <InputField
                      label="Regular Price"
                      name="regular_price"
                      type="text"
                      placeholder="Enter Regular Price"
                      // required
                      onChange={(e) => {
                        setFieldValue("regular_price", e.target.value);
                        setRegularPrice(e.target.value);
                      }}
                    />
                  )}
                  {!isFree && (
                    <InputField
                      label="Sale Price"
                      name="sale_price"
                      type="text"
                      placeholder="Enter Sale Price"
                      // required
                      onChange={(e) => {
                        setFieldValue("sale_price", e.target.value);
                        setSalePrice(e.target.value);
                      }}
                    />
                  )}
                  {!isFree && (
                    <InputField
                      label="Discount %"
                      name="discount_percentage"
                      type="text"
                      placeholder="Enter Discount"
                      onChange={(e) => {
                        setFieldValue("discount_percentage", e.target.value);
                        setDiscountPercentage(e.target.value);
                      }}
                    />
                  )}
                </div>

                <div className="grid md:grid-cols-1 gap-2 my-2">
                  <div>
                    <label className="block text-[#1D1D1F] text-base font-medium mb-2">
                      Thumbnail
                    </label>
                    <Fileinput
                      name="thumbnail"
                      accept="image/*"
                      type="file"
                      placeholder="Thumbnail"
                      preview={true}
                      selectedFile={values.thumbnail}
                      onChange={(e) => handleThumbnailChange(e, setFieldValue)}
                    />
                  </div>
                </div>

                <div className="grid md:grid-cols-1 gap-2 my-2">
                  <label className="block text-[#1D1D1F] text-base font-medium">
                    Description
                  </label>
                  <Textarea
                    placeholder="Description"
                    name="description"
                    onChange={(e) => {
                      setFieldValue("description", e.target.value);
                      setDescription(e.target.value);
                    }}
                  />
                </div>

                <div className="grid md:grid-cols-2 gap-2 py-5 justify-between"></div>
                <div className="flex justify-between">
                  <div>
                    <Button
                      type="submit"
                      className="btn text-center btn-danger my-5"
                      onClick={handleBackClick}
                    >
                      Back
                    </Button>
                  </div>
                  <div>
                    <Button
                      isLoading={isLoading}
                      type="submit"
                      className="btn text-center btn-primary my-5"
                    >
                      Submit
                    </Button>
                  </div>
                </div>
              </Form>
            )}
          </Formik>
        </SimpleBar>
      </div>
    </>
  );
};

export default Sidebar;
