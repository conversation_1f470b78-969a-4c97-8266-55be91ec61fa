import React, { Fragment, useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Card from "@/components/ui/Card";
import Icon from "@/components/ui/Icon";
import Tooltip from "@/components/ui/Tooltip";
import Button from "@/components/ui/Button";
import Create from "./create";
import CreateModuleOfSubject from "./CreateModuleOfSubject";
import Edit from "./edit";
import Delete from "./Delete";
import { Menu, Transition } from "@headlessui/react";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";
import { useParams } from "react-router-dom";
import Dropdown from "@/components/ui/Dropdown";
import ViewVideo from "../../../RawContent/VideoContentList/viewVideo";
import ViewScript from "../../../RawContent/ScriptList/viewScript";
import CreateScript from "../../../RawContent/ScriptList/createScript";
import CreateQuiz from "../../../RawContent/QuizList/createQuiz";
import ModuleAccordion from "./ModuleAccordion";

import {
  ChevronDown,
  Plus,
  BookOpen,
  Video,
  FileText,
  BrainCircuit,
} from "lucide-react";

import { useNavigate } from "react-router-dom";
import AddSubject from "../CourseSubject/AddSubject";
import CreateVideo from "@/pages/RawContent/VideoContentList/createVideo";
import EditScript from "@/pages/RawContent/ScriptList/editScript";
import EditQuiz from "@/pages/RawContent/QuizList/EditQuiz";
import { FaCheck } from "react-icons/fa";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import EditSubject from "../CourseSubject/EditSubject";
import DeleteSubject from "../CourseSubject/DeleteSubject";
import ImportSubject from "./ImportSubject";

const CourseCategoryPage = ({ data, subjects, course }) => {
  // console.log(subjects, "course data all");
  const navigate = useNavigate();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isSubjectSidebarOpen, setIsSubjectSidebarOpen] = useState(false);
  const [isModuleOfSubjectSidebarOpen, setIsModuleOfSubjectSidebarOpen] =
    useState(false);
  const [showModal, setShowModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [showVideoModal, setShowVideoModal] = useState(false);
  const [showScriptModal, setShowScriptModal] = useState(false);
  const [showEditScriptModal, setShowEditScriptModal] = useState(false);
  const [showQuizModal, setShowQuizModal] = useState(false);
  const [showEditQuizModal, setShowEditQuizModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const [showViewVideoModal, setShowViewVideoModal] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState(false);

  const [showViewScriptModal, setShowViewScriptModal] = useState(false);
  const [selectedScript, setSelectedScript] = useState(false);
  const [selectedSubject, setSelectedSubject] = useState(null);

  const [currentVideo, setCurrentVideo] = useState(null); // State for editing video
  const [currentScript, setCurrentScript] = useState(null); // State for editing script
  const [currentQuiz, setCurrentQuiz] = useState(null); // State for editing quiz
  const [isSubEditing, setIsSubEditing] = useState(false);
  const [isSubjectEditSidebarOpen, setIsSubjectEditOpen] = useState(false);
  const { showEditModal, editData } = useSelector(
    (state) => state.commonReducer
  );

  const { organization } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  const { id } = useParams();

  const actions = [
    {
      name: "Edit",
      icon: "lucide:edit",
      onClick: (val) => {
        dispatch(setEditData(data.data[val]));
        dispatch(setEditShowModal(true));
      },
    },

    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];
  // console.log(setShowEditScriptModal);
  const openSubjectModule = (val) => {
    setSelectedSubject(val);
    setIsModuleOfSubjectSidebarOpen(true);
  };

  const getTextColor = (bgColor) => {
    if (!bgColor) return "#000";

    const hex = bgColor.replace("#", "");
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    return luminance > 0.5 ? "#000" : "#FFF";
  };

  return (
    <section className="bg-gradient-to-br from-gray-50 to-gray-100  rounded-md shadow-lg pb-6 pt-3">
      {/* Header Section */}

      {organization?.host_url == 'https://bacbonschool.edupackbd.com/' &&
      <div className="mb-8 space-y-6 ">
        <div className="flex justify-between items-center border-b-2 px-6 pb-3">
          <div className="space-y-1">
            <h1 className="text-3xl font-bold text-gray-900">Course Outline</h1>
            <p className="text-gray-600">
              Manage your course structure and content
            </p>
            <ImportSubject course={course} />
          </div>

          <button
            onClick={() => setIsSubjectSidebarOpen(true)}
            className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg shadow-lg hover:bg-green-700 transition-colors duration-200 space-x-2"
          >
            <BookOpen className="w-5 h-5" />
            <span>Add Subject</span>
          </button>
        </div>
      </div>}
      <div className="grid grid-cols-1 gap-6 px-6">
        {subjects?.map((subject, index) => (
          <div key={index} className="bg-white rounded-lg border">
            {/* Subject Header */}
            <div
              className="flex items-center justify-between p-4"
              style={{
                backgroundColor: subject?.color_code,
                color: getTextColor(subject?.color_code),
              }}
            >
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  {/* <img
                    src={`${import.meta.env.VITE_ASSET_HOST_URL}${
                      subjecimport createVideo from './../../../RawContent/VideoContentList/createVideo';
                      t?.icon
                    }`}
                    alt="Subject Icon"
                    className="w-12 h-12 object-cover rounded-full shadow-lg border-2 border-white"
                  /> */}
                </div>
                <div>

                  <div className="relative">
                    <p className="text-xl font-bold">{subject?.name}</p>
               
                  </div>

                  <p className="text-sm opacity-80">
                    Explore modules and content
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <button
                  onClick={() => openSubjectModule(subject)}
                  className="flex items-center px-3 py-3 bg-blue-600 text-white text-xs font-medium rounded-full shadow hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>Add Module</span>
                </button>

                <span className="relative">
                  <Menu as="div" className="relative">
                    <Menu.Button
                      className="cursor-pointer"
                      style={{ color: getTextColor(subject?.color_code) }}
                    >
                      <Icon
                        icon="humbleicons:dots-vertical"
                        width="24"
                        height="24"
                      />
                    </Menu.Button>

                    <Transition
                      as={Fragment}
                      enter="transition ease-out duration-100"
                      enterFrom="transform opacity-0 scale-95"
                      enterTo="transform opacity-100 scale-100"
                      leave="transition ease-in duration-75"
                      leaveFrom="transform opacity-100 scale-100"
                      leaveTo="transform opacity-0 scale-95"
                    >
                      <Menu.Items className="absolute right-0 mt-2 w-32 bg-white border border-gray-200 rounded-md shadow-lg">
                        <div className="py-1 text-sm text-gray-700">
                          <Menu.Item>
                            {({ active }) => (
                              <button
                                className={`${
                                  active ? "bg-gray-100" : ""
                                } cursor-pointer px-4 py-2 w-full text-left flex items-center gap-1`}
                                onClick={() => (
                                  dispatch(setEditShowModal(true)),
                                  dispatch(setEditData(subject))
                                )}
                              >
                                <Icon icon="mage:edit" width="16" height="16" />{" "}
                                Edit
                              </button>
                            )}
                          </Menu.Item>
                          <Menu.Item>
                            {({ active }) => (
                              <button
                                className={`${
                                  active ? "bg-gray-100" : ""
                                } cursor-pointer px-4 py-2 w-full text-left flex items-center gap-1`}
                                onClick={() => (
                                  setShowDeleteModal(true),
                                  setDeleteData(subject)
                                )}
                              >
                                <Icon icon="mi:delete" width="16" height="16" />{" "}
                                Delete
                              </button>
                            )}
                          </Menu.Item>
                        </div>
                      </Menu.Items>
                    </Transition>
                  </Menu>
                </span>
              </div>
            </div>

            {/* Subject Content */}
            <div className="bg-gray-50 mt-4">
              {subject?.course_category?.map((module, idx) => (
                <ModuleAccordion
                  setShowVideoModal={setShowVideoModal}
                  setShowScriptModal={setShowScriptModal}
                  setShowEditScriptModal={setShowEditScriptModal}
                  setShowQuizModal={setShowQuizModal}
                  setShowEditQuizModal={setShowEditQuizModal}
                  setSelectedCategory={setSelectedCategory}
                  setSelectedVideo={setSelectedVideo}
                  setShowViewVideoModal={setShowViewVideoModal}
                  setSelectedScript={setSelectedScript}
                  setShowViewScriptModal={setShowViewScriptModal}
                  setCurrentVideo={setCurrentVideo} // Pass the setter
                  setCurrentScript={setCurrentScript} // Pass the setter
                  setCurrentQuiz={setCurrentQuiz} // Pass the setter
                  key={idx}
                  module={module}
                />
              ))}
            </div>
          </div>
        ))}

        {/* Other Modules Section */}
        <div className="bg-white rounded-lg overflow-hidden border mt-6">
          <div className="bg-gray-100 flex items-center justify-between px-4 py-3 shadow-md rounded-md">
            <h1 className="text-3xl font-bold text-gray-900">Modules</h1>
            <button
              onClick={() => setIsSidebarOpen(true)}
              className="flex items-center px-3 py-3 bg-blue-600 text-white text-xs font-medium rounded-full shadow hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>Add Module</span>
            </button>
          </div>

          <div className="bg-gray-50">
            {data?.map((module, idx) => (
              <ModuleAccordion
                setShowVideoModal={setShowVideoModal}
                setShowScriptModal={setShowScriptModal}
                setShowEditScriptModal={setShowEditScriptModal}
                setShowQuizModal={setShowQuizModal}
                setShowEditQuizModal={setShowEditQuizModal}
                setSelectedCategory={setSelectedCategory}
                setSelectedVideo={setSelectedVideo}
                setShowViewVideoModal={setShowViewVideoModal}
                setSelectedScript={setSelectedScript}
                setShowViewScriptModal={setShowViewScriptModal}
                setCurrentVideo={setCurrentVideo} // Pass the setter
                key={idx}
                module={module}
                setCurrentScript={setCurrentScript} // Pass the setter
                setCurrentQuiz={setCurrentQuiz} // Pass the setter
              />
            ))}
          </div>
        </div>
      </div>

      {/* Modals */}
      {isSidebarOpen && (
        <Create
          isSidebarOpen={isSidebarOpen}
          setIsSidebarOpen={setIsSidebarOpen}
        />
      )}
      {isModuleOfSubjectSidebarOpen && (
        <CreateModuleOfSubject
          isSidebarOpen={isModuleOfSubjectSidebarOpen}
          setIsSidebarOpen={setIsModuleOfSubjectSidebarOpen}
          subject={selectedSubject}
        />
      )}
      {isSubjectSidebarOpen && (
        <AddSubject
          isSubjectSidebarOpen={isSubjectSidebarOpen}
          setIsSubjectSidebarOpen={setIsSubjectSidebarOpen}
        />
      )}

      {showEditModal && (
        <EditSubject
          showEditModal={showEditModal}
          setShowEditModal={setEditShowModal}
          editData={editData}
        />
      )}

      {showDeleteModal && (
        <DeleteSubject
          showDeleteModal={showDeleteModal}
          setShowDeleteModal={setShowDeleteModal}
          data={deleteData}
        />
      )}

      {showVideoModal && (
        <CreateVideo
          isSidebarOpen={showVideoModal}
          setIsSidebarOpen={setShowVideoModal}
          category={selectedCategory}
          video={currentVideo} // Pass the current video (if any)
        />
      )}
      {showScriptModal && (
        <CreateScript
          isSidebarOpen={showScriptModal}
          setIsSidebarOpen={setShowScriptModal}
          category={selectedCategory}
        />
      )}
      {showEditScriptModal && (
        <EditScript
          isSidebarOpen={showEditScriptModal}
          setIsSidebarOpen={setShowEditScriptModal}
          category={selectedCategory}
          script={currentScript}
        />
      )}
      {showQuizModal && (
        <CreateQuiz
          isSidebarOpen={showQuizModal}
          setIsSidebarOpen={setShowQuizModal}
          category={selectedCategory}
        />
      )}
      {showEditQuizModal && (
        <EditQuiz
          isSidebarOpen={showEditQuizModal}
          setIsSidebarOpen={setShowEditQuizModal}
          category={selectedCategory}
          quiz={currentQuiz}
        />
      )}

      {showViewVideoModal && (
        <ViewVideo
          showModal={showViewVideoModal}
          setShowModal={setShowViewVideoModal}
          content={selectedVideo}
        />
      )}

      {showViewScriptModal && (
        <ViewScript
          showModal={showViewScriptModal}
          setShowModal={setShowViewScriptModal}
          content={selectedScript}
        />
      )}
    </section>
  );
};

export default CourseCategoryPage;
