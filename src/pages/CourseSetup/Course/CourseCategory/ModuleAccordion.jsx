// ModuleAccordion.jsx

import { useState } from "react";
import Icon from "@/components/ui/Icon";
import moduleIcon from "@/assets/images/svg/module icon.svg";
import clockIcon from "@/assets/images/all-img/clock.png";
import mediaIcon from "@/assets/images/all-img/media.png";
import examIcon from "@/assets/images/all-img/exam.png";
import documentIcon from "@/assets/images/all-img/document.png";
import { Link } from "react-router-dom";
import {
  FaVideo,
  FaFileAlt,
  FaQuestionCircle,
  FaEdit,
  FaCheck,
  FaImage,
  FaFilePdf,
} from "react-icons/fa";
import { GrView } from "react-icons/gr";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import AssesmentDelete from "./AllDelete/AssesmentDelete";
import Delete from "./Delete";

const ModuleAccordion = ({
  module,
  isOpen = false,
  setShowVideoModal,
  setShowScriptModal,
  setShowEditScriptModal,
  setShowQuizModal,
  setSelectedCategory,
  setSelectedVideo,
  setShowViewVideoModal,
  setSelectedScript,
  setShowViewScriptModal,
  setCurrentVideo,
  setCurrentScript,
  setShowEditQuizModal,
  setCurrentQuiz,
}) => {
  const [open, setOpen] = useState(
    isOpen || module?.course_outlines_count === 0
  );
  const [deleteData, setDeleteData] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [editedName, setEditedName] = useState(module?.name || "");
  const [updateApi] = useUpdateApiMutation();

  const handleSaveName = async () => {
    setIsEditing(false);
    let formData = new FormData();
    formData.append("name", editedName);
    formData.append("course_id", module?.course_id);
    formData.append("is_active", module?.is_active ? 1 : 0);
    formData.append("sequence", module?.sequence);

    await updateApi({
      end_point: `admin/course-category/${module.id}`,
      body: formData,
    });
  };

  const handleDelete = (content) => {
    setDeleteData(content);
    setShowDeleteModal(true);
  };

  return (
    <>
      <div className="pb-4">
        <div
          className={`accordion shadow-md rounded-lg border border-gray-300 bg-white`}
        >
          {/* Accordion Header */}
          <div
            onClick={() => setOpen(!open)}
            className="cursor-pointer flex justify-between items-center px-6 py-4 rounded-t-lg bg-slate-100 text-white"
          >
            <div className="flex items-center gap-3">
              <img src={moduleIcon} alt="Module Icon" className="w-8 h-8" />
              {isEditing ? (
                <div className="flex items-center gap-2">
                  <input
                    type="text"
                    value={editedName}
                    onChange={(e) => setEditedName(e.target.value)}
                    className="px-2 py-1 text-base text-gray-900 border border-gray-300 rounded focus:outline-none"
                  />
                  <button
                    onClick={handleSaveName}
                    className="text-green-500 hover:text-green-700"
                  >
                    <FaCheck />
                  </button>
                </div>
              ) : (
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  {module?.name}
                  <button
                    className="text-blue-500 hover:text-blue-700"
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsEditing(true);
                    }}
                  >
                    <FaEdit />
                  </button>

                  <button
                    className="text-blue-500 hover:text-blue-700"
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsDelete(true);
                    }}
                  >
                    <Icon
                      icon="material-symbols:delete-outline-sharp"
                      className="text-red-400"
                      width="22"
                      height="22"
                    />
                  </button>
                </h3>
              )}
            </div>
            <Icon
              icon="heroicons-outline:chevron-down"
              className={`text-xl transition-transform text-gray-600 ${
                open ? "rotate-180" : ""
              }`}
            />
          </div>

{/* Accordion Content */}
<div
  className={`transition-all duration-300 ease-in-out ${
    open ? "opacity-100 " : "max-h-0 opacity-0"
  } overflow-hidden bg-gray-50`}
>
  {/* Module Stats */}
  <div className="flex items-center gap-6 px-6 py-4 text-sm text-gray-700">
    <span className="flex items-center gap-2">
      <img src={documentIcon} alt="Learning Material" className="w-5 h-5" />
      {module?.scripts_number} Learning Material
    </span>
    <span className="flex items-center gap-2">
      <img src={mediaIcon} alt="Videos" className="w-5 h-5" />
      {module?.videos_number} Videos
    </span>
    <span className="flex items-center gap-2">
      <img src={examIcon} alt="Quizzes" className="w-5 h-5" />
      {module?.quizzes_number} Quizzes
    </span>
  </div>

        {/* Content List */}
        <div>
          {module?.course_outlines?.map((content, idx) => (
            <div
              key={idx}
              className="flex justify-between items-center px-6 py-4 border-b last:border-none"
            >
              <div className="flex items-center gap-4">
                {content?.chapter_video_id && content.video && (
                  <div
                    className="flex items-center gap-4 cursor-pointer"
                    onClick={() => {
                      setSelectedVideo(content);
                      setShowViewVideoModal(true);
                    }}
                  >
                    <FaVideo className="text-red-500 text-lg" />
                    <div className="flex flex-col">
                      <p className="text-gray-800">{content?.title}</p>
                      <div className="flex gap-2 mt-1 flex-wrap items-center">
                        {!content?.is_active && (
                          <span className="bg-red-100 text-red-800 text-xs px-2 py-0.5 rounded-full">
                            Inactive
                          </span>
                        )}
                        {content?.is_free && (
                          <span className="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full">
                            Free
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                )}
                {content?.chapter_script_id && (
                  <div
                    className="flex items-center gap-4 cursor-pointer"
                    onClick={() => {
                      setSelectedScript(content);
                      setShowViewScriptModal(true);
                    }}
                  >
                    {content?.script?.raw_url?.toLowerCase().match(/\.(jpeg|jpg|png|gif|webp|svg|bmp)$/) ? (
                      <FaImage className="text-green-500 text-lg" />
                    ) : content?.script?.raw_url?.toLowerCase().match(/\.pdf$/) ? (
                      <FaFilePdf className="text-red-500 text-lg" />
                    ) : (
                      <FaFileAlt className="text-purple-500 text-lg" />
                    )}
                    <div className="flex flex-col">
                      <p className="text-gray-800">{content?.title}</p>
                      <div className="flex gap-2 mt-1 flex-wrap items-center">
                        {!content?.is_active && (
                          <span className="bg-red-100 text-red-800 text-xs px-2 py-0.5 rounded-full">
                            Inactive
                          </span>
                        )}
                        {content?.is_free && (
                          <span className="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full">
                            Free
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                )}
                {content?.chapter_quiz_id && (
                  <Link
                    to={`/quiz-details/${content?.chapter_quiz_id}`}
                    className="flex items-center gap-4 cursor-pointer justify-between"
                  >
                    <div className="flex items-center gap-4">
                      <FaQuestionCircle className="text-orange-500 text-lg" />
                      <div className="flex flex-col">
                        <p className="text-gray-800">{content?.title}</p>
                        <div className="flex gap-2 mt-1 flex-wrap items-center">
                          {!content?.is_active && (
                            <span className="bg-red-100 text-red-800 text-xs px-2 py-0.5 rounded-full">
                              Inactive
                            </span>
                          )}
                          {content?.is_free && (
                            <span className="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full">
                              Free
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <button className="btn-sm btn-link text-blue-500 hover:text-blue-700">
                      Add / View Questions
                    </button>
                  </Link>
                )}
              </div>
              <div className="flex gap-4">
                {/* Edit Video Button */}
                {content?.video && (
                  <div className="flex gap-4">
                    <button
                      className="flex items-center gap-4 cursor-pointer"
                      onClick={() => {
                        setSelectedVideo(content);
                        setShowViewVideoModal(true);
                      }}
                    >
                      <GrView className="text-green-500 text-lg" />
                    </button>
                    <button
                      className="text-blue-500 hover:text-blue-700"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedCategory(module);
                        setCurrentVideo(content.video);
                        setShowVideoModal(true);
                      }}
                    >
                      <FaEdit />
                    </button>
                  </div>
                )}
                {/* Edit Learning Material Button */}
                {content?.script && (
                  <div className="flex gap-4">
                    <button
                      className="flex items-center gap-4 cursor-pointer"
                      onClick={() => {
                        setSelectedScript(content);
                        setShowViewScriptModal(true);
                      }}
                    >
                      <GrView className="text-green-500 text-lg" />
                    </button>
                    <button
                      className="text-blue-500 hover:text-blue-700"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedCategory(module);
                        setCurrentScript(content.script);
                        setShowEditScriptModal(true);
                      }}
                    >
                      <FaEdit />
                    </button>
                  </div>
                )}
                {/* Edit Quiz Button */}
                {content?.quiz && (
                  <div className="flex gap-4">
                    <Link
                      to={`/quiz-details/${content?.chapter_quiz_id}`}
                      className="flex items-center gap-4 cursor-pointer justify-between"
                    >
                      <GrView className="text-green-500 text-lg" />
                    </Link>
                    <button
                      className="text-blue-500 hover:text-blue-700"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedCategory(module);
                        setCurrentQuiz(content.quiz);
                        setShowEditQuizModal(true);
                      }}
                    >
                      <FaEdit />
                    </button>
                  </div>
                )}
                <button
                  className="text-red-500 hover:text-red-700"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleDelete(content);
                  }}
                >
                  <Icon icon="heroicons-outline:trash" />
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-center gap-4 py-4">
          <button
            onClick={() => {
              setSelectedCategory(module);
              setCurrentVideo(null);
              setShowVideoModal(true);
            }}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            <FaVideo className="mr-2" /> Add Video
          </button>
          <button
            onClick={() => {
              setSelectedCategory(module);
              setShowScriptModal(true);
            }}
            className="flex items-center px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            <FaFileAlt className="mr-2" /> Add Learning Material
          </button>
          <button
            onClick={() => {
              setSelectedCategory(module);
              setShowQuizModal(true);
            }}
            className="flex items-center px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
          >
            <FaQuestionCircle className="mr-2" /> Add Quiz
          </button>
        </div>
      </div>

        </div>

        {/* Delete Modals */}
        <AssesmentDelete
          showDeleteModal={showDeleteModal}
          setShowDeleteModal={setShowDeleteModal}
          data={deleteData}
        />

        {isDelete && (
          <Delete setShowDeleteModal={setIsDelete} showDeleteModal={isDelete} data={module} />
        )}
      </div>
    </>
  );
};

export default ModuleAccordion;