
import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { Formik, Form, Field } from 'formik';
import { initialValues, validationSchema} from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal } from "@/features/commonSlice";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import { useParams } from "react-router-dom";

const Edit = () => {

    const [updateApi, { isLoading, isError, error, isSuccess }] = useUpdateApiMutation();
    const dispatch = useDispatch()
    const { showEditModal } = useSelector((state) => state.commonReducer);
    const { editData } = useSelector((state) => state.commonReducer);
    const [isActive, setIsActive] = useState(editData.is_active);
    const { id } = useParams();

    const onSubmit = async (values, { resetForm }) => {
       
        let formData = new FormData();
        formData.append("course_id", id);
        formData.append("name", values.name);
        formData.append("name_bn", values.name_bn);
        formData.append("sequence", values.sequence);
        formData.append("is_active", isActive ? 1 : 0);
        values.id = id;
        values.is_active = isActive;
        const response = await updateApi({end_point: "admin/course-category/" + editData.id, body: values});
        dispatch(setEditShowModal(false));
    }
    return (

    <Modal
    activeModal={showEditModal}
    onClose={() => dispatch(setEditShowModal(false))}
    title="Update Course"
    className="max-w-5xl"
    footer={
        <Button
            text="Close"
            btnClass="btn-primary"
            onClick={() => dispatch(setEditShowModal(false))}
        />
        }
    >        
    <Formik 
    validationSchema={validationSchema}
    initialValues={editData}
    onSubmit={onSubmit}>
    {({ values,
        errors,
        touched,
        handleChange,
        handleBlur,
        handleSubmit,
        setFieldValue,
        isSubmitting, }) => (
        
            <Form>
            <>
              <div className="grid md:grid-cols-1 gap-4">
                <InputField
                  label="Category Name"
                  name="name"
                  type="text"
                  placeholder="Enter Category Name"
                  required
                />
                <InputField
                  label="Bangla Category Name"
                  name="name_bn"
                  type="text"
                  placeholder="Enter Bangla Category Name"
                />
                
                <NumberInput
                label="Sequence"
                name="sequence"
                placeholder="Sequence"
                onChange={(e) => setFieldValue('sequence', e.target.value)}
                />
              </div>

                <div className="mt-4">
                <Switch
                  label="Active"
                  activeClass="bg-success-500"
                  value={isActive}
                  name="is_active"
                  onChange={() => setIsActive(!isActive)}
                />
                </div>
              
            </>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
        </Formik>
        </Modal>
    );
}

export default Edit;
