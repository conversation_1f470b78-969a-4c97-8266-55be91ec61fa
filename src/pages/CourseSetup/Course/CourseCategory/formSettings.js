import React from "react";
import * as yup from "yup";

export const initialValues = { 
    sequence: '',
    name: '',
    name_bn: '',
    is_active: '',
};

export const validationSchema =  yup.object({
    name: yup.string().max(150, "Should not be more than 150 characters").min(3, "Should not be less than 3 characters").required("ModuleName is Required"),
    name_bn: yup.string().max(150, "Should not be more than 150 characters").min(3, "Should not be less than 3 characters")
})
