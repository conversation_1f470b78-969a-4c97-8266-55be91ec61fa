import React from "react";
import * as yup from "yup";

export const initialValues = {
    title: '',
    // title_bn: '',
    category_id: '',
    course_type_id: '',
    // youtube_url: '',
    // gp_product_id: '',
    description: '',
    thumbnail: '',
    // icon: '',
    // number_of_enrolled: '',
    regular_price: '',
    sale_price: '',
    discount_percentage: '',
    currency: 'BDT',
    // rating: '',
    has_life_coach: '',
    sequence: '',
    // appeared_from: '',
    // appeared_to: '',
    is_free: '',
    is_active: '',
    course_duration: 0,
    duration_per_day: 0,
};

export const validationSchema = yup.object({
    title: yup.string().max(250, "Should not be more than 250 characters").min(3, "Should not be less than 3 characters").required("Name is Required"),
    // title_bn: yup.string().max(50, "Should not be more than 50 characters").min(3, "Should not be less than 3 characters"),
    // youtube_url: yup.string().url("Invalid URL"),
    // number_of_enrolled: yup.number().positive("Number should be positive"),
    // regular_price: yup.number().positive("Number should be positive").required("Regular Price is Required"),
    // sale_price: yup.number().positive("Number should be positive").required("Sale Price is Required"),
    // discount_percentage: yup.number().min(0, "Discount Percentage should be positive").max(100, "Discount Percentage should be less than or equal to 100").required("Discount Percentage is Required"),
    currency: yup.string().oneOf(['BDT', 'AED', 'USD', 'EUR', 'GBP', 'S.Fr'], "Invalid currency").required("Currency is Required"),
    // rating: yup.number().positive("Rating should be positive"),
    // sequence: yup.number().positive("Number should be positive"),
    // appeared_from: yup.date().required("Appeared From is Required"),
    // appeared_to: yup.date().required("Appeared To is Required"),
    // description: yup.string().max(100, "Should not be more than 100 characters").min(20, "Should not be less than 90 characters").required("Description is Required"),
    course_duration: yup.number().min(0, "Course duration should be 0 or positive").nullable(),
    duration_per_day: yup.number().min(0, "Duration per day should be 0 or positive").nullable(),
})
