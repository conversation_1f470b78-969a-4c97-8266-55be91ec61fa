import React from "react";
import Card from "@/components/ui/Card";
import { Icon } from "@iconify/react";
import Button from "@/components/ui/Button";

const CertificateTemplateView = ({ course, template, onEdit, onDelete }) => {
  if (!template) {
    return (
      <Card className="p-6">
        <div className="text-center py-8">
          <Icon icon="heroicons:document-text" className="w-16 h-16 text-slate-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-2">
            No Certificate Template
          </h3>
          <p className="text-slate-500 dark:text-slate-400 mb-4">
            Create a certificate template for this course to enable certificate generation.
          </p>
          <Button onClick={onEdit} className="btn-primary">
            <Icon icon="heroicons:plus" className="w-4 h-4 mr-2" />
            Create Template
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
          Certificate Template
        </h3>
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            template.is_active 
              ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
              : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
          }`}>
            {template.is_active ? "Active" : "Inactive"}
          </span>
          <Button onClick={onEdit} size="sm" variant="outline">
            <Icon icon="heroicons:pencil-square" className="w-4 h-4 mr-1" />
            Edit
          </Button>
          <Button onClick={onDelete} size="sm" variant="outline" className="text-red-600 hover:text-red-700">
            <Icon icon="heroicons:trash" className="w-4 h-4 mr-1" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Template Information */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
              Authorize Person
            </label>
            <p className="text-slate-900 dark:text-white">
              {template.authorize_person || "Not specified"}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
              Designation
            </label>
            <p className="text-slate-900 dark:text-white">
              {template.designation || "Not specified"}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
              Certificate Prefix
            </label>
            <p className="text-slate-900 dark:text-white">
              {template.prefix || "Not specified"}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
              Certification Text
            </label>
            <div className="text-slate-900 dark:text-white prose prose-sm max-w-none">
              {template.certification_text ? (
                <div dangerouslySetInnerHTML={{ __html: template.certification_text }} />
              ) : (
                <p className="text-slate-500 dark:text-slate-400 italic">Not specified</p>
              )}
            </div>
          </div>
        </div>

        {/* Template Assets */}
        <div className="space-y-4">
          {/* Background Image */}
          {template.background_image && (
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                Background Image
              </label>
              <div className="border border-slate-200 dark:border-slate-600 rounded-lg p-2">
                <img
                  src={ import.meta.env.VITE_ASSET_HOST_URL + template.background_image}
                  alt="Certificate Background"
                  className="w-full h-32 object-cover rounded"
                />
              </div>
            </div>
          )}

          {/* Logo */}
          {template.logo && (
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                Logo
              </label>
              <div className="border border-slate-200 dark:border-slate-600 rounded-lg p-2 w-fit">
                <img
                  src={import.meta.env.VITE_ASSET_HOST_URL + template.logo}
                  alt="Certificate Logo"
                  className="h-16 w-auto object-contain"
                />
              </div>
            </div>
          )}

          {/* Signature */}
          {template.signature && (
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                Signature
              </label>
              <div className="border border-slate-200 dark:border-slate-600 rounded-lg p-2 w-fit">
                <img
                  src={import.meta.env.VITE_ASSET_HOST_URL + template.signature}
                  alt="Certificate Signature"
                  className="h-16 w-auto object-contain"
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Certificate Preview */}
      <div className="mt-6 pt-6 border-t border-slate-200 dark:border-slate-700">
        <h4 className="text-md font-medium text-slate-900 dark:text-white mb-4">
          Certificate Preview
        </h4>
        <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-4">
          <div 
            className="relative bg-white rounded-lg shadow-lg mx-auto"
            style={{ 
              width: "400px", 
              height: "280px",
              backgroundImage: template.background_image ? `url(${template.background_image})` : 'none',
              backgroundSize: 'cover',
              backgroundPosition: 'center'
            }}
          >
            {/* Logo */}
            {template.logo && (
              <img
                src={import.meta.env.VITE_ASSET_HOST_URL + template.logo}
                alt="Logo"
                className="absolute top-4 left-4 h-12 w-auto object-contain"
              />
            )}
            
            {/* Certificate Content */}
            <div className="absolute inset-0 flex flex-col items-center justify-center text-center p-8">
              <h2 className="text-xl font-bold text-slate-800 mb-2">Certificate of Completion</h2>
              {template.certification_text ? (
                <div
                  className="text-xs text-slate-600 mb-4 max-h-20 overflow-hidden"
                  dangerouslySetInnerHTML={{
                    __html: template.certification_text
                      .replace(/\[STUDENT_NAME\]/g, '[Student Name]')
                      .replace(/\[COURSE_TITLE\]/g, '[Course Title]')
                      .replace(/\[COMPLETION_DATE\]/g, '[Completion Date]')
                  }}
                />
              ) : (
                <>
                  <p className="text-sm text-slate-600 mb-4">This is to certify that</p>
                  <p className="text-lg font-semibold text-slate-800 mb-4">[Student Name]</p>
                  <p className="text-sm text-slate-600 mb-6">has successfully completed the course</p>
                </>
              )}
              
              {/* Signature */}
              <div className="absolute bottom-8 right-8 text-center">
                {template.signature && (
                  <img
                    src={import.meta.env.VITE_ASSET_HOST_URL + template.signature}
                    alt="Signature"
                    className="h-8 w-auto object-contain mb-1"
                  />
                )}
                <div className="text-xs text-slate-600">
                  <p>{template.authorize_person}</p>
                  <p>{template.designation}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default CertificateTemplateView;
