import React, { useState, useEffect } from "react";
import { usePostApiMutation, useUpdateApiMutation } from "@/store/api/master/commonSlice";
import Card from "@/components/ui/Card";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { Icon } from "@iconify/react";
import { toast } from "react-toastify";

// Custom Input Component
const CustomInput = ({ label, name, type = "text", placeholder, value, onChange, error, required = false }) => {
  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <input
        type={type}
        name={name}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        className={`appearance-none border rounded h-10 w-full py-2 px-3 text-slate-700 dark:text-slate-200 dark:bg-slate-800 leading-tight focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
          error ? "border-red-500" : "border-slate-300 dark:border-slate-600"
        }`}
      />
      {error && <p className="text-red-600 dark:text-red-400 text-sm mt-1">{error}</p>}
    </div>
  );
};

// Custom File Input Component
const CustomFileInput = ({ label, name, onChange, error, accept, preview, required = false }) => {
  const [isDragActive, setIsDragActive] = useState(false);

  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);

    const files = e.dataTransfer.files;
    if (files && files[0]) {
      onChange(files[0]);
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      onChange(file);
    }
  };

  const removeFile = () => {
    onChange(null);
  };

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>

      {!preview ? (
        <div
          className={`min-h-[120px] flex flex-col items-center justify-center border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors ${
            isDragActive
              ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
              : 'border-slate-300 dark:border-slate-600 hover:border-slate-400 dark:hover:border-slate-500'
          }`}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={() => document.getElementById(name).click()}
        >
          <input
            id={name}
            type="file"
            accept={accept}
            onChange={handleFileChange}
            className="hidden"
          />
          <Icon icon="heroicons:cloud-arrow-up" className="w-8 h-8 text-slate-400 mb-2" />
          <p className="text-sm text-slate-600 dark:text-slate-300">
            {isDragActive ? 'Drop the file here...' : 'Drag and drop a file here, or click to select'}
          </p>
          <p className="text-xs text-slate-500 mt-1">
            {accept && `Accepted formats: ${accept.split(',').join(', ')}`}
          </p>
        </div>
      ) : (
        <div className="relative border border-slate-200 dark:border-slate-600 rounded-lg p-2">
          <img
            src={preview}
            alt="Preview"
            className="w-full h-32 object-cover rounded"
          />
          <button
            type="button"
            onClick={removeFile}
            className="absolute top-1 right-1 bg-white dark:bg-slate-800 rounded-full p-1 text-red-600 shadow-lg hover:bg-red-50 dark:hover:bg-red-900/20"
          >
            <Icon icon="heroicons:x-mark" className="w-4 h-4" />
          </button>
        </div>
      )}

      {error && (
        <p className="text-red-600 dark:text-red-400 text-sm mt-1">{error}</p>
      )}
    </div>
  );
};

const CertificateTemplateForm = ({ template, course, onCancel, onSuccess }) => {
  const [postApi, { isLoading: isCreating }] = usePostApiMutation();
  const [updateApi, { isLoading: isUpdating }] = useUpdateApiMutation();
  
  const [formData, setFormData] = useState({
    course_id: course?.id || "",
    authorize_person: "",
    designation: "",
    prefix: "",
    is_active: true,
    background_image: null,
    logo: null,
    signature: null,
  });
  
  const [errors, setErrors] = useState({});
  const [previewImages, setPreviewImages] = useState({
    background_image: null,
    logo: null,
    signature: null,
  });
  
  const isEditing = !!template;
  const isLoading = isCreating || isUpdating;

  useEffect(() => {
    if (template) {
      setFormData({
        course_id: template.course_id || course?.id || "",
        authorize_person: template.authorize_person || "",
        designation: template.designation || "",
        prefix: template.prefix || "",
        is_active: template.is_active ?? true,
        background_image: null,
        logo: null,
        signature: null,
      });
      
      setPreviewImages({
        background_image: template.background_image || null,
        logo: template.logo || null,
        signature: template.signature || null,
      });
    }
  }, [template, course]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.course_id) {
      newErrors.course_id = "Course is required";
    }

    // For create operations, images are required
    if (!isEditing) {
      if (!formData.background_image && !previewImages.background_image) {
        newErrors.background_image = "Background image is required";
      }
      if (!formData.logo && !previewImages.logo) {
        newErrors.logo = "Logo is required";
      }
      if (!formData.signature && !previewImages.signature) {
        newErrors.signature = "Signature is required";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const formDataToSend = new FormData();
      
      // Add text fields
      formDataToSend.append("course_id", formData.course_id);
      formDataToSend.append("authorize_person", formData.authorize_person);
      formDataToSend.append("designation", formData.designation);
      formDataToSend.append("prefix", formData.prefix);
      formDataToSend.append("is_active", formData.is_active ? "1" : "0");
      
      // Add ID for update operations
      if (isEditing) {
        formDataToSend.append("id", template.id);
      }
      
      // Add image files if selected
      if (formData.background_image) {
        formDataToSend.append("background_image", formData.background_image);
      }
      if (formData.logo) {
        formDataToSend.append("logo", formData.logo);
      }
      if (formData.signature) {
        formDataToSend.append("signature", formData.signature);
      }

      const endpoint = "admin/create-or-update-certificate-template";
      
      if (isEditing) {
        await updateApi({
          end_point: endpoint,
          body: formDataToSend
        }).unwrap();
      } else {
        await postApi({
          end_point: endpoint,
          body: formDataToSend
        }).unwrap();
      }
      
      onSuccess();
    } catch (error) {
      if (error?.data?.errors) {
        setErrors(error.data.errors);
        Object.values(error.data.errors)
          .flat()
          .forEach((msg) => toast.error(msg));
      } else {
        toast.error(error?.data?.message || "An error occurred");
      }
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const handleFileChange = (file, fieldName) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: file
    }));
    
    // Create preview URL
    if (file) {
      const previewUrl = URL.createObjectURL(file);
      setPreviewImages(prev => ({
        ...prev,
        [fieldName]: previewUrl
      }));
    }
    
    // Clear error
    if (errors[fieldName]) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: ""
      }));
    }
  };

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
          {isEditing ? "Edit Certificate Template" : "Create Certificate Template"}
        </h3>
        <button
          onClick={onCancel}
          className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
        >
          <Icon icon="heroicons:x-mark" className="w-6 h-6" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <CustomInput
            label="Authorize Person"
            name="authorize_person"
            type="text"
            placeholder="e.g., John Doe"
            value={formData.authorize_person}
            onChange={handleChange}
            error={errors.authorize_person}
          />

          <CustomInput
            label="Designation"
            name="designation"
            type="text"
            placeholder="e.g., Director"
            value={formData.designation}
            onChange={handleChange}
            error={errors.designation}
          />
        </div>

        <CustomInput
          label="Certificate Prefix"
          name="prefix"
          type="text"
          placeholder="e.g., CERT"
          value={formData.prefix}
          onChange={handleChange}
          error={errors.prefix}
        />

        {/* Status */}
        <div className="flex items-center">
          <Switch
            label="Active"
            name="is_active"
            value={formData.is_active}
            onChange={() => setFormData(prev => ({ ...prev, is_active: !prev.is_active }))}
          />
        </div>

        {/* File Uploads */}
        <div className="space-y-6">
          <h4 className="text-md font-medium text-slate-900 dark:text-white">
            Template Assets
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Background Image */}
            <CustomFileInput
              name="background_image"
              label="Background Image"
              onChange={(file) => handleFileChange(file, "background_image")}
              error={errors.background_image}
              accept="image/jpeg,image/png,image/jpg,image/gif,image/svg+xml"
              preview={previewImages.background_image}
              required={!isEditing}
            />

            {/* Logo */}
            <CustomFileInput
              name="logo"
              label="Logo"
              onChange={(file) => handleFileChange(file, "logo")}
              error={errors.logo}
              accept="image/jpeg,image/png,image/jpg,image/gif,image/svg+xml"
              preview={previewImages.logo}
              required={!isEditing}
            />

            {/* Signature */}
            <CustomFileInput
              name="signature"
              label="Signature"
              onChange={(file) => handleFileChange(file, "signature")}
              error={errors.signature}
              accept="image/png,image/svg+xml"
              preview={previewImages.signature}
              required={!isEditing}
            />
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-slate-200 dark:border-slate-700">
          <Button
            type="button"
            onClick={onCancel}
            variant="outline"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
            className="flex items-center space-x-2"
          >
            {isLoading && <Icon icon="heroicons:arrow-path" className="w-4 h-4 animate-spin" />}
            <span>{isEditing ? "Update Template" : "Create Template"}</span>
          </Button>
        </div>
      </form>
    </Card>
  );
};

export default CertificateTemplateForm;
