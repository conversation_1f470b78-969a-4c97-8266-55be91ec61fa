import React, { useState, useEffect } from "react";
import { useGetApiQuery, useDeleteApiMutation } from "@/store/api/master/commonSlice";
import CertificateTemplateView from "./CertificateTemplateView";
import CertificateTemplateForm from "./CertificateTemplateForm";
import Loading from "@/components/Loading";
import { toast } from "react-toastify";
import { Icon } from "@iconify/react";

const CertificateTemplate = ({ course }) => {
  const [showForm, setShowForm] = useState(false);
  const [deleteConfirm, setDeleteConfirm] = useState(false);
  

  const [deleteApi] = useDeleteApiMutation();
  
  const template = course?.certificate_template;

  const handleEdit = () => {
    setShowForm(true);
  };

  const handleCancel = () => {
    setShowForm(false);
  };

  const handleSuccess = () => {
    setShowForm(false);
    refetch();
  };

  const handleDelete = async () => {
    if (!template) return;
    
    try {
      await deleteApi({
        end_point: `admin/certificate-templates/${template.id}`,
        body: {}
      }).unwrap();
      
      toast.success("Certificate template deleted successfully");
      setDeleteConfirm(false);
      refetch();
    } catch (error) {
      toast.error(error?.data?.message || "Failed to delete certificate template");
    }
  };

  const confirmDelete = () => {
    setDeleteConfirm(true);
  };

  if (!course) {
    return (
      <div className="text-center py-8">
        <Icon icon="heroicons:exclamation-triangle" className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
        <p className="text-slate-500 dark:text-slate-400">
          Course information is required to manage certificate templates.
        </p>
      </div>
    );
  }



  if (error && error.status !== 404) {
    return (
      <div className="text-center py-8">
        <Icon icon="heroicons:exclamation-triangle" className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <p className="text-red-500">
          Error loading certificate template: {error?.data?.message || "Unknown error"}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-900 dark:text-white">
            Certificate Template
          </h2>
          <p className="text-slate-600 dark:text-slate-400 mt-1">
            Manage certificate template for "{course.title}"
          </p>
        </div>
        {template && !showForm && (
          <div className="flex items-center space-x-2 text-slate-600 dark:text-slate-400">
            <Icon icon="heroicons:document-text" className="w-5 h-5" />
            <span className="text-sm">Template Configured</span>
          </div>
        )}
      </div>

      {/* Content */}
      {showForm ? (
        <CertificateTemplateForm
          template={template}
          course={course}
          onCancel={handleCancel}
          onSuccess={handleSuccess}
        />
      ) : (
        <CertificateTemplateView
          course={course}
          template={template}
          onEdit={handleEdit}
          onDelete={confirmDelete}
        />
      )}

      {/* Delete Confirmation Modal */}
      {deleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-slate-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <Icon icon="heroicons:exclamation-triangle" className="w-6 h-6 text-red-500 mr-2" />
              <h3 className="text-lg font-medium text-slate-900 dark:text-white">
                Confirm Delete
              </h3>
            </div>
            <p className="text-slate-600 dark:text-slate-400 mb-6">
              Are you sure you want to delete this certificate template? This action cannot be undone and will affect certificate generation for this course.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setDeleteConfirm(false)}
                className="px-4 py-2 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
              >
                Delete Template
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CertificateTemplate;
