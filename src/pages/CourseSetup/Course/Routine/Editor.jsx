import React, { useState } from 'react';
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { useParams } from "react-router-dom";
import { CKEditor } from '@ckeditor/ckeditor5-react';
import {
  ClassicEditor,
  Bold,
  Essentials,
  Heading,
  Indent,
  IndentBlock,
  Italic,
  Link,
  List,
  MediaEmbed,
  Paragraph,
  Table,
  Undo
} from 'ckeditor5';

import 'ckeditor5/ckeditor5.css';
import '@/assets/scss/custom-ckeditor.css'



const MyEditorStyle = () => <style>{Object.keys(style).map(key => `${key} {${Object.keys(style[key]).map(k => `${k}: ${style[key][k]};`).join('')}}`)}</style>;

const MyEditor = ({data}) => {
  const [value, setValue] = useState('');
 
  const { id } = useParams();
  const [postApi, { isLoading, isError, error, isSuccess }] = usePostApiMutation();

  const initialData = `
  <table border="1" cellpadding="1" cellspacing="1" style="width:100%">
    <tbody>
      <tr>
        <th>Day</th>
        <th>10AM-11AM</th>
        <th>11AM-12PM</th>
        <th>01PM-02PM</th>
        <th>02PM-03PM</th>
        <th>03PM-04PM</th>
        <th>04PM-05PM</th>
        <th>05PM-06PM</th>
      </tr>
      <tr>
        <td>Sunday</td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
      <tr>
        <td>Monday</td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
      <tr>
        <td>Tuesday</td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
      <tr>
        <td>Wednesday</td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
      <tr>
        <td>Thursday</td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
      <tr>
        <td>Friday</td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
      <tr>
        <td>Saturday</td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
    </tbody>
  </table>`;

  const onSubmit = async () => {
    console.log(value);
    let obj = {
      ...data,
      routine_text: value
    };
    delete obj.course_category;
    delete obj.course_faq;
    delete obj.course_feature;
    delete obj.course_mentor;
    delete obj.course_routine;
    delete obj.icon;
    delete obj.thumbnail;

    console.log(obj);
    await postApi({end_point: "admin/course-save-or-update", body: obj});
  };
  return (

    <Card className="flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-semibold">Routine</h2>
        <Button
          onClick={() => onSubmit()}
          className="btn-primary btn-sm"
          isLoading={isLoading}
        >
          {isLoading ? 'Updating...' : 'Update Routine'}
        </Button>
      </div>
    <CKEditor
    editor={ ClassicEditor }
    config={ {
      toolbar: [
        'undo', 'redo', '|',
        'heading', '|', 'bold', 'italic', '|',
        'link', 'insertTable', 'mediaEmbed', '|',
        'bulletedList', 'numberedList', 'indent', 'outdent'
      ],
      plugins: [
        Bold,
        Essentials,
        Heading,
        Indent,
        IndentBlock,
        Italic,
        Link,
        List,
        MediaEmbed,
        Paragraph,
        Table,
        Undo
      ],
      initialData: data.routine_text || initialData,
    } }
    onChange={ ( event, editor ) => {
      const data = editor.getData();
      setValue( data );
    } }
  />


  </Card>
  );
};

export default MyEditor;