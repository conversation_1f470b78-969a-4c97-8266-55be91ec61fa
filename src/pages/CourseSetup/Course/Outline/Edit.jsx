import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import Select from "@/components/ui/Select";
import { Formik, Form, Field } from 'formik';
import { initialValues, validationSchema} from "./formOutline";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal } from "@/features/commonSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { useGetClassListQuery } from "@/store/api/master/rowContentClassListSlice";
import { useGetChapterListBySubjectQuery, useGetSubjectChapterListQuery } from "@/store/api/master/rowContentChapterListSlice";
import { useParams } from "react-router-dom";
const EditOutline = () => {
    const { id } = useParams();
    const [postApi, { isLoading, isError, error, isSuccess }] = usePostApiMutation();
    const dispatch = useDispatch()
    const { showEditModal } = useSelector((state) => state.commonReducer);
    const { editData } = useSelector((state) => state.commonReducer);
    console.log(editData);
    const [isActive, setIsActive] = useState(editData.is_active);
    const [isFree, setIsFree] = useState(editData.is_free);
    const [contentType, setContentType] = useState(editData.chapter_script_id ? 3 : editData.chapter_video_id ? 2 : 1);


  const [classLevelId, setClassLevelId] = useState(editData.class_level_id);
  const [subjectId, setSubjectId] = useState(editData.subject_id);
  const [chapterId, setChapterId] = useState(editData.chapter_id);

    //   api 
    const classList = useGetClassListQuery({
        pagination:false
      })?.data;
    
    const subjectList = useGetSubjectChapterListQuery(classLevelId ? classLevelId : editData.class_level_id)?.data;
    const chapterList= useGetChapterListBySubjectQuery(subjectId ? subjectId : editData.subject_id)?.data;
    // const scriptList= useGetApiQuery('admin/script-list-by-chapter-id/' + chapterId)?.data;
    // const videoList= useGetApiQuery('admin/video-list-by-chapter-id/' + chapterId)?.data;
    // const quizList= useGetApiQuery('admin/quiz-list-by-chapter-id/' + chapterId)?.data;
    // const categoryList = useGetApiQuery('admin/course-category?course_id=' + id)?.data;

    const onSubmit = async (values, { resetForm }) => {
        console.log(values);
        let formData = new FormData();
        formData.append("id", editData.id);
        formData.append("title", values.title);
        formData.append("title_bn", values.title_bn);
        formData.append("course_id", id);
        formData.append("class_level_id", values.class_level_id);
        formData.append("subject_id", values.subject_id);
        formData.append("chapter_id", values.chapter_id);
        formData.append("category_id", values.category_id);
        formData.append("chapter_script_id", values.chapter_script_id || 0);
        formData.append("chapter_video_id", values.chapter_video_id || 0);
        formData.append("chapter_quiz_id", values.chapter_quiz_id || 0);
        formData.append("sequence", values.sequence);;
        formData.append("is_active", isActive? 1 : 0);
        formData.append("is_free", isFree? 1 : 0);
        formData.append("is_only_note", 0);
        // formData.append("is_active", values.is_active ? 1 : 0);
        
        const response = await postApi({end_point: "admin/course-outline-save-or-update", body: formData});
        console.log(response);
        dispatch(setEditShowModal(false));
    }
    return (
    <Modal
    activeModal={showEditModal}
    onClose={() => dispatch(setEditShowModal(false))}
    title="Add New Course Outline"
    className="max-w-5xl"
    footer={
        <Button
            text="Close"
            btnClass="btn-primary"
            onClick={() => dispatch(setEditShowModal(false))}
        />
        }
    >        
    <Formik 
    validationSchema={validationSchema}
    initialValues={editData}
    onSubmit={onSubmit}>
    {({ values,
        errors,
        touched,
        setFieldValue,
        isSubmitting, }) => (
        <Form>
        <div>
        <div className="grid md:grid-cols-2 gap-4">
                <InputField
                  label="Title"
                  name="title"
                  type="text"
                  placeholder="Enter Title"
                  required
                />
                <InputField
                  label="Title (Bangla)"
                  name="title_bn"
                  type="text"
                  placeholder="Enter Title (Bangla)"
                  required
                />
         </div>
              <div className="grid md:grid-cols-3 gap-4 mt-4">

              <>
              { classList?.length > 0 && (
                  <Select
                    defaultValue={editData.class_level_id}
                    label="Class"
                    placeholder="Select Class"
                    options={classList?.map((item) => {
                      return { label: item.name, value: item.id };
                    })}
                    name="class_level_id"
                    onChange={(e) => {
                      setFieldValue("class_level_id", e.target.value);
                      setClassLevelId(e.target.value);
                    }}
                    error={errors.class_level_id}
                  />
                )}
                </>
                <>
                  <Select
                    defaultValue={editData.subject_id}
                    label="Subject"
                    placeholder="Select Subject"
                    options={subjectList?.map((item) => {
                      return { label: item.name, value: item.id };
                    })}
                    name="subject_id"
                    onChange={(e) => {
                      setFieldValue("subject_id", e.target.value);
                      setSubjectId(e.target.value);
                    }}
                  />
                </>
                <>
                  <Select
                    defaultValue={editData.chapter_id}
                    label="Chapter"
                    placeholder="Select Chapter"
                    options={chapterList?.map((item) => {
                      return { label: item.name, value: item.id };
                    })}
                    name="class_level_id"
                    onChange={(e) => {
                      setFieldValue("chapter_id", e.target.value);
                      setChapterId(e.target.value);
                    }}
                  />
                </>
            </div>



            <div className="grid md:grid-cols-3 gap-4 mt-4">
                
                <Select
                    defaultValue={editData.course_content_id}
                    label="Category"
                    placeholder="Select Category"
                    options={categoryList?.data?.map((item) => {
                      return { label: item.name, value: item.id };
                    })}
                    name="course_content_id"
                    onChange={(e) => {
                      setFieldValue("course_content_id", e.target.value);
                      
                    }}
                  />
                <Select
                    defaultValue={editData.chapter_script_id ? 3 : editData.chapter_video_id ? 2 : 1}
                    label="Content Type"
                    placeholder="Select Type"
                    options={[
                      { label: "Quiz", value: 1 },
                      { label: "Video", value: 2 },
                      { label: "Script", value: 3 },
                    ]}
                    name="content_type"
                    onChange={(e) => {
                        setContentType(e.target.value);
                    }}
                  />

                  {contentType == 1 && (
                    <Select
                    defaultValue=""
                    label="Quiz"
                    placeholder="Select Quiz"
                    options={quizList?.map((item) => {
                      return { label: item.title, value: item.id };
                    })}
                    name="class_level_id"
                    onChange={(e) => {
                      setFieldValue("chapter_quiz_id", e.target.value);
                      
                    }}
                  />
                  )}
                  {contentType == 2 && (
                    <Select
                    defaultValue={editData.chapter_video_id}
                    label="Video"
                    placeholder="Select Video"
                    options={videoList?.map((item) => {
                      return { label: item.title, value: item.id };
                    })}
                    name="chapter_quiz_id"
                    onChange={(e) => {
                      setFieldValue("chapter_quiz_id", e.target.value);
                      
                    }}
                  />
                  )}
                  
                  {contentType == 3 && (
                    <Select
                    defaultValue=""
                    label="Script"
                    placeholder="Select Script"
                    options={scriptList?.map((item) => {
                      return { label: item.title, value: item.id };
                    })}
                    name="chapter_video_id"
                    onChange={(e) => {
                      setFieldValue("chapter_video_id", e.target.value);
                      
                    }}
                  />
                  )}
         </div>

        <div className="grid md:grid-cols-3 gap-4 mt-4">
                 <NumberInput
                label="Sequence"
                name="sequence"
                placeholder="Sequence"
                onChange={(e) => setFieldValue('sequence', e.target.value)}
                />
         </div>


                

                <div className="grid md:grid-cols-2  gap-4 py-5 mx-10">

                <Switch
                label="Free"
                activeClass="bg-success-500"
                value={isFree}
                name="is_free"
                onChange={() => setIsFree(!isFree)}
                />
                <Switch
                label="Active"
                activeClass="bg-success-500"
                value={isActive}
                name="is_active"
                onChange={() => setIsActive(!isActive)}
                />
                </div>
 
            <div className="mb-4">
            </div>
        </div>
            <div className="ltr:text-right rtl:text-left mt-5">
                <Button isLoading={isLoading}
                    type="submit"
                    className="btn text-center btn-primary"
                >
                    Submit
                </Button>
                </div>
          
          </Form>
        )}
        </Formik>
        </Modal>
    );
}

export default EditOutline;
