import React from "react";
import * as yup from "yup";

export const initialValues = { 
    title: '', 
    title_bn: '', 
    course_id: '',
    class_level_id: '',
    subject_id: '',
    chapter_id: '',
    chapter_script_id: '',
    chapter_video_id: '',
    chapter_quiz_id: '',
    sequence: '',
    is_free: false,
    is_active: true,
    is_only_note: false
};

export const validationSchema =  yup.object({
    title: yup.string().max(50, "Should not be more than 50 characters").min(3, "Should not be less than 3 characters").required("Title is Required"),
    title_bn: yup.string().max(50, "Should not be more than 50 characters").min(3, "Should not be less than 3 characters"),
    class_level_id: yup.string().required("Class Level is Required"),
    subject_id: yup.string().required("Subject is Required"),
    chapter_id: yup.string().required("Chapter is Required"),
    sequence: yup.number().positive("Number should be positive"),
})

