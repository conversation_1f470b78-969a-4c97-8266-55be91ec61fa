import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import Select from "@/components/ui/Select";
import { Formik, Form, Field } from 'formik';
import { initialValues, validationSchema} from "./formOutline";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { useGetClassListQuery } from "@/store/api/master/rowContentClassListSlice";
import { useGetChapterListBySubjectQuery, useGetSubjectChapterListQuery } from "@/store/api/master/rowContentChapterListSlice";
import { useParams } from "react-router-dom";
const createOutline = () => {
    const { id } = useParams();
    const [postApi, { isLoading, isError, error, isSuccess }] = usePostApiMutation();
    const dispatch = useDispatch()
    const { showModal } = useSelector((state) => state.commonReducer);
    const [isActive, setIsActive] = useState(false);
    const [isFree, setIsFree] = useState(false);
    const [contentType, setContentType] = useState('');


  const [classLevelId, setClassLevelId] = useState(null);
  const [subjectId, setSubjectId] = useState(null);
  const [chapterId, setChapterId] = useState(null);

    //   api 
    const classList = useGetClassListQuery({
        pagination:false
      })?.data;
    
    const subjectList = useGetSubjectChapterListQuery(classLevelId)?.data;
    const chapterList= useGetChapterListBySubjectQuery(subjectId)?.data;

    // const scriptList= useGetApiQuery('admin/script-list-by-chapter-id/' + chapterId)?.data;
    // const videoList= useGetApiQuery('admin/video-list-by-chapter-id/' + chapterId)?.data;
    // const quizList= useGetApiQuery('admin/quiz-list-by-chapter-id/' + chapterId)?.data;
    // const categoryList = useGetApiQuery('admin/course-category?course_id=' + id)?.data;

    const onSubmit = async (values, { resetForm }) => {
        console.log(values);
        let formData = new FormData();
        formData.append("title", values.title);
        formData.append("title_bn", values.title_bn);
        formData.append("course_id", id);
        formData.append("class_level_id", values.class_level_id);
        formData.append("subject_id", values.subject_id);
        formData.append("chapter_id", values.chapter_id);
        formData.append("course_content_id", values.course_content_id);
        formData.append("chapter_script_id", values.chapter_script_id || 0);
        formData.append("chapter_video_id", values.chapter_video_id || 0);
        formData.append("chapter_quiz_id", values.chapter_quiz_id || 0);
        formData.append("sequence", values.sequence);;
        formData.append("is_active", isActive? 1 : 0);
        formData.append("is_free", isFree? 1 : 0);
        formData.append("is_only_note", 0);
        // formData.append("is_active", values.is_active ? 1 : 0);

        const response = await postApi({end_point: "admin/course-outline-save-or-update", body: formData});
        console.log(response);
        dispatch(setShowModal(false));
    }
    return (
    <Modal
    activeModal={showModal}
    onClose={() => dispatch(setShowModal(false))}
    title="Add New Course Outline"
    className="max-w-5xl"
    footer={
        <Button
            text="Close"
            btnClass="btn-primary"
            onClick={() => dispatch(setShowModal(false))}
        />
        }
    >        
    <Formik 
    validationSchema={validationSchema}
    initialValues={initialValues}
    onSubmit={onSubmit}>
    {({ values,
        errors,
        touched,
        setFieldValue,
        isSubmitting, }) => (
        <Form>
        <div>
        <div className="grid md:grid-cols-2 gap-4">
                <InputField
                  label="Title"
                  name="title"
                  type="text"
                  placeholder="Enter Title"
                  required
                />
                <InputField
                  label="Title (Bangla)"
                  name="title_bn"
                  type="text"
                  placeholder="Enter Title (Bangla)"
                  required
                />
         </div>
              <div className="grid md:grid-cols-3 gap-4 mt-4">

              <>
                  <Select
                    defaultValue=""
                    label="Class"
                    placeholder="Select Class"
                    options={classList?.map((item) => {
                      return { label: item.name, value: item.id };
                    })}
                    name="class_level_id"
                    onChange={(e) => {
                      setFieldValue("class_level_id", e.target.value);
                      setClassLevelId(e.target.value);
                    }}
                    error={errors.class_level_id}
                  />
                </>
                <>
                  <Select
                    defaultValue=""
                    label="Subject"
                    placeholder="Select Subject"
                    options={subjectList?.map((item) => {
                      return { label: item.name, value: item.id };
                    })}
                    name="subject_id"
                    onChange={(e) => {
                      setFieldValue("subject_id", e.target.value);
                      setSubjectId(e.target.value);
                    }}
                  />
                </>
                <>
                  <Select
                    defaultValue=""
                    label="Chapter"
                    placeholder="Select Chapter"
                    options={chapterList?.map((item) => {
                      return { label: item.name, value: item.id };
                    })}
                    name="class_level_id"
                    onChange={(e) => {
                      setFieldValue("chapter_id", e.target.value);
                      setChapterId(e.target.value);
                    }}
                  />
                </>
            </div>



            <div className="grid md:grid-cols-3 gap-4 mt-4">
                
                <Select
                    defaultValue=""
                    label="Category"
                    placeholder="Select Category"
                    options={categoryList?.data?.map((item) => {
                      return { label: item.name, value: item.id };
                    })}
                    name="course_content_id"
                    onChange={(e) => {
                      setFieldValue("course_content_id", e.target.value);
                      
                    }}
                  />
                <Select
                    defaultValue=""
                    label="Content Type"
                    placeholder="Select Type"
                    options={[
                      { label: "Quiz", value: 1 },
                      { label: "Video", value: 2 },
                      { label: "Script", value: 3 },
                    ]}
                    name="content_type"
                    onChange={(e) => {
                        setContentType(e.target.value);
                      
                    }}
                  />

                  {contentType == 1 && (
                    <Select
                    defaultValue=""
                    label="Quiz"
                    placeholder="Select Quiz"
                    options={quizList?.map((item) => {
                      return { label: item.title, value: item.id };
                    })}
                    name="class_level_id"
                    onChange={(e) => {
                      setFieldValue("chapter_quiz_id", e.target.value);
                      
                    }}
                  />
                  )}
                  {contentType == 2 && (
                    <Select
                    defaultValue=""
                    label="Video"
                    placeholder="Select Video"
                    options={videoList?.map((item) => {
                      return { label: item.title, value: item.id };
                    })}
                    name="chapter_video_id"
                    onChange={(e) => {
                      setFieldValue("chapter_video_id", e.target.value);
                      
                    }}
                  />
                  )}
                  
                  {contentType == 3 && (
                    <Select
                    defaultValue=""
                    label="Learning Material"
                    placeholder="Select Learning Material"
                    options={scriptList?.map((item) => {
                      return { label: item.title, value: item.id };
                    })}
                    name="chapter_script_id"
                    onChange={(e) => {
                      setFieldValue("chapter_script_id", e.target.value);
                      
                    }}
                  />
                  )}
         </div>

        <div className="grid md:grid-cols-3 gap-4 mt-4">
                 <NumberInput
                label="Sequence"
                name="sequence"
                placeholder="Sequence"
                onChange={(e) => setFieldValue('sequence', e.target.value)}
                />
         </div>


                

                <div className="grid md:grid-cols-2  gap-4 py-5 mx-10">

                <Switch
                label="Free"
                activeClass="bg-success-500"
                value={isFree}
                name="is_free"
                onChange={() => setIsFree(!isFree)}
                />
                <Switch
                label="Active"
                activeClass="bg-success-500"
                value={isActive}
                name="is_active"
                onChange={() => setIsActive(!isActive)}
                />
                </div>
 
            <div className="mb-4">
            </div>
        </div>
            <div className="ltr:text-right rtl:text-left mt-5">
                <Button isLoading={isLoading}
                    type="submit"
                    className="btn text-center btn-primary"
                >
                    Submit
                </Button>
                </div>
          
          </Form>
        )}
        </Formik>
        </Modal>
    );
}

export default createOutline;
