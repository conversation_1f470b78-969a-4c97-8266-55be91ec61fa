import React, { useState } from "react";
import Icon from "@/components/ui/Icon";
import InputField from "@/components/ui/InputField";
import { Formik, Form } from "formik";
import { initialValues, validationSchema } from "./formCourse";
import { useDispatch, useSelector } from "react-redux";
// import { setShowModal } from "@/features/commonSlice";
import ProgressBar from "@/components/ui/ProgressBar";
import SimpleBar from "simplebar-react";
import Fileinput from "@/components/ui/Fileinput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import Textarea from "@/components/ui/Textarea";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import Select from "@/components/ui/Select";
import { useGetMenuListQuery } from "@/store/api/master/menuSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { useNavigate } from "react-router-dom";

const CreateCourse = () => {
  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const menuList = useGetMenuListQuery("pagination=false")?.data;

  const [subMenuList, setSubMenuList] = useState([]);
  const [isFree, setIsFree] = useState(true);
  const [isActive, setIsActive] = useState(false);
  const [hasLifeCoach, setHasLifeCoach] = useState(false);
  const [progressValue, setProgressValue] = useState(0);

  // State for storing the title
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [regularPrice, setRegularPrice] = useState("");
  const [salePrice, setSalePrice] = useState("");
  // const [uploadedImageUrl, setUploadedImageUrl] = useState("");
  const [uploadedThumbnailUrl, setUploadedThumbnailUrl] = useState("");
  const [discountPercentage, setDiscountPercentage] = useState("");

  //Truncate Text function
  const truncateText = (text, maxLength) => {
    return text.length > maxLength
      ? `${text.substring(0, maxLength)}...`
      : text;
  };

  const onSubmit = async (values, { resetForm }) => {
    let formData = new FormData();
    formData.append("title", values.title);
    formData.append("category_id", values.category_id || "");
    formData.append("sub_category_id", values.sub_category_id || "");
    formData.append("course_type_id", values.course_type_id || "");
    formData.append("description", values.description);
    formData.append("thumbnail", values.thumbnail);
    formData.append("regular_price", values.regular_price || "0");
    formData.append("sale_price", values.sale_price || "0");
    formData.append("discount_percentage", values.discount_percentage || "0");

    formData.append("is_free", isFree ? 1 : 0);
    formData.append("is_active", 0);
    formData.append("course_type_id", 1);

    const response = await postApi({
      end_point: "admin/course-save-or-update",
      body: formData,
    });
    console.log(response);
    if (response?.data) {
      setProgressValue(20);
      // dispatch(setShowModal(false));
      resetForm();
      navigate("/course-details/" + response?.data?.data.id);
    }
  };


  const handleThumbnailChange = (e, setFieldValue) => {
    const file = e.currentTarget.files[0];
    if (file) {
      const fileUrl = URL.createObjectURL(file);
      setUploadedThumbnailUrl(fileUrl);
      setFieldValue("thumbnail", file);
    }
  };

  const handleBackClick = () => {
    navigate("/course-list");
  };

  return (
    <>
    

   
    <ProgressBar
    value={progressValue}
    className="bg-primary-500 "
    striped
    backClass="h-5 rounded-[999px]"
    showValue
  />
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 mt-10">
      
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl overflow-hidden">
          <div className="relative justify-center">
            <img
              src={
                uploadedThumbnailUrl ||
                "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTmC3sLJeXtH4IeRNpKytSZxoIFGWEmsLmP9Q&s"
              }
              className="w-auto mx-auto h-40 object-fill"
              alt="Uploaded Icon"
            />
            <span className="absolute top-2 left-2 px-2 py-1 text-xs font-medium text-white rounded-full bg-red-500">
              Inactive
            </span>

            {!isFree && (
              <span className="absolute top-2 right-2 px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full dark:bg-red-900 dark:text-red-300 flex items-center">
                {discountPercentage || "0"}% Free
              </span>
            )}
          </div>
          <div className="p-1">
            <h2 className="text-base font-semibold text-gray-900 dark:text-white">
              {/* {title || "Untitled"} */}
              {truncateText(title || "Untitled", 28)}
            </h2>
            <p className="my-2 text-sm text-gray-600 dark:text-gray-300 tracking-tighter">
              {truncateText(description || "Default Description", 70)}
            </p>
          </div>
         
          {isFree ? (
            <div className="my-2 flex">
              <span className="text-xs font-medium text-white rounded-full bg-primary-500 p-1 mx-1">
                Free
              </span>
            </div>
          ) : (
            <div className="p-1 flex justify-between">
              <p className="bg-primary-500 text-base font-bold text-white rounded-lg flex justify-between items-center px-2 gap-2">
                <Icon size={30} icon="tabler:coin-taka-filled" className="" />
                <span
                  className={`text-base font-bold ${
                    salePrice ? "line-through" : ""
                  }`}
                >
                  {regularPrice || "0"} 
                </span>
                {salePrice && (
                  <span className="text-base font-bold">{discountPercentage ? (salePrice - ( discountPercentage / 100) * salePrice) : salePrice}</span>
                )}
              </p>
            </div>
          )}

        </div>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2">
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl my-5 p-4">
          <h2 className="text-base font-semibold text-gray-900 dark:text-white">
            <span className="text-primary-500">Title:</span>{" "}
            {title || "Untitled"}
          </h2>
          <p className="my-2 text-sm text-gray-600 dark:text-gray-300 tracking-tighter">
            <span className="text-primary-500">Description:</span>{" "}
            {description || "Default Description"}
          </p>
        </div>
      </div>

      <div
        className={`fixed right-0 top-0 w-[450px] bg-white dark:bg-slate-800 h-screen z-[9999] shadow-base2 border border-slate-200 dark:border-slate-700 transition-all duration-150`}
      >
        <SimpleBar className="px-6 h-full">
          <header className="flex items-center justify-between border-b border-slate-100 dark:border-slate-700 -mx-6 px-6 py-[25px] mb-">
            <div>
              <span className="block text-xl text-slate-900 font-medium dark:text-[#eee]">
                Add New Course
              </span>
            </div>
            <div
              className="cursor-pointer text-2xl text-slate-800 dark:text-slate-200"
              onClick={handleBackClick}
            >
              <Icon icon="heroicons-outline:x" />
            </div>
          </header>

          <Formik
            validationSchema={validationSchema}
            initialValues={initialValues}
            onSubmit={onSubmit}
          >
            {({ values, errors, setFieldValue }) => (
              <Form>
                <div className="grid md:grid-cols-1 gap-2">
                  <InputField
                    label="Title"
                    name="title"
                    type="text"
                    placeholder="Enter Title"
                    required
                    onChange={(e) => {
                      setFieldValue("title", e.target.value);
                      setTitle(e.target.value);
                    }}
                  />
                </div>

                <div className="grid md:grid-cols-2 gap-2 my-2">
                  <Select
                    label="Select Menu"
                    defaultValue=""
                    placeholder="Select Menu"
                    options={menuList?.map((item) => ({
                      label: item.name,
                      value: item.id,
                    }))}
                    name="category_id"
                    onChange={(e) => {
                      setFieldValue("category_id", e.target.value);
                      setSubMenuList(menuList?.find((item) => item.id == e.target.value)?.sub_categories);
                    }}
                    error={errors.category_id}
                  />
                  <Select
                    label="Select Sub Menu"
                    defaultValue=""
                    placeholder="Select Sub Menu"
                    options={subMenuList?.map((item) => ({
                      label: item.name,
                      value: item.id,
                    }))}
                    name="sub_category_id"
                    onChange={(e) => {
                      setFieldValue("sub_category_id", e.target.value);
                    }}
                  />
                  <div className="my-auto">
                    <Switch
                      label="Free"
                      activeClass="bg-success-500 my-5"
                      value={isFree}
                      name="is_free"
                      onChange={() => setIsFree(!isFree)}
                    />
                  </div>

                  {!isFree && (
                    <InputField
                      label="Regular Price"
                      name="regular_price"
                      type="text"
                      placeholder="Enter Regular Price"
                      // required
                      onChange={(e) => {
                        setFieldValue("regular_price", e.target.value);
                        setRegularPrice(e.target.value);
                      }}
                    />
                  )}
                  {!isFree && (
                    <InputField
                      label="Sale Price"
                      name="sale_price"
                      type="text"
                      placeholder="Enter Sale Price"
                      // required
                      onChange={(e) => {
                        setFieldValue("sale_price", e.target.value);
                        setSalePrice(e.target.value);
                      }}
                    />
                  )}
                  {!isFree && (
                    <InputField
                      label="Discount %"
                      name="discount_percentage"
                      type="text"
                      placeholder="Enter Discount"
                      onChange={(e) => {
                        setFieldValue("discount_percentage", e.target.value);
                        setDiscountPercentage(e.target.value);
                      }}
                    />
                  )}

                  {/* <InputField
                    label="Regular Price"
                    name="regular_price"
                    type="text"
                    placeholder="Enter Regular Price"
                    // required
                    onChange={(e) => {
                      setFieldValue("regular_price", e.target.value);
                      setRegularPrice(e.target.value);
                    }}
                  />
                  <InputField
                    label="Sale Price"
                    name="sale_price"
                    type="text"
                    placeholder="Enter Sale Price"
                    // required
                    onChange={(e) => {
                      setFieldValue("sale_price", e.target.value);
                      setSalePrice(e.target.value);
                    }}
                  />
                  <InputField
                    label="Discount %"
                    name="discount_percentage"
                    type="text"
                    placeholder="Enter Discount"
                    onChange={(e) => {
                      setFieldValue("discount_percentage", e.target.value);
                      setDiscountPercentage(e.target.value);
                    }}
                  /> */}
                  {/* <InputField
                  label="Number of Enrolled"
                  name="number_of_enrolled"
                  type="text"
                  placeholder="Enter Number of Enrolled"
                  required
                />
                <InputField
                  label="Rating"
                  name="rating"
                  type="text"
                  placeholder="Enter Rating"
                  required
                /> */}
                  {/* <InputField
                    label="Sequence"
                    name="sequence"
                    type="text"
                    placeholder="Enter Sequence"
                    // required
                  /> */}
                </div>

                <div className="grid md:grid-cols-1 gap-2 my-2">
                  {/* <div>
                    <label className="block text-[#1D1D1F] text-base font-medium mb-2">
                      Icon
                    </label>
                    <Fileinput
                      name="icon"
                      accept="image/*"
                      type="file"
                      placeholder="Icon"
                      preview={true}
                      selectedFile={values.icon}
                      // onChange={(e) =>
                      //   setFieldValue("icon", e.currentTarget.files[0])
                      // }
                      onChange={(e) => handleImageChange(e, setFieldValue)}
                    />
                  </div> */}
                  <div>
                    <label className="block text-[#1D1D1F] text-base font-medium mb-2">
                      Thumbnail
                    </label>
                    <Fileinput
                      name="thumbnail"
                      accept="image/*"
                      type="file"
                      placeholder="Thumbnail"
                      preview={true}
                      selectedFile={values.thumbnail}
                      // onChange={(e) =>
                      //   setFieldValue("thumbnail", e.currentTarget.files[0])
                      // }
                      onChange={(e) => handleThumbnailChange(e, setFieldValue)}
                    />
                  </div>
                </div>
                {/* <div className="grid md:grid-cols-2 gap-2 my-2">
                  <DatePicker
                    label="Appeared From"
                    placeholder="YYYY-MM-DD"
                    format="YYYY/MM/DD"
                    name="appeared_from"
                    error={errors?.appeared_from}
                    onChange={(e) => setFieldValue("appeared_from", e)}
                  />

                  <DatePicker
                    label="Appeared To"
                    placeholder="YYYY-MM-DD"
                    format="YYYY/MM/DD"
                    name="appeared_to"
                    error={errors?.appeared_to}
                    onChange={(e) => setFieldValue("appeared_to", e)}
                  />
                </div> */}

                <div className="grid md:grid-cols-1 gap-2 my-2">
                  <label className="block text-[#1D1D1F] text-base font-medium">
                    Description
                  </label>
                  <Textarea
                    placeholder="Description"
                    name="description"
                    // onChange={(e) =>
                    //   setFieldValue("description", e.target.value);
                    //   setDescription(e.target.value);
                    // }
                    onChange={(e) => {
                      setFieldValue("description", e.target.value);
                      setDescription(e.target.value);
                    }}
                  />
                </div>

                <div className="grid md:grid-cols-2 gap-2 py-5 justify-between">
                  {/* <Switch
                    label="Free"
                    activeClass="bg-success-500"
                    value={isFree}
                    name="is_free"
                    onChange={() => setIsFree(!isFree)}
                  /> */}
                  {/* <Switch
                    label="Active"
                    activeClass="bg-success-500"
                    value={isActive}
                    name="is_active"
                    onChange={() => setIsActive(!isActive)}
                  /> */}
                  {/* <Switch
                    label="Has Life Coach"
                    activeClass="bg-success-500"
                    value={hasLifeCoach}
                    name="has_life_coach"
                    onChange={() => setHasLifeCoach(!hasLifeCoach)}
                  /> */}
                </div>
                <div className="flex justify-between">
                  <div>
                    <Button
                      // isLoading={isLoading}
                      type="submit"
                      className="btn text-center btn-danger my-5"
                      onClick={handleBackClick}
                    >
                      Back
                    </Button>
                  </div>
                  <div>
                    <Button
                      isLoading={isLoading}
                      type="submit"
                      className="btn text-center btn-primary my-5"
                    >
                      Submit
                    </Button>
                  </div>
                </div>
                {/* <div className="flex justify-end"> */}
                {/* <Button
                    isLoading={isLoading}
                    text="Submit"
                    className="btn-dark mt-8 mb-5"
                    type="submit"
                    // loading={isSubmitting}
                    // disabled={isSubmitting}
                  /> */}
                {/* <Button
                    isLoading={isLoading}
                    type="submit"
                    className="btn text-center btn-dark my-5"
                  >
                    Submit
                  </Button>
                </div> */}
              </Form>
            )}
          </Formik>
        </SimpleBar>
      </div>
    </>
  );
};

export default CreateCourse;
