import React, { useState, useEffect } from "react";
import { Formik, Form } from "formik";
import { useParams, useNavigate } from "react-router-dom";
import { Icon } from "@iconify/react";
import General from "./Creation/General";
import Visuals from "./Creation/Visuals";
import Pricing from "./Creation/Pricing";
import {
  useGetApiQuery,
  usePostApiMutation,
} from "@/store/api/master/commonSlice";
import { useGetMenuListQuery } from "@/store/api/master/menuSlice";
import * as yup from "yup";

const CourseSettingsPage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [activeTab, setActiveTab] = useState(0);
  const [tabErrors, setTabErrors] = useState([false, false, false]); // Tracks error status of tabs
  const [localCourse, setLocalCourse] = useState(null);
  const [loading, setLoading] = useState(true);
  const [preview, setPreview] = useState(null);

  const { data: menuList, isMenuLoading } =
  useGetApiQuery(`admin/menu-list?course_category=1&pagination=false`);

    
  const {
    data: course,
    isLoading,
    error,
  } = id
    ? useGetApiQuery(`admin/course-details/${id}`)
    : { data: null, isLoading: false, error: null };

  const [postApi] = usePostApiMutation();

  useEffect(() => {
    const routeFrom = window.sessionStorage.getItem("routeFrom");
    if (routeFrom === "course-create" && id) {
      setActiveTab(1);
    }
  }, [id]);

  useEffect(() => {
    if (course) {
      setLocalCourse(course);
      if (course.thumbnail) {
        setPreview(
          `${import.meta.env.VITE_ASSET_HOST_URL}/${course.thumbnail}`
        );
      }
      setLoading(false);
    } else {
      const storedCourse = localStorage.getItem("draftCourse");
      if (storedCourse) {
        const parsedCourse = JSON.parse(storedCourse);
        setLocalCourse(parsedCourse);
        if (parsedCourse.thumbnail) {
          setPreview(
            `${import.meta.env.VITE_ASSET_HOST_URL}/${parsedCourse.thumbnail}`
          );
        }
      }
      setLoading(false);
    }
  }, [course]);

  const menuItems = [
    {
      icon: <Icon icon="mdi:file-document-outline" className="w-5 h-5" />,
      title: "General",
      description: "Name, description",
    },

    {
      icon: <span className="w-5 h-5 font-bold">৳</span>,
      title: "Pricing",
      description: "Set course prices",
    },
  ];

  const validationSchemas = [
    yup.object({
      title: yup.string().required("Course Title is Required"),
      category_id: yup.string().required("Please select a category"),
    }),
    yup.object({
      youtube_url: yup.string().url("Invalid URL"),
      thumbnail: yup
        .mixed()
        .nullable()
        .test("is-image", "Please select a valid image", (value) => {
          if (!value) return true; // Allow empty (optional) value
          if (value instanceof File) return value.type.startsWith("image");
          return false; // If provided, it must be a valid image
        }),
    }),
    yup.object({
      is_free: yup.boolean(),
      show_price: yup.boolean(),
      installment_type: yup
        .string()
        .when("is_free", {
          is: false,
          then: yup.string().required("Installment type is required"),
          otherwise: yup.string().notRequired(),
        }),
      minimum_enroll_amount: yup
        .number()
        .when("is_free", {
          is: false,
          then: yup
            .number()
            .typeError("Admission Fee should be a number")
            .required("Admission Fee is required"),
          otherwise: yup.number().notRequired(),
        }),
      // regular_price: !is_free || installment_type === "Monthly" ? yup.number().nullable(true) : yup
      //   .number()
      //   .typeError("Regular price should be a number")
      //   .positive("Regular price should be positive")
      //   .required("Regular price is required"),
        
      regular_price: yup
        .number()
        .nullable(true)
        .when(["is_free", "installment_type"], {
          is: (is_free, installment_type) =>
            !is_free || (installment_type != "Monthly"),
          then: yup
            .number()
            .typeError("Regular price should be a number")
            .required("Regular price is required"),
          otherwise: yup.number().nullable(true),
        }),
      sale_price: yup
        .number()
        .nullable(true)
        .when(["is_free", "installment_type", "regular_price"], {
          is: (is_free, installment_type, regular_price) =>
            !is_free && installment_type !== "Monthly" && regular_price != null,
          then: yup
            .number()
            .typeError("Sale price should be a number")
            .max(
              yup.ref("regular_price"),
              "Sale price should be less than or equal to the regular price"
            )
            .required("Sale price is required"),
          otherwise: yup.number().nullable(true),
        }),
  
      monthly_amount: yup
        .number()
        .when(["is_free", "installment_type"], {
          is: (is_free, installment_type) => !is_free && installment_type === "Monthly",
          then: yup
            .number()
            .typeError("Monthly amount should be a number")
            .required("Monthly amount is required"),
          otherwise: yup.number().nullable(true),
        }),
    }),
  ];
  
  

 const initialValues = {
    title: localCourse?.title || "",
    description: localCourse?.description || "",
    category_id: localCourse?.category_id || "",
    sub_category_id: localCourse?.sub_category_id || "",
    appeared_from: localCourse?.appeared_from
      ? new Date(localCourse.appeared_from)
      : "",
    appeared_to: localCourse?.appeared_to
      ? new Date(localCourse.appeared_to)
      : "",
    youtube_url: localCourse?.youtube_url || "",
    thumbnail: localCourse?.thumbnail || "",
    is_free: localCourse?.is_free || 0,
    show_price: localCourse?.show_price || 1,
    regular_price: localCourse?.regular_price || 0, // Ensure empty string
    sale_price: localCourse?.sale_price || 0, // Ensure empty string
    discount_percentage: localCourse?.discount_percentage || 0,
    monthly_amount: localCourse?.monthly_amount || 0,
    max_installment_qty: localCourse?.max_installment_qty || 0,
    minimum_enroll_amount: localCourse?.minimum_enroll_amount || 0,
    installment_type: "Monthly",
    currency: "BDT"
  };


  const handleTabSubmit = async (values, actions) => {
    const errors = await actions.validateForm();
    const tabErrorsCopy = [...tabErrors];
  
    if (Object.keys(errors).length === 0) {
      tabErrorsCopy[activeTab] = false; // Clear error state for the current tab
      setTabErrors(tabErrorsCopy);
  
      if (activeTab < 1) {
        setActiveTab((prev) => prev + 1);
      } else {
        await handleFinalSubmit(values);
      }
    } else {
      tabErrorsCopy[activeTab] = true; // Mark the current tab as having errors
      setTabErrors(tabErrorsCopy);
    }
  
    actions.setSubmitting(false);
  };
  

  const validateAllTabs = async (values) => {
    const errors = [];
    const tabErrorsCopy = [false, false, false];

    for (let i = 0; i < validationSchemas.length; i++) {
      try {
        await validationSchemas[i].validate(values, { abortEarly: false });
      } catch (error) {
        errors.push(error);
        tabErrorsCopy[i] = true; // Mark tab with validation errors
      }
    }

    setTabErrors(tabErrorsCopy);
    return errors.length === 0;
  };

  const handleFinalSubmit = async (values) => {
    const isValid = await validateAllTabs(values);
  
    if (!isValid) {
      return; // Prevent submission if any tab is invalid
    }
  
    const formData = new FormData();
    if (id) formData.append("id", id);
    if (values.installment_type === "One Time") {

      values.minimum_enroll_amount = values.sale_price;
      
      delete values.monthly_amount;
      delete values.max_installment_qty;

    } else if (values.installment_type === "Monthly") {

      delete values.regular_price;
      delete values.sale_price;
      delete values.discount_percentage;

    } else if (values.installment_type === "Installment") {

      delete values.monthly_amount;

    }

    Object.keys(values).forEach((key) => {
      if (key === "appeared_from" || key === "appeared_to") {
        formData.append(
          key,
          values[key] ? new Date(values[key]).toISOString().split("T")[0] : ""
        );
      } else if (key === "is_free") {
        formData.append(key, values[key] ? 1 : 0); // Convert boolean to 1/0
      }  else if (key === "show_price") {
        formData.append(key, values[key] ? 1 : 0); 
      } else if (
        values.is_free &&
        ["regular_price", "sale_price", "discount_percentage", "monthly_amount", "max_installment_qty", "minimum_enroll_amount"].includes(key)
      ) {
        formData.append(key, 0); // Set numeric fields to 0 when course is free
      } else if (key === "thumbnail" && values[key] instanceof File) {
        formData.append(key, values[key]);
      } else {
        formData.append(key, values[key] ?? ""); // Ensure no null values, use empty string otherwise
      }
    });
  
    formData.append("is_active", 0);
    formData.append("is_draft", 0);
    formData.append("course_type_id", 1);
  
    try {
      const response = await postApi({
        end_point: "admin/course-save-or-update",
        body: formData,
      }).unwrap();
  
      if (response?.data) {
        localStorage.removeItem("draftCourse");
        navigate(`/course-details/${response.data.id}`);
      }
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };
  

  if (loading || isLoading || isMenuLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading course details</div>;

  return (
    <div>
      <div className="flex items-center justify-between mb-5">
        <div className="text-xl font-bold">Create Course</div>
      </div>

      <div className="grid grid-cols-5 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl overflow-hidden p-4">
        <div className="col-span-1 border-r border-gray-200 dark:border-gray-700">
          {menuItems.map((item, index) => (
            <div
              key={index}
              onClick={() => setActiveTab(index)}
              className={`flex items-center p-3 mb-2 rounded-md cursor-pointer ${
                index === activeTab
                  ? "bg-blue-100 text-blue-600"
                  : tabErrors[index]
                  ? "bg-red-100 text-red-600"
                  : "text-gray-600 hover:bg-blue-50"
              }`}
            >
              <div
                className={`mr-4 ${
                  index === activeTab ? "text-blue-600" : "text-gray-400"
                }`}
              >
                {item.icon}
              </div>
              <div>
                <strong className="font-semibold">{item.title}</strong>
                <p className="text-sm text-gray-400">{item.description}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="col-span-4 pl-4">
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchemas[activeTab]}
            onSubmit={handleTabSubmit}
            validateOnChange={true} 
            validateOnBlur={true}
          >
            {(formikProps) => (
              <Form>
                {activeTab === 0 && (
                  <General {...formikProps} menuList={menuList} />
                )}
                {/* {activeTab === 1 && (
                  <Visuals
                    {...formikProps}
                    preview={preview}
                    setPreview={setPreview}
                  />
                )} */}
                {activeTab === 1 && <Pricing {...formikProps} />}
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </div>
  );
};

export default CourseSettingsPage;
