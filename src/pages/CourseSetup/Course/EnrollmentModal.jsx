import React, { useState, useEffect } from 'react';
import { useGetApiQuery } from '@/store/api/master/commonSlice';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import Select from 'react-select';
import { toast } from 'react-toastify';
import DatePicker from '@/components/partials/common-dateTimePicker/Date';
import { useDispatch } from 'react-redux';
import { setShowModal } from '@/features/commonSlice';
import CreateStudent from '../../StudentList/createStudent';
import NumberInput from '@/components/partials/common-numberInput/NumberInput';
import { Formik } from 'formik';

const EnrollmentModal = ({ courseId, onClose, onSuccess, postApi, isEnrolling }) => {
  const dispatch = useDispatch();
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [selectedBatch, setSelectedBatch] = useState(null);
  const [paidAmount, setPaidAmount] = useState('0');
  const [discount, setDiscount] = useState('0');
  const [isEnrolled, setIsEnrolled] = useState(false);
  const [previousPayment, setPreviousPayment] = useState(null);
  const [courseDetails, setCourseDetails] = useState(null);

  // Fetch students, batches, and course details
  const { data: students } = useGetApiQuery('admin/all-student-list-admin?pagination=false');
  const { data: batches } = useGetApiQuery(`admin/batches?course_id=${courseId}`);
  const { data: course } = useGetApiQuery(`admin/course-details/${courseId}`);

  console.log(batches);

  useEffect(() => {
    if (course) {
      setCourseDetails(course);
    }
  }, [course]);

  // Check if student is already enrolled
  useEffect(() => {
    if (courseId && selectedStudent?.value) {
      postApi({
        end_point: 'admin/check-enrollment-student',
        body: {
          course_id: courseId,
          student_id: selectedStudent.value,
        },
        notoast: true,
      }).then((response) => {
        setIsEnrolled(!response?.data.status);
        setPreviousPayment(response?.data.data);
      });
    }
  }, [courseId, selectedStudent]);

  const handleCreateStudent = () => {
    dispatch(setShowModal(true));
  };

  const handleApprovePayment = async (ids) => {
    try {
      const response = await postApi({
        end_point: 'admin/approve-pending-payment',
        body: { ids: ids },
      }).unwrap();

      if (response.status) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error approving payment:', error);
      toast.error('Failed to approve payment');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!selectedStudent) {
      toast.error('Please select a student');
      return;
    }

    try {
      const formData = new FormData();
      formData.append('course_id', courseId);
      formData.append('student_id', selectedStudent.value);
      formData.append('batch_id', selectedBatch ? selectedBatch.value : '');
      formData.append('paid_amount', paidAmount);
      formData.append('discount', discount);

      const response = await postApi({
        end_point: 'admin/enroll-student',
        body: formData,
      }).unwrap();

      if (response.status) {
        onSuccess();
      } else {
        toast.error(response.message || 'Failed to enroll student');
      }
    } catch (error) {
      console.error('Enrollment error:', error);
      toast.error('Failed to enroll student. Please try again.');
    }
  };

  return (
    <Modal
      title="Enroll Student"
      labelclassName="btn-outline-dark"
      activeModal={true}
      onClose={onClose}
      className="max-w-3xl"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="md:col-span-2">
            <div className="flex items-center gap-3">
              <div className="flex-grow">
                <label className="form-label">Select Student</label>
                <Select
                  options={students?.map(student => ({
                    value: student.id,
                    label: student.name
                  }))}
                  value={selectedStudent}
                  onChange={setSelectedStudent}
                  placeholder="Select a student"
                  className="react-select-container"
                  classNamePrefix="react-select"
                  isSearchable
                />
              </div>
              <div className="flex-shrink-0 mt-6">
                <button
                  type="button"
                  className="btn btn-primary btn-sm"
                  onClick={handleCreateStudent}
                >
                  Add Student
                </button>
              </div>
            </div>
          </div>

          <div>
            <label className="form-label">Select Batch</label>
            <Select
              options={batches?.data?.map(batch => ({
                value: batch.id,
                label: batch.name
              }))}
              value={selectedBatch}
              onChange={setSelectedBatch}
              placeholder="Select a batch"
              className="react-select-container"
              classNamePrefix="react-select"
            />
          </div>
        </div>

        {/* Course Price Details */}
        <div className="bg-slate-50 p-4 rounded-lg">
          <h5 className="text-lg font-semibold mb-2">Course Price Details</h5>
          <p>
            <strong>Price: </strong>
            {courseDetails && (
              <span>
                {courseDetails.sale_price > 0
                  ? courseDetails.sale_price + ' ' + courseDetails.currency 
                  : courseDetails.monthly_amount > 1
                    ? courseDetails.monthly_amount + ' ' + courseDetails.currency +  '/Month'
                    : <span className="text-green-500">Free</span>
                }
              </span>
            )}
          </p>
          <p>
            <strong>Admission Fee: </strong>
            {courseDetails && (
              <span>{courseDetails.minimum_enroll_amount + ' ' + courseDetails.currency }</span>
            )}
          </p>
        </div>

        {isEnrolled ? (
          previousPayment?.is_approved === 0 ? (
            <div className="alert alert-warning text-center p-6">
              <p>This enrollment is pending approval.</p>
              <p><strong>Transaction ID:</strong> {previousPayment.transaction_id}</p>
              <p><strong>Payment Method:</strong> {previousPayment.payment_method}</p>
              <p><strong>Paid Amount:</strong> {previousPayment.paid_amount} {previousPayment.currency}</p>
              <button
                type="button"
                onClick={() => handleApprovePayment([previousPayment.id])}
                className="btn btn-success mt-3"
              >
                Approve
              </button>
            </div>
          ) : (
            <div className="alert alert-danger text-center p-6">
              Already Enrolled in this Course
            </div>
          )
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="form-label">Paid Amount</label>
              <div className="formik-field-wrapper">
                <Formik initialValues={{ paid_amount: paidAmount }}>
                  {() => (
                    <NumberInput
                      name="paid_amount"
                      type="text"
                      placeholder="Enter paid amount"
                      value={paidAmount}
                      onChange={(e) => setPaidAmount(e.target.value)}
                    />
                  )}
                </Formik>
              </div>
            </div>

            <div>
              <label className="form-label">Discount</label>
              <div className="formik-field-wrapper">
                <Formik initialValues={{ discount: discount }}>
                  {() => (
                    <NumberInput
                      name="discount"
                      type="text"
                      placeholder="Enter discount"
                      value={discount}
                      onChange={(e) => setDiscount(e.target.value)}
                    />
                  )}
                </Formik>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            text="Cancel"
            btnClass="btn-outline-danger"
            onClick={onClose}
          />
          {!isEnrolled && (
            <Button
              text="Enroll Student"
              btnClass="btn-primary"
              type="submit"
              isLoading={isEnrolling}
            />
          )}
        </div>
      </form>
      <CreateStudent />
    </Modal>
  );
};

export default EnrollmentModal;
