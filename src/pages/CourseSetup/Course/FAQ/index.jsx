import React, { useState } from "react";
import Badge from "@/components/ui/Badge";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Create from "./create";
import Edit from "./edit";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";

const index = ({ data }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const dispatch = useDispatch();
  const { id } = useParams();
  const columns = [
    {
      label: "Question",
      field: "title",
    },
    {
      label: "Answer",
      field: "answer",
    },
    {
      label: "Status",
      field: "is_active",
    },
    {
      label: "Action",
      field: "",
    },
  ];

  return (
    <div className="flex flex-col p-5">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-semibold">FAQs</h2>
        <Button
          onClick={() => setIsSidebarOpen(true)}
          className="btn-primary btn-sm"
        >
          Add FAQ
        </Button>
      </div>
      <div className="flex">
        {!data.length && (
          <p className="text-sm text-red-500">No FAQ has been added</p>
        )}
        <ol className="list-decimal grid gap-2 px-4">
          {data?.map((item) => (
            <li className="" key={item.id}>
              <div className="">
                <b>{item.title}</b>
              </div>
              <div>
                <p>{item.answer}</p>
              </div>
            </li>
          ))}
        </ol>
      </div>
      <Create
        isSidebarOpen={isSidebarOpen}
        setIsSidebarOpen={setIsSidebarOpen}
      />
    </div>
  );
};

export default index;
