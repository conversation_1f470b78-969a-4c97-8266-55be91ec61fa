import React, { useState } from "react";
import InputField from "@/components/ui/InputField";
import Textarea from "@/components/ui/Textarea";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { Formik, Form, Field } from "formik";
import { initialValues, validationSchema } from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { useParams } from "react-router-dom";
import { useGetMentorListQuery } from "@/store/api/master/mentorSlice";
import SimpleBar from "simplebar-react";
import Icon from "@/components/ui/Icon";

const Create = ({ isSidebarOpen, setIsSidebarOpen }) => {
  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();
  const dispatch = useDispatch();
  const { id } = useParams();

  const [isActive, setIsActive] = useState(true);

  const [apiParam, setApiParam] = useState("pagination=false");
  const mentorList = useGetMentorListQuery(apiParam)?.data;

  const onSubmit = async (values, { resetForm }) => {
    let faq = [];
    values.is_active = isActive;
    values.course_id = id;
    faq.push(values);
    let obj = JSON.stringify(faq);
    const response = await postApi({
      end_point: "admin/faq-save-or-update",
      body: { faq: obj },
    });
    resetForm();
    setIsSidebarOpen(false);
  };
  return (
    <>
      {isSidebarOpen && (
        <div
          className={`fixed right-0 top-0 w-[450px] bg-white dark:bg-slate-800 h-screen z-[9999] shadow-base2 border border-slate-200 dark:border-slate-700 transition-all duration-150`}
        >
          <SimpleBar className="px-6 h-full">
            <header className="flex items-center justify-between border-b border-slate-100 dark:border-slate-700 px-6 py-[25px]">
              <div>
                <span className="block text-xl text-slate-900 font-medium dark:text-[#eee]">
                  Add New FAQ
                </span>
              </div>
              <div
                className="cursor-pointer text-2xl text-slate-800 dark:text-slate-200"
                onClick={() => setIsSidebarOpen(false)}
              >
                <Icon icon="heroicons-outline:x" />
              </div>
            </header>
            <Formik
              validationSchema={validationSchema}
              initialValues={initialValues}
              onSubmit={onSubmit}
            >
              {({ values, errors, setFieldValue }) => (
                <Form>
                  <>
                    <div className="grid md:grid-cols-1 gap-4">
                      <InputField
                        label="Question"
                        name="title"
                        type="text"
                        placeholder="Any asked question"
                      />

                      <Textarea
                        placeholder="Answer"
                        name="answer"
                        onChange={(e) => {
                          setFieldValue("answer", e.target.value);
                        }}
                      />
                      <div className="mt-4 pt-4">
                        <Switch
                          label="Active"
                          activeClass="bg-success-500"
                          value={isActive}
                          name="is_active"
                          onChange={() => setIsActive(!isActive)}
                        />
                      </div>
                    </div>
                  </>
                  <div className="ltr:text-right rtl:text-left mt-5">
                    <Button
                      isLoading={isLoading}
                      type="submit"
                      className="btn text-center btn-primary"
                    >
                      Submit
                    </Button>
                  </div>
                </Form>
              )}
            </Formik>
          </SimpleBar>
        </div>
      )}
    </>
  );
};

export default Create;
