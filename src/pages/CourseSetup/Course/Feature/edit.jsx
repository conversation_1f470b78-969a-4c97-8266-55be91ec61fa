import React, { useState } from "react";
import InputField from "@/components/ui/InputField";
import Button from "@/components/ui/Button";
import SimpleBar from "simplebar-react";
import Icon from "@/components/ui/Icon";
import { Formik, Form, Field } from "formik";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { useParams } from "react-router-dom";

const Create = ({ isSidebarOpen, setIsSidebarOpen }) => {
  const [postApi, { isLoading }] = usePostApiMutation();
  const { id } = useParams();
  const [isActive, setIsActive] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedIconIndex, setSelectedIconIndex] = useState(null);

  // State for feature titles
  const [titles, setTitles] = useState([
    { icon: "features/icon.png", title: "Experienced Instructors"},
    { icon: "features/Simplification.svg", title: "Pre-recorded class "},
    { icon: "features/Simplification-1.svg", title: "Assessments after class"},
    { icon: "features/Simplification-2.svg", title: "Live class"},
    { icon: "features/Simplification-5.svg", title: "Supporting scripts"},
    { icon: "features/Simplification-4.svg", title: "Assignment and review"},
    { icon: "features/Simplification-6.svg", title: "Certification"},
  ]);

  const icons = [
    "features/icon.png",
    "features/Simplification.svg",
    "features/Simplification-1.svg",
    "features/Simplification-2.svg",
    "features/Simplification-3.svg",
    "features/Simplification-4.svg",
    "features/Simplification-5.svg",
    "features/Simplification-6.svg",
    "features/Simplification-7.svg",
  ];

  const addFeature = () => {
    setTitles([...titles, { icon: "features/icon.png", title: `Feature ${titles.length + 1}` }]);
  };

  // Handle icon selection
  const openIconModal = (index) => {
    setSelectedIconIndex(index);
    setIsModalOpen(true);
  };

  const selectIcon = (icon) => {
    const updatedTitles = [...titles];
    updatedTitles[selectedIconIndex].icon = icon;
    setTitles(updatedTitles);
    setIsModalOpen(false);
  };

  const onSubmit = async (values, { resetForm }) => {
    const selectedFeatures = titles
      .map((item, index) => {
        if (values[`selected${index}`]) {
          return {
            title: values[`title${index + 1}`],
            title_bn: values[`title${index + 1}`],
            icon: item.icon,
            is_active: isActive,
            course_id: id,
          };
        }
        return null;
      })
      .filter(item => item !== null);

    const response = await postApi({
      end_point: "admin/feature-save-or-update",
      body: { feature: JSON.stringify(selectedFeatures) },
    });
    resetForm();
    setIsSidebarOpen(false);
  };

  return (
    <>
      {isSidebarOpen && (
        <div
          className={`fixed right-0 top-0 w-[450px] bg-white dark:bg-slate-800 h-screen z-[9999] shadow-base2 border border-slate-200 dark:border-slate-700 transition-all duration-150`}
        >
          <SimpleBar className="px-6 h-full">
            <header className="flex items-center justify-between border-b border-slate-100 dark:border-slate-700 px-6 py-[25px]">
              <div>
                <span className="block text-xl text-slate-900 font-medium dark:text-[#eee]">
                  Add Features
                </span>
              </div>
              <div
                className="cursor-pointer text-2xl text-slate-800 dark:text-slate-200"
                onClick={() => setIsSidebarOpen(false)}
              >
                <Icon icon="heroicons-outline:x" />
              </div>
            </header>
            <Formik
              enableReinitialize
              initialValues={{
                ...titles.reduce((acc, _, index) => {
                  acc[`selected${index}`] = false;
                  acc[`title${index + 1}`] = titles[index].title;
                  return acc;
                }, {})
              }}
              onSubmit={onSubmit}
            >
              {({ values }) => (
                <Form>
                  <div className="grid md:grid-cols-1 gap-4 mt-5">
                    {titles.map((item, index) => (
                      <div key={index} className="flex items-center gap-2 bg-slate-100 dark:bg-slate-700 p-2">
                        <Field
                          type="checkbox"
                          name={`selected${index}`}
                          className="form-checkbox"
                        />
                        <input
                          type="hidden"
                          name={`icon${index + 1}`}
                          value={item.icon} // Set the value of the hidden input to the selected icon
                        />
                        <img
                          className="w-8 h-8 cursor-pointer"
                          src={`${import.meta.env.VITE_ASSET_HOST_URL}${item.icon}`}
                          alt=""
                          onClick={() => openIconModal(index)}
                        />
                        <div className="flex-1">
                          <Field
                            name={`title${index + 1}`}
                            placeholder={item.title}
                            as={InputField}
                          />
                        </div>
                      </div>
                    ))}
                  </div>

                  <Button
                    type="button"
                    onClick={addFeature}
                    className="btn text-center btn-secondary mt-4"
                  >
                    Add More Features
                  </Button>

                  <div className="ltr:text-right rtl:text-left mt-5">
                    <Button
                      isLoading={isLoading}
                      type="submit"
                      className="btn text-center btn-primary"
                    >
                      Submit
                    </Button>
                  </div>
                </Form>
              )}
            </Formik>
          </SimpleBar>
        </div>
      )}

      {/* Icon Selection Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-[10000] flex items-center justify-center">
          <div className="bg-white p-4 rounded-lg shadow-lg max-w-md w-full">
            <h2 className="text-lg font-semibold mb-4">Select an Icon</h2>
            <div className="grid grid-cols-3 gap-4">
              {icons.map((icon, index) => (
                <img
                  key={index}
                  src={`${import.meta.env.VITE_ASSET_HOST_URL}${icon}`}
                  alt=""
                  className="w-12 h-12 cursor-pointer"
                  onClick={() => selectIcon(icon)}
                />
              ))}
            </div>
            <Button
              type="button"
              onClick={() => setIsModalOpen(false)}
              className="btn btn-secondary mt-4"
            >
              Close
            </Button>
          </div>
        </div>
      )}
    </>
  );
};

export default Create;
