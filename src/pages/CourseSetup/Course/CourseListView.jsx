import React from "react";
import Icon from "@/components/ui/Icon";
import { useNavigate } from "react-router-dom";

const CourseListView = ({ course }) => {
  const navigate = useNavigate();

  const handleCourseDetails = () => {
    if (course.is_draft) {
      navigate(`/course-create/${course.id}`, {
        state: { course },
      });
    } else {
      navigate(`/course-details/${course.id}`, {
        state: { course },
      });
    }
  };

  const showBadge = () => {
    if (course.discount_percentage > 0) {
      return (
        <span className="px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full dark:bg-red-900 dark:text-red-300">
          {Number.isInteger(course.discount_percentage) ? course.discount_percentage : parseFloat(course.discount_percentage).toFixed(2)}% Off
        </span>
      );
    }
    return null;
  };

  const showPrice = () =>
    !course.is_free ? (
      <p className="text-sm font-bold flex items-center gap-2">
        <Icon size={24} icon="tabler:coin-taka-filled" />
        { course.monthly_amount > 1 ? (
          <span className="text-sm font-bold">{course.monthly_amount} / Month</span>
        ): 
        <div>
        <span className={`${course.sale_price ? "line-through" : ""}`}>
          {course.regular_price} 
        </span>
        {course.sale_price > 0 && (
          <span className="text-sm font-bold"> {course.sale_price}</span>
        )}
        </div>}
      </p>
    ) : (
      <span className="text-sm font-bold flex items-center gap-2 text-green-500">
        <Icon size={24} icon="icon-park-outline:check-one" />
        Free
      </span>
    );

  return (
    <div
      className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm cursor-pointer flex items-center p-2"
      onClick={handleCourseDetails}
    >
      {/* Thumbnail */}
      <img
        src={`${import.meta.env.VITE_ASSET_HOST_URL}${course.thumbnail}`}
        alt={course.title}
        className="w-16 h-16 object-cover rounded-md mr-4"
      />

      {/* Course Information */}
      <div className="flex flex-col w-full">
        {/* Title and Badge */}
        <div className="flex justify-between">
          <h6 className="text-base font-semibold text-gray-900 dark:text-white truncate">
            {course.title}
          </h6>

          {/* Active Status */}
          <div className="text-xs text-gray-600 dark:text-gray-300">
            {course.is_active ? (
              <span className="px-1.5 py-0.5 text-green-700 bg-green-50 rounded dark:bg-green-800 dark:text-green-300 border border-green-300">
                Active
              </span>
            ) : (
              <span className="px-1.5 py-0.5 text-red-700 bg-red-50 rounded dark:bg-red-800 dark:text-red-300 border border-red-300">
                Inactive
              </span>
            )}
          </div>
        </div>

        {/* Badge */}

        <div className="flex items-center gap-4 mt-2">
          {course.is_draft ? (
            <span className="text-sm font-bold flex items-center gap-2 text-red-500">
              <Icon size={24} icon="icon-park-outline:check-one" />
              Draft
            </span>
          ) : (
            showPrice()
          )}
          {showBadge()}
        </div>
      </div>
    </div>
  );
};

export default CourseListView;
