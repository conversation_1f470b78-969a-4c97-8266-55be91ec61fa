import React, { useState, useEffect } from "react";
import Icon from "@/components/ui/Icon";
import InputField from "@/components/ui/InputField";
import { Formik, Form } from "formik";
import { initialValues, validationSchema } from "./formCourse";
import { useDispatch, useSelector } from "react-redux";
// import { setShowModal } from "@/features/commonSlice";
import SimpleBar from "simplebar-react";
import Fileinput from "@/components/ui/Fileinput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import Textarea from "@/components/ui/Textarea";
import Select from "@/components/ui/Select";
import { useGetMenuListQuery } from "@/store/api/master/menuSlice";
import { usePostApiMutation, useGetApiQuery } from "@/store/api/master/commonSlice";
import { useNavigate } from "react-router-dom";
import { useParams } from "react-router-dom";
import Outline from "./Outline";
import CourseCategory from "./OldCourseCategory";
import Feature from "./Feature";
import Routine from "./Routine";
import FAQ from "./FAQ";
import MentorAssign from "./MentorAssign";
import ProgressBar from "@/components/ui/ProgressBar";
import Loading from "@/components/Loading";

const CourseDetails = () => {
  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams();
  const course = useGetApiQuery("admin/course-details/" + id + "")?.data;
  // const course = useGetApiMutation("admin/course-details/" + id + "")?.data;
  const menuList = useGetMenuListQuery("pagination=false")?.data;
  const [subMenuList, setSubMenuList] = useState([]);

  const [isEditing, setIsEditing] = useState(false);
  const [isActive, setIsActive] = useState(false);
  const [hasLifeCoach, setHasLifeCoach] = useState(false);



  // State for storing the title
  const [title, setTitle] = useState(course?.title);
  const [description, setDescription] = useState(course?.description);

  const [isFree, setIsFree] = useState(course?.is_free);
  const [isFeatured, setIsFeatured] = useState(course?.is_featured);
  // const [isFeatured, setIsFeatured] = useState(course?.is_featured);

  console.log(isFree, 'Free');
  console.log(isFeatured, 'Feature');


  const [regularPrice, setRegularPrice] = useState(course?.regular_price);
  const [salePrice, setSalePrice] = useState(course?.sale_price);
  const [discountPercentage, setDiscountPercentage] = useState(course?.discount_percentage);


  useEffect(() => {
    if (course) {
      setIsFree(course?.is_free);
      setRegularPrice(course?.regular_price);
      setSalePrice(course?.sale_price);
      setIsFeatured(course?.is_featured);
      setDiscountPercentage(course?.discount_percentage);

      setSubMenuList(menuList?.find((item) => item.id == course?.category_id)?.sub_categories);
    }
  }, [course]);

  // const [uploadedImageUrl, setUploadedImageUrl] = useState("");
  const [uploadedThumbnailUrl, setUploadedThumbnailUrl] = useState("");

  //Truncate Text function
  const truncateText = (text, maxLength) => {
    return text.length > maxLength
      ? `${text.substring(0, maxLength)}...`
      : text;
  };

  const onSubmit = async (values, { resetForm }) => {
    let formData = new FormData();
    formData.append("id", course?.id);
    formData.append("title", values.title);
    formData.append("category_id", values.category_id || "");
    formData.append("sub_category_id", values.sub_category_id || "");
    formData.append("course_type_id", values.course_type_id || "");
    formData.append("description", values.description);
    if (uploadedThumbnailUrl) {
      formData.append("thumbnail", values.thumbnail);
    }
    formData.append("regular_price", values.regular_price || "0");
    formData.append("sale_price", values.sale_price || "0");
    formData.append("discount_percentage", values.discount_percentage || "0");

    formData.append("is_free", isFree ? 1 : 0);
    formData.append("is_featured", isFeatured ? 1 : 0);
    formData.append("is_active", course.is_active ? 1 : 0);
    formData.append("course_type_id", 1);

    const response = await postApi({
      end_point: "admin/course-save-or-update",
      body: formData,
    });

    if (response?.data) {
      resetForm();
      setIsEditing(false);
    }
  };


  const handleThumbnailChange = (e, setFieldValue) => {
    const file = e.currentTarget.files[0];
    if (file) {
      const fileUrl = URL.createObjectURL(file);
      setUploadedThumbnailUrl(fileUrl);
      setFieldValue("thumbnail", file);
    }
  };

  const publishCourse = async () => {
    let formData = new FormData();
    formData.append("id", course?.id);
    formData.append("title", course?.title);
    formData.append("course_type_id", course?.course_type_id);
    formData.append("is_active", 1);


    const response = await postApi({
      end_point: "admin/course-save-or-update",
      body: formData,
    });

  };


  const handleBackClick = () => {
    setIsEditing(false);
  };
  if (!course) {
    return <Loading />;
  }
  return (
    <>
    { course?.percentage < 100 &&
      <ProgressBar
      value={course?.percentage }
      className="bg-primary-500 "
      striped
      backClass="h-5 rounded-[999px]"
      showValue
    /> }
      <div className="grid mt-4">
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl overflow-hidden px-4"
       
        >
          <div className="relative justify-start">
            <img
              src={
                uploadedThumbnailUrl ||
                import.meta.env.VITE_ASSET_HOST_URL + course?.thumbnail
              }
              className="w-auto h-40 object-fill"
              alt="Uploaded Icon"
            />
            <span className={`absolute top-2 left-2 px-2 py-1 text-xs font-medium text-white rounded-full ${course?.is_active ? "bg-green-500" : "bg-red-500"}`}>
              {course?.is_active ? "Active" : "Inactive"}
            </span>

           
            <div className="absolute top-2 right-2 flex gap-2">
            {(!isFree && discountPercentage) && (
              <span className="px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full dark:bg-red-900 dark:text-red-300 flex items-center">
                Discount {discountPercentage || "0"}% 
              </span>
            )}
            {course?.is_active ?
            <Badge className="bg-success-500 text-white rounded-full"> 
              <Icon icon="tabler:check" className="w-4 h-4 mr-1" />
            Published
            </Badge> 
            : <Button
              className="btn btn-success btn-sm"
              disabled={course?.percentage < 100}
              onClick={() => publishCourse()}
            >
              {course?.is_active ? "Published" : "Publish"}
            </Button>
            }

            <Button
              className=" btn btn-primary btn-sm"
              onClick={() => setIsEditing(!isEditing)}
            >
              Edit
            </Button>
            </div>

          
          </div>
          <div className="p-1">
            <h2 className="text-base font-semibold text-gray-900 dark:text-white">
              {title || course?.title }
            </h2>
            <p className="my-2 text-sm text-gray-600 dark:text-gray-300 tracking-tighter">
              { description || course?.description }
            </p>
          </div>
          <div className="flex">
          {isFeatured && 
            <div className="my-2 flex">
              <span className="text-xs font-medium text-white rounded-full bg-success-500 p-1 mx-1">
                Featured
              </span>
            </div>
            }
          {isFree ? (
            <div className="my-2 flex">
        
              <span className="text-xs font-medium text-white rounded-full bg-primary-500 p-1 mx-1">
                Free
              </span>
            </div>
          ) : (
            <div className="p-1 flex justify-between">
              <p className="bg-primary-500 text-base font-bold text-white rounded-lg flex justify-between items-center px-2 gap-2">
                <Icon size={30} icon="tabler:coin-taka-filled" className="" />
                <span
                  className={`text-base font-bold ${
                  salePrice ? "line-through" : ""
                  }`}
                >
                  {regularPrice || "0"}
                </span>
                {salePrice && (
                  <span className="text-base font-bold">{ salePrice - (discountPercentage ? (discountPercentage * salePrice ) / 100  : 0 ) }</span>
                 
                )}
              </p>
            </div>
          )}
        </div>
        </div>

      </div>

      {course &&
      <div className="grid grid-cols-1 gap-4">
        
      <CourseCategory data={course?.course_category} />
      <Feature data={course?.course_feature} />
      <Routine data={course} />
      <FAQ data={course?.course_faq} />
      <MentorAssign course={course} />
      </div> 
       } 
      { isEditing &&
      <div
        className={`fixed right-0 top-0 w-[450px] bg-white dark:bg-slate-800 h-screen z-[9999] shadow-base2 border border-slate-200 dark:border-slate-700 transition-all duration-150`}
      >
        <SimpleBar className="px-6 h-full">
          <header className="flex items-center justify-between border-b border-slate-100 dark:border-slate-700 -mx-6 px-6 py-[25px] mb-">
            <div>
              <span className="block text-xl text-slate-900 font-medium dark:text-[#eee]">
                Edit
              </span>
            </div>
            <div
              className="cursor-pointer text-2xl text-slate-800 dark:text-slate-200"
              onClick={handleBackClick}
            >
              <Icon icon="heroicons-outline:x" />
            </div>
          </header>

          <Formik
            validationSchema={validationSchema}
            initialValues={{
              title: course?.title,
              description: course?.description,
              category_id: course?.category_id,
              sub_category_id: course?.sub_category_id,
              thumbnail: course?.thumbnail,
              sale_price: salePrice || course?.sale_price,
              regular_price: regularPrice || course?.regular_price,
              discount_percentage: discountPercentage || course?.discount_percentage,
            }}
            onSubmit={onSubmit}
          >
            {({ values, errors, setFieldValue }) => (
              <Form>
                <div className="grid md:grid-cols-1 gap-2">
                  <InputField
                    label="Title"
                    name="title"
                    type="text"
                    placeholder="Enter Title"
                    required
                    onChange={(e) => {
                      setFieldValue("title", e.target.value);
                      setTitle(e.target.value);
                    }}
                  />
                </div>

                <div className="grid md:grid-cols-2 gap-2 my-2">
                  <Select
                    label="Select Menu"
                    defaultValue={course?.category_id}
                    placeholder="Select Menu"
                    options={menuList?.map((item) => ({
                      label: item.name,
                      value: item.id,
                    }))}
                    name="category_id"
                    onChange={(e) => {
                      setFieldValue("category_id", e.target.value);
                      setSubMenuList(menuList?.find((item) => item.id == e.target.value)?.sub_categories);
                    }}
                    error={errors.category_id}
                  />
                  {subMenuList?.length > 0 ? (
                    
                  <Select
                  label="Select Sub Menu"
                  defaultValue={course?.sub_category_id}
                  placeholder="Select Sub Menu"
                  options={subMenuList?.map((item) => ({
                    label: item.name,
                    value: item.id,
                  }))}
                  name="sub_category_id"
                  onChange={(e) => {
                    setFieldValue("sub_category_id", e.target.value);
                  }}
                />
                  ) : <div> </div>}
        
                  <div className="my-auto">
                    <Switch
                      label="Free"
                      activeClass="bg-success-500 my-5"
                      value={isFree}
                      name="is_free"
                      onChange={() => setIsFree(!isFree)}
                    />
                  </div>

                  {!isFree && (
                    <InputField
                      label="Regular Price"
                      name="regular_price"
                      type="text"
                      placeholder="Enter Regular Price"
                      // required
                      onChange={(e) => {
                        setFieldValue("regular_price", e.target.value);
                        setRegularPrice(e.target.value);
                      }}
                    />
                  )}
                  {!isFree && (
                    <InputField
                      label="Sale Price"
                      name="sale_price"
                      type="text"
                      placeholder="Enter Sale Price"
                      // required
                      onChange={(e) => {
                        setFieldValue("sale_price", e.target.value);
                        setSalePrice(e.target.value);
                      }}
                    />
                  )}
                  {!isFree && (
                    <InputField
                      label="Discount %"
                      name="discount_percentage"
                      type="text"
                      placeholder="Enter Discount"
                      onChange={(e) => {
                        setFieldValue("discount_percentage", e.target.value);
                        setDiscountPercentage(e.target.value);
                      }}
                    />
                  )}

                </div>

                <div className="grid md:grid-cols-1 gap-2 my-2">
             
                  <div>
                    <label className="block text-[#1D1D1F] text-base font-medium mb-2">
                      Thumbnail
                    </label>
                    <Fileinput
                      name="thumbnail"
                      accept="image/*"
                      type="file"
                      placeholder="Thumbnail"
                      preview={true}
                      selectedFile={values.thumbnail}
                      onChange={(e) => handleThumbnailChange(e, setFieldValue)}
                    />
                  </div>
                </div>
       
                <div className="grid md:grid-cols-1 gap-2 my-2">
                  <label className="block text-[#1D1D1F] text-base font-medium">
                    Description
                  </label>
                  <Textarea
                    defaultValue={values.description}
                    placeholder="Description"
                    name="description"
               
                    onChange={(e) => {
                      setFieldValue("description", e.target.value);
                      setDescription(e.target.value);
                    }}
                  />
                </div>

                <div className="grid md:grid-cols-2 gap-2 py-5 justify-between">
                
                <div className="my-auto">
                    <Switch
                      label="Featured Course?"
                      activeClass="bg-success-500"
                      value={isFeatured}
                      name="is_featured"
                      onChange={() => setIsFeatured(!isFeatured)}
                    />
                  </div>

                </div>
                <div className="flex justify-between">
                  <div>
                    <Button
                      type="submit"
                      className="btn text-center btn-danger my-5 btn-sm"
                      onClick={handleBackClick}
                    >
                      Cancel
                    </Button>
                  </div>
                  <div>
                    <Button
                      isLoading={isLoading}
                      type="submit"
                      className="btn text-center btn-primary my-5 btn-sm"
                    >
                      Submit
                    </Button>
                  </div>
                </div>
              </Form>
            )}
          </Formik>
        </SimpleBar>
      </div> 
      }
    </>
  );
};

export default CourseDetails;
