import React from "react";
import InputField from "@/components/ui/InputField";
import Textarea from "@/components/ui/Textarea";
import Select from "@/components/ui/Select";
import Button from "@/components/ui/Button";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";

const General = ({ values, errors, touched, setFieldValue, setFieldTouched, menuList, handleSubmit }) => {
  const subMenuItems =
    menuList?.find(
      (item) => item.id === parseInt(values.category_id)
    )?.sub_categories || [];

  const handleCategoryChange = (e) => {
    setFieldValue("category_id", e.target.value);
    setFieldValue("sub_category_id", "");
  };

  const handleInputChange = (e, fieldName) => {
    setFieldValue(fieldName, e.target.value);
  };

  return (
    <>
      <InputField
        label="Course Title"
        name="title"
        type="text"
        placeholder="Enter Title"
        required
        value={values.title}
        onChange={(e) => handleInputChange(e, "title")}
        onBlur={() => setFieldTouched("title", true)}
      />

      <div className="grid md:grid-cols-1 gap-2 my-4">
        <Textarea
          value={values.description}
          label="Description"
          placeholder="Description"
          name="description"
          onChange={(e) => handleInputChange(e, "description")}
          onBlur={() => setFieldTouched("description", true)}
        />
      </div>

      <div className="grid md:grid-cols-2 gap-2 my-4">
        <Select
          label="Select Category"
          placeholder="Select Category"
          options={menuList?.map((item) => ({
            label: item.name,
            value: item.id.toString(),
          }))}
          name="category_id"
          value={values.category_id}
          onChange={handleCategoryChange}
          onBlur={() => setFieldTouched("category_id", true)}
          required
        />

        <Select
          label="Select Sub Category"
          placeholder="Select Sub Category"
          options={subMenuItems.map((item) => ({
            label: item.name,
            value: item.id.toString(),
          }))}
          name="sub_category_id"
          value={values.sub_category_id}
          onChange={(e) => handleInputChange(e, "sub_category_id")}
          onBlur={() => setFieldTouched("sub_category_id", true)}
        />
      </div>

      <div className="grid md:grid-cols-2 gap-2 my-4">
        <DatePicker
          value={values.appeared_from}
          label="Appeared From"
          placeholder="YYYY-MM-DD"
          format="YYYY/MM/DD"
          name="appeared_from"
          error={errors?.appeared_from}
          onChange={(date) => setFieldValue("appeared_from", date)}
        />
        <DatePicker
          value={values.appeared_to}
          label="Appeared To"
          placeholder="YYYY-MM-DD"
          format="YYYY/MM/DD"
          name="appeared_to"
          error={errors?.appeared_to}
          onChange={(date) => setFieldValue("appeared_to", date)}
        />
      </div>

      <div className="flex my-4 justify-end">
        <Button type="submit" className="btn-primary btn-sm">
          Continue
        </Button> 
      </div>
    </>
  );
};

export default General;