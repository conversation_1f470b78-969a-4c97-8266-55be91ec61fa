import React from "react";
import InputField from "@/components/ui/InputField";
import Textarea from "@/components/ui/Textarea";
import Select from "@/components/ui/Select";
import Button from "@/components/ui/Button";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import { Icon } from "@iconify/react";
import Card from "@/components/ui/Card";

const General = ({ values, errors, touched, setFieldValue, setFieldTouched, menuList, handleSubmit, isSubmitting }) => {
  const subMenuItems =
    menuList?.find(
      (item) => item.id === parseInt(values.category_id)
    )?.sub_categories || [];

  const handleCategoryChange = (e) => {
    setFieldValue("category_id", e.target.value);
    setFieldValue("sub_category_id", "");
  };

  const handleInputChange = (e, fieldName) => {
    setFieldValue(fieldName, e.target.value);
    if (errors[fieldName]) {
      setFieldTouched(fieldName, false);
    }
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="border-b border-gray-200 dark:border-gray-700 pb-3">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">Course Setup</h2>
        <p className="text-gray-500 dark:text-gray-400 text-sm">
          Basic information about your course
        </p>
      </div>

      {/* Course Information Section */}
      <Card className="">
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Course Information</h3>
          <p className="text-gray-500 dark:text-gray-400 text-sm">
            Provide essential details about your course
          </p>
        </div>

        <div className="space-y-4">
          <InputField
            label="Course Title"
            name="title"
            type="text"
            placeholder="e.g., Advanced Web Development with React"
            required
            value={values.title}
            onChange={(e) => handleInputChange(e, "title")}
            className="w-full"
            icon="heroicons:academic-cap"
          />

          <Textarea
            value={values.description}
            label="Course Description"
            placeholder="Describe what students will learn, course objectives, and key takeaways..."
            name="description"
            rows={5}
            error={touched.description && errors.description}
            onChange={(e) => handleInputChange(e, "description")}
            onBlur={() => setFieldTouched("description", true)}
            className="w-full"
            helperText="This will be displayed on the course landing page"
          />
        </div>
      </Card>

      {/* Category Selection Section */}
      <Card className="">
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Categorization</h3>
          <p className="text-gray-500 dark:text-gray-400 text-sm">
            Help students find your course by selecting appropriate categories
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <Select
            label="Main Category"
            placeholder="Select primary category"
            options={menuList?.map((item) => ({
              label: item.name,
              value: item.id.toString(),
              icon: "heroicons:folder"
            }))}
            name="category_id"
            value={values.category_id}
            error={touched.category_id && errors.category_id}
            onChange={handleCategoryChange}
            onBlur={() => setFieldTouched("category_id", true)}
            required
            icon="heroicons:tag"
          />

          <Select
            label="Subcategory"
            placeholder={values.category_id ? "Select subcategory" : "Select main category first"}
            options={subMenuItems.map((item) => ({
              label: item.name,
              value: item.id.toString(),
              icon: "heroicons:folder-open"
            }))}
            name="sub_category_id"
            value={values.sub_category_id}
            error={touched.sub_category_id && errors.sub_category_id}
            onChange={(e) => handleInputChange(e, "sub_category_id")}
            onBlur={() => setFieldTouched("sub_category_id", true)}
            disabled={!values.category_id}
            icon="heroicons:tag"
          />
        </div>
      </Card>

      {/* Duration Section */}
      <Card className="">
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Course Duration</h3>
          <p className="text-gray-500 dark:text-gray-400 text-sm">
            Set the availability period for your course
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <DatePicker
            value={values.appeared_from}
            label="Start Date"
            placeholder="Select course start date"
            format="MMMM D, YYYY"
            name="appeared_from"
            error={touched.appeared_from && errors.appeared_from}
            onChange={(date) => {
              setFieldValue("appeared_from", date);
              setFieldTouched("appeared_from", true);
            }}
            className="w-full"
            icon="heroicons:calendar"
            helperText="When the course will become available"
          />

          <DatePicker
            value={values.appeared_to}
            label="End Date"
            placeholder="Select course end date"
            format="MMMM D, YYYY"
            name="appeared_to"
            error={touched.appeared_to && errors.appeared_to}
            onChange={(date) => {
              setFieldValue("appeared_to", date);
              setFieldTouched("appeared_to", true);
            }}
            className="w-full"
            icon="heroicons:calendar"
            helperText="When the course will become unavailable"
            minDate={values.appeared_from}
          />
        </div>
      </Card>

      {/* Submit Section */}
      <div className="flex justify-end items-end border-t border-gray-200 dark:border-gray-700 pt-4">
        
        <Button
          type="submit"
          className="px-8 py-3 btn btn-primary btn-sm"
          variant="primary"
          disabled={isSubmitting}
          loading={isSubmitting}
          loadingText="Saving..."
        >
          Continue to Pricing
          <Icon icon="heroicons:arrow-right" className="ml-2 w-5 h-5" />
        </Button>
      </div>
    </div>
  );
};

export default General;