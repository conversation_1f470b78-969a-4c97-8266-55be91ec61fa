import React, { useEffect } from "react";
import Input<PERSON>ield from "@/components/ui/InputField";
import Button from "@/components/ui/Button";
import Switch from "@/components/ui/Switch";

const Pricing = ({ values, errors, setFieldValue, isSubmitting }) => {
  useEffect(() => {
    if (values.is_free) {
      // Set pricing fields to 0 when is_free is true
      setFieldValue("regular_price", 0);
      setFieldValue("sale_price", 0);
      setFieldValue("discount_percentage", 0);
    }
  }, [values.is_free, setFieldValue]);

  // Set default currency if not already set
  useEffect(() => {
    if (!values.currency) {
      setFieldValue("currency", "BDT");
    }
  }, [values.currency, setFieldValue]);

  return (
    <>
      {/* Free Switch */}
      <div className="flex gap-8 my-auto">
        <Switch
          label="Free"
          labelClass="my-5"
          activeClass="bg-success-500 my-5"
          value={values.is_free} // Controlled by Formik's state
          name="is_free"
          onChange={() => setFieldValue("is_free", !values.is_free)} // Toggle the value
        />

        <Switch
          label="Show Price"
          labelClass="my-5"
          activeClass="bg-success-500 my-5"
          value={values.show_price} // Controlled by Formik's state
          name="show_price"
          onChange={() => setFieldValue("show_price", !values.show_price)} // Toggle the value
        />
      </div>

      {/* Pricing Fields (Hidden when Free is enabled) */}
      {!values.is_free && (
        <>
          <div className="flex items-center mb-4">
            <div className="flex items-center mr-4">
              <input
                id="Monthly"
                className="mr-2"
                type="radio"
                name="installment_type"
                value="Monthly"
                onChange={() => setFieldValue("installment_type", "Monthly")}
                checked={values.installment_type === "Monthly"}
              />
              <label htmlFor="Monthly" className="text-sm">
                Monthly
              </label>
            </div>
            <div className="flex items-center mr-4">
              <input
                id="OneTime"
                className="mr-2"
                type="radio"
                name="installment_type"
                value="One Time"
                onChange={() => setFieldValue("installment_type", "One Time")}
                checked={values.installment_type === "One Time"}
              />
              <label htmlFor="OneTime" className="text-sm">
                One Time
              </label>
            </div>
            <div className="flex items-center">
              <input
                id="Installment"
                className="mr-2"
                type="radio"
                name="installment_type"
                value="Installment"
                onChange={() =>
                  setFieldValue("installment_type", "Installment")
                }
                checked={values.installment_type === "Installment"}
              />
              <label htmlFor="Installment" className="text-sm">
                Installment
              </label>
            </div>
          </div>

          {/* Currency Selection */}
          <div className="flex items-center mb-4">
            <h3 className="text-sm font-medium mr-4">Currency:</h3>
            <div className="flex items-center mr-4">
              <input
                id="BDT"
                className="mr-2"
                type="radio"
                name="currency"
                value="BDT"
                onChange={() => setFieldValue("currency", "BDT")}
                checked={values.currency === "BDT"}
              />
              <label htmlFor="BDT" className="text-sm">
                BDT
              </label>
            </div>
            <div className="flex items-center mr-4">
              <input
                id="AED"
                className="mr-2"
                type="radio"
                name="currency"
                value="AED"
                onChange={() => setFieldValue("currency", "AED")}
                checked={values.currency === "AED"}
              />
              <label htmlFor="AED" className="text-sm">
                AED
              </label>
            </div>

            <div className="flex items-center mr-4">
              <input
                id="USD"
                className="mr-2"
                type="radio"
                name="currency"
                value="USD"
                onChange={() => setFieldValue("currency", "USD")}
                checked={values.currency === "USD"}
              />
              <label htmlFor="USD" className="text-sm">
                USD
              </label>
            </div>

            <div className="flex items-center mr-4">
              <input
                id="EUR"
                className="mr-2"
                type="radio"
                name="currency"
                value="EUR"
                onChange={() => setFieldValue("currency", "EUR")}
                checked={values.currency === "EUR"}
              />
              <label htmlFor="EUR" className="text-sm">
                EUR
              </label>
            </div>


            <div className="flex items-center mr-4">
              <input
                id="GBP"
                className="mr-2"
                type="radio"
                name="currency"
                value="GBP"
                onChange={() => setFieldValue("currency", "GBP")}
                checked={values.currency === "GBP"}
              />
              <label htmlFor="GBP" className="text-sm">
                GBP
              </label>
            </div>

            <div className="flex items-center mr-4">
              <input
                id="S.Fr"
                className="mr-2"
                type="radio"
                name="currency"
                value="S.Fr"
                onChange={() => setFieldValue("currency", "S.Fr")}
                checked={values.currency === "S.Fr"}
              />
              <label htmlFor="S.Fr" className="text-sm">
                S.Fr
              </label>
            </div>

          </div>
          {values.installment_type != "Monthly" && (
            <>
              <InputField
                className="py-4"
                label={`Regular Price (${values.currency || 'BDT'})`}
                name="regular_price"
                type="number"
                placeholder={`Regular Price in ${values.currency || 'BDT'}`}
                value={values.regular_price}
                onChange={(e) => {
                  const regularPrice = e.target.value;
                  const salePrice =
                    regularPrice -
                    (regularPrice * values.discount_percentage) / 100;
                  setFieldValue("regular_price", regularPrice);
                  setFieldValue("sale_price", salePrice);
                }}
              />

              <InputField
                className="py-4"
                label="Discount Percentage"
                name="discount_percentage"
                type="number"
                placeholder="Discount Percentage"
                value={values.discount_percentage}
                onChange={(e) => {
                  const discountPercentage = e.target.value;
                  const salePrice =
                    values.regular_price -
                    (values.regular_price * discountPercentage) / 100;
                  setFieldValue("discount_percentage", discountPercentage);
                  setFieldValue("sale_price", salePrice);
                }}
              />
              <InputField
                className="py-4"
                label={`Sale Price (${values.currency || 'BDT'})`}
                name="sale_price"
                type="number"
                placeholder={`Sale Price in ${values.currency || 'BDT'}`}
                value={values.sale_price}
                onChange={(e) => {
                  const salePrice = e.target.value;
                  const discountPercentage = values.regular_price
                    ? ((values.regular_price - salePrice) /
                        values.regular_price) *
                      100
                    : 0;
                  setFieldValue("sale_price", salePrice);
                  setFieldValue("discount_percentage", discountPercentage);
                }}
              />

              {values.installment_type === "Installment" && (
                <>
                  <InputField
                    className="py-4"
                    label="Installment Number"
                    name="max_installment_qty"
                    type="number"
                    placeholder="Installment Number"
                    value={values.max_installment_qty}
                    onChange={(e) =>
                      setFieldValue("max_installment_qty", e.target.value)
                    }
                  />

                  <InputField
                    className="py-4"
                    label={`Admission Fee (${values.currency || 'BDT'})`}
                    name="minimum_enroll_amount"
                    type="number"
                    placeholder={`Admission Fee in ${values.currency || 'BDT'}`}
                    value={values.minimum_enroll_amount}
                    onChange={(e) => setFieldValue("minimum_enroll_amount", e.target.value)}
                  />
                </>
              )}
            </>
          )}
          {values.installment_type === "Monthly" && (
            <>
              <InputField
                className="py-4"
                label={`Monthly Amount (${values.currency || 'BDT'})`}
                name="monthly_amount"
                type="number"
                placeholder={`Monthly Amount in ${values.currency || 'BDT'}`}
                value={values.monthly_amount}
                onChange={(e) =>
                  setFieldValue("monthly_amount", e.target.value)
                }
              />
              <InputField
                className="py-4"
                label={`Admission Fee (${values.currency || 'BDT'})`}
                name="minimum_enroll_amount"
                type="number"
                placeholder={`Admission Fee in ${values.currency || 'BDT'}`}
                value={values.minimum_enroll_amount}
                onChange={(e) => setFieldValue("minimum_enroll_amount", e.target.value)}
              />
            </>
          )}

        </>
      )}


      {/* Submit Button */}
      <div className="flex my-4 justify-end">
        <Button
          type="submit"
          className="btn-primary btn-sm"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Saving..." : "Save Course"}
        </Button>
      </div>
    </>
  );
};

export default Pricing;
