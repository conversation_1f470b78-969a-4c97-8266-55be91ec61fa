import React, { useEffect } from "react";
import InputField from "@/components/ui/InputField";
import Button from "@/components/ui/Button";
import Switch from "@/components/ui/Switch";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import { Icon } from "@iconify/react";

const Pricing = ({ values, errors, setFieldValue, isSubmitting }) => {
  const { data: orgCurrenciesData } = useGetApiQuery("admin/org-currencies");
  const assignedCurrencies = orgCurrenciesData || [];

  const fallbackCurrencies = [
    { currency: { code: "BDT", name: "Bangladeshi Taka", symbol: "৳" } },
    { currency: { code: "USD", name: "US Dollar", symbol: "$" } },
    { currency: { code: "AED", name: "UAE Dirham", symbol: "د.إ" } }
  ];

  const availableCurrencies = assignedCurrencies.length > 0 ? assignedCurrencies : fallbackCurrencies;

  useEffect(() => {
    if (values.is_free) {
      setFieldValue("regular_price", 0);
      setFieldValue("sale_price", 0);
      setFieldValue("discount_percentage", 0);
    }
  }, [values.is_free, setFieldValue]);

  useEffect(() => {
    if (!values.currency && availableCurrencies.length > 0) {
      setFieldValue("currency", availableCurrencies[0].currency.code);
    }
  }, [values.currency, setFieldValue, availableCurrencies]);

  return (
    <div className="space-y-8">
      {/* Section: Pricing Options */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-sm p-6">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Pricing Options</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Switch
            label="Free Course"
            description="Enable to offer this course for free"
            labelClass="text-sm font-medium text-gray-700 dark:text-gray-300"
            activeClass="bg-success-500"
            value={values.is_free}
            name="is_free"
            onChange={() => setFieldValue("is_free", !values.is_free)}
          />
          <Switch
            label="Show Price"
            description="Display pricing information to students"
            labelClass="text-sm font-medium text-gray-700 dark:text-gray-300"
            activeClass="bg-success-500"
            value={values.show_price}
            name="show_price"
            onChange={() => setFieldValue("show_price", !values.show_price)}
          />
        </div>
      </div>

      {/* Section: Pricing Details */}
      {!values.is_free && (
        <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-sm p-6 space-y-8">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Pricing Details</h3>

          {/* Payment Type */}
          <div>
            <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">Payment Type</h4>
            <div className="flex flex-wrap gap-3">
              {["Monthly", "One Time", "Installment"].map((type) => (
                <label
                  key={type}
                  className={`flex items-center px-4 py-2 rounded-lg border cursor-pointer transition-colors ${
                    values.installment_type === type
                      ? "border-primary-500 bg-primary-50 dark:bg-primary-900/30"
                      : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
                  }`}
                >
                  <input
                    type="radio"
                    className="sr-only"
                    name="installment_type"
                    value={type}
                    checked={values.installment_type === type}
                    onChange={() => setFieldValue("installment_type", type)}
                  />
                  <span className="text-sm font-medium text-gray-800 dark:text-gray-200">
                    {type}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Currency Selection */}
          <div>
            <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">Select Currency</h4>
            <div className="flex flex-wrap gap-3">
              {availableCurrencies.map((orgCurrency) => (
                <label
                  key={orgCurrency.currency.code}
                  className={`flex items-center px-4 py-2 rounded-lg border cursor-pointer transition-colors ${
                    values.currency === orgCurrency.currency.code
                      ? "border-primary-500 bg-primary-50 dark:bg-primary-900/30"
                      : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
                  }`}
                >
                  <input
                    type="radio"
                    className="sr-only"
                    name="currency"
                    value={orgCurrency.currency.code}
                    checked={values.currency === orgCurrency.currency.code}
                    onChange={() => setFieldValue("currency", orgCurrency.currency.code)}
                  />
                  <span className="text-sm font-medium text-gray-800 dark:text-gray-200">
                    {orgCurrency.currency.symbol} ({orgCurrency.currency.code})
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Regular/Sale/Discount Price */}
          {values.installment_type !== "Monthly" && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <InputField
                label={`Regular Price (${values.currency || 'BDT'})`}
                name="regular_price"
                type="number"
                placeholder="0.00"
                value={values.regular_price}
                prefix={getCurrencySymbol(values.currency)}
                onChange={(e) => {
                  const regularPrice = e.target.value;
                  const salePrice = regularPrice - (regularPrice * values.discount_percentage) / 100;
                  setFieldValue("regular_price", regularPrice);
                  setFieldValue("sale_price", salePrice);
                }}
              />
              <InputField
                label="Discount Percentage"
                name="discount_percentage"
                type="number"
                placeholder="0"
                value={values.discount_percentage}
                suffix="%"
                onChange={(e) => {
                  const discountPercentage = e.target.value;
                  const salePrice = values.regular_price - (values.regular_price * discountPercentage) / 100;
                  setFieldValue("discount_percentage", discountPercentage);
                  setFieldValue("sale_price", salePrice);
                }}
              />
              <InputField
                label={`Sale Price (${values.currency || 'BDT'})`}
                name="sale_price"
                type="number"
                placeholder="0.00"
                value={values.sale_price}
                prefix={getCurrencySymbol(values.currency)}
                onChange={(e) => {
                  const salePrice = e.target.value;
                  const discountPercentage = values.regular_price
                    ? ((values.regular_price - salePrice) / values.regular_price) * 100
                    : 0;
                  setFieldValue("sale_price", salePrice);
                  setFieldValue("discount_percentage", discountPercentage);
                }}
              />
            </div>
          )}

          {/* Installment Specific Fields */}
          {values.installment_type === "Installment" && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <InputField
                label="Number of Installments"
                name="max_installment_qty"
                type="number"
                placeholder="e.g., 3"
                value={values.max_installment_qty}
                onChange={(e) => setFieldValue("max_installment_qty", e.target.value)}
              />
              <InputField
                label={`Admission Fee (${values.currency || 'BDT'})`}
                name="minimum_enroll_amount"
                type="number"
                placeholder="0.00"
                value={values.minimum_enroll_amount}
                prefix={getCurrencySymbol(values.currency)}
                onChange={(e) => setFieldValue("minimum_enroll_amount", e.target.value)}
              />
            </div>
          )}

          {/* Monthly Specific Fields */}
          {values.installment_type === "Monthly" && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <InputField
                label={`Monthly Amount (${values.currency || 'BDT'})`}
                name="monthly_amount"
                type="number"
                placeholder="0.00"
                value={values.monthly_amount}
                prefix={getCurrencySymbol(values.currency)}
                onChange={(e) => setFieldValue("monthly_amount", e.target.value)}
              />
              <InputField
                label={`Admission Fee (${values.currency || 'BDT'})`}
                name="minimum_enroll_amount"
                type="number"
                placeholder="0.00"
                value={values.minimum_enroll_amount}
                prefix={getCurrencySymbol(values.currency)}
                onChange={(e) => setFieldValue("minimum_enroll_amount", e.target.value)}
              />
            </div>
          )}
        </div>
      )}

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button
          type="submit"
          className="px-6 py-3 btn btn-primary btn-sm"
          disabled={isSubmitting}
          variant="primary"
        >
          {isSubmitting ? (
            <>
              <Icon icon="heroicons:arrow-path" className="w-5 h-5 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            "Save Course"
          )}
        </Button>
      </div>
    </div>
  );
};

const getCurrencySymbol = (currencyCode) => {
  const symbols = {
    BDT: '৳',
    USD: '$',
    AED: 'د.إ',
    EUR: '€',
    GBP: '£',
  };
  return symbols[currencyCode] || currencyCode;
};

export default Pricing;
