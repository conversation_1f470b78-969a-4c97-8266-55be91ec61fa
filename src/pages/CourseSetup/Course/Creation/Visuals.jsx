import React from "react";
import InputField from "@/components/ui/InputField";
import Button from "@/components/ui/Button";

const Visuals = ({ values, errors, setFieldValue, preview, setPreview }) => {
  const handleImageChange = (e) => {
    const file = e.target.files[0];
    setFieldValue("thumbnail", file);
    
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result);
      };
      reader.readAsDataURL(file);
    } else {
      setPreview(null);
    }
  };

  return (
    <>
      <InputField
        className="py-4"
        label="Promotional Video Link"
        name="youtube_url"
        type="text"
        placeholder="Enter Youtube Video Link"
        value={values.youtube_url}
        onChange={(e) => {
          let url = '';
          const value = e.target.value;
          const youtubeLink = value.replace("https://youtu.be/", "https://www.youtube.com/watch?v=");
          if(youtubeLink.startsWith('https://www.youtube.com/watch?v=')){
            url = youtubeLink;
          } else {
            const params = new URLSearchParams(value.split("?")[1]);
            const si = params.get("si");
            const link = si ? `${youtubeLink}&si=${si}` : youtubeLink;
            url = 'https://www.youtube.com/watch?' + youtubeLink.split("?")[1];
          }
          setFieldValue("youtube_url", url);
        }}
        error={errors.youtube_url}
      />

      <div className="xl:col-span-2 col-span-1 pt-4">
        <div className="border-dashed border-2 border-gray-300 p-4 rounded-lg flex flex-col items-center">
          {preview ? (
            <img
              src={preview}
              alt="Preview"
              className="w-auto h-40 object-cover mb-4"
            />
          ) : (
            <div className="text-gray-400 flex flex-col items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-12 w-12 mb-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 7h18M3 12h18m-9 5h9"
                />
              </svg>
              <p className="text-center">Upload Course Image</p>
              <p className="text-xs text-gray-500">
                Recommended size: 400 x 400 px (PNG or JPG)
              </p>
            </div>
          )}
          <input
            id="thumbnail"
            name="thumbnail"
            type="file"
            accept="image/*"
            className="hidden"
            onChange={handleImageChange}
          />
          <button
            type="button"
            onClick={() => document.getElementById("thumbnail").click()}
            className="mt-4 px-4 py-2 border border-blue-500 text-blue-500 rounded hover:bg-blue-500 hover:text-white transition"
          >
            Select file
          </button>
          {errors.thumbnail && (
            <p className="text-red-500 text-sm mt-2">{errors.thumbnail}</p>
          )}
        </div>
      </div>

      <div className="flex my-4 justify-end">
        <Button type="submit" className="btn-primary btn-sm">
          Continue
        </Button>
      </div>
    </>
  );
};

export default Visuals;