import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Card from "@/components/ui/Card";
import Icon from "@/components/ui/Icon";
import Tooltip from "@/components/ui/Tooltip";
import Button from "@/components/ui/Button";
import Create from "./create";
import Edit from "./edit";
import Delete from "./Delete";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";
import { useParams } from "react-router-dom";
import Dropdown from "@/components/ui/Dropdown";
import { Menu } from "@headlessui/react";
import CreateVideo from "../../../RawContent/VideoContentList/createVideo";
import ViewVideo from "../../../RawContent/VideoContentList/viewVideo";
import ViewScript from "../../../RawContent/ScriptList/viewScript";
import CreateScript from "../../../RawContent/ScriptList/createScript";
import CreateQuiz from "../../../RawContent/QuizList/createQuiz";

import { useNavigate } from "react-router-dom";
const CourseCategoryPage = ({ data }) => {
  const navigate = useNavigate();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [showVideoModal, setShowVideoModal] = useState(false);
  const [showScriptModal, setShowScriptModal] = useState(false);
  const [showQuizModal, setShowQuizModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const [showViewVideoModal, setShowViewVideoModal] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState(false);

  const [showViewScriptModal, setShowViewScriptModal] = useState(false);
  const [selectedScript, setSelectedScript] = useState(false);

  const dispatch = useDispatch();
  const { id } = useParams();

  const actions = [
    {
      name: "Edit",
      icon: "lucide:edit",
      onClick: (val) => {
        dispatch(setEditData(data.data[val]));
        dispatch(setEditShowModal(true));
      },
    },

    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  return (
    <Card className="flex flex-col mt-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-semibold">Course Outline</h2>
        <Button
          text="Add Outline"
          onClick={() => setIsSidebarOpen(true)}
          className="btn-primary btn-sm"
        />
      </div>
      <didv className="grid gap-2">
        {!data.length && (
          <p className="text-sm text-red-500">No outline has been added</p>
        )}
        {data?.map((item) => (
          <div key={item.id}>
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">{item.name}</h3>
              <div className="">
                <Dropdown
                  classMenuItems="right-0 w-[140px] top-[100%] "
                  label={
                    <Button text="Add Content" className="btn-primary btn-sm" />
                  }
                >
                  <div className="divide-y divide-slate-100 dark:divide-slate-800">
                    <Menu.Item>
                      <div
                        className={`
                        w-full border-b border-b-gray-500 border-opacity-10 px-4 py-2 text-sm  last:mb-0 cursor-pointer 
                        first:rounded-t last:rounded-b flex space-x-2 items-center rtl:space-x-reverse `}
                        onClick={() => {
                          setSelectedCategory(item);
                          setShowVideoModal(true);
                        }}
                      >
                        <span className="text-base">
                          {/* <Icon icon={item.icon} /> */}
                        </span>
                        <span>Video </span>
                      </div>
                    </Menu.Item>
                    <Menu.Item>
                      <div
                        className={`
                        w-full border-b border-b-gray-500 border-opacity-10 px-4 py-2 text-sm  last:mb-0 cursor-pointer 
                        first:rounded-t last:rounded-b flex space-x-2 items-center rtl:space-x-reverse `}
                        onClick={() => {
                          setSelectedCategory(item);
                          setShowScriptModal(true);
                        }}
                      >
                        <span className="text-base">
                          {/* <Icon icon={item.icon} /> */}
                        </span>
                        <span>Script </span>
                      </div>
                    </Menu.Item>
                    <Menu.Item>
                      <div
                        className={`
                        w-full border-b border-b-gray-500 border-opacity-10 px-4 py-2 text-sm  last:mb-0 cursor-pointer 
                        first:rounded-t last:rounded-b flex space-x-2 items-center rtl:space-x-reverse `}
                        onClick={() => {
                          setSelectedCategory(item);
                          setShowQuizModal(true);
                        }}
                      >
                        <span className="text-base">
                          {/* <Icon icon={item.icon} /> */}
                        </span>
                        <span>Quiz </span>
                      </div>
                    </Menu.Item>
                  </div>
                </Dropdown>
              </div>
            </div>
            <div className="flex items-center justify-between">
              {item.course_outlines_count == 0 && (
                <p className="text-red-500 text-sm">
                  {" "}
                  No content has been added
                </p>
              )}

              <div className="grid grid-cols-1 space-y-2 mt-2 gap-2">
                {item.course_outlines.map((outline, index) => (
                  <div
                    className={`flex space-x-2 items-center ${
                      index != item.course_outlines.length - 1 &&
                      "border-b pb-3"
                    }`}
                    key={outline.id}
                  >
                    {outline.video && (
                      <Tooltip
                        content={"Watch : " + outline.video.title}
                        placement="top"
                      >
                        <div
                          className="cursor-pointer flex gap-4"
                          onClick={() => {
                            setSelectedVideo(outline.video);
                            setShowViewVideoModal(true);
                          }}
                        >
                          {outline.video.thumbnail ? (
                            <img
                              src={
                                import.meta.env.VITE_ASSET_HOST_URL +
                                outline.video.thumbnail
                              }
                              className="h-10 w-10 rounded-full"
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-full border-2 border-red-500 text-red-500 flex items-center justify-center ">
                              <Icon icon="bx:play-circle" />
                            </div>
                          )}

                          <div className="flex-1 text-sm">
                            <b>{outline.video.title}</b>

                            <p>{outline.video.description}</p>
                          </div>
                        </div>
                      </Tooltip>
                    )}
                    {outline.script && (
                      <Tooltip
                        content={"Watch : " + outline.script.title}
                        placement="top"
                      >
                        <div
                          className="cursor-pointer flex gap-4"
                          onClick={() => {
                            setSelectedScript(outline.script);
                            setShowViewScriptModal(true);
                          }}
                        >
                          {outline.script.thumbnail ? (
                            <img
                              src={
                                import.meta.env.VITE_ASSET_HOST_URL +
                                outline.script?.thumbnail
                              }
                              className="h-10 w-10 rounded-full"
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-full border-2 border-red-500 text-red-500 flex items-center justify-center ">
                              <Icon icon="carbon:document" />
                            </div>
                          )}

                          <div className="flex-1 text-sm">
                            <b>{outline.script.title}</b>
                            <p> {outline.script.description}</p>
                          </div>
                        </div>
                      </Tooltip>
                    )}
                    {outline.quiz && (
                      <Tooltip
                        content={"Details : " + outline.quiz.title}
                        placement="top"
                      >
                        <div
                          className="cursor-pointer flex gap-4"
                          onClick={() => {
                            navigate("/quiz-details/" + outline.quiz.id);
                          }}
                        >
                          {outline.quiz.thumbnail ? (
                            <img
                              src={
                                import.meta.env.VITE_ASSET_HOST_URL +
                                outline.quiz?.thumbnail
                              }
                              className="h-10 w-10 rounded-full"
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-full border-2 border-red-500 text-red-500 flex items-center justify-center ">
                              <Icon icon="carbon:document" />
                            </div>
                          )}

                          <div className="flex-1 text-sm">
                            <b>{outline.quiz.title}</b>

                            <p> {outline.quiz.description}</p>
                          </div>
                        </div>
                      </Tooltip>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </didv>

      {isSidebarOpen && (
        <Create
          isSidebarOpen={isSidebarOpen}
          setIsSidebarOpen={setIsSidebarOpen}
        />
      )}

      {/* {showModal && (
        <Create showModal={showModal} setShowModal={setShowModal} />
      )} */}
      {/* {showVideoModal && (
        <CreateVideo
          showModal={showVideoModal}
          setShowModal={setShowVideoModal}
          category={selectedCategory}
        />
      )} */}

      {showVideoModal && (
        <CreateVideo
          isSidebarOpen={showVideoModal}
          setIsSidebarOpen={setShowVideoModal}
          category={selectedCategory}
        />
      )}

      {showScriptModal && (
        <CreateScript
          isSidebarOpen={showScriptModal}
          setIsSidebarOpen={setShowScriptModal}
          category={selectedCategory}
        />
      )}
      {showQuizModal && (
        <CreateQuiz
          isSidebarOpen={showQuizModal}
          setIsSidebarOpen={setShowQuizModal}
          category={selectedCategory}
        />
      )}

      {showViewVideoModal && (
        <ViewVideo
          showModal={showViewVideoModal}
          setShowModal={setShowViewVideoModal}
          content={selectedVideo}
        />
      )}

      {showViewScriptModal && (
        <ViewScript
          showModal={showViewScriptModal}
          setShowModal={setShowViewScriptModal}
          content={selectedScript}
        />
      )}
      <Delete
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />
    </Card>
  );
};

export default CourseCategoryPage;
