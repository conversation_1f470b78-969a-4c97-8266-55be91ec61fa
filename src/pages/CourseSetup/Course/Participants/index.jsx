import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import CreateParticipant from "./createParticipant";
import EditParticipant from "./editParticipant";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import { useParams } from "react-router-dom";


const index = () => {
  const [showModal, setShowModal] = useState(false);
  const dispatch = useDispatch();

  const { id } = useParams();
  const res = useGetApiQuery('admin/student-Participant-list-by-course-id/' + id);
  console.log(res.data);
  const data = res.data;
  const columns = [
    {
      label: "SL",
      field: "id",
    },
    {
      label: "Name",
      field: "name",
    },
    {
      label: "Email",
      field: "email",
    },
    {
      label: "Contact No",
      field: "contact_no",
    }
  
  ];

  const tableData = data?.student_list?.map((item, index) => {
    return {
      id: item.id,
      name: item.name,
      email: item.email,
      contact_no: item.contact_no,
    };
  });


  const handleSubmit = () => {
    setShowModal(false);
  };

  // const createPage = <CreateParticipant />;
  // const editPage = <EditParticipant />;

  return (
    <div>
      
        <BasicTablePage
          title="Course Participant"
          submitForm={handleSubmit}
          columns={columns}
          data={tableData}
          // changePage={changePage}
          // currentPage={data?.current_page}
          // totalPages={Math.ceil(data?.total / data?.per_page)}
        />
    
    </div>
  );
};

export default index;
