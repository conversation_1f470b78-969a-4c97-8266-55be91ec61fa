import React, { useState } from 'react';
import { useGetApiQuery, usePostApiMutation } from '@/store/api/master/commonSlice';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Icon from '@/components/ui/Icon';
import Loading from '@/components/Loading';
import { toast } from 'react-toastify';
import EnrollmentModal from './EnrollmentModal';

const Students = ({ course }) => {
  const [showEnrollModal, setShowEnrollModal] = useState(false);
  const [postApi, { isLoading: isEnrolling }] = usePostApiMutation();

  const {
    data: studentData,
    isLoading,
    refetch
  } = useGetApiQuery(`admin/student-Participant-list-by-course-id/${course?.id}`);


  console.log(studentData);

  const handleEnrollmentSuccess = () => {
    refetch(); // Refresh the student list
    setShowEnrollModal(false);
  };

  if (isLoading) {
    return <Loading />;
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h4 className="text-xl font-medium">
          Students Enrolled in {studentData?.course_name || course?.title}
        </h4>
        <Button
          text="Enroll Student"
          icon="heroicons-outline:user-add"
          btnClass="btn-primary"
          onClick={() => setShowEnrollModal(true)}
        />
      </div>

      {studentData?.student_list?.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {studentData?.student_list.map((student) => (
            <Card key={student.id} className="hover:shadow-md transition-shadow duration-200">
              <div className="flex items-center p-2">
                <div className="rounded-full w-12 h-12 bg-slate-100 flex items-center justify-center mr-3">
                  {student.image ? (
                    <img
                      src={import.meta.env.VITE_ASSET_HOST_URL + student.image}
                      className="rounded-full w-full h-full object-cover"
                      alt={student.name}
                    />
                  ) : (
                    <Icon icon="heroicons-outline:user" className="text-2xl text-slate-400" />
                  )}
                </div>
                <div className="flex flex-col overflow-hidden flex-1">
                  <span className="font-semibold text-sm text-gray-800 truncate">
                    {student.name}
                  </span>
                  <span className="text-xs text-gray-600 truncate">
                    {student.email || 'No Email'}
                  </span>
                  <span className="text-xs text-gray-600">
                    {student.contact_no || 'No Contact'}
                  </span>
                </div>
                <div className="flex flex-col space-y-2">
                  <button
                    className="text-slate-600 hover:text-primary-500"
                    title="View Student Details"
                  >
                    <Icon icon="heroicons-outline:eye" className="text-lg" />
                  </button>
                  <button
                    className="text-slate-600 hover:text-danger-500"
                    title="Remove from Course"
                    onClick={() => {
                      // Implement remove functionality
                      toast.info('Remove functionality to be implemented');
                    }}
                  >
                    <Icon icon="heroicons-outline:trash" className="text-lg" />
                  </button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <div className="flex flex-col items-center justify-center py-12">
            <Icon icon="heroicons-outline:user-group" className="text-5xl text-slate-300 mb-4" />
            <h5 className="text-lg font-medium text-slate-600 mb-2">No Students Enrolled</h5>
            <p className="text-sm text-slate-500 mb-6">
              There are no students enrolled in this course yet.
            </p>
            <Button
              text="Enroll First Student"
              icon="heroicons-outline:user-add"
              btnClass="btn-outline-primary"
              onClick={() => setShowEnrollModal(true)}
            />
          </div>
        </Card>
      )}

      {showEnrollModal && (
        <EnrollmentModal
          courseId={course?.id}
          onClose={() => setShowEnrollModal(false)}
          onSuccess={handleEnrollmentSuccess}
          postApi={postApi}
          isEnrolling={isEnrolling}
        />
      )}
    </div>
  );
};

export default Students;
