import React from 'react';
import { Formik, Form } from 'formik';
import SimpleBar from 'simplebar-react';
import Icon from '@/components/ui/Icon';
import InputField from '@/components/ui/InputField';
import Switch from '@/components/ui/Switch';
import Button from '@/components/ui/Button';
import Textarea from '@/components/ui/Textarea';
import Select from '@/components/ui/Select';
import Fileinput from '@/components/ui/Fileinput';
import DatePicker from "@/components/partials/common-dateTimePicker/Date";

const CourseEditForm = ({
  course,
  menuList,
  subMenuList,
  isFree,
  setIsFree,
  validationSchema,
  onSubmit,
  handleBackClick,
  setSubMenuList,
  isLoading,
}) => {
  const [showPrice, setShowPrice] = React.useState(true);

  const handleThumbnailChange = (e, setFieldValue) => {
    const file = e.currentTarget.files[0];
    if (file) {
      URL.createObjectURL(file);
      setFieldValue('thumbnail', file);
    }
  };

  return (
    <div className="fixed right-0 top-0 w-[450px] bg-white dark:bg-slate-800 h-screen z-[9999] shadow-base2 border border-slate-200 dark:border-slate-700 transition-all duration-150">
      <SimpleBar className="px-6 h-full">
        <header className="flex items-center justify-between border-b border-slate-100 dark:border-slate-700 -mx-6 px-6 py-[25px]">
          <div>
            <span className="block text-xl text-slate-900 font-medium dark:text-[#eee]">
              Edit
            </span>
          </div>
          <div
            className="cursor-pointer text-2xl text-slate-800 dark:text-slate-200"
            onClick={handleBackClick}
          >
            <Icon icon="heroicons-outline:x" />
          </div>
        </header>

        <Formik
          validationSchema={validationSchema}
          initialValues={{
            title: course?.title,
            category_id: course?.category_id,
            sub_category_id: course?.sub_category_id,
            sale_price: course?.sale_price,
            regular_price: course?.regular_price,
            discount_percentage: course?.discount_percentage,
            currency: course?.currency || 'BDT',
            appeared_from: course?.appeared_from,
            appeared_to: course?.appeared_to,
            installment_type: course?.installment_type || 'One Time',
            monthly_amount: course?.monthly_amount || 0,
            minimum_enroll_amount: course?.minimum_enroll_amount || 0,
            max_installment_qty: course?.max_installment_qty || 0,
            course_duration: course?.course_duration || 0,
            duration_per_day: course?.duration_per_day || 0,
          }}
          onSubmit={onSubmit}
        >
          {({ values, errors, setFieldValue }) => {
            const [lastChangedField, setLastChangedField] = React.useState(null);

            const handleDiscountChange = (e) => {
              setLastChangedField('discount_percentage');
              setFieldValue('discount_percentage', e.target.value);
            };

            React.useEffect(() => {
              const rp = parseFloat(values.regular_price);
              const dp = parseFloat(values.discount_percentage);

              if (isFree) {
                setFieldValue('sale_price', '');
                setFieldValue('discount_percentage', '');
                return;
              }

              if (isNaN(rp) || rp <= 0) {
                setFieldValue('sale_price', '');
                if (lastChangedField !== 'sale_price') {
                  setFieldValue('discount_percentage', '');
                }
                return;
              }

              if (lastChangedField === 'discount_percentage' && !isNaN(dp) && dp >= 0 && dp <= 100) {
                const newSalePrice = rp - (dp * rp / 100);
                setFieldValue('sale_price', newSalePrice.toFixed(2));
              } else if (lastChangedField === null) {
                const sp = parseFloat(values.sale_price);
                if (!isNaN(sp) && sp <= rp && sp >= 0) {
                  const discount = ((rp - sp) / rp) * 100;
                  setFieldValue('discount_percentage', discount.toFixed(2));
                } else {
                  setFieldValue('sale_price', '');
                  setFieldValue('discount_percentage', '');
                }
              }
            }, [
              values.regular_price,
              values.discount_percentage,
              values.sale_price,
              isFree,
              showPrice,
              lastChangedField,
              setFieldValue,
            ]);

            return (
              <Form>
                <div className="grid md:grid-cols-1 gap-2">
                  <InputField
                    label="Title"
                    name="title"
                    type="text"
                    placeholder="Enter Title"
                    required
                  />
                </div>

                <div className="grid md:grid-cols-2 gap-2 my-2">
                  <Select
                    label="Select Category"
                    defaultValue={course?.category_id}
                    placeholder="Select Category"
                    options={menuList?.map((item) => ({
                      label: item.name,
                      value: item.id,
                    }))}
                    name="category_id"
                    onChange={(e) => {
                      setFieldValue('category_id', e.target.value);
                      setSubMenuList(
                        menuList?.find((item) => item.id == e.target.value)?.sub_categories
                      );
                    }}
                    error={errors.category_id}
                  />

                  {subMenuList?.length > 0 && (
                    <Select
                      label="Select Sub Category"
                      defaultValue={course?.sub_category_id}
                      placeholder="Select Sub Category"
                      options={subMenuList?.map((item) => ({
                        label: item.name,
                        value: item.id,
                      }))}
                      name="sub_category_id"
                      onChange={(e) => setFieldValue('sub_category_id', e.target.value)}
                    />
                  )}

                  <div className="my-auto py-2">
                    <Switch
                      label="Free"
                      activeClass="bg-success-500"
                      value={isFree}
                      name="is_free"
                      onChange={() => setIsFree(!isFree)}
                    />
                  </div>

                  <div className="my-auto py-2">
                    <Switch
                      label="Show Price"
                      activeClass="bg-info-500"
                      value={showPrice}
                      name="show_price"
                      onChange={() => setShowPrice(!showPrice)}
                    />
                  </div>

                  <Select
                    label="Currency"
                    name="currency"
                    defaultValue={values.currency || 'BDT'}
                    placeholder="Select Currency"
                    options={[
                      { label: 'BDT', value: 'BDT' },
                      { label: 'AED', value: 'AED' },
                      { label: 'USD', value: 'USD' },
                      { label: 'EUR', value: 'EUR' },
                      { label: 'GBP', value: 'GBP' },
                      { label: 'S.Fr', value: 'S.Fr' },
                    ]}
                    onChange={(e) => setFieldValue('currency', e.target.value)}
                  />

                  {!isFree && (
                    <>
                      <Select
                        label="Payment Type"
                        name="installment_type"
                        defaultValue={values.installment_type}
                        placeholder="Select Payment Type"
                        options={[
                          { label: 'One Time', value: 'One Time' },
                          { label: 'Monthly', value: 'Monthly' },
                          { label: 'Installment', value: 'Installment' },
                        ]}
                        onChange={(e) => {
                          const val = e.target.value;
                          setFieldValue("installment_type", val);

                          // Reset all payment-related fields
                          setFieldValue("regular_price", 0);
                          setFieldValue("discount_percentage", 0);
                          setFieldValue("sale_price", 0);
                          setFieldValue("monthly_amount", 0);
                          setFieldValue("minimum_enroll_amount", 0);
                          setFieldValue("max_installment_qty", 0);
                        }}
                      />

                      {values.installment_type !== 'Monthly' && (
                        <>
                          <InputField
                            label={`Regular Price (${values.currency || 'BDT'})`}
                            name="regular_price"
                            type="text"
                            placeholder={`Enter Regular Price in ${values.currency || 'BDT'}`}
                            onChange={(e) => {
                              const newRegularPrice = e.target.value;
                              setLastChangedField('regular_price');
                              setFieldValue('regular_price', newRegularPrice);

                              const parsedPrice = parseFloat(newRegularPrice);
                              if (!isNaN(parsedPrice) && parsedPrice > 0) {
                                setFieldValue('discount_percentage', 0);
                                setFieldValue('sale_price', parsedPrice);
                              } else {
                                setFieldValue('discount_percentage', 0);
                                setFieldValue('sale_price', 0);
                              }
                            }}
                            value={values.regular_price || ''}
                          />

                          <InputField
                            label="Discount %"
                            name="discount_percentage"
                            type="text"
                            placeholder="Enter Discount"
                            onChange={handleDiscountChange}
                            value={values.discount_percentage || ''}
                          />

                          <InputField
                            label={`Sale Price (${values.currency || 'BDT'})`}
                            name="sale_price"
                            type="text"
                            placeholder={`Sale Price in ${values.currency || 'BDT'}`}
                            readOnly
                            value={values.sale_price || ''}
                          />
                        </>
                      )}

                      {values.installment_type === 'Monthly' && (
                        <>
                          <InputField
                            className="py-4"
                            label={`Monthly Amount (${values.currency || 'BDT'})`}
                            name="monthly_amount"
                            type="number"
                            placeholder={`Monthly Amount in ${values.currency || 'BDT'}`}
                            value={values.monthly_amount}
                            onChange={(e) => setFieldValue('monthly_amount', e.target.value)}
                          />
                          <InputField
                            className="py-4"
                            label={`Admission Fee (${values.currency || 'BDT'})`}
                            name="minimum_enroll_amount"
                            type="number"
                            placeholder={`Admission Fee in ${values.currency || 'BDT'}`}
                            value={values.minimum_enroll_amount}
                            onChange={(e) => setFieldValue('minimum_enroll_amount', e.target.value)}
                          />
                        </>
                      )}

                      {values.installment_type === 'Installment' && (
                        <>
                          <InputField
                            className="py-4"
                            label="Installment Number"
                            name="max_installment_qty"
                            type="number"
                            placeholder="Installment Number"
                            value={values.max_installment_qty}
                            onChange={(e) => setFieldValue('max_installment_qty', e.target.value)}
                          />
                          <InputField
                            className="py-4"
                            label={`Admission Fee (${values.currency || 'BDT'})`}
                            name="minimum_enroll_amount"
                            type="number"
                            placeholder={`Admission Fee in ${values.currency || 'BDT'}`}
                            value={values.minimum_enroll_amount}
                            onChange={(e) => setFieldValue('minimum_enroll_amount', e.target.value)}
                          />
                        </>
                      )}
                    </>
                  )}
                </div>

                <div className="grid md:grid-cols-2 gap-2 my-4">
                  <DatePicker
                    value={values.appeared_from}
                    label="Appeared From"
                    placeholder="YYYY-MM-DD"
                    format="YYYY/MM/DD"
                    name="appeared_from"
                    error={errors?.appeared_from}
                    onChange={(date) => setFieldValue("appeared_from", date)}
                  />
                  <DatePicker
                    value={values.appeared_to}
                    label="Appeared To"
                    placeholder="YYYY-MM-DD"
                    format="YYYY/MM/DD"
                    name="appeared_to"
                    error={errors?.appeared_to}
                    onChange={(date) => setFieldValue("appeared_to", date)}
                  />
                </div>

                {/* Duration Fields */}
                <div className="grid md:grid-cols-2 gap-2 my-4">
                  <InputField
                    label="Course Duration (Hours)"
                    name="course_duration"
                    type="number"
                    placeholder="Enter Course Duration in hours"
                    value={values.course_duration}
                    onChange={(e) => setFieldValue('course_duration', e.target.value)}
                    error={errors?.course_duration}
                  />
                  <InputField
                    label="Duration Per Day (Hours)"
                    name="duration_per_day"
                    type="number"
                    placeholder="Enter daily duration in hours"
                    value={values.duration_per_day}
                    onChange={(e) => setFieldValue('duration_per_day', e.target.value)}
                    error={errors?.duration_per_day}
                  />
                </div>

                <div className="flex justify-between">
                  <Button
                    type="button"
                    className="btn text-center btn-danger my-5 btn-sm"
                    onClick={handleBackClick}
                  >
                    Cancel
                  </Button>
                  <Button
                    isLoading={isLoading}
                    type="submit"
                    className="btn text-center btn-primary my-5 btn-sm"
                  >
                    Submit
                  </Button>
                </div>
              </Form>
            );
          }}
        </Formik>
      </SimpleBar>
    </div>
  );
};


export default CourseEditForm;
