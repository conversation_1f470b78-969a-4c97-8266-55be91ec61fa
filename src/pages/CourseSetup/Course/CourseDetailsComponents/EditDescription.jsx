import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { CKEditor } from "@ckeditor/ckeditor5-react";
import {
  ClassicEditor,
  Bold,
  Essentials,
  Heading,
  Indent,
  IndentBlock,
  Italic,
  Link,
  List,
  MediaEmbed,
  Paragraph,
  Table,
  Undo,
  FontSize,
  FontColor
} from "ckeditor5";

import "ckeditor5/ckeditor5.css";
import "@/assets/scss/custom-ckeditor.css";

const EditDescription = ({ showModal, setShowModal, course }) => {
  const [postApi, { isLoading }] = usePostApiMutation();
  
  // Set default description if empty
  const defaultDescription = `
  <h1 style="margin-bottom: 16px;">${course?.title}</h1>

  <br />
  <p style="margin-bottom: 12px;"><strong>Course Overview:</strong></p>
  <p style="margin-bottom: 16px;">Welcome to the <strong>${course?.title}</strong> course! This comprehensive program is designed to provide in-depth knowledge and practical skills in the subject matter. Whether you're a beginner or an experienced professional, this course will guide you step-by-step through essential concepts, advanced techniques, and real-world applications.</p>

  <br />
  <p style="margin-bottom: 12px;"><strong>What You'll Learn:</strong></p>
  <ul style="margin-bottom: 16px;">
      <li>Fundamental concepts and core principles</li>
      <li>Hands-on projects to apply theoretical knowledge</li>
      <li>Advanced strategies and best practices</li>
      <li>Industry-relevant case studies</li>
      <li>Tools and resources for continued learning</li>
  </ul>
  <br />
  <p style="margin-bottom: 12px;"><strong>Who Should Enroll?</strong></p>
  <p style="margin-bottom: 16px;">This course is perfect for:</p>
  <ul style="margin-bottom: 16px;">
      <li>Students and beginners looking to build a strong foundation</li>
      <li>Professionals seeking to enhance their skill set</li>
      <li>Entrepreneurs and freelancers wanting to stay ahead in the industry</li>
      <li>Anyone with a passion for learning and career growth</li>
  </ul>

  <br />

  <p style="margin-bottom: 12px;"><strong>Course Structure:</strong></p>
  <ol style="margin-bottom: 16px;">
      <li>Introduction and Fundamentals</li>
      <li>Core Concepts and Techniques</li>
      <li>Intermediate Topics and Real-World Applications</li>
      <li>Advanced Strategies and Optimization</li>
      <li>Final Project and Certification</li>
  </ol>

  <br />

  <p style="margin-bottom: 12px;"><strong>Certification & Career Opportunities:</strong></p>
  <p style="margin-bottom: 16px;">Upon successful completion of this course, you will receive a certification that validates your expertise. This certification can be a valuable addition to your resume and portfolio, helping you stand out in job applications and career advancements.</p>

  <p style="margin-bottom: 20px;">Enroll now and take the first step towards mastering <strong>${course?.title}</strong>!</p>
`;

  const [value, setValue] = useState(course?.description?.trim() || defaultDescription);

  const onSubmit = async () => {
    let obj = {
      id: course.id,
      description: value
    };

    await postApi({ end_point: "admin/course-save-or-update", body: obj });
    setShowModal(false);
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      title="Course Description"
      className="max-w-5xl"
      footer={null}
    >
      <div className="min-h-[600px] flex flex-col">
        {/* CKEditor */}
        <div className="flex-1">
          <CKEditor
            editor={ClassicEditor}
            config={{
              toolbar: [
                "undo",
                "redo",
                "|",
                "heading",
                "fontSize",
                "fontColor",
                "fontBackgroundColor",
                "|",
                "bold",
                "italic",
                "|",
                "link",
                "insertTable",
                "mediaEmbed",
                "|",
                "bulletedList",
                "numberedList",
                "indent",
                "outdent"
              ],
              plugins: [
                Bold,
                Essentials,
                Heading,
                Indent,
                IndentBlock,
                Italic,
                Link,
                List,
                MediaEmbed,
                Paragraph,
                Table,
                Undo,
                FontSize,
                FontColor,
              ],
              fontSize: {
                options: [10, 12, 14, 16, 18, 20, 24, 28, 32, 36]
              },
              fontColor: {
                columns: 6,
                documentColors: 12
              },
            }}
            data={value}
            onChange={(event, editor) => {
              setValue(editor.getData());
            }}
          />
        </div>

        {/* Bottom Button Container */}
        <div className="p-4 flex justify-end bg-white border-t">
          <Button text="Save" btnClass="btn-primary" onClick={onSubmit} isLoading={isLoading} />
        </div>
      </div>
    </Modal>
  );
};

export default EditDescription;
