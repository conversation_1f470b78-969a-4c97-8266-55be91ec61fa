import React from 'react';
import Icon from '@/components/ui/Icon';

const TabButton = ({ tab, activeTab, onClick }) => (
  <button
    className={`
      group flex items-center gap-3 p-4 text-left
      rounded-xl transition-all duration-300
      font-medium text-base justify-between
      ${
        activeTab === tab.id
          ? 'bg-blue-500 text-white shadow-lg shadow-blue-200 translate-x-2'
          : 'text-gray-600 hover:bg-gray-50 hover:text-blue-500'
      }
    `}
    onClick={() => onClick(tab.id)}
  >
    <div className="flex gap-3">
      <div
        className={`
          p-2 rounded-lg transition-all duration-300
          ${activeTab === tab.id ? 'bg-blue-400' : 'bg-gray-100 group-hover:bg-blue-50'}
        `}
      >
        <Icon
          icon={tab.icon}
          className={`
            w-6 h-6 transition-all duration-300
            ${activeTab === tab.id ? 'text-white' : 'text-gray-500 group-hover:text-blue-500'}
          `}
        />
      </div>
      <div className="flex flex-col">
        <span className="flex-1">{tab.label}</span>
        {tab.count && (
          <span
            className={`
              transition-all duration-300 text-xs
              ${activeTab === tab.id ? 'text-white' : 'text-gray-500 group-hover:text-blue-500'}
            `}
          >
            {tab.count} {tab.countLabel}
          </span>
        )}
      </div>
    </div>

    {activeTab === tab.id && (
      <Icon
        icon="material-symbols:arrow-right-alt-rounded"
        className="w-6 h-6 text-white text-end"
      />
    )}
  </button>
);

const CourseTabs = ({ tabs, activeTab, onTabClick }) => {
  return (
    <div className="flex md:flex-col flex-row flex-wrap">
      {tabs.map((tab) => (
        <TabButton
          key={tab.id}
          tab={tab}
          activeTab={activeTab}
          onClick={onTabClick}
        />
      ))}
    </div>
  );
};

export default CourseTabs;