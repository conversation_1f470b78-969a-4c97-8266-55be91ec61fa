import React, { useState } from "react";
import Icon from "@/components/ui/Icon";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";

const CourseMedia = ({ course }) => {
  const [postApi, { isLoading: isSaving }] = usePostApiMutation();

  const [hovered, setHovered] = useState(false);
  const [videoHovered, setVideoHovered] = useState(false);
  const [thumbnail, setThumbnail] = useState(
    import.meta.env.VITE_ASSET_HOST_URL + course?.thumbnail
  );
  const [youtubeUrl, setYoutubeUrl] = useState(course?.youtube_url);
  const [editingVideoLink, setEditingVideoLink] = useState(false);

  const handleVideoLink = (link) => {
    const url = new URL(link);
    if (url.hostname.includes("youtube.com")) {
      if (url.searchParams.has("v")) {
        return url.searchParams.get("v");
      } else {
        return url.pathname.substr(1);
      }
    } else if (url.hostname.includes("youtu.be")) {
      return url.pathname.substr(1);
    } else {
      return null;
    }
  };

  const handleImageChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      setThumbnail(imageUrl);
    }

    const formData = new FormData();
    formData.append("thumbnail", file);
    formData.append("id", course.id);
    try {
      const response = await postApi({
        end_point: "admin/course-save-or-update",
        body: formData,
        notoast: true,
      }).unwrap();
      if (response.status) {
        toast.success("Thumbnail updated successfully");
      }
    } catch (error) {
      console.error("Error uploading image:", error);
    }
  };

  const formatYouTubeUrl = (url) => {
  try {
    const parsedUrl = new URL(url);

    // For short youtu.be links
    if (parsedUrl.hostname === 'youtu.be') {
      const videoId = parsedUrl.pathname.slice(1); // remove the leading slash
      return `https://www.youtube.com/watch?v=${videoId}`;
    }

    // For already valid YouTube links, just return them
    if (
      parsedUrl.hostname === 'www.youtube.com' ||
      parsedUrl.hostname === 'youtube.com'
    ) {
      return url;
    }
  } catch (e) {
    console.warn('Invalid YouTube URL:', url);
  }

  return url; // fallback to original if something goes wrong
};


  const handleVideoLinkUpdate = async () => {
    try {
      const formattedUrl = formatYouTubeUrl(youtubeUrl);

      const response = await postApi({
        end_point: "admin/course-save-or-update",
        body: { id: course.id, youtube_url: formattedUrl },
        notoast: true,
      }).unwrap();

      if (response.status) {
        toast.success("Video link updated successfully");
        setEditingVideoLink(false);
      }
    } catch (error) {
      console.error("Error updating video link:", error);
      toast.error("Failed to update video link");
    }
  };

  return (
    <div className="overflow-hidden flex flex-col space-y-4 px-2 py-2">
      {/* Video Section */}
      <div
        className="h-[200px] shadow-md relative"
        onMouseEnter={() => setVideoHovered(true)}
        onMouseLeave={() => setVideoHovered(false)}
      >
        {youtubeUrl ? (
          <iframe
            width="100%"
            height="100%"
            src={`https://www.youtube.com/embed/${handleVideoLink(youtubeUrl)}`}
            title={course.title}
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            className="rounded-lg pointer-events-auto"
          ></iframe>
        ) : (
          <div className="text-center">
            Add your promotional video YouTube link here
          </div>
        )}

        {/* Update Video Link */}
        {videoHovered && !editingVideoLink && (
          <div className="absolute bottom-2 left-2 bg-blue-500 text-white px-3 py-2 rounded-lg cursor-pointer hover:bg-blue-600">
            <button className="flex items-center gap-2" onClick={() => setEditingVideoLink(true)}>
              <Icon icon="carbon:edit" />
              Update Promotional Video Link (YouTube)
            </button>
          </div>
        )}

        {/* Editing Video Link */}
        {editingVideoLink && (
          <div className="absolute bottom-2 left-2 bg-white px-4 py-2 rounded-lg flex items-center space-x-2">
            <input
              type="text"
              value={youtubeUrl}
              onChange={(e) => setYoutubeUrl(e.target.value)}
              className="border rounded px-2 py-1 w-full"
              placeholder="Enter YouTube link"
            />
            <button
              className="bg-green-500 text-white px-2 py-1 rounded hover:bg-green-600"
              onClick={handleVideoLinkUpdate}
            >
              <Icon icon="carbon:checkmark" />
            </button>
          </div>
        )}
      </div>

      {/* Thumbnail Section */}
      {thumbnail && (
        <div
          className="relative shadow-md"
          onMouseEnter={() => setHovered(true)}
          onMouseLeave={() => setHovered(false)}
        >
          <img
            src={thumbnail}
            className="w-full object-cover rounded-lg shadow-lg"
            alt="Course Thumbnail"
          />
          {hovered && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
              <label
                htmlFor="thumbnail-upload"
                className="text-white bg-blue-500 px-4 py-2 rounded-lg cursor-pointer hover:bg-blue-600 flex items-center"
              >
                <Icon icon="carbon:camera" className="mr-2" />
                Change Thumbnail
              </label>
              <input
                id="thumbnail-upload"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleImageChange}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CourseMedia;
