import React from 'react';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';

const assetBaseURL = import.meta.env.VITE_ASSET_HOST_URL || '';
const placeholderImage = 'https://via.placeholder.com/300x400?text=No+Image';

const PromotionalItemViewModal = ({ item, isOpen, onClose }) => {
  if (!item) return null;

  return (
    <Modal
      title="Promotional Item Details"
      activeModal={isOpen}
      onClose={onClose}
      className="max-w-4xl"
    >
      <div className="p-5">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left column - Details */}
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">{item.title}</h2>

            <div className="grid grid-cols-2 gap-4 mb-6">
              <div>
                <p className="text-sm text-gray-500">Type</p>
                <p className="text-lg font-medium capitalize">{item.type}</p>
              </div>

              <div>
                <p className="text-sm text-gray-500">Item Name</p>
                <p className="text-lg font-medium">
                  {item.type === 'course' ? (item.course_name || 'N/A') :
                   item.type === 'ebook' ? (item.ebook_name || 'N/A') : 'N/A'}
                </p>
              </div>

              <div>
                <p className="text-sm text-gray-500">Status</p>
                <p className="text-lg font-medium">
                  <span className={`inline-block px-2 py-1 rounded-full text-xs ${
                    item.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {item.is_active ? 'Active' : 'Inactive'}
                  </span>
                </p>
              </div>
            </div>

            {item.description && (
              <div className="mb-6">
                <p className="text-sm text-gray-500 mb-1">Description</p>
                <p className="text-gray-700">{item.description}</p>
              </div>
            )}
          </div>

          {/* Right column - Image */}
          <div>
            <p className="text-sm text-gray-500 mb-2">Promotional Image</p>
            <img
              src={item.image ? `${assetBaseURL}${item.image}` : placeholderImage}
              alt={item.title}
              className="w-full h-auto rounded-lg shadow-md object-cover"
              onError={(e) => { e.target.src = placeholderImage }}
            />
          </div>
        </div>

        <div className="flex justify-end mt-6">
          <Button
            text="Close"
            className="btn-outline-dark"
            onClick={onClose}
            type="button"
          />
        </div>
      </div>
    </Modal>
  );
};

export default PromotionalItemViewModal;
