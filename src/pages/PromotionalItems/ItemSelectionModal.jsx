import React, { useState, useEffect } from 'react';
import Button from '@/components/ui/Button';
import Icon from '@/components/ui/Icon';
import Loading from '@/components/Loading';

const ItemSelectionModal = ({
  isOpen,
  onClose,
  onSelect,
  items,
  selectedItemId,
  itemType,
  isLoading,
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (isOpen) {
      setSearchTerm('');
    }
  }, [isOpen]);

  const getFilteredItems = () => {
    if (!searchTerm) return items;

    return items.filter(item =>
      item.title.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[80vh] flex flex-col">
        <div className="p-4 border-b flex justify-between items-center">
          <h3 className="text-lg font-semibold">
            Select {itemType === 'course' ? 'Course' : 'Ebook'}
          </h3>
          <button
            type="button"
            className="text-gray-400 hover:text-gray-500"
            onClick={onClose}
          >
            <Icon icon="heroicons-outline:x" className="w-5 h-5" />
          </button>
        </div>

        <div className="p-4 border-b">
          <div className="relative">
            <input
              type="text"
              className="w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder={`Search ${itemType === 'course' ? 'courses' : 'ebooks'}...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <div className="absolute left-3 top-2.5 text-gray-400">
              <Icon icon="heroicons-outline:search" className="w-5 h-5" />
            </div>
          </div>
        </div>

        <div className="overflow-y-auto flex-1 p-2">
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <Loading />
            </div>
          ) : getFilteredItems().length > 0 ? (
            <div className="grid grid-cols-1 gap-2">
              {getFilteredItems().map((item) => (
                <div
                  key={item.id}
                  onClick={() => onSelect(item)}
                  className={`
                    flex items-center p-3 border rounded-md cursor-pointer transition-all duration-200
                    ${selectedItemId === item.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50'}
                  `}
                >
                  <div className="h-12 w-12 mr-3 flex-shrink-0 flex items-center justify-center rounded bg-opacity-20 text-2xl">
                    {itemType === 'course' ? (
                      <div className="bg-blue-100 text-blue-600 h-full w-full flex items-center justify-center rounded">
                        <Icon icon="heroicons-outline:academic-cap" />
                      </div>
                    ) : (
                      <div className="bg-green-100 text-green-600 h-full w-full flex items-center justify-center rounded">
                        <Icon icon="heroicons-outline:book-open" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-800">{item.title}</p>
                  </div>
                  {selectedItemId === item.id && (
                    <div className="ml-2 text-blue-500">
                      <Icon icon="heroicons-outline:check" className="w-5 h-5" />
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-40 text-gray-500">
              <Icon icon="heroicons-outline:search" className="w-10 h-10 mb-2" />
              <p>No {itemType === 'course' ? 'courses' : 'ebooks'} found</p>
            </div>
          )}
        </div>

        <div className="p-4 border-t flex justify-end">
          <Button
            text="Cancel"
            className="btn-outline-dark"
            onClick={onClose}
            type="button"
          />
        </div>
      </div>
    </div>
  );
};

export default ItemSelectionModal;
