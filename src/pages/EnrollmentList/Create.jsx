import React, { useState, useEffect } from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import { useGetApiQuery, usePostApiMutation } from "@/store/api/master/commonSlice";
import { object, string, number } from "yup";
import Select from "react-select";
import Card from "@/components/ui/Card";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import SelectBatch from "./SelectBatch";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import CreateStudent from "../StudentList/createStudent";
/**
 * Create Enrollment Form
 *
 * A form for creating a new enrollment record.
 *
 * @returns {JSX.Element} The form component.
 */
const Create = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const [courseId, setCourseId] = useState(null);
    const [studentId, setStudentId] = useState(null);
    const [isEnrolled, setIsEnrolled] = useState(false);
    const [previousPayment, setPreviousPayment] = useState(null);
    const [selectedCourse, setSelectedCourse] = useState(null); // To store the selected course details

    const { data: courseList, isLoading: courseLoading } = useGetApiQuery(
        "admin/course-list?pagination=false"
    );
    const { data: studentList, isLoading: studentLoading } = useGetApiQuery(
        "admin/all-student-list-admin?pagination=false"
    );

    const [postApi, { isLoading: isLoadingPost }] = usePostApiMutation();

    const validationSchema = object().shape({
        course_id: string().required("Course is required"),
        student_id: string().required("Student is required"),
        paid_amount: number().required("Paid Amount is required"),
        discount: number().min(0, "Discount cannot be negative"),
    });

    const { student } = useSelector((state) => state.studentReducer);

    const handleSubmit = async (values, { setSubmitting, resetForm }) => {
        try {
            const formData = new FormData();
            formData.append("course_id", values.course_id);
            formData.append("batch_id", values.batch_id || '');
            formData.append("student_id", values.student_id);
            formData.append("paid_amount", values.paid_amount);
            formData.append("discount", values.discount);

            const response = await postApi({
                end_point: "admin/enroll-student",
                body: formData,
            });
            console.log(response);
            if (response.data.status) {
                // console.log('askd');
                navigate("/invoice/" + response?.data?.data?.transaction_id);
            }

            resetForm();
        } catch (error) {
            console.error(error);
        } finally {
            setSubmitting(false);
        }
    };

    const handleApprovePayment = async (ids) => {
        const response = await postApi({
          end_point: "admin/approve-pending-payment",
          body: { ids: ids },
        })
        if (response.data.status) {
            navigate("/enrollment-list");
        }
      };


    const handleCreateStudent = () => {
        dispatch(setShowModal(true));
    };

    useEffect(() => {
        if (courseId && studentId) {
            postApi({
                end_point: "admin/check-enrollment-student",
                body: {
                    course_id: courseId,
                    student_id: studentId,
                },
                notoast: true,
            }).then((response) => {
                setIsEnrolled(!response?.data.status);
                setPreviousPayment(response?.data.data);
                console.log(response?.data.data);
                console.log(previousPayment);
            });
        }
    }, [courseId, studentId]);

    return (
        <Card className="">
            <h4 className="card-title mb-2">Enrollment Student</h4>
            <div className="mt-4">
                <Formik
                    initialValues={{
                        course_id: "",
                        student_id: "",
                        paid_amount: 0,
                        discount: 0,
                    }}
                    validationSchema={validationSchema}
                    onSubmit={handleSubmit}
                >
                    {({ isSubmitting, isValid, setFieldValue, values }) => {
                        useEffect(() => {
                            if (student) {
                                setFieldValue("student_id", student.id);
                            }
                        }, [student, setFieldValue]);

                        return (
                            <Form className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {/* Select Course */}
                                <div>
                                    <div className="mb-4">
                                        <label htmlFor="course_id" className="form-label">
                                            Course <span className="text-red-500">*</span>
                                        </label>
                                        <Select
                                            placeholder="Select Course"
                                            options={courseList?.map((course) => ({
                                                label: course.title,
                                                value: course.id,
                                            }))}
                                            onChange={(option) => {
                                                setCourseId(option?.value);
                                                setFieldValue("course_id", option?.value || "");
                                                const selected = courseList?.find(
                                                    (course) => course.id === option?.value
                                                );
                                                setSelectedCourse(selected || null); // Set the selected course
                                            }}
                                            className="react-select-container"
                                            classNamePrefix="react-select"
                                        />
                                        <ErrorMessage
                                            name="course_id"
                                            component="div"
                                            className="text-danger mt-1"
                                        />
                                    </div>

                                    <SelectBatch
                                        courseId={values.course_id}
                                        onChange={(value) => {
                                            setFieldValue("batch_id", value);
                                        }}
                                        ErrorMessage={ErrorMessage}
                                    />

                                    <div className="mb-4 flex align-items-center gap-3">
                                    <div className="flex-grow">
                                        <label
                                            htmlFor="course_id"
                                            className="form-label"
                                        >
                                            Student{" "}
                                            <span className="text-red-500">*</span>
                                        </label>
                                        <Select
                                            placeholder="Select Student"
                                            options={studentList?.map((student) => ({
                                                label: student.name,
                                                value: student.id,
                                            }))}
                                            onChange={(option) => {
                                                setStudentId(option?.value);
                                                setFieldValue(
                                                    "student_id",
                                                    option?.value || ""
                                                );
                                            }}
                                            className="react-select-container"
                                            classNamePrefix="react-select"
                                        />
                                        <ErrorMessage
                                            name="student_id"
                                            component="div"
                                            className="text-danger mt-1"
                                        />
                                    </div>

                                        <div className="flex-shrink-1">
                                            <label
                                                htmlFor="course_id"
                                                className="form-label"
                                            >
                                                &nbsp;
                                            </label>
                                            <button
                                                type="button"
                                                className="btn btn-primary btn-sm"
                                                onClick={handleCreateStudent}
                                            >
                                                Add Student
                                            </button>
                                        </div>
                                        </div>
                                    </div>



                                {/* Select Student */}
                                <div className="space-y-2">
                                    {/* Display Course Price */}
                                  
                                        <div className="text-base text-sm">
                                            <h5 className="text-lg font-semibold mb-2">Course Price Details</h5>
                                            <p>
                                                <strong>Price: </strong>
                                                { selectedCourse && 
                                                    <span>
                                                        { selectedCourse.sale_price > 0 ? selectedCourse.sale_price + ' ' + selectedCourse.currency : selectedCourse.monthly_amount > 1 ? selectedCourse.monthly_amount + ' ' + selectedCourse.currency + '/Month' : 
                                                        <span className="text-green-500"> Free </span>
                                                        
                                                        }
                                                    </span>  
                                                }
                                            </p>
                                            <p >
                                                <strong>Admission Fee: </strong>
                                                { selectedCourse && 
                                                    <span>{selectedCourse.minimum_enroll_amount + ' ' + selectedCourse.currency}</span>
                                                }
                                            </p>
                                        </div>
                                   
                                        {isEnrolled ? (
                                            previousPayment?.is_approved == 0 ? (
                                                <div className="alert alert-warning text-center p-6">
                                                    <p>This enrollment is pending approval.</p>
                                                    <p><strong>Transaction ID:</strong> {previousPayment.transaction_id}</p>
                                                    <p><strong>Payment Method:</strong> {previousPayment.payment_method}</p>
                                                    <p><strong>Paid Amount:</strong> {previousPayment.paid_amount} {previousPayment.currency}</p>
                                                    <button type="button" onClick={() => handleApprovePayment([previousPayment.id])} className="btn btn-success mt-3">Approve</button> 
                                                </div>
                                            ) : (
                                                <div className="alert alert-danger text-center p-6">
                                                    Already Enrolled in this Course
                                                </div>
                                            )
                                        ) : (
                                        <>
                                            {/* Paid Amount */}
                                            <NumberInput
                                                label="Paid Amount"
                                                name="paid_amount"
                                                type="text"
                                                placeholder="Paid Amount"
                                                required
                                            />

                                            {/* Discount */}
                                            <NumberInput
                                                label="Discount"
                                                name="discount"
                                                type="text"
                                                placeholder="Discount"
                                            />

                                            {/* Submit Button */}
                                            <div className="flex justify-end mt-4">
                                                <button
                                                    type="submit"
                                                    className="btn btn-primary"
                                                    disabled={isSubmitting || !isValid}
                                                >
                                                    {isSubmitting
                                                        ? "Submitting..."
                                                        : "Create Enrollment"}
                                                </button>
                                            </div>
                                        </>
                                    )}
                                </div>
                            </Form>
                        );
                    }}
                </Formik>
            </div>
            <CreateStudent />
        </Card>
    );
};

export default Create;


