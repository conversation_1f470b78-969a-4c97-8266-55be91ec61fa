import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import { useGetApiQuery, usePostApiMutation } from "@/store/api/master/commonSlice";
import Modal from "@/components/ui/Modal";

const PendingList = () => {
  const { data, isLoading, isFetching } = useGetApiQuery("admin/pending-payments");

  const [postApi, { isApproving }] = usePostApiMutation();

  const [modalVisible, setModalVisible] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [selectedItems, setSelectedItems] = useState([]);

  const handleImageClick = (image) => {
    setSelectedImage(image);
    setModalVisible(true);
  };

  const handleCloseModal = () => {
    setModalVisible(false);
    setSelectedImage(null);
  };

  const handleCheckboxChange = (id) => {
    setSelectedItems((prevSelected) =>
      prevSelected.includes(id)
        ? prevSelected.filter((itemId) => itemId !== id)
        : [...prevSelected, id]
    );
  };

  const handleApprovePayment = async (ids) => {
    const response = await postApi({
      end_point: "admin/approve-pending-payment",
      body: { ids: ids },
    })
    console.log(response);
  };

  const handleApproveAll = async() => {
    if (selectedItems.length > 0) {

      handleApprovePayment(selectedItems);
 
      setSelectedItems([]);
    }

  };

  const columns = [
    {
      label: "Select",
      field: "checkbox",
    },
    {
      label: "Student Name",
      field: "user_name",
    },
    {
      label: "Course Title",
      field: "course_title",
    },
    {
      label: "Payment Method",
      field: "payment_method",
    },
    {
      label: "Transaction ID",
      field: "trx_id",
    },
    {
      label: "Document",
      field: "image",
    },
    {
      label: "Payment Date",
      field: "created_at",
    },
    {
      label: "Paid Amount",
      field: "paid_amount",
    },
    {
      label: "Actions",
      field: "actions",
    },
  ];

  const tableData = data?.map((item) => {
    const isSelected = selectedItems.includes(item.id);
    return {
      id: item.id,
      checkbox: (
        <input
          type="checkbox"
          checked={isSelected}
          onChange={() => handleCheckboxChange(item.id)}
        />
      ),
      user_name: item.user_name,
      course_title: item.course_title,
      payment_method: item.payment_method,
      trx_id: item.trx_id,
      image: (
        item.image ? (
          <img
            src={`${import.meta.env.VITE_ASSET_HOST_URL}/${item.image}`}
            alt="image"
            className="w-auto h-12 cursor-pointer"
            onClick={() => handleImageClick(item.image)}
          />
        ) : '--'
      ),
      created_at: new Date(item.created_at).toLocaleString('en-US', {
        day: "numeric",
        month: "short",
        year: "numeric",
      }),
      paid_amount: item.paid_amount,
      actions: (
        <button
          type="button"
          onClick={() => handleApprovePayment([item.id])}
          className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600"
        >
          Approve
        </button>
      ),
    };
  });

  return (
    <>
      <BasicTablePage
        loading={isLoading || isFetching}
        title="Pending Payment List"
        columns={columns}
        data={tableData}
      />
    {tableData?.length > 0 && (
      <div className="flex justify-end mt-4">
        <button
          type="button"
          onClick={handleApproveAll}
          className={`bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 `}
          disabled={selectedItems.length === 0}
        >
          Approve {selectedItems.length > 0 ? `Selected (${selectedItems.length})` : ''}
        </button>
      </div>
    )}

      {modalVisible && (
        <Modal
          activeModal={modalVisible}
          onClose={handleCloseModal}
          title="Document"
          className="max-w-5xl"
        >
          <img
            src={import.meta.env.VITE_ASSET_HOST_URL + selectedImage}
            alt="image"
            className="w-full h-full"
          />
        </Modal>
      )}
    </>
  );
};

export default PendingList;
