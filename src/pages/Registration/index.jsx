import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Icon } from "@iconify/react";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import RegistrationForm from "./RegistrationForm";
import LMSCustomization from "./LMSCustomization";

const Registration = () => {
  const { t, i18n } = useTranslation();
  const [step, setStep] = useState(1);
  const [selectedTemplate, setSelectedTemplate] = useState("template_two");

  const handleLanguageChange = (lang) => {
    i18n.changeLanguage(lang);
    localStorage.setItem("lang", lang);
  };

  const renderStepIndicator = () => {
    return (
      <div className="flex justify-center mb-8">
        <div className="flex items-center">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? "bg-primary-500 text-white" : "bg-slate-200 text-slate-600"}`}>
            1
          </div>
          <div className={`w-16 h-1 ${step >= 2 ? "bg-primary-500" : "bg-slate-200"}`}></div>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? "bg-primary-500 text-white" : "bg-slate-200 text-slate-600"}`}>
            2
          </div>
          <div className={`w-16 h-1 ${step >= 3 ? "bg-primary-500" : "bg-slate-200"}`}></div>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 3 ? "bg-primary-500 text-white" : "bg-slate-200 text-slate-600"}`}>
            3
          </div>
        </div>
      </div>
    );
  };

  const renderStepTitle = () => {
    switch (step) {
      case 1:
        return t("registration.choose_template");
      case 2:
        return t("registration.lms_details");
      case 3:
        return t("registration.user_information");
      default:
        return "";
    }
  };

  const renderTemplateSelection = () => {
    return (
      <div className="max-w-4xl mx-auto">
        <Card title={t("registration.choose_a_template")} className="mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Template One */}
            <div className={`border rounded-lg p-4 ${selectedTemplate === "template_one" ? "border-primary-500 bg-primary-50" : "border-slate-200"}`}>
              <div className="aspect-video bg-slate-100 mb-4 rounded overflow-hidden">
                <img 
                  src="https://placehold.co/600x400/e2e8f0/1e293b?text=Template+One" 
                  alt="Template One"
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="text-lg font-medium mb-2">{t("registration.template_one")}</h3>
              <p className="text-sm text-slate-600 mb-4">{t("registration.template_one_desc")}</p>
              <div className="flex justify-between items-center">
                <Button
                  text={selectedTemplate === "template_one" ? t("registration.selected") : t("registration.select")}
                  className={selectedTemplate === "template_one" ? "btn-primary" : "btn-outline-primary"}
                  onClick={() => setSelectedTemplate("template_one")}
                />
                <Button
                  text={t("registration.preview")}
                  className="btn-outline-dark"
                  onClick={() => window.open("#", "_blank")}
                />
              </div>
            </div>

            {/* Template Two */}
            <div className={`border rounded-lg p-4 ${selectedTemplate === "template_two" ? "border-primary-500 bg-primary-50" : "border-slate-200"}`}>
              <div className="aspect-video bg-slate-100 mb-4 rounded overflow-hidden">
                <img 
                  src="https://placehold.co/600x400/e2e8f0/1e293b?text=Template+Two" 
                  alt="Template Two"
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="text-lg font-medium mb-2">{t("registration.template_two")}</h3>
              <p className="text-sm text-slate-600 mb-4">{t("registration.template_two_desc")}</p>
              <div className="flex justify-between items-center">
                <Button
                  text={selectedTemplate === "template_two" ? t("registration.selected") : t("registration.select")}
                  className={selectedTemplate === "template_two" ? "btn-primary" : "btn-outline-primary"}
                  onClick={() => setSelectedTemplate("template_two")}
                />
                <Button
                  text={t("registration.preview")}
                  className="btn-outline-dark"
                  onClick={() => window.open("#", "_blank")}
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end mt-6">
            <Button
              text={t("registration.next")}
              className="btn-primary"
              onClick={() => setStep(2)}
            />
          </div>
        </Card>
      </div>
    );
  };

  return (
    <div className="bg-slate-100 min-h-screen py-8 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-2xl font-bold text-slate-900">{t("registration.lms_registration")}</h1>
          
          <div className="flex items-center space-x-2">
            <button 
              className={`px-3 py-1 rounded ${i18n.language === 'en' ? 'bg-primary-500 text-white' : 'bg-slate-200'}`}
              onClick={() => handleLanguageChange('en')}
            >
              EN
            </button>
            <button 
              className={`px-3 py-1 rounded ${i18n.language === 'bn' ? 'bg-primary-500 text-white' : 'bg-slate-200'}`}
              onClick={() => handleLanguageChange('bn')}
            >
              BN
            </button>
            <button 
              className={`px-3 py-1 rounded ${i18n.language === 'jp' ? 'bg-primary-500 text-white' : 'bg-slate-200'}`}
              onClick={() => handleLanguageChange('jp')}
            >
              JP
            </button>
            <button 
              className={`px-3 py-1 rounded ${i18n.language === 'kr' ? 'bg-primary-500 text-white' : 'bg-slate-200'}`}
              onClick={() => handleLanguageChange('kr')}
            >
              KR
            </button>
            <button 
              className={`px-3 py-1 rounded ${i18n.language === 'ar' ? 'bg-primary-500 text-white' : 'bg-slate-200'}`}
              onClick={() => handleLanguageChange('ar')}
            >
              AR
            </button>
          </div>
        </div>

        {renderStepIndicator()}
        
        <h2 className="text-xl font-medium text-center mb-6">{renderStepTitle()}</h2>

        {step === 1 && renderTemplateSelection()}
        {step === 2 && (
          <div className="max-w-4xl mx-auto">
            <LMSCustomization />
            <div className="flex justify-between mt-6">
              <Button
                text={t("registration.back")}
                className="btn-outline-dark"
                onClick={() => setStep(1)}
              />
              <Button
                text={t("registration.next")}
                className="btn-primary"
                onClick={() => setStep(3)}
              />
            </div>
          </div>
        )}
        {step === 3 && (
          <div className="max-w-4xl mx-auto">
            <RegistrationForm />
            <div className="flex justify-between mt-6">
              <Button
                text={t("registration.back")}
                className="btn-outline-dark"
                onClick={() => setStep(2)}
              />
              <Button
                text={t("registration.next")}
                className="btn-primary"
                onClick={() => alert("Registration completed!")}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Registration;
