import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Icon } from "@iconify/react";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Textinput from "@/components/ui/Textinput";
import Textarea from "@/components/ui/Textarea";

const LMSCustomization = () => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    tagline: "",
    shortDescription: "",
    videoUrl: "",
  });
  const [logoFile, setLogoFile] = useState(null);
  const [bannerFile, setBannerFile] = useState(null);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setLogoFile(file);
    }
  };

  const handleBannerUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setBannerFile(file);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission
    console.log("Form submitted:", {
      ...formData,
      logoFile,
      bannerFile,
    });
  };

  return (
    <div className="flex justify-center items-center min-h-screen bg-slate-100 py-12 px-4 sm:px-6 lg:px-8">
      <Card title={t("registration.customize_lms")} className="max-w-2xl w-full">
        <div className="text-slate-600 dark:text-slate-300 mb-5">
          {t("registration.create_engaging")}
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Tagline field */}
          <Textinput
            name="tagline"
            label={t("registration.tagline")}
            placeholder={t("registration.tagline_placeholder")}
            value={formData.tagline}
            onChange={handleChange}
            required
          />

          {/* Short Description field */}
          <Textarea
            name="shortDescription"
            label={t("registration.short_description")}
            placeholder={t("registration.short_description")}
            value={formData.shortDescription}
            onChange={handleChange}
            required
            rows={4}
          />

          {/* Logo Upload */}
          <div>
            <label className="block text-sm font-medium text-slate-600 dark:text-slate-300 mb-1">
              {t("registration.logo")}
            </label>
            <div className="flex items-center space-x-3">
              <label className="cursor-pointer border border-slate-200 dark:border-slate-700 rounded-md p-3 bg-slate-50 dark:bg-slate-900 hover:bg-slate-100 dark:hover:bg-slate-800 transition-all">
                <input
                  type="file"
                  className="hidden"
                  accept="image/*"
                  onChange={handleLogoUpload}
                />
                <Icon icon="heroicons:cloud-arrow-up" className="text-xl" />
                <span className="ml-2">{t("registration.logo")}</span>
              </label>
              {logoFile && (
                <div className="text-sm text-green-500">
                  {logoFile.name} ({Math.round(logoFile.size / 1024)} KB)
                </div>
              )}
            </div>
            <div className="text-xs text-slate-500 mt-1">
              {t("registration.logo_later")}
            </div>
          </div>

          {/* Banner Upload */}
          <div>
            <label className="block text-sm font-medium text-slate-600 dark:text-slate-300 mb-1">
              {t("registration.upload_banner")}
            </label>
            <div className="flex items-center space-x-3">
              <label className="cursor-pointer border border-slate-200 dark:border-slate-700 rounded-md p-3 bg-slate-50 dark:bg-slate-900 hover:bg-slate-100 dark:hover:bg-slate-800 transition-all">
                <input
                  type="file"
                  className="hidden"
                  accept="image/*"
                  onChange={handleBannerUpload}
                />
                <Icon icon="heroicons:cloud-arrow-up" className="text-xl" />
                <span className="ml-2">{t("registration.upload_banner")}</span>
              </label>
              {bannerFile && (
                <div className="text-sm text-green-500">
                  {bannerFile.name} ({Math.round(bannerFile.size / 1024)} KB)
                </div>
              )}
            </div>
            <div className="text-xs text-slate-500 mt-1">
              {t("registration.banner_later")}
            </div>
          </div>

          {/* Video URL field */}
          <Textinput
            name="videoUrl"
            label={t("registration.video_url")}
            placeholder={t("registration.video_url")}
            value={formData.videoUrl}
            onChange={handleChange}
          />

          {/* Featured Mentors section */}
          <div>
            <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-3">
              {t("registration.featured_mentors")}
            </h3>
            <div className="flex items-center space-x-4">
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-slate-200 dark:bg-slate-700 rounded-full flex items-center justify-center">
                  <Icon icon="heroicons:user" className="text-2xl" />
                </div>
                <button className="text-xs text-primary-500 mt-1">Add</button>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-slate-200 dark:bg-slate-700 rounded-full flex items-center justify-center">
                  <Icon icon="heroicons:plus" className="text-2xl" />
                </div>
                <button className="text-xs text-primary-500 mt-1">Add</button>
              </div>
            </div>
          </div>

          {/* Globe and Worldwide User section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="border border-slate-200 dark:border-slate-700 rounded-md p-4">
              <h3 className="text-md font-medium text-slate-900 dark:text-white mb-2">
                {t("registration.rotating_globe")}
              </h3>
              <div className="flex items-center justify-center h-32 bg-slate-100 dark:bg-slate-800 rounded-md">
                <Icon icon="mdi:earth" className="text-5xl text-primary-500" />
              </div>
            </div>
            <div className="border border-slate-200 dark:border-slate-700 rounded-md p-4">
              <h3 className="text-md font-medium text-slate-900 dark:text-white mb-2">
                {t("registration.worldwide_user")}
              </h3>
              <div className="flex items-center justify-center h-32 bg-slate-100 dark:bg-slate-800 rounded-md">
                <Icon icon="mdi:account-group" className="text-5xl text-primary-500" />
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              text={t("registration.back")}
              className="btn-outline-dark"
              type="button"
            />
            <Button
              text={t("registration.next")}
              className="btn-primary"
              type="submit"
            />
          </div>
        </form>
      </Card>
    </div>
  );
};

export default LMSCustomization;
