import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Icon } from "@iconify/react";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Textinput from "@/components/ui/Textinput";

const RegistrationForm = () => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    name: "",
    username: "",
    email: "",
    phone: "",
    password: "",
    confirmPassword: "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission
    console.log("Form submitted:", formData);
  };

  return (
    <div className="flex justify-center items-center min-h-screen bg-slate-100 py-12 px-4 sm:px-6 lg:px-8">
      <Card title={t("registration.create_account")} className="max-w-md w-full">
        <div className="text-slate-600 dark:text-slate-300 mb-5">
          {t("registration.login_explore")}
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Name field */}
          <Textinput
            name="name"
            label={t("registration.your_name")}
            placeholder={t("registration.enter_name")}
            value={formData.name}
            onChange={handleChange}
            required
          />

          {/* Username field */}
          <Textinput
            name="username"
            label={t("registration.username")}
            placeholder={t("registration.enter_username")}
            value={formData.username}
            onChange={handleChange}
            required
          />

          {/* Email field */}
          <Textinput
            name="email"
            label={t("registration.email")}
            placeholder={t("registration.enter_email")}
            type="email"
            value={formData.email}
            onChange={handleChange}
            required
          />

          {/* Phone field */}
          <Textinput
            name="phone"
            label={t("registration.phone")}
            placeholder={t("registration.enter_phone")}
            value={formData.phone}
            onChange={handleChange}
            required
          />

          {/* Password field */}
          <Textinput
            name="password"
            label={t("registration.password")}
            placeholder={t("registration.enter_password")}
            type="password"
            value={formData.password}
            onChange={handleChange}
            required
          />

          {/* Confirm Password field */}
          <Textinput
            name="confirmPassword"
            label={t("registration.confirm_password")}
            placeholder={t("registration.confirm_your_password")}
            type="password"
            value={formData.confirmPassword}
            onChange={handleChange}
            required
          />

          <div className="mt-6">
            <Button
              text={t("registration.create_account")}
              className="btn-primary w-full"
              type="submit"
            />
          </div>
        </form>

        <div className="mt-4 text-center">
          <p>
            {t("registration.chosen_template_two")}
          </p>
        </div>
      </Card>
    </div>
  );
};

export default RegistrationForm;
