import React from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Button from "@/components/ui/Button";
import InputSelect from "@/components/ui/InputSelect";
import { Formik, Form } from "formik";
import { initialValues, validationSchema } from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal, setEditShowModal } from "@/features/commonSlice";
import { usePostApiMutation, useUpdateApiMutation } from "@/store/api/master/commonSlice";
import Textarea from "@/components/ui/Textarea";
import Fileinput from "@/components/ui/Fileinput";

const TestimonialForm = () => {
  const [postApi, { isLoading: isCreateLoading }] = usePostApiMutation();
  const [updateApi, { isLoading: isUpdateLoading }] = useUpdateApiMutation();
  const dispatch = useDispatch();
  const { showModal, showEditModal, editData } = useSelector((state) => state.commonReducer);

  // Determine if this is an edit form based on whether editData is present and showEditModal is true
  const isEditMode = showEditModal && editData;
  const activeModal = isEditMode ? showEditModal : showModal;

  const closeModal = () => {
    if (isEditMode) {
      dispatch(setEditShowModal(false));
    } else {
      dispatch(setShowModal(false));
    }
  };

  const onSubmit = async (values) => {
    let formData = new FormData();
    formData.append("name", values.name);
    formData.append("designation", values.designation);
    formData.append("user_type", values.user_type);
    formData.append("message", values.message);
    formData.append("is_active", 1);

    // Handle image upload differently for create vs edit
    if (values.image) {
      // Only append image if it's a new file (not a string URL)
      if (typeof values.image !== "string") {
        formData.append("image", values.image);
      }
    }

    if (isEditMode) {
      await updateApi({
        end_point: `admin/testimonials/${editData.id}`,
        body: formData,
      });
      dispatch(setEditShowModal(false));
    } else {
      await postApi({
        end_point: "admin/testimonials",
        body: formData,
      });
      dispatch(setShowModal(false));
    }
  };

  return (
    <Modal
      activeModal={activeModal}
      onClose={closeModal}
      title={isEditMode ? "Edit Testimonial" : "Add New Testimonial"}
      className="max-w-3xl"
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={isEditMode ? editData : initialValues}
        onSubmit={onSubmit}
        enableReinitialize
      >
        {({ setFieldValue }) => (
          <Form>
            <div className="grid md:grid-cols-2 gap-4">
              <InputField
                label="Name"
                name="name"
                type="text"
                placeholder="Enter Name"
                required
              />
              <InputField
                label="Designation"
                name="designation"
                type="text"
                placeholder="Enter Designation"
                required
              />
            </div>
            <div className="grid md:grid-cols-2 gap-4 my-4">
              <InputSelect
                label="User Type"
                name="user_type"
                options={[
                  { label: "Teacher", value: "Teacher" },
                  { label: "Student", value: "Student" },
                  { label: "Guardian", value: "Guardian" },
                ]}
                required
              />
              <div>
                <Fileinput
                  name="image"
                  label="Browse"
                  labelText="Select Image"
                  onChange={(e) => {
                    if (e.target.files.length > 0) {
                      setFieldValue("image", e.target.files[0]);
                    }
                  }}
                  placeholder="Choose an image file"
                  accept="image/*"
                />
                {isEditMode && editData.image && typeof editData.image === "string" && (
                  <div className="mt-2">
                    <p className="text-sm text-gray-500 mb-1">Current Image:</p>
                    <img
                      src={import.meta.env.VITE_ASSET_HOST_URL + editData.image}
                      alt="Current"
                      className="h-20 w-20 object-cover rounded"
                    />
                  </div>
                )}
              </div>
            </div>
            <div className="grid md:grid-cols-1 gap-4 my-4">
              <Textarea
                label="Message"
                name="message"
                placeholder="Enter Testimonial Message"
                row={4}
                onChange={(e) => setFieldValue("message", e.target.value)}
              />
            </div>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                text="Close"
                className="btn text-center btn-danger"
                onClick={closeModal}
              />
              <Button
                isLoading={isEditMode ? isUpdateLoading : isCreateLoading}
                type="submit"
                className="btn text-center btn-primary ml-3"
              >
                {isEditMode ? "Update" : "Submit"}
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default TestimonialForm;
