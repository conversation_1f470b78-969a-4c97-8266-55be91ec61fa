import React from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Button from "@/components/ui/Button";
import InputSelect from "@/components/ui/InputSelect";
import { Formik, Form } from "formik";
import { updateValidationSchema } from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal } from "@/features/commonSlice";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import Textarea from "@/components/ui/Textarea";
import Fileinput from "@/components/ui/Fileinput";

const EditTestimonial = () => {
  const [updateApi, { isLoading }] = useUpdateApiMutation();
  const dispatch = useDispatch();
  const { showEditModal, editData } = useSelector((state) => state.commonReducer);

  const onSubmit = async (values) => {
    let formData = new FormData();
    formData.append("name", values.name);
    formData.append("designation", values.designation);
    formData.append("user_type", values.user_type);
    formData.append("message", values.message);
    formData.append("is_active", 1);

    // Only append image if it's a new file (not a string URL)
    if (values.image && typeof values.image !== "string") {
      formData.append("image", values.image);
    }

    await updateApi({
      end_point: `admin/testimonials/${editData.id}`,
      body: formData,
    });

    dispatch(setEditShowModal(false));
  };

  return (
    <Modal
      activeModal={showEditModal}
      onClose={() => dispatch(setEditShowModal(false))}
      title="Edit Testimonial"
      className="max-w-3xl"
      footerContent={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setEditShowModal(false))}
        />
      }
    >
      <Formik
        validationSchema={updateValidationSchema}
        initialValues={editData}
        onSubmit={onSubmit}
        enableReinitialize
      >
        {({ setFieldValue }) => (
          <Form>
            <div className="grid md:grid-cols-2 gap-4">
              <InputField
                label="Name"
                name="name"
                type="text"
                placeholder="Enter Name"
                required
              />
              <InputField
                label="Designation"
                name="designation"
                type="text"
                placeholder="Enter Designation"
                required
              />
            </div>
            <div className="grid md:grid-cols-1 gap-4 my-4">
              <InputSelect
                label="User Type"
                name="user_type"
                options={[
                  { label: "Teacher", value: "Teacher" },
                  { label: "Student", value: "Student" },
                  { label: "Guardian", value: "Guardian" },
                ]}
                required
              />
            </div>
            <div className="grid md:grid-cols-1 gap-4 my-4">
              <Textarea
                label="Message"
                name="message"
                placeholder="Enter Testimonial Message"
                row={4}
                value={editData.message}
                onChange={(e) => setFieldValue("message", e.target.value)}
                required
              />
            </div>
            <div className="grid md:grid-cols-1 gap-4 my-4">
              <div>
                <Fileinput
                  name="image"
                  label="Browse"
                  labelText="Select Image"
                  onChange={(e) => {
                    if (e.target.files.length > 0) {
                      setFieldValue("image", e.target.files[0]);
                    }
                  }}
                  placeholder="Choose an image file"
                  accept="image/*"
                />
                {editData.image && typeof editData.image === "string" && (
                  <div className="mt-2">
                    <p className="text-sm text-gray-500 mb-1">Current Image:</p>
                    <img
                      src={import.meta.env.VITE_ASSET_HOST_URL + editData.image}
                      alt="Current"
                      className="h-20 w-20 object-cover rounded"
                    />
                  </div>
                )}
              </div>
            </div>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Update
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default EditTestimonial;
