import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import avatar from "@/assets/images/avatar/av-1.svg";

const ViewTestimonial = ({ showViewModal, setShowViewModal, data }) => {
  if (!data) return null;

  return (
    <Modal
      title="Testimonial Details"
      activeModal={showViewModal}
      className="max-w-3xl"
      onClose={() => setShowViewModal(false)}
      footerContent={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowViewModal(false)}
        />
      }
    >
      <div className="p-4">
        <div className="flex flex-col md:flex-row gap-6">
          <div className="md:w-1/3 flex flex-col items-center">
            <div className="mb-4">
              <img
                src={
                  data.image
                    ? import.meta.env.VITE_ASSET_HOST_URL + data.image
                    : avatar
                }
                className="rounded-full w-32 h-32 object-cover border-4 border-slate-200"
                alt="testimonial"
              />
            </div>
            <div className="text-center">
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                {data.name}
              </h3>
              <p className="text-sm text-slate-600 dark:text-slate-300">
                {data.designation}
              </p>
              <div className="mt-2">
                <span className="inline-block px-3 py-1 bg-slate-100 dark:bg-slate-700 rounded-full text-xs font-medium">
                  {data.user_type}
                </span>
              </div>
            </div>
          </div>
          <div className="md:w-2/3">
            <div className="mb-4">
              <h4 className="text-base font-medium text-slate-900 dark:text-white mb-2">
                Message
              </h4>
              <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                <p className="text-slate-600 dark:text-slate-300 whitespace-pre-line">
                  {data.message === "null" || !data.message ? "No message available" : data.message}
                </p>
              </div>

            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium text-slate-500 dark:text-slate-400">
                  Created At
                </h4>
                <p className="text-slate-900 dark:text-white">
                  {new Date(data.created_at).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default ViewTestimonial;
