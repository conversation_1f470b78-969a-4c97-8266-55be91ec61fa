import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import TestimonialForm from "./TestimonialForm";
import DeleteTestimonial from "./deleteTestimonial";
import ViewTestimonial from "./ViewTestimonial";
import { useDispatch } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";
import avatar from "@/assets/images/avatar/av-1.svg";

const Testimonials = () => {
  const dispatch = useDispatch();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);
  const [viewData, setViewData] = useState(null);

  const [apiParam, setApiParam] = useState("");
  const { data, isLoading, isFetching } = useGetApiQuery(`admin/testimonials${apiParam}`);

  const changePage = (val) => {
    setApiParam(val);
  };

  const columns = [
    {
      label: "SL",
      field: "index",
    },
    {
      label: "Name",
      field: "name",
    },
    {
      label: "Designation",
      field: "designation",
    },
    {
      label: "User Type",
      field: "user_type",
    },
    {
      label: "Message",
      field: "message",
    },
    {
      label: "Image",
      field: "image",
    },
    {
      label: "Action",
      field: "",
    }
  ];

  // Handle different API response formats
  const testimonials = Array.isArray(data) ? data : data?.data;

  const tableData = testimonials?.map((item, index) => {
    return {
      index: index + 1,
      name: item.name,
      designation: item.designation,
      user_type: item.user_type,
      message: (
        <div className="max-w-xs truncate">
          {item.message}
        </div>
      ),
      image: (
        <div className="flex items-center">
          <img
            src={
              item.image
                ? import.meta.env.VITE_ASSET_HOST_URL + item.image
                : avatar
            }
            className="rounded-full w-10 h-10 object-cover"
            alt="testimonial"
          />
        </div>
      )
    };
  });

  const actions = [
    {
      name: "view",
      icon: "heroicons-outline:eye",
      onClick: (val) => {
        setViewData(testimonials[val]);
        setShowViewModal(true);
      },
    },
    {
      name: "edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        dispatch(setEditData(testimonials[val]));
        dispatch(setEditShowModal(true));
      },
    },
    {
      name: "delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(testimonials[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  const formPage = <TestimonialForm />;

  return (
    <div>
      <BasicTablePage
        title="Testimonials List"
        createButton="Add New Testimonial"
        createPage={formPage}
        editPage={formPage}
        actions={actions}
        columns={columns}
        data={tableData}
        changePage={changePage}
        currentPage={data?.current_page || 1}
        totalPages={data?.last_page || (data?.total && data?.per_page ? Math.ceil(data?.total / data?.per_page) : 1)}
        loading={isLoading || isFetching}
      />

      {showDeleteModal && (
        <DeleteTestimonial
          showDeleteModal={showDeleteModal}
          setShowDeleteModal={setShowDeleteModal}
          data={deleteData}
        />
      )}

      {showViewModal && (
        <ViewTestimonial
          showViewModal={showViewModal}
          setShowViewModal={setShowViewModal}
          data={viewData}
        />
      )}
    </div>
  );
};

export default Testimonials;