import * as yup from "yup";

export const initialValues = {
    name: '',
    designation: '',
    user_type: 'Teacher',
    message: '',
    image: ''
};

export const validationSchema = yup.object({
    name: yup.string()
        .max(100, "Should not be more than 100 characters")
        .min(2, "Should not be less than 2 characters")
        .required("Name is Required"),
    designation: yup.string()
        .max(100, "Should not be more than 100 characters")
        .min(2, "Should not be less than 2 characters")
        .required("Designation is Required"),
    user_type: yup.string()
        .oneOf(['Teacher', 'Student', 'Guardian'], "Please select a valid user type")
        .required("User Type is Required")
});


