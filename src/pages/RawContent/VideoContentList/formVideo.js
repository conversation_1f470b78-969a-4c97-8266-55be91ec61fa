import React from "react";
import * as yup from "yup";

export const initialValues = { 
    title: '', 
    title_bn: '', 
    author_name: '',
    author_details: '',
    description: '',
    raw_url: '',
    s3_url: '',
    youtube_url: '',
    download_url: '',
    class_level_id: '',
    subject_id: '',
    chapter_id: '',
    video_type: 'raw_url',
    duration:'',
    thumbnail:'',
    sequence: '',
    price: '',
    rating:'',
    is_active:'',
    is_free:'',

};

export const validationSchema =  yup.object({
    title: yup.string().max(100, "Should not be more than 100 characters").required("Title is Required"),
    // author_name: yup.string().max(50, "Should not be more than 50 characters").min(3, "Should not be less than 3 characters").required("Author Name is Required"),

    raw_url: yup.string().when('video_type', {
      is: 'raw_url',
      then: yup.string().required("Raw URL is Required")
    }),
    s3_url: yup.string().when('video_type', {
      is: 's3_url',
      then: yup.string().required("S3 URL is Required")
    }),
    youtube_url: yup.string().when('video_type', {
      is: 'youtube_url',
      then: yup.string().required("YouTube URL is Required")
    }),
    thumbnail: yup.string().required("Thumbnail is Required"),
    // duration: yup.number().positive("Number should be positive").required("Duration is Required"),
    // link: yup.string().required("Link/URL is Required")
})
