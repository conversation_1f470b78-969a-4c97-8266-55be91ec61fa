import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetVideoContentListQuery } from "@/store/api/master/rowContentVideoContentlistSlice";
import { useGetClassListQuery } from "@/store/api/master/rowContentClassListSlice";
import { useGetSubjectChapterListQuery } from "@/store/api/master/rowContentChapterListSlice";
import { useGetChapterListBySubjectQuery } from "@/store/api/master/rowContentChapterListSlice";
import Select from "@/components/ui/Select";
import CreateVideo from "./createVideo";
import EditVideo from "./editVideo";
import { setEditShowModal, setEditData } from "@/features/commonSlice";
import { useDispatch } from "react-redux";

const Filter = ({ setApiParam }) => {
  const [classId, setClassId] = useState(null);
  const [subjectId, setSubjectId] = useState(null);
  const classList = useGetClassListQuery("?pagination=false").data;
  const subjectList = useGetSubjectChapterListQuery(classId).data;
  const chapterList = useGetChapterListBySubjectQuery(subjectId).data;
  console.log(classList);
  return (
    <div className="flex gap-2">
      <Select
        className="w-52"
        defaultValue=""
        placeholder="Select Class"
        options={classList?.map((item) => {
          return { label: item.name, value: item.id };
        })}
        name="class_level"
        onChange={(e) => {
          setClassId(e.target.value);
          setApiParam("?class_id=" + e.target.value);
        }}
      />

      <Select
        className="w-40"
        defaultValue=""
        placeholder="Select Subject"
        options={subjectList?.map((item) => {
          return { label: item.name, value: item.id };
        })}
        name="subject"
        onChange={(e) => {
          setSubjectId(e.target.value);
          setApiParam(`?class_id=${classId}&subject_id=${e.target.value}`);
        }}
      />
      <Select
        className="w-40"
        defaultValue=""
        placeholder="Select Chapter"
        options={chapterList?.map((item) => {
          return { label: item.name, value: item.id };
        })}
        name="chapter"
        onChange={(e) => {
          setApiParam(
            `?class_id=${classId}&subject_id=${subjectId}&chapter_id=${e.target.value}`
          );
        }}
      />
    </div>
  );
};

const index = () => {
  const [showModal, setShowModal] = useState(false);
  const dispatch = useDispatch();
  const [apiParam, setApiParam] = useState("");

  const res = useGetVideoContentListQuery(apiParam);

  const changePage = (val) => {
    setApiParam(val);
  };

  console.log(res.data?.data);
  const data = res.data;
  const columns = [
    {
      label: "SL",
      field: "id",
    },
    {
      label: "Title",
      field: "title",
    },
    {
      label: "Class-Subject-Chapter",
      field: "class_name",
    },
    {
      label: "Price",
      field: "price",
    },
    {
      label: "Free",
      field: "is_free",
    },
    {
      label: "Status",
      field: "is_active",
    },
    {
      label: "Action",
      field: "",
    },
  ];
  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      title: item.title,
      class_name: (
        <div>
          {item.class_name}
          {" -> "}
          {item.subject_name}
          {" -> "}
          {item.chapter_name}
        </div>
      ),
      price: item.price,
      is_free: (
        <Badge
          className={
            item.is_free
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {item.is_free ? "Yes" : "No"}
        </Badge>
      ),
      is_active: (
        <Badge
          className={
            item.is_active
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {item.is_active ? "Active" : "Inactive"}
        </Badge>
      ),
    };
  });

  const actions = [
    {
      name: "edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        console.log(val);
        dispatch(setEditData(data.data[val]));
        dispatch(setEditShowModal(true));
      },
    },
  ];
  //   const changePage = (item) => {
  //     console.log(item);
  //   };

  const handleSubmit = () => {
    setShowModal(false);
  };
  const createPage = <CreateVideo />;
  const editPage = <EditVideo />;

  const filter = <Filter setApiParam={setApiParam} />;
  return (
    <div>
      {/* {tableData?.length > 0 && ( */}
      <BasicTablePage
        title="Video Content List"
        createButton="Create Video"
        actions={actions}
        createPage={createPage}
        editPage={editPage}
        columns={columns}
        data={tableData}
        filter={filter}
        changePage={changePage}
        currentPage={data?.current_page}
        totalPages={Math.ceil(data?.total / data?.per_page)}
      />
      {/* )} */}
    </div>
  );
};

export default index;
