import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Fileinput from "@/components/ui/Fileinput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import Select from "@/components/ui/Select";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import { Formik, Form, Field } from "formik";
import { initialValues, validationSchema } from "./formChapter";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal } from "@/features/commonSlice";
import {
  useChapterCreateOrUpdateMutation,
  useGetSubjectChapterListQuery,
} from "@/store/api/master/rowContentChapterListSlice";
import { useGetClassListQuery } from "@/store/api/master/rowContentClassListSlice";
const editChapter = () => {
  const dispatch = useDispatch();
  const { showEditModal } = useSelector((state) => state.commonReducer);
  const { editData } = useSelector((state) => state.commonReducer);

  const [chapterCreateOrUpdate, { isLoading, isError, error, isSuccess }] =
    useChapterCreateOrUpdateMutation();

  // Switch  State
  const [isActive, setIsActive] = useState(editData.is_active);
  const [isFree, setIsFree] = useState(editData.is_free);

  const [classLevelId, setClassLevelId] = useState(null);

  // API
  const classList = useGetClassListQuery()?.data;
  // console.log(classList);
  const subjectList = useGetSubjectChapterListQuery(classLevelId ? classLevelId : editData.class_level_id)?.data;
  // console.log(subjectList);

  const onSubmit = async (values, { resetForm }) => {

    let formData = new FormData();
    formData.append("id", values.id);
    formData.append("name", values.name);
    formData.append("name_bn", values.name_bn);
    formData.append("class_level_id", values.class_level_id);
    formData.append("subject_id", values.subject_id);
    formData.append("price", values.price);
    formData.append("color_code", values.color_code);
    formData.append("sequence", 1);
    formData.append("icon", values.icon);
    formData.append("is_active", isActive ? 1 : 0);
    formData.append("is_free", isFree ? 1 : 0);

    const response = await chapterCreateOrUpdate(formData);
    dispatch(setEditShowModal(false));
  };
  return (
    <Modal
      activeModal={showEditModal}
      onClose={() => dispatch(setEditShowModal(false))}
      title="Edit Chapter"
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setEditShowModal(false))}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={editData}
        onSubmit={onSubmit}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          handleSubmit,
          setFieldValue,
          isSubmitting,
        }) => (
          <Form>
            <>
              <div className="grid md:grid-cols-1 gap-4">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  placeholder="Enter Name"
                  required
                />
                <InputField
                  label="Bangla Name"
                  name="name_bn"
                  type="text"
                  placeholder="Enter Bangla Name"
                />
              </div>

              <div className="grid md:grid-cols-2 gap-4 my-4">
                <>
                  <Select
                    defaultValue={editData.class_level_id}
                    label="Class"
                    placeholder="Select Class"
                    options={classList?.data?.map((item) => {
                      return { label: item.name, value: item.id };
                    })}
                    name="class_level_id"
                    onChange={(e) => {
                      setFieldValue("class_level_id", e.target.value);
                      setClassLevelId(e.target.value);
                    }}
                  />
                </>
                <>
                  <Select
                    defaultValue={editData.subject_id}
                    label="Subject"
                    placeholder="Select Subject"
                    options={subjectList?.map((item) => {
                      return { label: item.name, value: item.id };
                    })}
                    name="subject_id"
                    onChange={(e) => {
                      setFieldValue("subject_id", e.target.value);
                    }}
                  />
                </>
              </div>
              <div className="grid md:grid-cols-1 gap-4 my-4">
                <NumberInput
                  label="Price"
                  name="price"
                  type="text"
                  placeholder="Enter Price"
                  required
                />
              </div>
              <div className="grid md:grid-cols-2 gap-4 my-4">
                <InputField
                  label="Color Code"
                  name="color_code"
                  type="text"
                  placeholder="Enter Color Code"
                />
                <NumberInput
                  label="Sequence"
                  name="sequence"
                  type="text"
                  placeholder="Enter Sequence"
                  required
                />
              </div>
              <div className="grid md:grid-cols-2 gap-4 my-4">
                <Switch
                  label="Active"
                  activeClass="bg-success-500"
                  value={isActive}
                  name="is_active"
                  onChange={() => setIsActive(!isActive)}
                />

                <Switch
                  label="Free"
                  activeClass="bg-success-500"
                  value={isFree}
                  name="is_free"
                  onChange={() => setIsFree(!isFree)}
                />
              </div>
              <div className="grid md:grid-cols-1 gap-4 my-4">
                <>
                  <div className="mb-4">
                    <label className="block text-[#1D1D1F] text-base font-medium mb-2">
                      Icon
                    </label>
                    <Fileinput
                      name="icon"
                      accept="image/*"
                      type="file"
                      placeholder="Icon"
                      preview={true}
                      selectedFile={values.icon}
                      onChange={(e) => {
                        setFieldValue("icon", e.currentTarget.files[0]);
                      }}
                    />
                  </div>
                </>
              </div>
            </>

            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default editChapter;
