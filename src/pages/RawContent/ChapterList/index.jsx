import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetChapterListQuery } from "@/store/api/master/rowContentChapterListSlice";
import CreateChapter from "./createChapter";
import EditChapter from "./editChapter";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";

const index = () => {
  const dispatch = useDispatch();

  const [showModal, setShowModal] = useState(false);
  const [apiParam, setApiParam] = useState("itemsPerPage=6");

  const res = useGetChapterListQuery(apiParam);
  const changePage = (val) => {
    setApiParam(val + "&itemsPerPage=6");
  };

  console.log(res.data?.data);
  const data = res.data;
  const columns = [
    {
      label: "SL",
      field: "id",
    },
    {
      label: "Name",
      field: "name",
    },
    {
      label: "Class-Subject",
      field: "class_name",
    },
    {
      label: "Price",
      field: "price",
    },
    {
      label: "Free",
      field: "is_free",
    },
    {
      label: "Status",
      field: "is_active",
    },
    {
      label: "Action",
      field: "",
    },
  ];
  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      name: item.name,
      class_name:(
        <div>
          {item.class_name}{" -> "}{item.subject_name}
        </div>
      ),
      price: item.price,
      is_free: (
        <Badge
          className={
            item.is_free
              ? "bg-success-500 text-white"
              : "bg-danger-500 text-white"
          }
        >
          {item.is_free ? "YES" : "NO"}
        </Badge>
      ),
      is_active: (
        <Badge
          className={
            item.is_active
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {item.is_active ? "Active" : "Inactive"}
        </Badge>
      ),
    };
  });

  const actions = [
    {
      name: "edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        console.log(data.data[val]);
        dispatch(setEditData(data.data[val]));
        dispatch(setEditShowModal(true));
      },
    },
  ];
  // const changePage = (item) => {
  //   console.log(item);
  // };

  const handleSubmit = () => {
    setShowModal(false);
  };

  const createPage = <CreateChapter />;
  const editPage = <EditChapter />;

  return (
    <div>
      <BasicTablePage
        title="Chapter List"
        createButton="Add New Chapter"
        createPage={createPage}
        editPage={editPage}
        actions={actions}
        columns={columns}
        data={tableData}
        changePage={changePage}
        submitForm={handleSubmit}
        currentPage={data?.current_page}
        totalPages={Math.ceil(data?.total / data?.per_page)}
      />
    </div>
  );
};

export default index;
