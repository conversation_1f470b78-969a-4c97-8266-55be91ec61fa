import React from "react";
import * as yup from "yup";

export const initialValues = { 
    title: '', 
    price: 0,
    is_free: '',
    icon: '',
    color_code: '',
    sequence: 1,
    is_active: '',
    description: '',
    class_level_id: '',
    subject_id: '',
    chapter_id: '',
    raw_url: '',
    thumbnail: ''

};

export const validationSchema = (uploadType) =>
    yup.object().shape({
      title: yup.string().required("Title is required"),
      description: yup.string(),
      raw_url: uploadType === "url" ? yup.string().url("Invalid URL").required("URL is required") : yup.string(),
      file: uploadType === "pdf" ? yup.mixed().required("PDF file is required") : yup.mixed(),
});