import React, { useState } from "react";
import InputField from "@/components/ui/InputField";
import FileInput from "@/components/ui/Fileinput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import Textarea from "@/components/ui/Textarea";
import { Formik, Form } from "formik";
import * as Yup from "yup"; // Ensure Yup is installed
import SimpleBar from "simplebar-react";
import Icon from "@/components/ui/Icon";
import { usePostApiMutation } from "@/store/api/master/commonSlice";

// Define the validation schema dynamically based on uploadType
const getValidationSchema = (uploadType) => {
  return Yup.object().shape({
    title: Yup.string().required("Title is required"),
    description: Yup.string().required("Description is required"),
    thumbnail: Yup.mixed(), // Thumbnail is optional; handle required status based on your needs
    raw_url:
      uploadType === "url"
        ? Yup.string()
            .url("Invalid URL format")
            .required("Script URL is required")
        : Yup.string(),
    file:
      uploadType === "pdf"
        ? Yup.mixed().required("PDF file is required")
        : Yup.mixed(),
  });
};

const EditScript = ({ isSidebarOpen, setIsSidebarOpen, category, script }) => {
  // Initialize state based on the script prop
  console.log(script);
  const [isActive, setIsActive] = useState(script.is_active);
  // const [isFree, setIsFree] = useState(script.is_free);
  const [isFree, setIsFree] = useState(script ? Boolean(script.is_free) : false);
  const [uploadType, setUploadType] = useState(script.raw_url ? "url" : "pdf");

  const [postApi, { isLoading, isError, error }] = usePostApiMutation();
  console.log(script, 'script data');

  // Define initial form values based on the script prop
  const initialFormValues = {
    title: script.title || "",
    description: script.description || "",
    thumbnail: null, // File inputs start as null; handle existing thumbnails separately
    raw_url: script.raw_url || "",
    file: null, // File inputs start as null
  };

  // Handle form submission
  const onSubmit = async (values, { resetForm }) => {
    let formData = new FormData();
    formData.append("id", script.id); // Include ID for update
    formData.append("course_id", category.course_id);
    formData.append("course_category_id", category.id);
    formData.append("title", values.title);
    formData.append("description", values.description);
    formData.append("is_active", isActive ? 1 : 0);
    formData.append("is_free", isFree ? 1 : 0);
    formData.append("price", script.price || 0); // Use existing price or default to 0
    formData.append("sequence", script.sequence || 1); // Use existing sequence or default to 1
  
    // Handle file or URL based on upload type
    if (uploadType === "pdf" && values.file) {
      formData.append("file", values.file);
      formData.append("raw_url", ""); // Clear URL if uploading PDF
    } else if (uploadType === "url") {
      formData.append("raw_url", values.raw_url);
      formData.append("file", ""); // Clear file if using URL
    }
  
    // Handle thumbnail upload or removal
    if (values.thumbnail) {
      formData.append("thumbnail", values.thumbnail);
    } else {
      formData.append("thumbnail", ""); // Clear thumbnail if not updated
    }
  
    try {
      // Call the API
      const response = await postApi({
        end_point: "admin/chapter-script-save-or-update",
        body: formData,
      }).unwrap(); // unwrap to handle errors properly
  
      if (response.error) {
        // Handle error and reset form with errors
        resetForm({ values, errors: response.error.data.errors });
      } else {
        // Reset form and close sidebar on success
        resetForm();
        setIsSidebarOpen(false);
        // Optionally, refresh video list if needed
        // e.g., dispatch(fetchVideos(category.id));
      }
    } catch (error) {
      console.error("API call failed:", error);
      // Optionally handle general error (e.g., show a toast message)
    }
  };
  

  return (
    <>
      {isSidebarOpen && (
        <div className="fixed right-0 top-0 w-[450px] bg-white dark:bg-slate-800 h-screen z-[9999] shadow-base2 border border-slate-200 dark:border-slate-700 transition-all duration-150">
          <SimpleBar className="px-6 h-full">
            <header className="flex items-center justify-between border-b border-slate-100 dark:border-slate-700 px-6 py-[25px]">
              <div>
                <span className="block text-xl text-slate-900 font-medium dark:text-[#eee]">
                  Edit Learning Material
                </span>
              </div>
              <div
                className="cursor-pointer text-2xl text-slate-800 dark:text-slate-200"
                onClick={() => setIsSidebarOpen(false)}
              >
                <Icon icon="heroicons-outline:x" />
              </div>
            </header>
            <Formik
              validationSchema={getValidationSchema(uploadType)} // Pass the type to validation
              initialValues={initialFormValues}
              enableReinitialize={true} // Allow Formik to reinitialize when initialFormValues change
              onSubmit={onSubmit}
            >
              {({ values, errors, touched, setFieldValue }) => (
                <Form>
                  {/* Title Field */}
                  <div className="grid md:grid-cols-1 gap-4 my-3">
                    <InputField
                      label="Title"
                      name="title"
                      type="text"
                      placeholder="Enter Title"
                      required
                    />
                    {errors.title && touched.title && (
                      <div className="text-red-500 text-sm">
                        {errors.title}
                      </div>
                    )}
                  </div>

                  {/* Description Field */}
                  <div className="grid md:grid-cols-1 gap-4 my-3">
                    <label className="block text-[#1D1D1F] text-base font-medium">
                      Description
                    </label>
                    <Textarea
                      placeholder="Enter Description"
                      name="description"
                      value={values.description}
                      onChange={(e) =>
                        setFieldValue("description", e.target.value)
                      }
                    />
                    {errors.description && touched.description && (
                      <div className="text-red-500 text-sm">
                        {errors.description}
                      </div>
                    )}
                  </div>


                  {/* Upload Type Selection */}
                  <div className="grid md:grid-cols-1 gap-4 my-3">
                    <label className="block text-base font-medium">
                      Upload Type
                    </label>
                    <div className="flex gap-4">
                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          name="uploadType"
                          value="url"
                          checked={uploadType === "url"}
                          onChange={() => setUploadType("url")}
                        />
                        URL
                      </label>
                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          name="uploadType"
                          value="pdf"
                          checked={uploadType === "pdf"}
                          onChange={() => setUploadType("pdf")}
                        />
                        File
                      </label>
                    </div>
                  </div>

                  {/* Script URL Field */}
                  {uploadType === "url" && (
                    <div className="grid md:grid-cols-1 gap-4 my-3">
                      <InputField
                        label="Script URL"
                        name="raw_url"
                        type="text"
                        placeholder="Enter Script URL"
                        required
                      />
                      {errors.raw_url && touched.raw_url && (
                        <div className="text-red-500 text-sm">
                          {errors.raw_url}
                        </div>
                      )}
                    </div>
                  )}

                  {/* PDF File Upload Field */}
                  {uploadType === "pdf" && (
                    <div className="grid md:grid-cols-1 gap-4 my-3">
                      <label className="block font-medium mb-2">
                        Upload File (PDF)
                        <span className="text-red-500 ml-1">*</span>
                      </label>

                      <FileInput
                        name="file"
                        accept="application/pdf"
                        type="file"
                        placeholder="Upload PDF"
                        preview={false} // PDFs typically don't have previews
                        selectedFile={values.file}
                        onChange={(e) =>
                          setFieldValue("file", e.target.files[0])
                        }
                      />
                      {/* Display existing PDF link if no new file is selected */}
                      {script.raw_url && uploadType === "pdf" && !values.file && (
                        <div className="mt-2">
                          <a
                            href={script.raw_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-500 underline"
                          >
                            View Existing PDF
                          </a>
                        </div>
                      )}
                      {errors.file && touched.file && (
                        <div className="text-red-500 text-sm">
                          {errors.file}
                        </div>
                      )}
                    </div>
                  )}

                  
                  {/* Active and Free Switches */}
                  <div className="grid md:grid-cols-2 gap-4 my-3">
                    <Switch
                      label="Active"
                      activeClass="bg-success-500"
                      value={isActive}
                      name="is_active"
                      onChange={() => setIsActive(!isActive)}
                    />
                    <Switch
                      label="Free"
                      activeClass="bg-success-500"
                      value={isFree}
                      name="is_free"
                      onChange={() => setIsFree(!isFree)}
                    />
                  </div>

                  {/* Submit Button */}
                  <div className="ltr:text-right rtl:text-left mt-5">
                    <Button
                      isLoading={isLoading}
                      type="submit"
                      className="btn btn-primary"
                    >
                      Submit
                    </Button>
                  </div>

               
                </Form>
              )}
            </Formik>
          </SimpleBar>
        </div>
      )}
    </>
  );
};

export default EditScript;
