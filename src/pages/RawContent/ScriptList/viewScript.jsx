import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";



const ViewScript = ({showModal, setShowModal, content}) => {
  console.log(content);


  return (
    <Modal
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      title={content.title}
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowModal(false)}
        />
      }
    >
  <div className="w-full h-[700px] overflow-y-auto">

  <iframe src={content?.script?.s3_url || content?.script?.raw_url} width="100%" height="100%" />
  </div>
    </Modal>
  );
}

export default ViewScript;

