import React, { useState, useEffect } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Textarea from "@/components/ui/Textarea";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import Select from "@/components/ui/Select";
import { Formik, Form } from "formik";
import * as Yup from "yup"; // Ensure Yup is installed
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { useGetQuizTypeListQuery } from "@/store/api/master/quizTypeSlice";
import SimpleBar from "simplebar-react";
import Icon from "@/components/ui/Icon";

const EditQuiz = ({ isSidebarOpen, setIsSidebarOpen, category, quiz }) => {
  // Initialize state based on the quiz prop
  const [checkedIsFree, setCheckIsFree] = useState(quiz.is_free);
  const [checkedIsActive, setCheckIsActive] = useState(quiz.is_active);

  // Fetch quiz types if needed
  const { data: quizList } = useGetQuizTypeListQuery();

  // Initialize the post API mutation
  const [postApi, { isLoading, isError, error }] = usePostApiMutation();

  console.log(quiz, 'quiz data');

  // Define initial form values based on the quiz prop
  const initialFormValues = {
    title: quiz.title || "",
    title_bn: quiz.title_bn || "",
    description: quiz.description || "",
    duration: quiz.duration || "",
    number_of_question: quiz.number_of_question || "",
    positive_mark: quiz.positive_mark || "",
    negative_mark: quiz.negative_mark || 0,
    total_mark: quiz.total_mark || 0,
    sequence: quiz.sequence || 1,
    quiz_attempts: quiz.quiz_attempts || 1,
    quiz_type_id: quiz.quiz_type_id || "",
  };

  // Define the validation schema
  const validationSchema = Yup.object().shape({
    title: Yup.string().required("Title is required"),
    title_bn: Yup.string(), // If title_bn is optional
    description: Yup.string().required("Description is required"),
    duration: Yup.number()
      .typeError("Duration must be a number")
      .required("Duration is required"),
    number_of_question: Yup.number()
      .typeError("Number of questions must be a number")
      .required("Number of questions is required"),
    positive_mark: Yup.number()
      .typeError("Positive mark must be a number")
      .required("Positive mark is required"),
    negative_mark: Yup.number()
      .typeError("Negative mark must be a number")
      .min(0, "Negative mark cannot be less than 0"),
    total_mark: Yup.number()
      .typeError("Total mark must be a number")
      .required("Total mark is required"),
    sequence: Yup.number()
      .typeError("Sequence must be a number")
      .required("Sequence is required"),
    quiz_type_id: Yup.string().required("Quiz type is required"),
  });

  // Handle form submission
  const onSubmit = async (values, { resetForm }) => {
    let formData = new FormData();

    formData.append("id", quiz.id); // Include ID for update
    formData.append("course_id", category.course_id);
    formData.append("course_category_id", category.id);
    formData.append("title", values.title);
    formData.append("title_bn", values.title_bn);
    formData.append("description", values.description);
    formData.append("quiz_type_id", values.quiz_type_id);
    formData.append("duration", values.duration);
    formData.append("positive_mark", values.positive_mark);
    formData.append("quiz_attempts", values.quiz_attempts);
    formData.append("negative_mark", values.negative_mark || 0);
    formData.append("total_mark", values.total_mark);
    formData.append("number_of_question", values.number_of_question);
    formData.append("sequence", values.sequence);
    formData.append("is_active", checkedIsActive ? 1 : 0);
    formData.append("is_free", checkedIsFree ? 1 : 0);

    try {
      // Call the API
      const response = await postApi({
        end_point: "admin/chapter-quiz-save-or-update",
        body: formData,
      }).unwrap(); // unwrap to handle errors properly

      if (response.error) {
        // Handle error and reset form with errors
        resetForm({ values, errors: response.error.data.errors });
      } else {
        // Reset form and close sidebar on success
        resetForm();
        setIsSidebarOpen(false);
        // Optionally, refresh video list if needed
        // e.g., dispatch(fetchVideos(category.id));
      }
    } catch (error) {
      console.error("API call failed:", error);
      // Optionally handle general error (e.g., show a toast message)
    }
  };

  return (
    <>
      {isSidebarOpen && (
        <div
          className={`fixed right-0 top-0 w-[450px] bg-white dark:bg-slate-800 h-screen z-[9999] shadow-base2 border border-slate-200 dark:border-slate-700 transition-all duration-150`}
        >
          <SimpleBar className="px-6 h-full">
            <header className="flex items-center justify-between border-b border-slate-100 dark:border-slate-700 px-6 py-[25px]">
              <div>
                <span className="block text-xl text-slate-900 font-medium dark:text-[#eee]">
                  Edit Quiz
                </span>
              </div>
              <div
                className="cursor-pointer text-2xl text-slate-800 dark:text-slate-200"
                onClick={() => setIsSidebarOpen(false)}
              >
                <Icon icon="heroicons-outline:x" />
              </div>
            </header>
            <Formik
              validationSchema={validationSchema}
              initialValues={initialFormValues}
              enableReinitialize={true} // Allow Formik to reinitialize when initialFormValues change
              onSubmit={onSubmit}
            >
              {({
                values,
                errors,
                touched,
                setFieldValue,
                handleChange,
                handleBlur,
              }) => {
                // Update total_mark whenever number_of_question or positive_mark changes
                useEffect(() => {
                  const numberOfQuestions = parseFloat(values.number_of_question) || 0;
                  const positiveMark = parseFloat(values.positive_mark) || 0;
                  const calculatedTotalMark = numberOfQuestions * positiveMark;

                  // Only update if the calculated value is different from current value
                  if (values.total_mark !== calculatedTotalMark) {
                    setFieldValue("total_mark", calculatedTotalMark);
                  }
                }, [values.number_of_question, values.positive_mark, setFieldValue]);

                return (
                <Form>
                  {/* Title Fields */}
                  <div className="grid md:grid-cols-1 gap-4 my-3">
                    <InputField
                      label="Title"
                      name="title"
                      type="text"
                      placeholder="Enter Title"
                      required
                    />
                
                  </div>


                  {/* Description Field */}
                  <div className="grid md:grid-cols-1 gap-4 my-3">
                    <label className="block text-[#1D1D1F] text-base font-medium">
                      Description
                    </label>
                    <Textarea
                      placeholder="Enter Description"
                      name="description"
                      value={values.description}
                      onChange={(e) =>
                        setFieldValue("description", e.target.value)
                      }
                    />
               
                  </div>

                  {/* Number Inputs */}
                  <div className="grid md:grid-cols-1 gap-4 my-3">
                    <NumberInput
                      label="Duration (Minute)"
                      name="duration"
                      type="text"
                      placeholder="Enter Duration"
                      required
                    />
                
                  </div>

                  <div className="grid md:grid-cols-1 gap-4 my-3">
                    <NumberInput
                      label="Number of Questions"
                      name="number_of_question"
                      type="text"
                      placeholder="Number of Questions"
                      required
                    />
                  
                  </div>

                  <div className="grid md:grid-cols-1 gap-4 my-3">
                    <NumberInput
                      label="Positive Mark"
                      name="positive_mark"
                      type="text"
                      placeholder="Enter Positive Mark"
                      required
                    />
                 
                  </div>

                  <div className="grid md:grid-cols-1 gap-4 my-3">
                    <NumberInput
                      label="Negative Mark"
                      name="negative_mark"
                      type="text"
                      placeholder="Enter Negative Mark"
                      required
                    />
                  
                  </div>

                  <div className="grid md:grid-cols-1 gap-4 my-3">
                    <NumberInput
                      label="Total Mark"
                      name="total_mark"
                      type="text"
                      placeholder="Enter Total Mark"
                      readOnly
                    />

                  </div>

                    {/* Number Inputs */}
                    <div className="grid md:grid-cols-1 gap-4 my-3">
                      <NumberInput
                      label="Attempts Allowed (Number)"
                      name="quiz_attempts"
                      type="text"
                      placeholder="Enter Attempts Number"
                      required
                    />
                  </div>
                  
                  {/* Sequence Input */}
                  <div className="grid md:grid-cols-1 gap-4 my-3">
                    <NumberInput
                      label="Sequence"
                      name="sequence"
                      type="text"
                      placeholder="Sequence"
                      required
                    />
                 
                  </div>

                  {/* Quiz Type Selection */}
                  <div className="grid md:grid-cols-1 gap-4 my-3">
                    <Select
                      label="Quiz Type"
                      name="quiz_type_id"
                      placeholder="Select Quiz Type"
                      options={
                        quizList
                          ? quizList.map((item) => ({
                              label: item.name,
                              value: item.id,
                            }))
                          : []
                      }
                      value={values.quiz_type_id}
                      onChange={(value) => setFieldValue("quiz_type_id", value)}
                      required
                    />
                    {errors.quiz_type_id && touched.quiz_type_id && (
                      <div className="text-red-500 text-sm">
                        {errors.quiz_type_id}
                      </div>
                    )}
                  </div>

                  {/* Switches */}
                  <div className="flex gap-8 my-5">
                    {/* <Switch
                      label="Free"
                      activeClass="bg-success-500"
                      value={checkedIsFree}
                      name="is_free"
                      onChange={() => setCheckIsFree(!checkedIsFree)}
                    /> */}
                    <Switch
                      label="Active"
                      activeClass="bg-success-500"
                      value={checkedIsActive}
                      name="is_active"
                      onChange={() => setCheckIsActive(!checkedIsActive)}
                    />
                  </div>

                  {/* Submit Button */}
                  <div className="ltr:text-right rtl:text-left my-5">
                    <Button
                      isLoading={isLoading}
                      type="submit"
                      className="btn text-center btn-primary"
                    >
                      Submit
                    </Button>
                  </div>


                </Form>
                );
              }}
            </Formik>
          </SimpleBar>
        </div>
      )}
    </>
  );
};

export default EditQuiz;
