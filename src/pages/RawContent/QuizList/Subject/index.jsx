import React, {useState} from "react";

import Card from "@/components/ui/Card";
import { useGetSubjectListQuery } from "@/store/api/master/rowContentSubjectListSlice";
import Badge from "@/components/ui/Badge";
import { useDispatch, useSelector } from "react-redux";
import { useGetSubjectByQuizListQuery, useGetQuizDetailsQuery } from "@/store/api/master/rowContentQuizListSlice";
import { useParams } from "react-router-dom";
import CreateSubject from "./createSubject"

const index = () => {
  const [showModal, setShowModal] = useState(false);
  const dispatch = useDispatch();
  const { id } = useParams();
  const subjectList = useGetSubjectByQuizListQuery(id)?.data;
  const quiz = useGetQuizDetailsQuery(id)?.data;

  
  return (
    <>
      <Card>
        <div className="flex bg-white p-4 rounded-lg shadow border dark:border-gray-700">
          <div className="w-1/3 px-4">
            <div className="flex gap-4">
              <div className="col-span-1 font-bold">Quiz Title</div>
              <div className="col-span-2">{quiz?.title}</div>
            </div>
            <div className="flex gap-4">
              <div className="col-span-1 font-bold">Quiz Code</div>
              <div className="col-span-2">{quiz?.quiz_code}</div>
            </div>
            <div className="flex gap-4">
              <div className="col-span-1 font-bold">Class</div>
              <div className="col-span-2">{quiz?.class_name}</div>
            </div>
            <div className="flex gap-4">
              <div className="col-span-1 font-bold">Subject & Chapter</div>
              <div className="col-span-2">{quiz?.class_name}</div>
            </div>
          </div>
          <div className="w-1/3 px-4">
            <div className="flex gap-4">
              <div className="col-span-1 font-bold">Total Question</div>
              <div className="col-span-2">{quiz?.number_of_question}</div>
            </div>
            <div className="flex gap-4">
              <div className="col-span-1 font-bold">Total Mark</div>
              <div className="col-span-2">{quiz?.total_mark}</div>
            </div>
            <div className="flex gap-4">
              <div className="col-span-1 font-bold">Positive Mark</div>
              <div className="col-span-2">{quiz?.positive_mark}</div>
            </div>
            <div className="flex gap-4">
              <div className="col-span-1 font-bold">Negative Mark</div>
              <div className="col-span-2">{quiz?.negative_mark}</div>
            </div>
          </div>
          <div className="w-1/3 px-4">
            
          <div className="flex gap-4">
              <div className="col-span-1 font-bold">Duration</div>
              <div className="col-span-2">{quiz?.duration} Minutes</div>
            </div>
            <div className="flex gap-4">
              <div className="col-span-1 font-bold">Status</div>
              <div className="col-span-2">
              <Badge className={quiz?.is_active ? `bg-success-500 text-white` : `bg-danger-500 text-white` }> {quiz?.is_active ? 'Active' : 'Inactive'} </Badge>
              </div>
            </div>
            <div className="flex gap-4">
              <div className="col-span-1 font-bold">Free</div>
              <div className="col-span-2">
              <Badge className={quiz?.is_free ? `bg-success-500 text-white` : `bg-danger-500 text-white` }> {quiz?.is_free ? 'Yes' : 'No'} </Badge>
              </div>
            </div>
          </div>
        </div>
        <div className="flex mt-2 pb-2 bg-white p-4 rounded-lg shadow border dark:border-gray-700">
          <p><b>Description: </b> {quiz?.description} </p>
        </div>

        <button className="btn btn-primary btn-sm mt-4"  onClick={() => setShowModal(true)}>Add New Subject</button>

     
        <div className="flex mt-2 pb-2 bg-white p-4 rounded-lg shadow border dark:border-gray-700">
          <div className="w-full">
            <table className="table w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="px-4 py-2">Subject</th>
                  <th className="px-4 py-2">Number of Question</th>
                  {/* <th className="px-4 py-2">Action</th> */}
                </tr>
              </thead>
              <tbody>
                {subjectList?.map((item, index) => (
                  <tr key={index} className="border-b">
                    <td className="px-4 py-2 text-center">{item.subject_name}</td>
                    <td className="px-4 py-2 text-center">{item.no_of_question}</td>
                    {/* <td className="px-4 py-2 text-center">
                      <button className="btn btn-sm btn-secondary">Edit</button>
                      <button className="btn btn-sm btn-danger">Delete</button>
                    </td> */}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </Card>
      <CreateSubject showModal={showModal} setShowModal={setShowModal} quiz={quiz} />
    </>
  );
};

export default index;