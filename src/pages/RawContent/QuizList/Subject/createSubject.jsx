import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import Checkbox from "@/components/ui/Checkbox";
import Button from "@/components/ui/Button";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import Select from "@/components/ui/Select";
import { Formik, Form, Field } from "formik";
import { initialValues, validationSchema } from "./formSettings";
import { useDispatch } from "react-redux";
import { 
  useGetCoreSubjectListQuery,
  useGetSubjectByQuizListQuery,
  useQuizSubjectCreateOrUpdateMutation
 } from "@/store/api/master/rowContentQuizListSlice";
const createSubject = ({showModal, setShowModal, quiz}) => {
  const dispatch = useDispatch();

  // const [quizCreateOrUpdate, { isLoading, isError, error, isSuccess }] = useQuizCreateOrUpdateMutation();
  const [quizSubjectCreateOrUpdate, { isLoading, isError, error, isSuccess }] = useQuizSubjectCreateOrUpdateMutation();

  // API
  const subjectList = useGetCoreSubjectListQuery()?.data; 

  console.log(subjectList);
  const onSubmit = async (values, { resetForm }) => {
    values.chapter_quiz_id = quiz.id;
    console.log(values);
    quizSubjectCreateOrUpdate(values);
    setShowModal(false);
  };
  return (
    <Modal
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      title="Add New Subject"
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowModal(false)}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={initialValues}
        onSubmit={onSubmit}
      >
        {({
          values,
          errors, touched,
          setFieldValue
        }) => (
          <Form>
      
            <div className="grid md:grid-cols-5 gap-2 p-5 border-b border-slate-200 dark:border-slate-700">


            <div className="col-span-2 pt-2">
              <Select
                  defaultValue=""
                  placeholder="Select Subject"
                  options={subjectList?.map((item) => {
                    return { label: item.name, value: item.id };
                  })}
                  name="quiz_core_subject_id"
                  onChange={(e) => {
                    setFieldValue("quiz_core_subject_id", e.target.value);
                  }}
                  error={errors.quiz_core_subject_id}
                />
              </div>
              <div className="col-span-2">
              <NumberInput
                name="no_of_question"
                placeholder="Number Of Question"
                onChange={(e) => setFieldValue('no_of_question', e.target.value)}
              />
              </div>

              <Checkbox
              className="col-span-1"
              name="is_active"
              label="Active"
              value={values.is_active}
              onChange={(e) => setFieldValue('is_active', e.target.checked)}
            />
            </div>

           
  
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default createSubject;
