import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Textarea from "@/components/ui/Textarea";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import Select from "@/components/ui/Select";
import { Formik, Form, Field } from "formik";
import { initialValues, validationSchema } from "./formQuiz";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { useGetQuizTypeListQuery } from "@/store/api/master/quizTypeSlice";
import SimpleBar from "simplebar-react";
import Icon from "@/components/ui/Icon";

const createQuiz = ({ isSidebarOpen, setIsSidebarOpen, category }) => {
  // const [quizCreateOrUpdate, { isLoading, isError, error, isSuccess }] = useQuizCreateOrUpdateMutation();
  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();

  const [checkedIsFree, setCheckIsFree] = useState(false);
  const [checkedIsActive, setCheckIsActive] = useState(true);
  //   api
  // const quizList = useGetQuizTypeListQuery()?.data;

  const onSubmit = async (values, { resetForm }) => {
    let formData = new FormData();

    formData.append("course_id", category.course_id);
    formData.append("course_category_id", category.id);
    formData.append("title", values.title);
    formData.append("title_bn", values.title);
    formData.append("description", values.description);
    formData.append("quiz_type_id", 1);
    formData.append("duration", values.duration);
    formData.append("positive_mark", values.positive_mark);
    formData.append("negative_mark", values.negative_mark || 0);
    formData.append("total_mark", values.number_of_question * values.positive_mark);
    formData.append("number_of_question", values.number_of_question);
    formData.append("quiz_attempts", values.quiz_attempts);
    formData.append("sequence", values.sequence);
    formData.append("is_active", checkedIsActive ? 1 : 0);
    formData.append("is_free", checkedIsFree ? 1 : 0);

    // const response = await quizCreateOrUpdate(formData);
    const response = await postApi({
      end_point: "admin/chapter-quiz-save-or-update",
      body: formData,
    });
    // console.log(response);
    // setShowModal(false);
    resetForm();
    setIsSidebarOpen(false);
  };
  return (
    <>
      {isSidebarOpen && (
        <div
          className={`fixed right-0 top-0 w-[450px] bg-white dark:bg-slate-800 h-screen z-[9999] shadow-base2 border border-slate-200 dark:border-slate-700 transition-all duration-150`}
        >
          <SimpleBar className="px-6 h-full">
            <header className="flex items-center justify-between border-b border-slate-100 dark:border-slate-700 px-6 py-[25px]">
              <div>
                <span className="block text-xl text-slate-900 font-medium dark:text-[#eee]">
                  Add New Quiz
                </span>
              </div>
              <div
                className="cursor-pointer text-2xl text-slate-800 dark:text-slate-200"
                onClick={() => setIsSidebarOpen(false)}
              >
                <Icon icon="heroicons-outline:x" />
              </div>
            </header>
            <Formik
              validationSchema={validationSchema}
              initialValues={initialValues}
              onSubmit={onSubmit}
            >
              {({
                values,
                errors,
                touched,
                handleChange,
                handleBlur,
                handleSubmit,
                setFieldValue,
                isSubmitting,
              }) => (
                <Form>
                  <>
                    <div className="grid md:grid-cols-1 gap-4">
                      <InputField
                        label="Title"
                        name="title"
                        type="text"
                        placeholder="Enter Name"
                        required
                      />
                      {/* <InputField
                        label="Bangla Title"
                        name="title_bn"
                        type="text"
                        placeholder="Enter Bangla Name"
                      /> */}
                    </div>
                    {/* <div className="grid md:grid-cols-1 mt-2">
          <InputField
            label="Description"
            name="description"
            type="text"
            placeholder="Description"
          />
        </div> */}
                    <div className="grid md:grid-cols-1 gap-4 mt-2">
                      <>
                        <label className="block text-[#1D1D1F] text-base font-medium">
                          Description
                        </label>
                        <Textarea
                          placeholder="Enter Description"
                          name="description"
                          onChange={(e) => {
                            setFieldValue("description", e.target.value);
                          }}
                        />
                      </>
                    </div>

                    <div className="grid md:grid-cols-1 gap-4 my-3">
                      <NumberInput
                        label="Duration (Minute)"
                        name="duration"
                        type="text"
                        placeholder="Enter Duration"
                        required
                      />
                      <NumberInput
                        label="Number of Question"
                        name="number_of_question"
                        type="text"
                        placeholder="Number of Question"
                        required
                      />
                      <NumberInput
                        label="Positive Mark"
                        name="positive_mark"
                        type="text"
                        placeholder="Enter Positive Mark"
                        required
                      />
                      <NumberInput
                        label="Negative Mark"
                        name="negative_mark"
                        type="text"
                        placeholder="Enter Negative Mark"
                        required
                      />
                      <NumberInput
                        label="Total Mark"
                        name="total_mark"
                        type="text"
                        placeholder="Enter Total Mark"
                        value={values.number_of_question * values.positive_mark}
                        readOnly
                      />
                      {/* <NumberInput
                        label="Sequence"
                        name="sequence"
                        type="text"
                        placeholder="Sequence"
                        required
                      /> */}
                    </div>


                    {/* Number Inputs */}
                    <div className="grid md:grid-cols-1 gap-4 my-3">
                      <NumberInput
                        label="Attempts Allowed (Number)"
                        name="quiz_attempts"
                        type="text"
                        placeholder="Enter Attempts Number"
                        required
                      />
                    </div>

                    <div className="grid md:grid-cols-1 gap-4 my-3">
                      {/* <>
                        <Select
                          defaultValue=""
                          label="Quiz Type"
                          placeholder="Select Quiz"
                          options={quizList?.map((item) => {
                            return { label: item.name, value: item.id };
                          })}
                          name="item.id"
                          onChange={(e) => {
                            setFieldValue("quiz_type_id", e.target.value);
                          }}
                        />
                      </> */}
                      <div className="flex gap-8 md:mt-8">
                        {/* <Switch
                          label="Free"
                          activeClass="bg-success-500"
                          value={checkedIsFree}
                          name="is_Free"
                          onChange={() => setCheckIsFree(!checkedIsFree)}
                        /> */}
                        <Switch
                          label="Active"
                          activeClass="bg-success-500"
                          value={checkedIsActive}
                          name="is_Active"
                          onChange={() => setCheckIsActive(!checkedIsActive)}
                        />
                      </div>
                    </div>
                  </>
                  <div className="ltr:text-right rtl:text-left my-5">
                    <Button
                      isLoading={isLoading}
                      type="submit"
                      className="btn text-center btn-primary"
                    >
                      Submit
                    </Button>
                  </div>
                </Form>
              )}
            </Formik>
          </SimpleBar>
        </div>
      )}
    </>
  );
};

export default createQuiz;
