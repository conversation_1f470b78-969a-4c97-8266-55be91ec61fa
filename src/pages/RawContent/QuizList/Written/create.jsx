import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import Checkbox from "@/components/ui/Checkbox";
import Button from "@/components/ui/Button";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import FileInput from "@/components/ui/Fileinput";
import Select from "@/components/ui/Select";
import { Formik, Form, Field } from "formik";
import { initialValues, validationSchema } from "./formSettings";
import { useDispatch } from "react-redux";
import { usePostApiMutation } from "@/store/api/master/commonSlice";

const Create = ({showModal, setShowModal, quiz}) => {
  const dispatch = useDispatch();

  // const [quizCreateOrUpdate, { isLoading, isError, error, isSuccess }] = useQuizCreateOrUpdateMutation();
  const [postApi, { isLoading, isError, error, isSuccess }] = usePostApiMutation();

  // API

  const onSubmit = async (values, { resetForm }) => {
    
    let formData = new FormData();
    formData.append('chapter_quiz_id', quiz.id);
    formData.append('question_attachment', values.question_attachment);
    formData.append('no_of_question', values.no_of_question);
    formData.append('marks', values.marks);
    formData.append('is_active', 1);

    const response = await postApi({
      end_point: "admin/written-question-save-or-update",
      body: formData
    });
    setShowModal(false);
  };
  return (
    <Modal
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      title="Add Written Question"
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowModal(false)}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={initialValues}
        onSubmit={onSubmit}
      >
        {({
          values,
          errors, touched,
          setFieldValue
        }) => (
          <Form>
      
            <div className="grid md:grid-cols-1 gap-2 border-b border-slate-200 dark:border-slate-700">


            <div className="">
              <label className="block text-[#1D1D1F] text-base font-medium mb-2">Question Attachment</label>
              <FileInput
                label="Attachments"
                name="question_attachment"
                accept="file/*"
                type="file"
                placeholder="Question Attachment"
                preview={true}
                selectedFile={values.question_attachment}
                onChange={(e) => {
                setFieldValue("question_attachment", e.currentTarget.files[0]);
                }}
                />
              </div>
              <div className="">
              <NumberInput
                label="Marks"
                name="marks"
                placeholder="Marks"
                onChange={(e) => setFieldValue('marks', e.target.value)}
              />
              </div>

              <div className="">
              <NumberInput
                label="No. of Question"
                name="no_of_question"
                placeholder="Number Of Question"
                onChange={(e) => setFieldValue('no_of_question', e.target.value)}
              />
              </div>
            
            </div>

           
  
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default Create;
