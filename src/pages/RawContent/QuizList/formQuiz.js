import React from "react";
import * as yup from "yup";

export const initialValues = { 
    title:'',
    title_bn:'',
    description:'',
    class_level_id:'',
    quiz_type_id:'',
    subject_id:'',
    chapter_id:'',
    duration:'',
    positive_mark:1,
    negative_mark: 0,
    quiz_attempts: 1,
    total_mark:'',
    number_of_question:15,
    is_free:'',
    sequence:1,
    is_active:'',
};

export const validationSchema =  yup.object({
    title: yup.string()
        .max(250, "Should not be more than 250 characters")
        .min(3, "Should not be less than 3 characters")
        .required("Title is Required"),
    duration: yup.string()
        .required("Duration is Required"),
    number_of_question: yup.string()
        .required("Number of Question is Required"),

    positive_mark: yup.number()
        .required("Positive Mark is Required"),

    quiz_attempts: yup.number()
        .required("Quiz Attempts is Required"),

    negative_mark: yup.number()
            .required("Nagative Mark is Required"),
})