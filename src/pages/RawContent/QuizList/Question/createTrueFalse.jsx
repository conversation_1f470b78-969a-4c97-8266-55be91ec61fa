import React from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import CompactFileInput from "@/components/ui/CompactFileInput";
import Button from "@/components/ui/Button";
import { Formik, Form, Field } from "formik";
import * as yup from "yup";
import { usePostApiMutation } from "@/store/api/master/commonSlice";

const CreateTrueFalse = ({ showTrueFalseModal, setShowTrueFalseModal, quiz, editData }) => {
  const [postApi, { isLoading }] = usePostApiMutation();

  const isEditMode = !!editData;

  const initialValues = {
    chapter_quiz_id: quiz?.id || '',
    question_text: '',
    question_image: '',
    answer: 'false',
    explanation_text: '',
    explanation_image: '',
    is_active: 1
  };

  const getFormInitialValues = () => {
    if (isEditMode) {
      const question = editData.question || editData;
      console.log("True/False Edit Data:", editData);
      console.log("Question data for form:", question);

      return {
        id: question.id,
        chapter_quiz_id: quiz?.id || '',
        question_text: question.question_text || '',
        question_image: '',
        answer: question.answer === 1 || question.answer === true ? 'true' : 'false',
        explanation_text: question.explanation_text || '',
        explanation_image: '',
        is_active: 1
      };
    }
    return initialValues;
  };

  // Validation schema
  const validationSchema = yup.object({
    question_text: yup.string()
      .max(250, "Should not be more than 250 characters")
      .min(3, "Should not be less than 3 characters")
      .required("Question Text is Required"),
    answer: yup.string().required("Please select True or False"),
    explanation_text: yup.string().max(250, "Should not be more than 250 characters")
  });

  const onSubmit = async (values) => {
    console.log("Form values before submission:", values);
    console.log("Question image:", values.question_image);
    console.log("Explanation image:", values.explanation_image);
    console.log("Are they the same?", values.question_image === values.explanation_image);

    let formData = new FormData();
    if (isEditMode) {
      const question = editData.question || editData;
      formData.append('id', question.id);
      console.log("Including ID in form data for update:", question.id);
    }
    formData.append('chapter_quiz_id', quiz.id);
    formData.append('question_text', values.question_text);
    formData.append('answer', values.answer === 'true' ? 1 : 0);
    formData.append('explanation_text', values.explanation_text || '');
    formData.append('is_active', 1);

    if (values.question_image) {
      console.log("Adding question_image to formData:", values.question_image);
      formData.append('question_image', values.question_image);
    }

    if (values.explanation_image) {
      console.log("Adding explanation_image to formData:", values.explanation_image);
      formData.append('explanation_image', values.explanation_image);
    }

    console.log("Submitting True/False question with data:");
    for (let [key, value] of formData.entries()) {
      console.log(`${key}: ${value}`);
    }

    try {
      const response = await postApi({
        end_point: "admin/chapter-quiz-true-false-save-or-update",
        body: formData,
      });
      console.log("API Response:", response);
      if (response.data) {
        console.log(isEditMode ? "True/False question updated successfully" : "True/False question created successfully");
      }

      setShowTrueFalseModal(false);
    } catch (error) {
      console.error("Error saving True/False question:", error);
    }
  };

  return (
    <Modal
      activeModal={showTrueFalseModal}
      onClose={() => setShowTrueFalseModal(false)}
      title={isEditMode ? "Edit True/False Question" : "Add True/False Question"}
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowTrueFalseModal(false)}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={getFormInitialValues()}
        onSubmit={onSubmit}
      >
        {({
          values,
          setFieldValue,
          errors
        }) => (
          <Form>
        
            <div className="grid md:grid-cols-1 gap-2 pb-5 border-b border-slate-200 dark:border-slate-700">
              <InputField
                name="question_text"
                label="Question"
                value={values.question_text}
                placeholder="Question Text"
                onChange={(e) => setFieldValue('question_text', e.target.value)}
              />

              <CompactFileInput
                name="question_image"
                label="Question Image"
                placeholder="Upload Question Image (Optional)"
              />
            </div>

            {/* True/False Radio Buttons */}
            <div className="grid md:grid-cols-1 gap-2 p-5 border-b border-slate-200 dark:border-slate-700">
              <label className="block text-gray-600 text-sm font-medium mb-2">
                Answer <span className="text-rose-500">*</span>
              </label>

              <div className="flex space-x-6">
                <label className="inline-flex items-center">
                  <Field
                    type="radio"
                    name="answer"
                    value="true"
                    className="form-radio h-5 w-5 text-blue-600"
                  />
                  <span className="ml-2 text-gray-700">True</span>
                </label>

                <label className="inline-flex items-center">
                  <Field
                    type="radio"
                    name="answer"
                    value="false"
                    className="form-radio h-5 w-5 text-blue-600"
                  />
                  <span className="ml-2 text-gray-700">False</span>
                </label>
              </div>

              {errors.answer && (
                <div className="text-danger-500 text-sm mt-1">{errors.answer}</div>
              )}
            </div>

            {/* Explanation */}
            <div className="grid md:grid-cols-1 gap-2 p-5 border-b border-slate-200 dark:border-slate-700">
              <InputField
                name="explanation_text"
                label="Explanation (Optional)"
                value={values.explanation_text}
                placeholder="Explanation Text"
                onChange={(e) => setFieldValue('explanation_text', e.target.value)}
              />

              <CompactFileInput
                name="explanation_image"
                label="Explanation Image"
                placeholder="Upload Explanation Image (Optional)"
              />
            </div>

            {/* Submit Button */}
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default CreateTrueFalse;
