import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import FileInput from "@/components/ui/Fileinput";
import Button from "@/components/ui/Button";
import { Formik, Form, Field } from "formik";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import * as yup from "yup";

import { usePostApiMutation } from "@/store/api/master/commonSlice";
const BulkUpload = ({showModal, setShowModal, quiz}) => {
  const dispatch = useDispatch();

  const { id } = useParams();
  // const [quizCreateOrUpdate, { isLoading, isError, error, isSuccess }] = useQuizCreateOrUpdateMutation();
  const [postApi, { isLoading, isError, error, isSuccess }] = usePostApiMutation();
  const initialValues = { 
    chapter_quiz_id: ''
  };

  const validationSchema =  yup.object({

  });
  const onSubmit = async (values, { resetForm }) => {

    let formData = new FormData();

    formData.append('chapter_quiz_id', quiz.id);
    formData.append('file', values.file);
    
    const response = await postApi({
      end_point: "admin/import-question",
      body: formData
    });
    console.log(response);
    setShowModal(false);
  };
  return (
    <Modal
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      title="Upload Excel File of Questions"
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowModal(false)}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={initialValues}
        onSubmit={onSubmit}
      >
        {({
          values, errors,
          setFieldValue
        }) => (
          <Form>
           
           <div className="grid md:grid-cols-1 pb-4 border-b border-slate-200 dark:border-slate-700">
            
           <div>
                    <label className="block text-[#1D1D1F] text-base font-medium mb-2">
                      Upload CSV File
                    </label>
                    <FileInput
                      name="file"
                      accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                      type="file"
                      placeholder="Question CSV File"
                      preview={true}
                      selectedFile={values.file}
                      onChange={(e) => 
                        setFieldValue("file", e.currentTarget.files[0])}
                    />
                  </div>
            </div>
    
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default BulkUpload;
