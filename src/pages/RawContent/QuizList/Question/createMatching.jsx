import React, { useState, useEffect } from "react";
import Mo<PERSON> from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import CompactFileInput from "@/components/ui/CompactFileInput";
import Button from "@/components/ui/Button";
import { Formik, Form } from "formik";
import * as yup from "yup";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { Icon } from "@iconify/react";
import { toast } from "react-toastify";

const CreateMatching = ({ showMatchingModal, setShowMatchingModal, quiz, editData }) => {
  const [postApi, { isLoading }] = usePostApiMutation();

  // Determine if we're in edit mode
  const isEditMode = !!editData;

  // Initialize matching pairs with default values or from edit data
  const [matchingPairs, setMatchingPairs] = useState([
    { id: 1, question: "", answer: "" },
    { id: 2, question: "", answer: "" }
  ]);

  // Keep track of removed pairs that had server IDs
  const [removedPairIds, setRemovedPairIds] = useState([]);

  // Effect to populate form with edit data when available
  useEffect(() => {
    if (isEditMode && editData) {
      console.log("Edit data received:", editData);

      // Access matching_answers from the correct location in the data structure
      const question = editData.question || editData;
      const matchingAnswers = question.matching_answers || [];

      console.log("Matching answers:", matchingAnswers);

      if (matchingAnswers.length > 0) {
        // Convert the matching_answers array to the format used by the component
        const pairs = matchingAnswers.map((item, index) => ({
          id: item.id || index + 1,
          question: item.left_item || "",
          answer: item.right_item || ""
        }));

        console.log("Converted pairs:", pairs);

        if (pairs.length > 0) {
          setMatchingPairs(pairs);
        }
      }
    } else {
      // Reset to default state when not in edit mode
      setMatchingPairs([
        { id: 1, question: "", answer: "" },
        { id: 2, question: "", answer: "" }
      ]);
      setRemovedPairIds([]);
    }
  }, [isEditMode, editData]);

  const addMatchingPair = () => {
    const newId = matchingPairs.length > 0
      ? Math.max(...matchingPairs.map(p => p.id)) + 1
      : 1;

    setMatchingPairs([
      ...matchingPairs,
      { id: newId, question: "", answer: "" }
    ]);
  };

  const removeMatchingPair = (id) => {
    if (matchingPairs.length > 1) {
      // If this is a server-assigned ID (positive number), track it for deletion
      if (typeof id === 'number' && id > 0) {
        setRemovedPairIds(prev => [...prev, id]);
        console.log("Tracking removed pair ID for deletion:", id);
      }

      setMatchingPairs(matchingPairs.filter(pair => pair.id !== id));
    }
  };

  const updateMatchingPair = (id, field, value) => {
    setMatchingPairs(
      matchingPairs.map(pair =>
        pair.id === id ? { ...pair, [field]: value } : pair
      )
    );
  };

  // Get initial values based on edit data if available
  const getInitialValues = () => {
    if (isEditMode) {
      const question = editData.question || editData;
      console.log("Getting initial values from:", question);

      return {
        id: question.id,
        chapter_quiz_id: quiz?.id || '',
        question_title: question.question_text || '',
        explanation_text: question.explanation_text || '',
        explanation_image: '',
        is_active: 1
      };
    }

    return {
      chapter_quiz_id: quiz?.id || '',
      question_title: '',
      explanation_text: '',
      explanation_image: '',
      is_active: 1
    };
  };

  const validationSchema = yup.object({
    question_title: yup.string()
      .max(100, "Should not be more than 100 characters")
      .required("Question title is required"),
  });

  const onSubmit = async (values, { resetForm }) => {
    const isValid = matchingPairs.every(pair => pair.question.trim() && pair.answer.trim());

    if (!isValid) {
      toast.error("Please fill in all questions and answers");
      return;
    }

    if (matchingPairs.length < 2) {
      toast.error("Please add at least 2 matching pairs");
      return;
    }

    let formData = new FormData();

    // If editing, include the question ID
    if (isEditMode) {
      const question = editData.question || editData;
      formData.append('id', question.id);
      console.log("Including question ID in form data for update:", question.id);
    }

    formData.append('chapter_quiz_id', quiz.id);
    formData.append('question_text', values.question_title);
    formData.append('explanation_text', values.explanation_text || '');
    formData.append('is_active', 1);
    formData.append('question_type', 'matching');

    if (values.explanation_image) {
      formData.append('explanation_image', values.explanation_image);
    }

    console.log("Matching pairs to submit:", matchingPairs);

    matchingPairs.forEach((pair, index) => {
      formData.append(`matching_answers[${index}][left_item]`, pair.question);
      formData.append(`matching_answers[${index}][right_item]`, pair.answer);

      // Only include the ID if in edit mode and the ID is positive (server ID)
      if (isEditMode && typeof pair.id === 'number' && pair.id > 0) {
        formData.append(`matching_answers[${index}][id]`, pair.id);
        console.log(`Including ID for pair ${index}:`, pair.id);
      }
    });

    // Include removed pair IDs for deletion
    if (isEditMode && removedPairIds.length > 0) {
      formData.append('deleted_matching_answer_ids', JSON.stringify(removedPairIds));
      console.log("Including deleted pair IDs:", removedPairIds);
    }

    // Log the form data being sent
    console.log("Submitting matching question with data:");
    for (let [key, value] of formData.entries()) {
      console.log(`${key}: ${value}`);
    }

    try {
      const response = await postApi({
        end_point: "admin/chapter-quiz-matching-save-or-update",
        body: formData,
      });

      if (response.data) {
        console.log(isEditMode
          ? "Matching question updated successfully"
          : "Matching question created successfully"
        );

        resetForm();
        setMatchingPairs([
          { id: 1, question: "", answer: "" },
          { id: 2, question: "", answer: "" }
        ]);

        // Reset the removed pair IDs
        setRemovedPairIds([]);

        setShowMatchingModal(false);
      }
    } catch (error) {
      console.error("Error creating matching question:", error);

      if (error?.data?.errors) {
        const errorMessages = Object.values(error.data.errors)
          .flat()
          .join(', ');
        toast.error(errorMessages);
      } else if (error?.data?.message) {
        toast.error(error.data.message);
      } else {
        toast.error("Failed to create matching question");
      }
    }
  };

  return (
    <Modal
      activeModal={showMatchingModal}
      onClose={() => setShowMatchingModal(false)}
      title={isEditMode ? "Edit Matching Question" : "Add Matching Question"}
      className="max-w-4xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-outline-primary"
          onClick={() => setShowMatchingModal(false)}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={getInitialValues()}
        onSubmit={onSubmit}
      >
        {({
          values,
          setFieldValue
        }) => (
          <Form>
            <div className="grid md:grid-cols-1 gap-2 pb-5 border-b border-slate-200 dark:border-slate-700">
              <InputField
                name="question_title"
                label="Question Title"
                value={values.question_title}
                placeholder="Enter a title for this matching question"
                onChange={(e) => setFieldValue('question_title', e.target.value)}
              />
            </div>

            <div className="grid md:grid-cols-1 gap-2 p-5 border-b border-slate-200 dark:border-slate-700">
              <div className="mb-4">
                <h3 className="text-lg font-medium mb-2">Matching Question</h3>
                <p className="text-sm text-gray-600 mb-4">
                  Add items and their matching answers below. Students will need to match items from the left column with their corresponding answers from the right column.
                </p>
                <div className="bg-blue-50 p-3 rounded-md mb-4 text-sm">
                  <p className="font-medium text-blue-700">How it works:</p>
                  <ul className="list-disc pl-5 mt-1 text-blue-600">
                    <li>Add at least 2 matching pairs (item → answer)</li>
                    <li>Items will be shown in the left column</li>
                    <li>Answers will be shown in the right column in randomized order</li>
                    <li>Students must drag and connect the correct pairs</li>
                  </ul>
                </div>

                <div className="grid grid-cols-12 gap-4 mb-4 items-center">
                  <div className="col-span-5 text-center font-medium">
                    Question
                  </div>
                  <div className="col-span-1"></div>
                  <div className="col-span-5 text-center font-medium">
                    Answer
                  </div>
                  <div className="col-span-1"></div>
                </div>

                {matchingPairs.map((pair) => (
                  <div key={pair.id} className="grid grid-cols-12 gap-4 mb-4">
                    <div className="col-span-5 flex items-center">
                      <input
                        type="text"
                        className="form-control h-[40px] w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 focus:outline-none focus:ring-0 focus:border-primary"
                        name={`question_${pair.id}`}
                        placeholder="Enter item (e.g., France)"
                        value={pair.question}
                        onChange={(e) => updateMatchingPair(pair.id, 'question', e.target.value)}
                      />
                    </div>

                    <div className="col-span-1 flex justify-center items-center">
                      <Icon icon="heroicons-outline:arrow-right" className="text-xl text-gray-400" />
                    </div>

                    <div className="col-span-5 flex items-center">
                      <input
                        type="text"
                        className="form-control h-[40px] w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 focus:outline-none focus:ring-0 focus:border-primary"
                        name={`answer_${pair.id}`}
                        placeholder="Enter matching answer (e.g., Paris)"
                        value={pair.answer}
                        onChange={(e) => updateMatchingPair(pair.id, 'answer', e.target.value)}
                      />
                    </div>

                    <div className="col-span-1 flex items-center justify-center">
                      <button
                        type="button"
                        className="h-[40px] w-[40px] flex items-center justify-center rounded-md bg-red-100 text-red-500 hover:bg-red-200"
                        onClick={() => removeMatchingPair(pair.id)}
                        disabled={matchingPairs.length <= 1}
                      >
                        <Icon icon="heroicons-outline:trash" className="text-lg" />
                      </button>
                    </div>
                  </div>
                ))}

                <div className="flex justify-center mt-4">
                  <Button
                    type="button"
                    className="btn btn-sm btn-outline-primary"
                    onClick={addMatchingPair}
                  >
                    <Icon icon="heroicons-outline:plus" className="mr-1" />
                    Add Another Matching Pair
                  </Button>
                </div>
              </div>
            </div>

            <div className="grid md:grid-cols-1 gap-2 p-5 border-b border-slate-200 dark:border-slate-700">
              <InputField
                name="explanation_text"
                label="Explanation (Optional)"
                value={values.explanation_text}
                placeholder="Explanation Text"
                onChange={(e) => setFieldValue('explanation_text', e.target.value)}
              />

              <CompactFileInput
                name="explanation_image"
                placeholder="Explanation Image (Optional)"
              />
            </div>

            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
                disabled={isLoading || !values.question_title || matchingPairs.some(pair => !pair.question || !pair.answer) || matchingPairs.length < 2}
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default CreateMatching;
