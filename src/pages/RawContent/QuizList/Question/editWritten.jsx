import React, { useState, useEffect } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Textarea from "@/components/ui/Textarea";
import FileInput from "@/components/ui/Fileinput";
import Checkbox from "@/components/ui/Checkbox";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import Select from "@/components/ui/Select";
import { Formik, Form, Field } from "formik";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import * as yup from "yup";
import { 
  useGetSubjectByQuizListQuery,
  useGetQuestionSetListQuery,
  useQuestionCreateOrUpdateMutation } from "@/store/api/master/rowContentQuizListSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";

const EditWritten = ({showEditWrittenModal, setEditShowWrittenModal, written_questions}) => {
  const dispatch = useDispatch();
  const assetUrl = import.meta.env.VITE_ASSET_HOST_URL;
  const { id } = useParams();
  const [postApi, { isLoading, isError, error, isSuccess }] = usePostApiMutation();
  const [showFileInput, setShowFileInput] = useState(false);

  const [previewUrl, setPreviewUrl] = useState(null);



  const initialValues = { 
    id: written_questions?.id,
    description: written_questions?.description || '',
    marks: written_questions?.marks || '',
    no_of_question: written_questions?.no_of_question || '',
    instruction: written_questions?.instruction || '',
    duration: written_questions?.duration || '',
    question_attachment: ''
  };

  const validationSchema = yup.object({
    description: yup.string()
        .max(250, "Should not be more than 250 characters")
        .min(3, "Should not be less than 3 characters")
        .required("Question is Required"),
    marks: yup.number()
        .required("Marks is Required"),
    no_of_question: yup.number()
        .required("Question Number is Required"),
    instruction: yup.string()
        .max(300, "Should not be more than 300 characters")
        .required("Instruction is Required"),
    duration: yup.string()
        .required("Duration is Required"),
  });

  const onSubmit = async (values, { resetForm }) => {
    let formData = new FormData();
    formData.append('id', values.id);
    formData.append('chapter_quiz_id', id);
    formData.append('description', values.description);
    formData.append('marks', values.marks);
    formData.append('instruction', values.instruction);
    formData.append('duration', values.duration);
    formData.append('no_of_question', values.no_of_question);
    if (values.question_attachment) {
      formData.append('question_attachment', values.question_attachment);
    }
    formData.append('is_active', 1);

    await postApi({
      end_point: "admin/written-question-save-or-update",
      body: formData,
    });

    setEditShowWrittenModal(false);
  };

  return (
    <Modal
      activeModal={showEditWrittenModal}
      onClose={() => setEditShowWrittenModal(false)}
      title="Update Written Question"
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setEditShowWrittenModal(false)}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={initialValues}
        onSubmit={onSubmit}
      >
        {({
          values, errors,
          setFieldValue
        }) => (
          <Form>
            <div className="grid md:grid-cols-1 gap-2 pb-5 border-b border-slate-200 dark:border-slate-700">
              <label className="block">
                Question <span className="text-red-500">*</span>
              </label>
              <Textarea 
                placeholder="Write Question" 
                name="description"
                defaultValue={values.description}
                onChange={(e) => setFieldValue("description", e.target.value)}
              />
              
              <label className="block">
                Question Image <span className="text-red-500 ml-1">*</span>
              </label>
              {previewUrl || written_questions?.question_attachment ? (
                <img
                  src={previewUrl ? previewUrl : `${assetUrl}/${written_questions?.question_attachment}`}
                  alt="Question Attachment"
                  className="w-24 h-24 object-cover rounded-md cursor-pointer"
                  onClick={() => {
                    setPreviewUrl(null);
                    setFieldValue("question_attachment", null); // optionally clear file
                  }}
                />
              ) : null}

              <FileInput
                  name="question_attachment"
                  accept="image/*"
                  type="file"
                  placeholder="Question Images (Optional)"
                  preview={true}
                  selectedFile={values.question_attachment}
                  onChange={(e) => {
                    setFieldValue("question_attachment", e.target.files[0]);
                    setPreviewUrl(e.target.files[0] ? URL.createObjectURL(e.target.files[0]) : null);
                  }}
                />
              <NumberInput
                label="Question Number"
                name="no_of_question"
                type="text"
                placeholder="Enter Number of Question"
                required
              />

              <NumberInput
                label="Mark"
                name="marks"
                type="text"
                placeholder="Enter Mark"
                required
              />
              <>
                <label className="block">Instruction 
                  <span className="text-red-500">*</span>
                </label>
             
                  <Textarea placeholder="Write Instruction " name="instruction"
                    defaultValue={values.instruction}
                    onChange={(e) => {
                    setFieldValue("instruction", e.target.value);
                    }} 
                    className={errors.instruction ? "border-red-500" : ""}
                    />
                  {errors.instruction && <div className="text-red-500 text-xs">{errors.instruction}</div>}
                  </>
  
                <NumberInput
                label="Duration (Minute)"
                name="duration"
                type="text"
                placeholder="Ex. 30"
                required
                />
            </div>

            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default EditWritten;
