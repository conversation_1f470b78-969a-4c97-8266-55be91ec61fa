import React, { useState, useRef } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import CompactFileInput from "@/components/ui/CompactFileInput";
import Button from "@/components/ui/Button";
import { Formik, Form } from "formik";
import * as yup from "yup";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { Icon } from "@iconify/react";
import { toast } from "react-toastify";
import "./fillBlanksStyles.css";

const CreateFillBlanks = ({ showFillBlanksModal, setShowFillBlanksModal, quiz, editData }) => {
  const [postApi, { isLoading }] = usePostApiMutation();
  const [previewText, setPreviewText] = useState("");
  const [warningMessage, setWarningMessage] = useState("");
  const textareaRef = useRef(null);
  const isEditMode = !!editData;
  const defaultInitialValues = {
    chapter_quiz_id: quiz?.id || '',
    question_text: '',
    question_image: '',
    explanation_text: '',
    explanation_image: '',
    is_active: 1
  };

  const convertToEditorFormat = (questionText, blankAnswers) => {
    if (!questionText || !blankAnswers || !Array.isArray(blankAnswers) || blankAnswers.length === 0) {
      return questionText;
    }

    let result = questionText;
    let offset = 0;

    const sortedBlanks = [...blankAnswers].sort((a, b) => {
      const keyA = parseInt(a.blank_key.split('_')[1]);
      const keyB = parseInt(b.blank_key.split('_')[1]);
      return keyA - keyB;
    });

    sortedBlanks.forEach(blank => {
      const blankIndex = result.indexOf('_______', offset);
      if (blankIndex !== -1) {
        const before = result.substring(0, blankIndex);
        const after = result.substring(blankIndex + 7); // 7 is the length of '_______'
        result = before + `[blank:${blank.blank_answer}]` + after;
        offset = blankIndex + `[blank:${blank.blank_answer}]`.length;
      }
    });

    return result;
  };

  const getFormInitialValues = () => {
    if (isEditMode) {
      const question = editData.question || editData;
      console.log("Fill in blanks Edit Data:", editData);
      console.log("Question data for form:", question);

      const editorQuestionText = convertToEditorFormat(
        question.question_text,
        question.blank_answers
      );

      console.log("Converted editor text:", editorQuestionText);

      return {
        id: question.id,
        chapter_quiz_id: quiz?.id || '',
        question_text: editorQuestionText || '',
        question_image: '',
        explanation_text: question.explanation_text || '',
        explanation_image: '',
        is_active: 1
      };
    }
    return defaultInitialValues;
  };

  const validationSchema = yup.object({
    question_text: yup.string()
      .max(500, "Should not be more than 500 characters")
      .min(10, "Should not be less than 10 characters")
      .required("Question text is required")
  });
  const insertBlank = () => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const text = textarea.value;
      const selectedText = text.substring(start, end);

      const isWithinBlank = isSelectionWithinBlank(text, start, end);
      if (isWithinBlank) {
        setWarningMessage("Cannot create a blank within an existing blank");
        setTimeout(() => {
          setWarningMessage("");
        }, 3000);

        return;
      }

      const blank = selectedText ? `[blank:${selectedText}]` : '[blank:answer]';
      const newText = text.substring(0, start) + blank + text.substring(end);
      textarea.value = newText;
      if (formikRef.current) {
        formikRef.current.setFieldValue('question_text', newText);
      }
      updatePreview(newText);
      textarea.focus();
      const newCursorPosition = start + blank.length;
      textarea.setSelectionRange(newCursorPosition, newCursorPosition);
    }
  };

  const isSelectionWithinBlank = (text, start, end) => {
    const blanks = [];
    const regex = /\[blank:(.*?)\]/g;
    let match;

    while ((match = regex.exec(text)) !== null) {
      blanks.push({
        startIndex: match.index,
        endIndex: match.index + match[0].length
      });
    }
    for (const blank of blanks) {
      if (start >= blank.startIndex && start < blank.endIndex) {
        return true;
      }
      if (end > blank.startIndex && end <= blank.endIndex) {
        return true;
      }
      if (start <= blank.startIndex && end >= blank.endIndex) {
        return true;
      }
    }

    return false;
  };

  const updatePreview = (text) => {
    if (!text) {
      setPreviewText("");
      return;
    }

    let index = 0;
    const previewHtml = text.replace(/\[blank:(.*?)\]/g, (_) => {
      const blankHtml = `
        <span class="inline-flex items-center mx-3 my-2">
          <input type="text" class="border border-gray-300 rounded px-3 py-1.5 mx-2 w-28 bg-gray-100" disabled placeholder="________" />
          <button
            type="button"
            class="text-red-500 hover:text-red-700 ml-2 p-1.5 rounded-full hover:bg-red-100"
            data-blank-index="${index}"
            onclick="window.removeBlank(${index})"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </span>
      `;
      index++;
      return blankHtml;
    });

    setPreviewText(previewHtml);
  };

  const formikRef = useRef(null);
  const removeBlank = (index) => {
    if (textareaRef.current && formikRef.current) {
      const textarea = textareaRef.current;
      const text = textarea.value;
      const blanks = [];
      const regex = /\[blank:(.*?)\]/g;
      let match;

      while ((match = regex.exec(text)) !== null) {
        blanks.push({
          fullMatch: match[0],
          answer: match[1],
          startIndex: match.index,
          endIndex: match.index + match[0].length
        });
      }
      if (index >= 0 && index < blanks.length) {
        const blankToRemove = blanks[index];
        const newText =
          text.substring(0, blankToRemove.startIndex) +
          blankToRemove.answer +
          text.substring(blankToRemove.endIndex);
        textarea.value = newText;
        formikRef.current.setFieldValue('question_text', newText);
        updatePreview(newText);
      }
    }
  };
  React.useEffect(() => {
    window.removeBlank = removeBlank;
    return () => {
      delete window.removeBlank;
    };
  }, []);
  React.useEffect(() => {
    if (isEditMode && editData) {
      const question = editData.question || editData;
      const editorQuestionText = convertToEditorFormat(
        question.question_text,
        question.blank_answers
      );

      if (editorQuestionText) {
        updatePreview(editorQuestionText);
      }
    }
  }, [isEditMode, editData]);
  const extractBlanks = (text) => {
    const blanks = [];
    const regex = /\[blank:(.*?)\]/g;
    let match;
    let blankIndex = 0;

    while ((match = regex.exec(text)) !== null) {
      blanks.push({
        blank_key: `blank_${blankIndex}`,
        blank_answer: match[1]
      });
      blankIndex++;
    }

    return blanks;
  };

  const onSubmit = async (values, { resetForm }) => {
    const blanks = extractBlanks(values.question_text);
    if (blanks.length === 0) {
      toast.error("Please add at least one blank to the question");
      return;
    }

    // Validate that all blanks have answers
    const emptyBlanks = blanks.filter(blank => !blank.blank_answer.trim());
    if (emptyBlanks.length > 0) {
      toast.error("All blanks must have answers. Please fill in all blank answers.");
      return;
    }

    const displayText = values.question_text.replace(/\[blank:(.*?)\]/g, '_______');

    let requestData;
    const hasImages = values.question_image || values.explanation_image;

    if (hasImages) {
      let formData = new FormData();

      // If editing, include the question ID
      if (isEditMode) {
        const question = editData.question || editData;
        formData.append('id', question.id);
      }

      formData.append('chapter_quiz_id', quiz.id);
      formData.append('question_text', displayText);
      formData.append('explanation_text', values.explanation_text || '');
      formData.append('is_active', 1);
      formData.append('question_type', 'fill_blanks');
      formData.append('blank_answers_json', JSON.stringify(blanks));
      blanks.forEach((blank, index) => {
        formData.append(`blank_answers[${index}][blank_key]`, blank.blank_key);
        formData.append(`blank_answers[${index}][blank_answer]`, blank.blank_answer);
      });

      if (values.question_image) {
        formData.append('question_image', values.question_image);
      }

      if (values.explanation_image) {
        formData.append('explanation_image', values.explanation_image);
      }

      // Log the form data for debugging
      console.log('Submitting form data with images:');
      for (let [key, value] of formData.entries()) {
        console.log(`${key}: ${value}`);
      }

      requestData = formData;
    } else {
      const jsonData = {
        chapter_quiz_id: quiz.id,
        question_text: displayText,
        explanation_text: values.explanation_text || '',
        is_active: 1,
        question_type: 'fill_blanks',
        blank_answers: blanks,
      };

      // If editing, include the question ID
      if (isEditMode) {
        const question = editData.question || editData;
        jsonData.id = question.id;
      }

      console.log('Submitting JSON data:', jsonData);
      requestData = jsonData;
    }

    try {
      let response;
      try {
        response = await postApi({
          end_point: "admin/chapter-quiz-fill-in-blank-save-or-update",
          body: requestData,
        });
      } catch (firstError) {
        console.error("First attempt failed:", firstError);
        const simplifiedData = {
          chapter_quiz_id: quiz.id,
          question_text: displayText,
          explanation_text: values.explanation_text || '',
          is_active: 1,
          question_type: 'fill_blanks',
          blank_answers: blanks.map(b => ({
            blank_key: b.blank_key,
            blank_answer: b.blank_answer
          }))
        };

        // If editing, include the question ID
        if (isEditMode) {
          const question = editData.question || editData;
          simplifiedData.id = question.id;
          console.log("Including ID in simplified data for update:", question.id);
        }

        response = await postApi({
          end_point: "admin/quiz-question-save",
          body: simplifiedData,
        });
      }

      if (response.data) {
        console.log(isEditMode
          ? "Fill in the blanks question updated successfully"
          : "Fill in the blanks question created successfully"
        );
        resetForm();
        setPreviewText("");
        setShowFillBlanksModal(false);
      }
    } catch (error) {
      console.error("Error creating fill in the blanks question:", error);
      if (error?.data?.errors) {
        const errorMessages = Object.values(error.data.errors)
          .flat()
          .join(', ');
        toast.error(errorMessages);
      } else {
        toast.error(error?.data?.message || "Failed to create fill in the blanks question");
      }
    }
  };

  return (
    <Modal
      activeModal={showFillBlanksModal}
      onClose={() => setShowFillBlanksModal(false)}
      title={isEditMode ? "Edit Fill in the Blanks Question" : "Add Fill in the Blanks Question"}
      className="max-w-4xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-outline-primary"
          onClick={() => setShowFillBlanksModal(false)}
        />
      }
    >
      <Formik
        innerRef={formikRef}
        validationSchema={validationSchema}
        initialValues={getFormInitialValues()}
        onSubmit={onSubmit}
      >
        {({
          values,
          errors,
          touched,
          setFieldValue
        }) => (
          <Form>
            <div className="bg-blue-50 p-4 rounded-md mb-5">
              <h6 className="text-blue-700 font-medium mb-2">How to Create Fill in the Blanks Questions:</h6>
              <ol className="list-decimal pl-5 text-blue-600 space-y-2">
                <li>Type your question text in the box below</li>
                <li>Select the text you want to turn into a blank</li>
                <li>Click the "Insert Blank" button to create a blank with the selected text as the answer (if no text is selected, a default blank will be inserted)</li>
                <li>To remove a blank, click the X button next to it in the preview section</li>
                <li>Format: <code className="bg-white px-2 py-1 rounded">[blank:answer]</code> where "answer" is the correct answer</li>
              </ol>
            </div>
            {warningMessage && (
              <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4 rounded animate-fade-in-out" aria-live="polite">
                <p>{warningMessage}</p>
              </div>
            )}

            <div className="grid md:grid-cols-1 gap-2 pb-5 border-b border-slate-200 dark:border-slate-700">
              <div className="flex justify-between items-center mb-1">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                  Question Text <span className="text-red-500">*</span>
                </label>
                <Button
                  type="button"
                  className="btn btn-sm btn-outline-primary"
                  onClick={insertBlank}
                  disabled={!values.question_text}
                >
                  <Icon icon="heroicons-outline:document-text" className="mr-1" />
                  Insert Blank
                </Button>
              </div>

              <textarea
                ref={textareaRef}
                name="question_text"
                className="form-control h-32 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 focus:outline-none focus:ring-0 focus:border-primary"
                placeholder="Enter your question text here. Select text and click 'Insert Blank' to create blanks. Example: The capital of France is [blank:Paris]."
                value={values.question_text}
                onChange={(e) => {
                  setFieldValue('question_text', e.target.value);
                  updatePreview(e.target.value);
                }}
              />

              {errors.question_text && touched.question_text && (
                <div className="text-red-500 text-sm mt-1">{errors.question_text}</div>
              )}

              <CompactFileInput
                name="question_image"
                placeholder="Question Image (Optional)"
              />
            </div>

            {/* Preview Section */}
            {previewText && (
              <div className="mt-5 mb-5 border-b border-slate-200 dark:border-slate-700 pb-5">
                <h3 className="text-lg font-medium mb-3">Preview:</h3>
                <div
                  className="p-6 bg-white border rounded-md leading-relaxed preview-fill-blanks"
                  dangerouslySetInnerHTML={{ __html: previewText }}
                />
              </div>
            )}
            <div className="grid md:grid-cols-1 gap-2 p-5 border-b border-slate-200 dark:border-slate-700">
              <InputField
                name="explanation_text"
                label="Explanation (Optional)"
                value={values.explanation_text}
                placeholder="Explanation Text"
                onChange={(e) => setFieldValue('explanation_text', e.target.value)}
              />

              <CompactFileInput
                name="explanation_image"
                placeholder="Explanation Image (Optional)"
              />
            </div>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
                disabled={isLoading || !values.question_text || !previewText || errors.question_text}
              >
                {isLoading ? "Submitting..." : "Submit"}
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default CreateFillBlanks;
