import React from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import CompactFileInput from "@/components/ui/CompactFileInput";
import Checkbox from "@/components/ui/Checkbox";
import Button from "@/components/ui/Button";
import { Formik, Form } from "formik";
import { initialValues, validationSchema } from "./formSettings";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";

const createQuestion = ({showModal, setShowModal, quiz, editData}) => {
  const [postApi, { isLoading }] = usePostApiMutation();
  const isEditMode = !!editData;

  const onSubmit = async (values) => {
    let formData = new FormData();

    if (isEditMode) {
      const question = editData.question || editData;
      formData.append('id', question.id);
      console.log("Including ID in form data for update:", question.id);
    }

    formData.append('chapter_quiz_id', quiz.id);
    formData.append('question_text', values.question_text);
    if (values.question_image) {
      formData.append('question_image', values.question_image);
    }

    formData.append('option1', values.option1);
    if (values.option1_image) {
      formData.append('option1_image', values.option1_image);
    }

    formData.append('option2', values.option2);
    if (values.option2_image) {
      formData.append('option2_image', values.option2_image);
    }
    formData.append('option3', values.option3);
    if (values.option3_image) {
      formData.append('option3_image', values.option3_image);
    }
    formData.append('option4', values.option4);
    if (values.option4_image) {
      formData.append('option4_image', values.option4_image);
    }
    formData.append('answer1', values.answer1 ? 1 : 0);
    formData.append('answer2', values.answer2 ? 1 : 0);
    formData.append('answer3', values.answer3 ? 1 : 0);
    formData.append('answer4', values.answer4 ? 1 : 0);
    formData.append('explanation_text', values.explanation_text || '');
    if (values.explanation_image) {
      formData.append('explanation_image', values.explanation_image);
    }
    formData.append('is_active', 1);

    console.log("Submitting MCQ question with data:");
    for (let [key, value] of formData.entries()) {
      console.log(`${key}: ${value}`);
    }

    try {
      const response = await postApi({
        end_point: "admin/chapter-quiz-question-save-or-update",
        body: formData,
      });

      console.log("API Response:", response);

      if (response.data) {
        console.log(isEditMode
          ? "MCQ question updated successfully"
          : "MCQ question created successfully"
        );
      }

      setShowModal(false);
    } catch (error) {
      console.error("Error saving MCQ question:", error);

      if (error?.data?.errors) {
        const errorMessages = Object.values(error.data.errors)
          .flat()
          .join(', ');
        toast.error(errorMessages);
      } else if (error?.data?.message) {
        toast.error(error.data.message);
      } else {
        toast.error("Failed to save MCQ question");
      }
    }
  };
  const getFormInitialValues = () => {
    if (isEditMode) {
      const question = editData.question || editData;
      console.log("MCQ Edit Data:", editData);
      console.log("Question data for form:", question);

      return {
        id: question.id,
        question_text: question.question_text || '',
        question_image: '',
        option1: question.option1 || '',
        option1_image: '',
        option2: question.option2 || '',
        option2_image: '',
        option3: question.option3 || '',
        option3_image: '',
        option4: question.option4 || '',
        option4_image: '',
        answer1: question.answer1 === 1 || question.answer1 === true,
        answer2: question.answer2 === 1 || question.answer2 === true,
        answer3: question.answer3 === 1 || question.answer3 === true,
        answer4: question.answer4 === 1 || question.answer4 === true,
        explanation_text: question.explanation_text || '',
        explanation_image: '',
        is_active: 1
      };
    }
    return initialValues;
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      title={isEditMode ? "Edit Question" : "Add New Question"}
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowModal(false)}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={getFormInitialValues()}
        onSubmit={onSubmit}
      >
        {({
          values,
          setFieldValue
        }) => (
          <Form>


           <div className="grid md:grid-cols-1 gap-2 pb-5 border-b border-slate-200 dark:border-slate-700">
            <InputField
              name="question_text"
              label="Question"
              value={values.question_text}
              placeholder="Question Text"
              onChange={(e) => setFieldValue('question_text', e.target.value)}
            />

              <CompactFileInput
                name="question_image"
                placeholder="Question Image (Optional)"
              />
            </div>

            <div className="grid md:grid-cols-5 gap-2 p-5 border-b border-slate-200 dark:border-slate-700">
            <Checkbox
              className="col-span-1"
              name="answer1"
              label="Option 1"
              value={values.answer1}
              onChange={(e) => setFieldValue('answer1', e.target.checked)}
            />
            <div className="col-span-2">
            <InputField
              name="option1"
              placeholder="Option 1"
              onChange={(e) => setFieldValue('option1', e.target.value)}
            />
            </div>
              <div className="col-span-2">
                <CompactFileInput
                  name="option1_image"
                  placeholder="Option 1 Image"
                />
              </div>
            </div>


            <div className="grid md:grid-cols-5 gap-2 p-5 border-b border-slate-200 dark:border-slate-700">
            <Checkbox
              className="col-span-1"
              name="answer2"
              label="Option 2"
              value={values.answer2}
              onChange={(e) => setFieldValue('answer2', e.target.checked)}
            />
              <div className="col-span-2">
            <InputField
              name="option2"
              placeholder="Option 2"
              onChange={(e) => setFieldValue('option2', e.target.value)}
            />
            </div>
              <div className="col-span-2">
                <CompactFileInput
                  name="option2_image"
                  placeholder="Option 2 Image"
                />
              </div>
            </div>


            <div className="grid md:grid-cols-5 gap-2 p-5 border-b border-slate-200 dark:border-slate-700">
            <Checkbox
              className="col-span-1"
              name="answer3"
              label="Option 3"
              value={values.answer3}
              onChange={(e) => setFieldValue('answer3', e.target.checked)}
            />
              <div className="col-span-2">
            <InputField
              name="option3"
              placeholder="Option 3"
              onChange={(e) => setFieldValue('option3', e.target.value)}
            />
            </div>
              <div className="col-span-2">
                <CompactFileInput
                  name="option3_image"
                  placeholder="Option 3 Image"
                />
              </div>
            </div>

            <div className="grid md:grid-cols-5 gap-2 p-5 border-b border-slate-200 dark:border-slate-700">
            <Checkbox
              className="col-span-1"
              name="answer4"
              label="Option 4"
              value={values.answer4}
              onChange={(e) => setFieldValue('answer4', e.target.checked)}
            />
              <div className="col-span-2">
            <InputField
              name="option4"
              placeholder="Option 4"
              onChange={(e) => setFieldValue('option4', e.target.value)}
            />
            </div>
              <div className="col-span-2">
                <CompactFileInput
                  name="option4_image"
                  placeholder="Option 4 Image"
                />
              </div>
            </div>

           <div className="grid md:grid-cols-1 gap-2 p-5 border-b border-slate-200 dark:border-slate-700">
            <InputField
              name="explanation_text"
              label="Explanation (Optional)"
              value={values.explanation_text}
              placeholder="Explanation Text"
              onChange={(e) => setFieldValue('explanation_text', e.target.value)}
            />

              <CompactFileInput
                name="explanation_image"
                placeholder="Explanation Image (Optional)"
              />
            </div>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default createQuestion;
