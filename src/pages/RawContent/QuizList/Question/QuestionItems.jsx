import React, { useState } from "react";
import Card from "@/components/ui/Card";
import { Icon } from "@iconify/react";
import Badge from "@/components/ui/Badge";
import Modal from "@/components/ui/Modal";

const QuestionItems = ({ quiz_items, onDelete, onEdit }) => {
  const ASSET_URL = import.meta.env.VITE_ASSET_HOST_URL || '';
  const [zoomImage, setZoomImage] = useState(null);
  const [showImageModal, setShowImageModal] = useState(false);

  // Helper function to handle image clicks
  const handleImageClick = (imageUrl) => {
    setZoomImage(imageUrl);
    setShowImageModal(true);
  };

  if (!quiz_items || quiz_items.length === 0) {
    return (
      <div className="text-center py-6">
        <Icon icon="heroicons-outline:inbox" className="mx-auto text-3xl text-gray-400 mb-2" />
        <h3 className="text-sm font-medium text-gray-600 dark:text-gray-300">No questions available</h3>
      </div>
    );
  }

  const getQuestionTypeLabel = (type) => {
    const typeLabels = {
      mcq: { label: "MCQ", className: "bg-indigo-500" },
      true_false: { label: "T/F", className: "bg-emerald-500" },
      fill_in_blank: { label: "Fill In Blank", className: "bg-indigo-500" },
      fill_blanks: { label: "Fill In Blank", className: "bg-indigo-500" },
      matching: { label: "Matching", className: "bg-amber-500" },
    };
    const { label, className } = typeLabels[type] || { label: type, className: "bg-gray-500" };
    return <Badge label={label} className={`${className} text-white text-xs px-1.5 py-0.5 rounded`} />;
  };

  const renderQuestionContent = (item) => {
    const question = item.question || item;

    if (item.type === 'mcq') {
      return (
        <div className="grid grid-cols-1 gap-1.5 mt-2">
          {['option1', 'option2', 'option3', 'option4'].map((opt, optIndex) => {
            if (!question[opt]) return null;
            const isCorrect = question[`answer${optIndex + 1}`];
            const optionImage = question[`${opt}_image`];
            return (
              <div
                key={optIndex}
                className={`text-xs p-2 rounded border ${
                  isCorrect ? 'border-emerald-500 bg-emerald-50 dark:bg-emerald-900/10' : 'border-gray-200 dark:border-slate-700'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <span className="font-medium">{String.fromCharCode(65 + optIndex)}.</span>
                  <span className="flex-1">{question[opt]}</span>
                  {optionImage && (
                    <img
                      src={`${ASSET_URL}/${optionImage}`}
                      alt={`Option ${optIndex + 1}`}
                      className="w-8 h-8 object-cover rounded border cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => handleImageClick(`${ASSET_URL}/${optionImage}`)}
                    />
                  )}
                </div>
              </div>
            );
          })}
        </div>
      );
    }

    if (item.type === 'true_false') {
      return (
        <div className="space-y-2 mt-2">
          <div className="flex gap-1.5">
            {['True', 'False'].map((value) => {
              const isCorrect = (question.answer && value === 'True') || (!question.answer && value === 'False');
              return (
                <span
                  key={value}
                  className={`text-xs px-2 py-0.5 rounded ${
                    isCorrect ? 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20' : 'bg-gray-100 text-gray-600 dark:bg-slate-700'
                  }`}
                >
                  {value}
                </span>
              );
            })}
          </div>
   
        </div>
      );
    }

    if (item.type === 'fill_in_blank' || item.type === 'fill_blanks') {
      let blankAnswers = [];

      if (Array.isArray(question.blank_answers)) {
        blankAnswers = question.blank_answers;
      } else if (typeof question.blank_answers === 'object' && question.blank_answers !== null) {
        blankAnswers = Object.values(question.blank_answers);
      } else if (question.blank_answers_json) {
        try {
          const parsed = JSON.parse(question.blank_answers_json);
          if (Array.isArray(parsed)) {
            blankAnswers = parsed;
          }
        } catch (e) {
          console.error("Failed to parse blank_answers_json", e);
        }
      }

      if (blankAnswers.length === 0) {
        return (
          <div className="mt-2 text-xs text-gray-500 italic">
            No blank answers available
          </div>
        );
      }

      return (
        <div className="mt-2 space-y-2">
          <div className="flex flex-wrap items-center gap-1.5">
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300 mr-1 my-0.5">
              Correct Answers:
            </span>
            {blankAnswers.map((blank, idx) => {
              let answer = '';
              if (typeof blank === 'string') {
                answer = blank;
              } else if (blank && typeof blank === 'object') {
                answer = blank.blank_answer || blank.answer || '';
              }

              const blankNumber = idx + 1;

              return (
                <div
                  key={idx}
                  className="bg-indigo-50 border border-indigo-200 text-indigo-800 dark:bg-indigo-900/10 dark:border-indigo-800/30 dark:text-indigo-200 text-xs px-2 py-1 rounded flex items-center my-0.5"
                >
                  <span className="bg-indigo-100 dark:bg-indigo-800/30 text-indigo-700 dark:text-indigo-300 rounded-full w-4 h-4 inline-flex items-center justify-center mr-1.5 text-[10px] font-medium">
                    {blankNumber}
                  </span>
                  <span className="font-medium">{answer}</span>
                </div>
              );
            })}
          </div>
      
        </div>
      );
    }

    if (item.type === 'matching') {
      const matchingAnswers = question.matching_answers || [];

      if (matchingAnswers.length === 0) {
        return (
          <div className="mt-2 text-xs text-gray-500 italic">
            No matching pairs available
          </div>
        );
      }

      return (
        <div className="mt-2 space-y-2">
          <div className="space-y-1.5">
            {matchingAnswers.map((pair, idx) => (
              <div
                key={idx}
                className="flex items-center gap-2 text-xs"
              >
                <div className="bg-amber-50 border border-amber-200 text-amber-800 dark:bg-amber-900/10 dark:border-amber-800/30 dark:text-amber-200 px-2 py-1 rounded flex-1">
                  {pair.left_item}
                </div>
                <Icon icon="heroicons-outline:arrow-right" className="text-gray-400" />
                <div className="bg-emerald-50 border border-emerald-200 text-emerald-800 dark:bg-emerald-900/10 dark:border-emerald-800/30 dark:text-emerald-200 px-2 py-1 rounded flex-1">
                  {pair.right_item}
                </div>
              </div>
            ))}
          </div>
          {/* Matching Question Image */}
          {question.question_image && (
            <img
              src={`${ASSET_URL}/${question.question_image}`}
              alt="Matching Question"
              className="max-w-full h-16 object-cover rounded border cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => handleImageClick(`${ASSET_URL}/${question.question_image}`)}
            />
          )}
        </div>
      );
    }

    return null;
  };

  return (
    <div className="mt-3">
      <div className="flex items-center justify-between mb-2">
        <h2 className="text-base font-semibold text-gray-800 dark:text-white">Questions</h2>
        <span className="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full dark:bg-blue-900 dark:text-blue-300">
          {quiz_items.length}
        </span>
      </div>

      <div className="space-y-2 grid grid-cols-2 gap-2">
        {quiz_items.map((item, index) => {
          const question = item.question || item;
          return (
            <Card
              key={index}
              className="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 p-3 relative"
            >
              <div className="absolute top-2 right-2 flex items-center gap-1 z-10">
                <button
                  onClick={() => onEdit(item)}
                  className="text-blue-500 hover:text-blue-700 transition-colors bg-blue-50 hover:bg-blue-100 p-1.5 rounded-md"
                >
                  <Icon icon="material-symbols:edit-outline-rounded" className="text-lg" />
                </button>
                <button
                  onClick={() => onDelete(item)}
                  className="text-red-500 hover:text-red-700 transition-colors bg-red-50 hover:bg-red-100 p-1.5 rounded-md"
                >
                  <Icon icon="material-symbols:delete-outline-rounded" className="text-lg" />
                </button>
              </div>

              <div className="flex items-start gap-1.5 mt-4">
                <div className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-full w-5 h-5 flex items-center justify-center flex-shrink-0 text-xs">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <div className="flex items-center mb-1">
                    <div className="flex items-center gap-1">
                      {getQuestionTypeLabel(item.type)}
                      {question.answer !== undefined && (
                        <span className={`text-xs px-1 py-0.5 rounded ${
                          question.answer ? 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20' : 'bg-rose-100 text-rose-800 dark:bg-rose-900/20'
                        }`}>
                          {question.answer ? 'T' : 'F'}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="space-y-2">
                    {(item.type === 'fill_blanks' || item.type === 'fill_in_blank') ? (
                      <p className="text-sm text-gray-800 dark:text-white">
                        {question.question_text.includes('_______') ? (
                          // If the question text contains underscores, split and render with blanks
                          question.question_text.split('_______').map((part, i, arr) => (
                            <React.Fragment key={i}>
                              {part}
                              {i < arr.length - 1 && (
                                <span className="bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-200 px-1.5 mx-0.5 rounded inline-flex items-center">
                                  <span className="bg-indigo-200 dark:bg-indigo-800/50 text-indigo-700 dark:text-indigo-300 rounded-full w-3.5 h-3.5 inline-flex items-center justify-center mr-1 text-[9px] font-medium">
                                    {i+1}
                                  </span>
                                  <span className="text-indigo-700 dark:text-indigo-300">____</span>
                                </span>
                              )}
                            </React.Fragment>
                          ))
                        ) : (
                          question.question_text
                        )}
                      </p>
                    ) : (
                      <p className="text-sm text-gray-800 dark:text-white line-clamp-2">{question.question_text}</p>
                    )}

                    {/* Question Image */}
                    {question.question_image && (
                      <div className="mt-2">
                        <img
                          src={`${ASSET_URL}/${question.question_image}`}
                          alt="Question"
                          className="max-w-full h-20 object-cover rounded border cursor-pointer hover:shadow-md transition-shadow"
                          onClick={() => handleImageClick(`${ASSET_URL}/${question.question_image}`)}
                        />
                      </div>
                    )}
                  </div>

                  {renderQuestionContent(item)}

                  {(question.explanation_text || question.explanation_image) && (
                    <div className="mt-1 pt-1 border-t border-gray-200 dark:border-slate-700 space-y-2">
                      {question.explanation_text && (
                        <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2">
                          <span className="font-medium">Note:</span> {question.explanation_text}
                        </p>
                      )}
                      {question.explanation_image && (
                        <img
                          src={`${ASSET_URL}/${question.explanation_image}`}
                          alt="Explanation"
                          className="max-w-full h-16 object-cover rounded border cursor-pointer hover:shadow-md transition-shadow"
                          onClick={() => handleImageClick(`${ASSET_URL}/${question.explanation_image}`)}
                        />
                      )}
                    </div>
                  )}
                </div>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Image Preview Modal */}
      <Modal
        activeModal={showImageModal}
        onClose={() => {
          setShowImageModal(false);
          setZoomImage(null);
        }}
        title="Image Preview"
        className="max-w-4xl"
        centered={true}
        themeClass="bg-slate-900 dark:bg-slate-800"
      >
        {zoomImage && (
          <div className="flex justify-center items-center">
            <img
              src={zoomImage}
              alt="Zoomed Image"
              className="max-w-full max-h-[70vh] object-contain rounded"
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default QuestionItems;