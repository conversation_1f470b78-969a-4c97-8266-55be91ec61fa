import React, { useState } from 'react';
import Modal from '@/components/ui/Modal';
const WrittenQuestion = ({
  chapter_quiz_id,
  created_at,
  created_by,
  deleted_at,
  description,
  id,
  is_active,
  marks,
  no_of_question,
  instruction,
  duration,
  question_attachment,
  updated_at,
}) => {
  const [showImage, setShowImage] = useState(false);
  const assetUrl = import.meta.env.VITE_ASSET_HOST_URL;

  return (
    <div className="w-full p-6 ">
      <div className="flex gap-4 flex-col md:flex-row">
        {question_attachment && (
          <img
            src={`${assetUrl}/${question_attachment}`}
            alt="Question Attachment"
            className="w-24 h-24 object-cover rounded-md cursor-pointer"
            onClick={() => setShowImage(true)}
          />
        )}
        {showImage && (
          <Modal activeModal={showImage} onClose={() => setShowImage(false)} title="Image Preview" className="max-w-5xl" centered={true} themeClass="bg-slate-900 dark:bg-slate-800">
            <div className="flex justify-center items-center">
              <img
                src={`${assetUrl}/${question_attachment}`}
                alt="Zoomed Image"
                className="max-w-full max-h-[70vh] object-contain rounded"
              />
            </div>
          </Modal>
        )}
         
        <div className="flex flex-col w-full">
          <h2 className="text-xl font-bold text-gray-800">Written Question</h2>
          <p className="text-gray-600 mt-4 md:mt-0">
            {description}
          </p>
          <div className="mt-2 flex gap-8">
            <div className="flex gap-2">
              <span className="font-semibold">Marks:</span> <span>{marks}</span>
            </div>
            <div className="flex gap-3">
              <span className="font-semibold">Duration:</span> <span>{duration} minutes</span>
            </div>
            <div className="flex gap-3">
              <span className="font-semibold">No. of Questions:</span> <span>{no_of_question}</span>
            </div>
          </div>
        </div>
      </div>
      <p className="text-xs mt-4">
        <b>Instructions:</b> {instruction}
      </p>
    </div>
  );
};

export default WrittenQuestion;
