import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";
const Delete = ({showDeleteModal, setShowDeleteModal, data}) => {
    const [postApi, { isLoading }] = usePostApiMutation();
    let rawQuestionText = data?.question_text || (data?.question && data.question.question_text) || "this question";
    rawQuestionText = rawQuestionText.replace(/<[^>]*>/g, '');
    rawQuestionText = rawQuestionText.replace(/_______/g, '___');
    const questionText = rawQuestionText.length > 100
        ? rawQuestionText.substring(0, 97) + "..."
        : rawQuestionText;
    const onSubmit = async () => {
        try {
            let endpoint = '/admin/delete-question';
            const response = await postApi({
                end_point: endpoint,
                body: {id: data?.id}
            });
            setShowDeleteModal(false);
        } catch (error) {
            console.error('Error deleting question:', error);
            toast.error(error?.data?.message || "Failed to delete question");
        }
    }
    return (

    <Modal
    activeModal={showDeleteModal}
    onClose={() => setShowDeleteModal(false)}
    title="Delete Question"
    className="max-w-5xl"
    footer={
        <Button
            text="Close"
            btnClass="btn-primary"
            onClick={() => setShowDeleteModal(false)}
        />
        }
    >

    <h3 className="text-center">Are you sure?</h3>
    <p className="text-center text-slate-500 text-sm mt-4">
        You are going to delete this question:
    </p>
    <div className="bg-gray-50 dark:bg-slate-800 p-3 my-3 rounded-md border border-gray-200 dark:border-slate-700 max-h-24 overflow-auto">
        <p className="text-center text-gray-800 dark:text-gray-200 text-sm">
            {questionText}
        </p>
    </div>
    <p className="text-center text-slate-500 text-sm">
        Once you have done this, there is no going back.
    </p>

    <div className="ltr:text-right rtl:text-left mt-5 gap-4">
        <Button
            // isLoading={isLoading}
            type="button"
            className="btn text-center btn-primary mr-4"
            onClick={() => setShowDeleteModal(false)}
        >
            Cancel
        </Button>
        <Button
            isLoading={isLoading}
            type="button"
            className="btn text-center btn-danger"
            onClick={onSubmit}
        >
            Delete
        </Button>
    </div>
    {/* <Button text="Delete" btnClass="btn btn-danger" /> */}
    </Modal>
    );
}

export default Delete;
