import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetClassListQuery } from "@/store/api/master/rowContentClassListSlice";
import CreateClass from "./createClass";
import EditClass from "./editClass";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";
import { calcLength } from "framer-motion";

const index = () => {
  const dispatch = useDispatch();

  const [showModal, setShowModal] = useState(false);
  const [apiParam, setApiParam] = useState("");

  const res = useGetClassListQuery(apiParam);

  // const classList = useGetClassListQuery({
  //   pagination:false
  // })?.data;

  const changePage = (val) => {
    console.log(val)
    setApiParam(val);
  };
  console.log(res.data?.data);
  const data = res.data;
  const columns = [
    {
      label: "SL",
      field: "id",
    },
    {
      label: "Name",
      field: "name",
    },
    {
      label: "Price",
      field: "price",
    },
    {
      label: "Free",
      field: "is_free",
    },
    {
      label: "Status",
      field: "is_active",
    },
    {
      label: "Action",
      field: "",
    },
  ];
  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      name: item.name,
      price: item.price,
      is_free: (
        <Badge
          className={
            item.is_free
              ? "bg-success-500 text-white"
              : "bg-danger-500 text-white"
          }
        >
          {item.is_free ? "YES" : "NO"}
        </Badge>
      ),
      is_active: (
        <Badge
          className={
            item.is_active
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {item.is_active ? "Active" : "Inactive"}
        </Badge>
      ),
    };
  });

  const actions = [
    {
      name: "edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        console.log(data.data[val]);
        dispatch(setEditData(data.data[val]));
        dispatch(setEditShowModal(true));
      },
    },
  ];

  // const changePage = (item) => {
  //   console.log(item);
  // };

  const handleSubmit = () => {
    setShowModal(false);
  };

  const createPage = <CreateClass />;
  const editPage = <EditClass />;

  return (
    <div>
      
        <BasicTablePage
          title="Class List"
          createButton="Add New Class"
          createPage={createPage}
          editPage={editPage}
          actions={actions}
          columns={columns}
          data={tableData}
          changePage={changePage}
          submitForm={handleSubmit}
          currentPage={data?.current_page}
          totalPages={Math.ceil(data?.total / data?.per_page)}
        />
    </div>
  );
};

export default index;
