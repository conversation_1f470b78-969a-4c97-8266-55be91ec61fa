import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import FileInput from "@/components/ui/Fileinput";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { Formik, Form, Field } from "formik";
import { initialValues, validationSchema } from "./formClass";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal } from "@/features/commonSlice";
import { useClassCreateOrUpdateMutation } from "@/store/api/master/rowContentClassListSlice";

const editClass = () => {
  const [classCreateOrUpdate, { isLoading, isError, error, isSuccess }] =
    useClassCreateOrUpdateMutation();
  const dispatch = useDispatch();
  const { showEditModal } = useSelector((state) => state.commonReducer);
  const { editData } = useSelector((state) => state.commonReducer);

  // Switch  State
  const [isActive, setIsActive] = useState(editData.is_active);
  const [isFree, setIsFree] = useState(editData.is_free);

  const onSubmit = async (values, { resetForm }) => {
    let formData = new FormData();
    formData.append("id", values.id);
    formData.append("name", values.name);
    formData.append("name_bn", values.name_bn);
    formData.append("price", values.price);
    formData.append("color_code", values.color_code);
    formData.append("sequence", 1);
    formData.append("icon", values.icon);
    formData.append("is_active", isActive ? 1 : 0);
    formData.append("is_free", isFree ? 1 : 0);

    const response = await classCreateOrUpdate(formData);
    dispatch(setEditShowModal(false));
  };
  return (
    <Modal
      activeModal={showEditModal}
      onClose={() => dispatch(setEditShowModal(false))}
      title="Edit Class"
      className="max-w-lg"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setEditShowModal(false))}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={editData}
        onSubmit={onSubmit}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          handleSubmit,
          setFieldValue,
          isSubmitting,
        }) => (
          <Form>
            <>
              <div className="grid md:grid-cols-1 gap-4">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  placeholder="Enter Name"
                  required
                />
                <InputField
                  label="Bangla Name"
                  name="name_bn"
                  type="text"
                  placeholder="Enter Bangla Name"
                />
                <NumberInput
                  label="Price"
                  name="price"
                  type="text"
                  placeholder="Enter Price"
                  required
                />

                <InputField
                  label="Color Code"
                  name="color_code"
                  type="text"
                  placeholder="Enter Color Code"
                />
                <NumberInput
                  label="Sequence"
                  name="sequence"
                  type="text"
                  placeholder="Enter Sequence"
                  required
                />
              </div>

              <div className="grid md:grid-cols-2 gap-4 my-4">
                <Switch
                  label="Active"
                  activeClass="bg-success-500"
                  value={isActive}
                  name="is_active"
                  onChange={() => setIsActive(!isActive)}
                />

                <Switch
                  label="Free"
                  activeClass="bg-success-500"
                  value={isFree}
                  name="is_free"
                  onChange={() => setIsFree(!isFree)}
                />
              </div>
              <div className="grid md:grid-cols-1 gap-4 my-4">
                <>
                  <label className="block text-[#1D1D1F] text-base font-medium">
                    Icon
                  </label>
                  <FileInput
                    name="icon"
                    accept="image/*"
                    type="file"
                    placeholder="Icon"
                    preview={true}
                    selectedFile={values.icon}
                    onChange={(e) => {
                      setFieldValue("icon", e.currentTarget.files[0]);
                    }}
                  />
                </>
              </div>
            </>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default editClass;
