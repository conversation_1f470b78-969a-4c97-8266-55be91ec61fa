import React from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Textarea from "@/components/ui/Textarea";
import Button from "@/components/ui/Button";
import FileInput from "@/components/ui/FileInput";
import { Formik, Form } from "formik";
import * as yup from "yup";
import { usePostApiMutation, useUpdateApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";

const validationSchema = yup.object({
  name: yup.string().required("Name is required").max(255, "Name must not exceed 255 characters"),
  designation: yup.string().nullable().max(255, "Designation must not exceed 255 characters"),
  speech: yup.string().required("Speech is required"),
  datetime: yup.date().nullable(),
  is_active: yup.boolean(),
  sort_order: yup.number().min(0, "Sort order must be 0 or greater")
});

const TestimonialForm = ({ 
  showModal, 
  onClose, 
  onSuccess, 
  userStoryId, 
  editData = null 
}) => {
  const [postApi, { isLoading: isCreating }] = usePostApiMutation();
  const [updateApi, { isLoading: isUpdating }] = useUpdateApiMutation();
  
  const isEditing = !!editData;
  const isLoading = isCreating || isUpdating;

  const initialValues = {
    name: editData?.name || "",
    designation: editData?.designation || "",
    speech: editData?.speech || "",
    datetime: editData?.datetime
      ? new Date(editData.datetime).toISOString().slice(0, 16)
      : new Date().toISOString().slice(0, 16),
    is_active: editData?.is_active ?? true,
    sort_order: editData?.sort_order || 0,
    image: editData?.image || null
  };

  const onSubmit = async (values, { resetForm }) => {
    try {
      const formData = new FormData();

      // Append form data
      formData.append("user_story_id", userStoryId);
      formData.append("name", values.name);
      formData.append("designation", values.designation || "");
      formData.append("speech", values.speech);

      // Convert datetime-local to proper datetime format if provided
      if (values.datetime) {
        const dateTime = new Date(values.datetime).toISOString();
        formData.append("datetime", dateTime);
      }

      formData.append("is_active", values.is_active ? 1 : 0);
      formData.append("sort_order", values.sort_order);

      // Handle image upload if present
      if (values.image && values.image instanceof File) {
        formData.append("image", values.image);
      }

      if (isEditing) {
        // Update existing testimonial
        await updateApi({
          end_point: `admin/user-story-testimonials/${editData.id}`,
          body: formData
        });
      } else {
        // Create new testimonial
        await postApi({
          end_point: "admin/user-story-testimonials",
          body: formData
        });
      }

      resetForm();
      onSuccess();
      onClose();
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error(error?.data?.message || error?.message || "Failed to save testimonial");
    }
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={onClose}
      title={isEditing ? "Edit Testimonial" : "Create Testimonial"}
      className="max-w-2xl"
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={initialValues}
        onSubmit={onSubmit}
        enableReinitialize
      >
        {({
          values,
          setFieldValue,
        }) => {
          return (
          <Form>
            <div className="grid md:grid-cols-2 gap-4 mb-4">
              <InputField
                label="Name"
                name="name"
                type="text"
                placeholder="Enter name"
                required
              />
              <InputField
                label="Designation"
                name="designation"
                type="text"
                placeholder="Enter designation"
              />
            </div>

            <div className="mb-4">
              <Textarea
                label="Speech"
                defaultValue={values.speech}
                name="speech"
                placeholder="Enter testimonial speech"
                row={4}
                required
                onChange={(e) => {
                  setFieldValue("speech", e.target.value);
                }}
              />
            </div>

            <div className="grid md:grid-cols-2 gap-4 mb-4">
              <FileInput
                name="image"
                label="Image"
                accept={{
                  'image/*': ['.jpg', '.jpeg', '.png', '.gif', '.webp']
                }}
                accepts="image/*"
              />
              <InputField
                label="Date & Time"
                name="datetime"
                type="datetime-local"
              />
            </div>

            <div className="grid md:grid-cols-2 gap-4 mb-4">
              <InputField
                label="Sort Order"
                name="sort_order"
                type="number"
                placeholder="0"
              />
              <div className="flex items-center gap-2 mt-6">
                <input
                  type="checkbox"
                  id="testimonial_active"
                  className="form-checkbox"
                  checked={values.is_active}
                  onChange={(e) => setFieldValue("is_active", e.target.checked)}
                />
                <label htmlFor="testimonial_active" className="form-label mb-0">
                  Active
                </label>
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end gap-3 mt-6">
              <Button
                type="button"
                text="Cancel"
                className="btn-outline-secondary"
                onClick={onClose}
              />
              <Button
                type="submit"
                text={isEditing ? "Update Testimonial" : "Create Testimonial"}
                className="btn-primary"
                isLoading={isLoading}
                disabled={isLoading}
              />
            </div>
          </Form>
          );
        }}
      </Formik>
    </Modal>
  );
};

export default TestimonialForm;
