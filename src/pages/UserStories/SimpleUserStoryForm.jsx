import React from "react";
import Card from "@/components/ui/Card";
import InputField from "@/components/ui/InputField";
import Textarea from "@/components/ui/Textarea";
import Button from "@/components/ui/Button";
import { Formik, Form } from "formik";
import * as yup from "yup";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";

const validationSchema = yup.object({
  title: yup.string().required("Title is required").max(255, "Title must not exceed 255 characters"),
  short_description: yup.string().nullable(),
  color: yup.string().required("Color is required").matches(/^#[0-9A-Fa-f]{6}$/, "Invalid color format"),
  background: yup.string().required("Background color is required").matches(/^#[0-9A-Fa-f]{6}$/, "Invalid color format"),
  is_active: yup.boolean(),
  sort_order: yup.number().min(0, "Sort order must be 0 or greater")
});

const initialValues = {
  title: "",
  short_description: "",
  color: "#2563eb",
  background: "#f8fafc",
  is_active: true,
  sort_order: 0
};

const SimpleUserStoryForm = ({ onSuccess }) => {
  const [postApi, { isLoading }] = usePostApiMutation();

  const onSubmit = async (values, { resetForm }) => {
    try {
      const formData = new FormData();

      // Append main user story data
      formData.append("title", values.title);
      formData.append("short_description", values.short_description || "");
      formData.append("color", values.color);
      formData.append("background", values.background);
      formData.append("is_active", values.is_active ? 1 : 0);
      formData.append("sort_order", values.sort_order);



      await postApi({
        end_point: "admin/user-stories",
        body: formData
      });

      resetForm();
      onSuccess();
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error(error?.data?.message || error?.message || "Failed to create user story");
    }
  };

  return (
    <Card title="Create User Story">
      <Formik
        validationSchema={validationSchema}
        initialValues={initialValues}
        onSubmit={onSubmit}
      >
        {({
          values,
          setFieldValue,
        }) => (
          <Form>
            {/* Main User Story Details */}
            <div className="grid md:grid-cols-2 gap-4 mb-4">
              <InputField
                label="Title"
                name="title"
                type="text"
                placeholder="Enter story title"
                required
              />
              <InputField
                label="Sort Order"
                name="sort_order"
                type="number"
                placeholder="0"
              />
            </div>

            <div className="mb-4">
              <Textarea
                label="Short Description"
                name="short_description"
                placeholder="Enter short description"
                row={3}
                onChange={(e) => setFieldValue("short_description", e.target.value)}
              />
            </div>

            <div className="grid md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="form-label">Text Color *</label>
                <div className="flex items-center gap-2">
                  <input
                    type="color"
                    className="w-10 h-10 border border-slate-300 rounded cursor-pointer"
                    value={values.color}
                    onChange={(e) => setFieldValue("color", e.target.value)}
                  />
                  <InputField
                    name="color"
                    type="text"
                    placeholder="#2563eb"
                  />
                </div>
              </div>

              <div>
                <label className="form-label">Background Color *</label>
                <div className="flex items-center gap-2">
                  <input
                    type="color"
                    className="w-10 h-10 border border-slate-300 rounded cursor-pointer"
                    value={values.background}
                    onChange={(e) => setFieldValue("background", e.target.value)}
                  />
                  <InputField
                    name="background"
                    type="text"
                    placeholder="#f8fafc"
                  />
                </div>
              </div>
            </div>

            {/* Color Preview */}
            <div className="p-4 rounded-lg border mb-4" style={{ backgroundColor: values.background }}>
              <h4 className="font-semibold" style={{ color: values.color }}>
                Preview: {values.title || "Your Story Title"}
              </h4>
              <p className="text-sm mt-1" style={{ color: values.color, opacity: 0.8 }}>
                {values.short_description || "Your short description will appear here"}
              </p>
            </div>

            <div className="flex items-center gap-2 mb-4">
              <input
                type="checkbox"
                id="is_active"
                className="form-checkbox"
                checked={values.is_active}
                onChange={(e) => setFieldValue("is_active", e.target.checked)}
              />
              <label htmlFor="is_active" className="form-label mb-0">
                Active
              </label>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end mt-6">
              <Button
                type="submit"
                text="Create User Story"
                className="btn-primary"
                isLoading={isLoading}
                disabled={isLoading}
              />
            </div>
          </Form>
        )}
      </Formik>
    </Card>
  );
};

export default SimpleUserStoryForm;
