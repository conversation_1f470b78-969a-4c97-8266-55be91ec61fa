import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { useDeleteApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";

const TestimonialDeleteModal = ({ 
  showModal, 
  onClose, 
  onSuccess, 
  testimonial 
}) => {
  const [deleteApi, { isLoading }] = useDeleteApiMutation();

  const handleDelete = async () => {
    try {
      
      await deleteApi({
        end_point: `admin/user-story-testimonials/${testimonial.id}`,
        body: {}
      });

      onSuccess();
      onClose();
    } catch (error) {
      console.error("Delete error:", error);
      toast.error(error?.data?.message || error?.message || "Failed to delete testimonial");
    }
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={onClose}
      title="Delete Testimonial"
      className="max-w-md"
    >
      <div className="space-y-4">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <svg
              className="h-6 w-6 text-red-600"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Delete Testimonial
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Are you sure you want to delete the testimonial by{" "}
            <span className="font-semibold">{testimonial?.name}</span>? This action cannot be undone.
          </p>
        </div>

        <div className="flex justify-end gap-3 mt-6">
          <Button
            type="button"
            text="Cancel"
            className="btn-outline-secondary"
            onClick={onClose}
            disabled={isLoading}
          />
          <Button
            type="button"
            text="Delete"
            className="btn-danger"
            onClick={handleDelete}
            isLoading={isLoading}
            disabled={isLoading}
          />
        </div>
      </div>
    </Modal>
  );
};

export default TestimonialDeleteModal;
