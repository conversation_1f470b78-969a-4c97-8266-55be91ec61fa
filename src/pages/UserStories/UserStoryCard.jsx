import React from "react";
import Card from "@/components/ui/Card";
import Badge from "@/components/ui/Badge";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";
import { formattedDate } from "@/utils/date";


const UserStoryCard = ({ data, onRefresh, onEdit, onEditTestimonial, onDeleteTestimonial, onCreateTestimonial }) => {
  const { 
    title, 
    short_description, 
    color, 
    background, 
    is_active, 
    active_testimonials = [],
    created_at,
    updated_at
  } = data;

  return (
    <div className="space-y-6">
      {/* Main User Story Card */}
      <Card>
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white">
                  {title}
                </h3>
                <Badge
                  label={is_active ? "Active" : "Inactive"}
                  className={is_active ? "bg-success-500" : "bg-warning-500"}
                />
              </div>
              {short_description && (
                <p className="text-slate-600 dark:text-slate-300">
                  {short_description}
                </p>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button
                icon="heroicons-outline:pencil"
                className="btn-outline-secondary btn-sm"
                onClick={() => {
                  onEdit(data);
                }}
              />
            </div>
          </div>

          {/* Color Preview */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Text Color:</span>
              <div 
                className="w-6 h-6 rounded border border-slate-300"
                style={{ backgroundColor: color }}
              />
              <span className="text-xs text-slate-500">{color}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Background:</span>
              <div 
                className="w-6 h-6 rounded border border-slate-300"
                style={{ backgroundColor: background }}
              />
              <span className="text-xs text-slate-500">{background}</span>
            </div>
          </div>

          {/* Timestamps */}
          <div className="flex items-center gap-4 text-xs text-slate-500">
            <span>Created: {formattedDate(created_at)}</span>
            <span>Updated: {formattedDate(updated_at)}</span>
          </div>
        </div>
      </Card>

      {/* Testimonials Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-lg font-semibold text-slate-900 dark:text-white">
            Active Testimonials ({active_testimonials.length})
          </h4>
          <Button
            text="Add Testimonial"
            className="btn-outline-primary btn-sm"
            icon="heroicons-outline:plus"
            onClick={() => {
              onCreateTestimonial(data.id);
            }}
          />
        </div>

        {active_testimonials.length === 0 ? (
          <Card>
            <div className="text-center py-8">
              <Icon 
                icon="heroicons-outline:chat-bubble-left-right" 
                className="mx-auto h-12 w-12 text-slate-400 mb-4"
              />
              <p className="text-slate-500">No testimonials available</p>
            </div>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {active_testimonials.map((testimonial) => (
              <TestimonialCard
                key={testimonial.id}
                testimonial={testimonial}
                onEditTestimonial={onEditTestimonial}
                onDeleteTestimonial={onDeleteTestimonial}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

const TestimonialCard = ({ testimonial, onEditTestimonial, onDeleteTestimonial }) => {
  const {
    name,
    designation,
    speech,
    image,
    datetime,
    sort_order
  } = testimonial;

  return (
    <Card className="h-full">
      <div className="space-y-4">
        {/* Header with image and info */}
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0">
            {image ? (
              <img
                src={`${import.meta.env.VITE_ASSET_HOST_URL}${image}`}
                alt={name}
                className="w-12 h-12 rounded-full object-cover border-2 border-slate-200"
              />
            ) : (
              <div className="w-12 h-12 rounded-full bg-slate-200 flex items-center justify-center">
                <Icon icon="heroicons-outline:user" className="w-6 h-6 text-slate-500" />
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <h5 className="font-semibold text-slate-900 dark:text-white truncate">
              {name}
            </h5>
            {designation && (
              <p className="text-sm text-slate-600 dark:text-slate-300 truncate">
                {designation}
              </p>
            )}
          </div>
          <div className="flex items-center gap-1">
            <Button
              icon="heroicons-outline:pencil"
              className="btn-outline-secondary btn-sm"
              onClick={() => {
                onEditTestimonial(testimonial);
              }}
            />
            <Button
              icon="heroicons-outline:trash"
              className="btn-outline-danger btn-sm"
              onClick={() => {
                onDeleteTestimonial(testimonial);
              }}
            />
          </div>
        </div>

        {/* Speech */}
        <div className="bg-slate-50 dark:bg-slate-700 rounded-lg p-3">
          <p className="text-sm text-slate-700 dark:text-slate-300 leading-relaxed">
            "{speech}"
          </p>
        </div>

        {/* Footer info */}
        <div className="flex items-center justify-between text-xs text-slate-500">
          <span>Order: {sort_order}</span>
          {datetime && <span>{formattedDate(datetime)}</span>}
        </div>
      </div>
    </Card>
  );
};

export default UserStoryCard;
