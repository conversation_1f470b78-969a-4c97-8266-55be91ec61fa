import React, { useState } from "react";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Loading from "@/components/Loading";
import UserStoryCard from "./UserStoryCard";
import SimpleUserStoryForm from "./SimpleUserStoryForm";
import UserStoryEditForm from "./UserStoryEditForm";
import TestimonialForm from "./TestimonialForm";
import TestimonialDeleteModal from "./TestimonialDeleteModal";

const UserStories = () => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [editData, setEditData] = useState(null);

  // Testimonial states
  const [showTestimonialForm, setShowTestimonialForm] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [testimonialEditData, setTestimonialEditData] = useState(null);
  const [testimonialToDelete, setTestimonialToDelete] = useState(null);
  const [currentUserStoryId, setCurrentUserStoryId] = useState(null);

  const { data, isLoading, error, refetch } = useGetApiQuery("admin/user-stories");

  if (isLoading) {
    return <Loading />;
  }

  // Show create form if data is null or if user clicks create button
  if (!data || showCreateForm) {
    return (
      <div className="space-y-5">
        <div className="flex items-center justify-between">
          <h4 className="card-title">User Stories</h4>
          {data && (
            <Button
              text="Back to View"
              className="btn-outline-secondary"
              onClick={() => setShowCreateForm(false)}
            />
          )}
        </div>
        <SimpleUserStoryForm
          onSuccess={() => {
            setShowCreateForm(false);
            refetch();
          }}
        />
      </div>
    );
  }

  return (
    <div className="space-y-5">
      <div className="flex items-center justify-between">
        <h4 className="card-title">User Stories</h4>
      
      </div>

      <UserStoryCard
        data={data}
        onRefresh={refetch}
        onEdit={(userStoryData) => {
          setEditData(userStoryData);
          setShowEditForm(true);
        }}
        onCreateTestimonial={(userStoryId) => {
          setCurrentUserStoryId(userStoryId);
          setTestimonialEditData(null);
          setShowTestimonialForm(true);
        }}
        onEditTestimonial={(testimonial) => {
          setCurrentUserStoryId(testimonial.user_story_id);
          setTestimonialEditData(testimonial);
          setShowTestimonialForm(true);
        }}
        onDeleteTestimonial={(testimonial) => {
          setTestimonialToDelete(testimonial);
          setShowDeleteModal(true);
        }}
      />

      {/* Edit Form Modal */}
      <UserStoryEditForm
        showModal={showEditForm}
        onClose={() => {
          setShowEditForm(false);
          setEditData(null);
        }}
        onSuccess={() => {
          refetch();
        }}
        editData={editData}
      />

      {/* Testimonial Form Modal */}
      <TestimonialForm
        showModal={showTestimonialForm}
        onClose={() => {
          setShowTestimonialForm(false);
          setTestimonialEditData(null);
          setCurrentUserStoryId(null);
        }}
        onSuccess={() => {
          refetch();
        }}
        userStoryId={currentUserStoryId}
        editData={testimonialEditData}
      />

      {/* Testimonial Delete Modal */}
      <TestimonialDeleteModal
        showModal={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false);
          setTestimonialToDelete(null);
        }}
        onSuccess={() => {
          refetch();
        }}
        testimonial={testimonialToDelete}
      />
    </div>
  );
};

export default UserStories;
