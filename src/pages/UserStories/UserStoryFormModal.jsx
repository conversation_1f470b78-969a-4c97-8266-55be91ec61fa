import React from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Textarea from "@/components/ui/Textarea";
import Button from "@/components/ui/Button";
import { Formik, Form, FieldArray } from "formik";
import * as yup from "yup";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";

const validationSchema = yup.object({
  title: yup.string().required("Title is required").max(255, "Title must not exceed 255 characters"),
  short_description: yup.string().nullable(),
  color: yup.string().required("Color is required").matches(/^#[0-9A-Fa-f]{6}$/, "Invalid color format"),
  background: yup.string().required("Background color is required").matches(/^#[0-9A-Fa-f]{6}$/, "Invalid color format"),
  is_active: yup.boolean(),
  sort_order: yup.number().min(0, "Sort order must be 0 or greater"),
  testimonials: yup.array().of(
    yup.object({
      name: yup.string().nullable().max(255, "Name must not exceed 255 characters"),
      designation: yup.string().nullable().max(255, "Designation must not exceed 255 characters"),
      speech: yup.string().nullable(),
      datetime: yup.date().nullable(),
      is_active: yup.boolean(),
      sort_order: yup.number().min(0, "Sort order must be 0 or greater")
    })
  )
}).test('testimonials-validation', 'Invalid testimonials', function(value) {
  const { testimonials } = value;

  // Check if any testimonial has only name or only speech (incomplete)
  const hasIncompleteTestimonials = testimonials.some(testimonial => {
    const hasName = testimonial.name && testimonial.name.trim();
    const hasSpeech = testimonial.speech && testimonial.speech.trim();

    // If one field is filled but not the other, it's incomplete
    return (hasName && !hasSpeech) || (!hasName && hasSpeech);
  });

  if (hasIncompleteTestimonials) {
    return this.createError({
      path: 'testimonials',
      message: 'If you provide a name for a testimonial, you must also provide speech, and vice versa.'
    });
  }

  return true;
});

const initialValues = {
  title: "",
  short_description: "",
  color: "#2563eb",
  background: "#f8fafc",
  is_active: true,
  sort_order: 0,
  testimonials: []
};

const UserStoryFormModal = ({ showModal, onClose, onSuccess }) => {
  const [postApi, { isLoading }] = usePostApiMutation();

  const onSubmit = async (values, { resetForm }) => {
    try {
      const formData = new FormData();
      
      // Append main user story data
      formData.append("title", values.title);
      formData.append("short_description", values.short_description || "");
      formData.append("color", values.color);
      formData.append("background", values.background);
      formData.append("is_active", values.is_active ? 1 : 0);
      formData.append("sort_order", values.sort_order);

      await postApi({
        end_point: "admin/user-stories",
        body: formData
      });

      resetForm();
      onSuccess();
      onClose();
    } catch (error) {
      toast.error(error?.data?.message || "Failed to create user story");
    }
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={onClose}
      title="Create User Story"
      className="max-w-4xl"
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={initialValues}
        onSubmit={onSubmit}
      >
        {({
          values,
          setFieldValue,
        }) => (
          <Form>
            {/* Main User Story Details */}
            <div className="grid md:grid-cols-2 gap-4 mb-4">
              <InputField
                label="Title"
                name="title"
                type="text"
                placeholder="Enter story title"
                required
              />
              <InputField
                label="Sort Order"
                name="sort_order"
                type="number"
                placeholder="0"
              />
            </div>

            <div className="mb-4">
              <Textarea
                label="Short Description"
                name="short_description"
                placeholder="Enter short description"
                row={3}
              />
            </div>

            <div className="grid md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="form-label">Text Color *</label>
                <div className="flex items-center gap-2">
                  <input
                    type="color"
                    className="w-10 h-10 border border-slate-300 rounded cursor-pointer"
                    value={values.color}
                    onChange={(e) => setFieldValue("color", e.target.value)}
                  />
                  <InputField
                    name="color"
                    type="text"
                    placeholder="#2563eb"
                  />
                </div>
              </div>

              <div>
                <label className="form-label">Background Color *</label>
                <div className="flex items-center gap-2">
                  <input
                    type="color"
                    className="w-10 h-10 border border-slate-300 rounded cursor-pointer"
                    value={values.background}
                    onChange={(e) => setFieldValue("background", e.target.value)}
                  />
                  <InputField
                    name="background"
                    type="text"
                    placeholder="#f8fafc"
                  />
                </div>
              </div>
            </div>

            {/* Color Preview */}
            <div className="p-4 rounded-lg border mb-4" style={{ backgroundColor: values.background }}>
              <h4 className="font-semibold" style={{ color: values.color }}>
                Preview: {values.title || "Your Story Title"}
              </h4>
              <p className="text-sm mt-1" style={{ color: values.color, opacity: 0.8 }}>
                {values.short_description || "Your short description will appear here"}
              </p>
            </div>

            <div className="flex items-center gap-2 mb-4">
              <input
                type="checkbox"
                id="is_active"
                className="form-checkbox"
                checked={values.is_active}
                onChange={(e) => setFieldValue("is_active", e.target.checked)}
              />
              <label htmlFor="is_active" className="form-label mb-0">
                Active
              </label>
            </div>

            {/* Testimonials Section */}
            <div className="border-t pt-4">
              <h5 className="font-medium mb-4">Testimonials</h5>
              <FieldArray name="testimonials">
                {({ push, remove }) => (
                  <div className="space-y-4">
                    {values.testimonials.map((testimonial, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4 space-y-3">
                        <div className="flex items-center justify-between">
                          <h6 className="font-medium">Testimonial #{index + 1}</h6>
                          {values.testimonials.length > 1 && (
                            <button
                              type="button"
                              className="text-red-600 hover:text-red-800"
                              onClick={() => remove(index)}
                            >
                              Remove
                            </button>
                          )}
                        </div>

                        <div className="grid md:grid-cols-2 gap-3">
                          <InputField
                            label="Name"
                            name={`testimonials.${index}.name`}
                            type="text"
                            placeholder="Enter name"
                            required
                          />
                          <InputField
                            label="Designation"
                            name={`testimonials.${index}.designation`}
                            type="text"
                            placeholder="Enter designation"
                          />
                        </div>

                        <Textarea
                          label="Speech"
                          name={`testimonials.${index}.speech`}
                          placeholder="Enter testimonial speech"
                          row={2}
                          required
                        />

                        <div className="grid md:grid-cols-3 gap-3">
                          <div>
                            <label className="form-label">Image</label>
                            <input
                              type="file"
                              accept="image/*"
                              className="form-control"
                              onChange={(e) => setFieldValue(`testimonials.${index}.image`, e.target.files)}
                            />
                          </div>
                          <InputField
                            label="Date & Time"
                            name={`testimonials.${index}.datetime`}
                            type="datetime-local"
                          />
                          <InputField
                            label="Sort Order"
                            name={`testimonials.${index}.sort_order`}
                            type="number"
                            placeholder="0"
                          />
                        </div>

                        <div className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            id={`testimonial_active_${index}`}
                            className="form-checkbox"
                            checked={testimonial.is_active}
                            onChange={(e) => setFieldValue(`testimonials.${index}.is_active`, e.target.checked)}
                          />
                          <label htmlFor={`testimonial_active_${index}`} className="form-label mb-0">
                            Active
                          </label>
                        </div>
                      </div>
                    ))}

                    <button
                      type="button"
                      className="btn btn-outline-primary btn-sm"
                      onClick={() => push({
                        name: "",
                        designation: "",
                        speech: "",
                        datetime: "",
                        is_active: true,
                        sort_order: values.testimonials.length
                      })}
                    >
                      Add Testimonial
                    </button>
                  </div>
                )}
              </FieldArray>
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end gap-3 mt-6">
              <Button
                type="button"
                text="Cancel"
                className="btn-outline-secondary"
                onClick={onClose}
              />
              <Button
                type="submit"
                text="Create User Story"
                className="btn-primary"
                isLoading={isLoading}
                disabled={isLoading}
              />
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default UserStoryFormModal;
