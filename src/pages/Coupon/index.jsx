import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Create from "./create";
import Edit from "./edit";
import Delete from "./Delete";
import ShowCoupon from "./details";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";

const Index = () => {
  const dispatch = useDispatch();

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [deleteData, setDeleteData] = useState(null);
  const [couponId, setCouponId] = useState(null);



  const { showModal, showEditModal } = useSelector((state) => state.commonReducer);

  const [apiParam, setApiParam] = useState("");

  const { data, isLoading, error } = useGetApiQuery(`admin/coupons${apiParam ? `?${apiParam}` : ''}`);

  const changePage = (val) => {
    setApiParam(val);
  };

  const columns = [
    { label: "SL", field: "id" },
    { label: "Code", field: "code" },
    { label: "Discount", field: "discount" },
    { label: "Discount Type", field: "discount_type" },
    { label: "Expiry Date", field: "expiry_date" },
    { label: "Is Active", field: "is_active" },
    { label: "Action", field: "" },
  ];

  const tableData = data?.map((item, index) => ({
    id: index + 1,
    code: item.code,
    discount: `${item.discount}${item.discount_type === "percentage" ? "%" : " (Flat Amount)"}`,
    discount_type: item.discount_type.charAt(0).toUpperCase() + item.discount_type.slice(1),
    expiry_date: item.expiry_date
      ? new Intl.DateTimeFormat("en-GB", {
          day: "numeric",
          month: "short",
          year: "numeric",
        }).format(new Date(item.expiry_date))
      : "N/A",
    is_active: (
      <Badge
        className={`text-white ${item.is_active ? "bg-success-500" : "bg-danger-500"}`}
      >
        {item.is_active ? "Active" : "Inactive"}
      </Badge>
    ),
  }));

  // const [couponId, setCouponId] = useState(null);
  // const [showDetails, setShowDetails] = useState(false);
  const actions = [    {
      name: "View",
      icon: "heroicons:Eye",
      onClick: (rowIndex) => {
        setCouponId(data?.[rowIndex]?.id);
        setShowDetails(true);
      },
    },
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (rowIndex) => {
        const selectedData = data?.[rowIndex];
        dispatch(setEditData(selectedData));
        dispatch(setEditShowModal(true));
      },
    },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (rowIndex) => {
        setDeleteData(data?.[rowIndex]);
        setShowDeleteModal(true);
      },
    },
  ];

  const handleSubmit = () => {
    // Optional callback after form submission
  };

  return (
    <div>
      <BasicTablePage
        title="Coupon List"
        createButton="Create a Coupon"
        actions={actions}
        columns={columns}
        data={tableData}
        changePage={changePage}
        submitForm={handleSubmit}
        currentPage={data?.current_page}
        totalPages={Math.ceil(data?.total / data?.per_page)}
        filter={false}
        isLoading={isLoading}
      />

      {showModal && <Create />}
      {showEditModal && <Edit />}
      {showDeleteModal && (
        <Delete
          showDeleteModal={showDeleteModal}
          setShowDeleteModal={setShowDeleteModal}
          data={deleteData}
        />
      )}

      { showDetails && <ShowCoupon showModal={showDetails} setShowModal={setShowDetails} id={couponId} />}
    </div>
  );
};

export default Index;
