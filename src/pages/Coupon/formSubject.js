import React from "react";
import * as yup from "yup";

export const initialValues = {
    code: '',
    discount: '',
    discount_type: 'percentage',
    max_usage: '',
    expiry_date: '',
    is_active: 1
  };

  export const validationSchema = yup.object({
    code: yup
      .string()
      .required("Code is required"),
      // You can't validate uniqueness client-side reliably.
    
    max_usage: yup
      .number()
      .required("Usage Limit is required"),
    discount: yup
      .number()
      .typeError("Discount must be a number")
      .required("Discount is required")
      .min(0, "Discount cannot be negative"),
  
    discount_type: yup
      .string()
      .required("Discount type is required")
      .oneOf(["percentage", "fixed"], "Invalid discount type"),
  
    expiry_date: yup
      .date()
      .nullable()
      .typeError("Invalid date format"),
  
    is_active: yup
      .boolean()
  });

  export const validationUpdateSchema = yup.object({
    code: yup
      .string()
      .required("Code is required"),
    max_usage: yup
    .number()
    .required("Usage Limit is required"),
    discount: yup
      .number()
      .typeError("Discount must be a number")
      .required("Discount is required")
      .min(0, "Discount cannot be negative"),
  
    discount_type: yup
      .string()
      .required("Discount type is required")
      .oneOf(["percentage", "fixed"], "Invalid discount type"),
  
    expiry_date: yup
      .date()
      .nullable()
      .typeError("Invalid date format"),
  
    is_active: yup
      .boolean()
  });
  