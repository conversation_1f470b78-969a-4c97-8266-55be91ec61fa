import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Badge from "@/components/ui/Badge";
import Icon from "@/components/ui/Icon";

const ShowCoupon = ({ showModal, setShowModal, id }) => {
  const { data: couponDetails, isLoading, error } = useGetApiQuery(`admin/coupons/${id}`);

  if (isLoading) return <div className="text-center py-10 text-lg">Loading...</div>;
  if (error) return <div className="text-center py-10 text-danger-500 text-lg">Failed to load coupon details</div>;

  const expiryDate = new Date(couponDetails.expiry_date).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  const usagePercentage = (couponDetails.usage_number / couponDetails.max_usage) * 100;

  return (
    <Modal
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      title="Coupon Details"
      className="max-w-5xl"
      footer={
        <Button text="Close" btnClass="btn-primary" onClick={() => setShowModal(false)} />
      }
    >
      <div className="space-y-6">
        {/* Coupon Summary */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Info Card */}
          <div className="bg-white dark:bg-slate-800 p-6 rounded-2xl shadow-sm">
            <h3 className="text-xl font-semibold mb-5 flex items-center gap-2">
              <Icon icon="heroicons:ticket" className="text-primary-500" />
              Coupon Info
            </h3>
            <ul className="space-y-3 text-sm">
              <li className="flex justify-between">
                <span className="text-slate-500">Code:</span>
                <span className="font-medium">{couponDetails.code}</span>
              </li>
              <li className="flex justify-between">
                <span className="text-slate-500">Discount:</span>
                <span className="font-medium">
                  {couponDetails.discount}
                  {couponDetails.discount_type === "percentage" ? "%" : ""}
                </span>
              </li>
              <li className="flex justify-between">
                <span className="text-slate-500">Expiry Date:</span>
                <span className="font-medium">{expiryDate}</span>
              </li>
              <li className="flex justify-between">
                <span className="text-slate-500">Status:</span>
                <Badge
                  label={couponDetails.is_active ? "Active" : "Inactive"}
                  className={couponDetails.is_active ? "bg-success-500 text-white" : "bg-danger-500 text-white"}
                />
              </li>
              <li className="flex justify-between">
                <span className="text-slate-500">Usage:</span>
                <span className="font-medium">
                  {couponDetails.usage_number} / {couponDetails.max_usage}
                </span>
              </li>
            </ul>
          </div>

          {/* Progress Card */}
          <div className="bg-white dark:bg-slate-800 p-6 rounded-2xl shadow-sm">
            <h3 className="text-xl font-semibold mb-5 flex items-center gap-2">
              <Icon icon="lucide:bar-chart-horizontal" className="text-primary-500" />
              Usage Progress
            </h3>
            <div className="mb-3 text-sm flex justify-between">
              <span>Used: {couponDetails.usage_number}</span>
              <span>Remaining: {couponDetails.max_usage - couponDetails.usage_number}</span>
            </div>
            <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-3">
              <div
                className="bg-primary-500 h-3 rounded-full transition-all duration-300"
                style={{ width: `${usagePercentage}%` }}
              ></div>
            </div>
            <div className="mt-4 text-sm text-slate-500 dark:text-slate-300">
              {couponDetails.usage_number >= couponDetails.max_usage ? (
                <span className="text-danger-500 font-medium">Max usage limit reached</span>
              ) : (
                <span>
                  {couponDetails.max_usage - couponDetails.usage_number} more uses available.
                </span>
              )}
            </div>
          </div>
        </div>

        {/* History Table */}
        <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-base p-6">
          <h3 className="text-xl font-semibold mb-5 flex items-center gap-2">
            <Icon icon="ph:clock-counter-clockwise" className="text-primary-500" />
            Usage History
          </h3>

          {couponDetails.usages.length === 0 ? (
            <div className="text-center text-slate-500 dark:text-slate-300 py-8">
              No usage history found.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full text-sm">
                <thead className="bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 uppercase">
                  <tr>
                    <th className="px-4 py-3 text-left">User</th>
                    <th className="px-4 py-3 text-left">Item</th>
                    <th className="px-4 py-3 text-left">Amount</th>
                    <th className="px-4 py-3 text-left">Date</th>
                    <th className="px-4 py-3 text-left">Status</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-100 dark:divide-slate-700">
                  {couponDetails.usages.map((usage) => (
                    <tr key={usage.id} className="hover:bg-slate-50 dark:hover:bg-slate-700 transition">
                      <td className="px-4 py-3">
                        <div className="font-medium text-slate-900 dark:text-white">{usage.user?.name || "Unknown"}</div>
                        <div className="text-slate-500 text-xs">{usage.user?.contact_no || "N/A"}</div>
                      </td>
                      <td className="px-4 py-3">
                        <div>{usage.item_type}</div>
                        <div className="text-slate-400 text-xs">{usage.course?.title}</div>
                      </td>
                      <td className="px-4 py-3">
                        <div>
                           {usage.payable_amount} {usage.currency}
                        </div>
                        {usage.discount_amount > 0 && (
                          <div className="text-success-500 text-xs">Saved  {usage.discount_amount} {usage.currency}</div>
                        )}
                      </td>
                      <td className="px-4 py-3 text-slate-500 dark:text-slate-300">
                        {new Date(usage.created_at).toLocaleDateString()}
                      </td>
                      <td className="px-4 py-3">
                        <Badge
                          label={usage.status}
                          className={
                            usage.status === "Completed"
                              ? "bg-success-500 text-white"
                              : usage.status === "Pending"
                              ? "bg-warning-500 text-white"
                              : "bg-danger-500 text-white"
                          }
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default ShowCoupon;
