import React from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Button from "@/components/ui/Button";
import { Formik, Form } from "formik";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import { initialValues, validationSchema } from "./formSubject";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import Select from "@/components/ui/Select";

const CreateCoupon = () => {
  const [postApi, { isLoading }] = usePostApiMutation();
  const dispatch = useDispatch();
  const { showModal } = useSelector((state) => state.commonReducer);

  const formatDateToSql = (date) => {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };



  const onSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    Object.entries(values).forEach(([key, value]) => {
      if (key === "expiry_date" && value) {
        formData.append(key, formatDateToSql(value));
      } else {
        formData.append(key, value);
      }
    });

    await postApi({
      end_point: "admin/coupons",
      body: formData,
    });

    dispatch(setShowModal(false));
    resetForm();
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={() => dispatch(setShowModal(false))}
      title="Create a Coupon"
      className="max-w-3xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setShowModal(false))}
        />
      }
    >
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
      >
        {({ values, errors, touched, setFieldValue }) => (
          <Form>
            <div className="grid md:grid-cols-3 gap-4 mb-4">
              <InputField
                label="Code"
                name="code"
                type="text"
                placeholder="Enter coupon code"
                required
              />
              <InputField
                label="Discount"
                name="discount"
                type="number"
                placeholder="Enter discount"
                required
              />
              <InputField
                label="Usage Limit"
                name="max_usage"
                type="number"
                placeholder="Enter max usage (Limit)"
                required
              />
            </div>

            <div className="grid md:grid-cols-2 gap-4 mb-4">
            <div>
              <Select
                label="Discount Type"
                name="discount_type"
                value={values.discount_type}
                onChange={(e) => setFieldValue("discount_type", e.target.value)}
                options={[
                  { value: "percentage", label: "Percentage" },
                  { value: "fixed", label: "Fixed" },
                ]}
                placeholder="Select Type"
                error={touched.discount_type && errors.discount_type ? errors.discount_type : ""}
                required
              />
            </div>

              <DatePicker
                label="Expiry Date"
                name="expiry_date"
                format="YYYY-MM-DD"
                placeholder="YYYY-MM-DD"
                onChange={(date) => setFieldValue("expiry_date", date)}
              />
            </div>


            <div className="text-right mt-5">
              <Button isLoading={isLoading} type="submit" className="btn btn-primary">
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default CreateCoupon;
