import React, { useMemo } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Button from "@/components/ui/Button";
import { Formik, Form } from "formik";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import { validationUpdateSchema } from "./formSubject";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal } from "@/features/commonSlice";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import Select from "@/components/ui/Select";
import dayjs from "dayjs";

const EditSubject = () => {
  const dispatch = useDispatch();
  const { showEditModal, editData } = useSelector((state) => state.commonReducer);
  const [updateApi, { isLoading }] = useUpdateApiMutation();

  // Format initial values safely
  const formattedInitialValues = useMemo(() => {
    return {
      ...editData,
      expiry_date: editData?.expiry_date ? dayjs(editData.expiry_date).toDate() : null,
    };
  }, [editData]);



  
  const onSubmit = async (values) => {
    let formData = new FormData();

    Object.entries(values).forEach(([key, value]) => {
      if (key === "expiry_date" && value) {
        // format as YYYY-MM-DD in local timezone
        formData.append(key, dayjs(value).format("YYYY-MM-DD"));
      } else if (key === "is_active") {
        formData.append(key, value ? 1 : 0);
      } else {
        formData.append(key, value);
      }
    });

    await updateApi({
      end_point: "admin/coupons/" + editData.id,
      body: formData,
    });

    dispatch(setEditShowModal(false));
  };

  return (
    <Modal
      activeModal={showEditModal}
      onClose={() => dispatch(setEditShowModal(false))}
      title="Update Coupon"
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setEditShowModal(false))}
        />
      }
    >
      <Formik
        initialValues={formattedInitialValues}
        validationSchema={validationUpdateSchema}
        enableReinitialize
        onSubmit={onSubmit}
      >
        {({ values, errors, touched, setFieldValue }) => (
          <Form>
            <div className="grid md:grid-cols-3 gap-4 mb-4">
              <InputField
                label="Code"
                name="code"
                type="text"
                placeholder="Enter coupon code"
                required
              />
              <InputField
                label="Discount"
                name="discount"
                type="number"
                placeholder="Enter discount"
                required
              />
              <InputField
                label="Usage Limit"
                name="max_usage"
                type="number"
                placeholder="Enter max usage (Limit)"
                required
              />
            </div>

            <div className="grid md:grid-cols-2 gap-4 mb-4">
              <Select
                label="Discount Type"
                name="discount_type"
                value={values.discount_type}
                onChange={(e) => setFieldValue("discount_type", e.target.value)}
                options={[
                  { value: "percentage", label: "Percentage" },
                  { value: "fixed", label: "Fixed" },
                ]}
                placeholder="Select Type"
                error={
                  touched.discount_type && errors.discount_type
                    ? errors.discount_type
                    : ""
                }
                required
              />

              <DatePicker
                label="Expiry Date"
                name="expiry_date"
                format="YYYY-MM-DD"
                placeholder="YYYY-MM-DD"
                value={values.expiry_date}
                onChange={(date) => setFieldValue("expiry_date", date)}
              />
            </div>

            <div className="text-right mt-5">
              <Button isLoading={isLoading} type="submit" className="btn btn-primary">
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default EditSubject;
