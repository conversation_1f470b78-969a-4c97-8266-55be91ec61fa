import { useState } from "react";
import { Icon } from "@iconify/react";
import "./indexTag.css";
import {
  useGetTagsListQuery,
  useTagsCreateOrUpdateMutation,
  useTagsDeleteMutation,
} from "@/store/api/master/masterSettingTagListSlice";

function Index() {
  const { data, isSuccess, isFetching, isError } = useGetTagsListQuery();
  const [tagsCreateOrUpdate] = useTagsCreateOrUpdateMutation();
  const [tagsDelete] = useTagsDeleteMutation();
  const [tags, setTags] = useState([]);
  const [updateState, setUpdateState] = useState(null);
  const [message, setMessage] = useState(""); // State to hold messages

  const handleKeyDown = (e) => {
    if (e.key !== "Enter") return;
    const value = e.target.value;
    if (!value.trim()) return;
    setTags([...tags, value]);
    e.target.value = "";
  };

  const removeTag = (index) => {
    setTags(tags.filter((_, i) => i !== index));
  };

  const handleSubmit = async () => {
    if (tags.length === 0) {
      setMessage("Please add tags");
      return;
    }
    const tagArr = JSON.stringify(tags);

    try {
      const result = await tagsCreateOrUpdate({ tags: tagArr }).unwrap();
      setMessage(result.message);
      setTags([]);
    } catch (error) {
      setMessage(error.data.message);
    }
  };

  const handleDelete = async (id) => {
    try {
      const result = await tagsDelete(id).unwrap();
      setMessage(result.message);
    } catch (error) {
      setMessage(error.data.message);
    }
  };

  const TagsEdit = ({ current }) => {
    const [edit, setEdit] = useState(current.tags);

    const submitKeyDown = (e) => {
      if (e.key !== "Enter") return;
      if (!edit.trim()) return;
      handleEditSubmit(current.id);
    };

    const handleEditSubmit = async (id) => {
      try {
        const result = await tagsCreateOrUpdate({ id, tags: edit }).unwrap();
        setMessage(result.message);
        setUpdateState(null);
      } catch (error) {
        setMessage(error.data.message);
      }
    };

    return (
      <span className="tag-item">
        <input
          type="text"
          className="tags-input"
          placeholder="Type something"
          name="edit"
          value={edit}
          onChange={(e) => setEdit(e.target.value)}
          onKeyDown={submitKeyDown}
        />
        <div className="flex gap-2 my-2">
        <span className="pointer" onClick={() => setUpdateState(null)}>
          <Icon icon="mdi:minus-circle" color="red" width="17" />
        </span>
        <span className="pointer" onClick={() => handleEditSubmit(current.id)}>
          <Icon icon="mdi:upload" color="green" width="17" />
        </span>
        </div>
        
      </span>
    );
  };

  return (
    <div className="text-center">
      <div className="my-5">
        {message && <p className="message">{message}</p>}
        {data?.map((tag, index) =>
          updateState === tag.id ? (
            <TagsEdit current={tag} key={index} />
          ) : (
            <div className="tag-item" key={index}>
              <span className="text me-1">{tag.tags?.slice(0, 20)}</span>
              <div className="flex gap-2 my-2">
              <span className="pointer" onClick={() => setUpdateState(tag.id)}>
                <Icon icon="mdi:pencil" width="17" />
              </span>
              <span className="pointer" onClick={() => handleDelete(tag.id)}>
                <Icon icon="mdi:trash-can" color="red" width="17" />
              </span>

              </div>
              
            </div>
          )
        )}
      </div>

      <div className="area">
        <div className="tags-input-container">
          {tags.map((tag, index) => (
            <div className="tag-item" key={index}>
              <span className="text">{tag?.slice(0, 20)}</span>
              <span className="close" onClick={() => removeTag(index)}>
                &times;
              </span>
            </div>
          ))}
          <input
            onKeyDown={handleKeyDown}
            type="text"
            className="tags-input"
            placeholder="Type something and enter!"
          />
        </div>
      </div>
      <div className="text-center mt-2">
        <button onClick={handleSubmit} className="btn btn-success btn-sm">
          Submit
        </button>
      </div>
    </div>
  );
}

export default Index;
