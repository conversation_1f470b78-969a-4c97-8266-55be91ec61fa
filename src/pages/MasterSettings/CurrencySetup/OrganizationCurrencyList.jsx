import React, { useState } from "react";
import { useGetApiQuery, useDeleteApiMutation } from "@/store/api/master/commonSlice";
import Card from "@/components/ui/Card";
import { Icon } from "@iconify/react";
import Loading from "@/components/Loading";
import { toast } from "react-toastify";

const OrganizationCurrencyList = ({ onEdit, onAdd }) => {
  const { data: orgCurrenciesData, isLoading, error, refetch } = useGetApiQuery("admin/org-currencies");
  const [deleteApi] = useDeleteApiMutation();
  const [deleteConfirm, setDeleteConfirm] = useState(null);

  const orgCurrencies = orgCurrenciesData || [];

  const handleRemove = async (id) => {
    try {
      await deleteApi({
        end_point: `admin/org-currencies/${id}`,
        body: {}
      }).unwrap();
      toast.success("Currency removed from organization successfully");
      setDeleteConfirm(null);
      refetch();
    } catch (error) {
      toast.error(error?.data?.message || "Failed to remove currency");
    }
  };

  const getStatusBadge = (isActive) => {
    return (
      <span
        className={`px-2 py-1 rounded-full text-xs font-medium ${
          isActive
            ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
            : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
        }`}
      >
        {isActive ? "Active" : "Inactive"}
      </span>
    );
  };

  const getDefaultBadge = (isDefault) => {
    if (!isDefault) return null;
    return (
      <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
        Default
      </span>
    );
  };

  if (isLoading) return <Loading />;
  if (error) return <div className="text-red-500">Error loading organization currencies</div>;

  return (
    <Card>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
          Organization Currencies
        </h2>
        <button
          onClick={onAdd}
          className="flex items-center space-x-2 px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
        >
          <Icon icon="heroicons:plus" className="w-4 h-4" />
          <span>Assign Currency</span>
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
          <thead className="bg-slate-50 dark:bg-slate-800">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                Currency
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                Code
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                Symbol
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                Default
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-slate-900 divide-y divide-slate-200 dark:divide-slate-700">
            {orgCurrencies.map((orgCurrency) => (
              <tr key={orgCurrency.id} className="hover:bg-slate-50 dark:hover:bg-slate-800">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-slate-900 dark:text-white">
                    {orgCurrency.currency.name}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-slate-900 dark:text-white font-mono">
                    {orgCurrency.currency.code}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-slate-900 dark:text-white font-mono text-lg">
                    {orgCurrency.currency.symbol}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(orgCurrency.is_active)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getDefaultBadge(orgCurrency.is_default)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => onEdit(orgCurrency)}
                      className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                      title="Edit"
                    >
                      <Icon icon="heroicons:pencil-square" className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => setDeleteConfirm(orgCurrency.id)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                      title="Remove"
                    >
                      <Icon icon="heroicons:trash" className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {orgCurrencies.length === 0 && (
          <div className="text-center py-8">
            <Icon icon="heroicons:currency-dollar" className="w-12 h-12 text-slate-400 mx-auto mb-4" />
            <p className="text-slate-500 dark:text-slate-400">No currencies assigned to this organization</p>
            <button
              onClick={onAdd}
              className="mt-2 text-primary-500 hover:text-primary-600"
            >
              Assign your first currency
            </button>
          </div>
        )}
      </div>

      {/* Remove Confirmation Modal */}
      {deleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-slate-800 rounded-lg p-6 max-w-sm w-full mx-4">
            <div className="flex items-center mb-4">
              <Icon icon="heroicons:exclamation-triangle" className="w-6 h-6 text-red-500 mr-2" />
              <h3 className="text-lg font-medium text-slate-900 dark:text-white">
                Confirm Remove
              </h3>
            </div>
            <p className="text-slate-600 dark:text-slate-400 mb-6">
              Are you sure you want to remove this currency from your organization? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setDeleteConfirm(null)}
                className="px-4 py-2 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200"
              >
                Cancel
              </button>
              <button
                onClick={() => handleRemove(deleteConfirm)}
                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
              >
                Remove
              </button>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};

export default OrganizationCurrencyList;
