import React, { useState } from "react";
import { Icon } from "@iconify/react";
import OrganizationCurrencyList from "./OrganizationCurrencyList";
import CurrencyAssignmentForm from "./CurrencyAssignmentForm";

const CurrencySetup = () => {
  const [showForm, setShowForm] = useState(false);
  const [editingOrgCurrency, setEditingOrgCurrency] = useState(null);

  const handleAdd = () => {
    setEditingOrgCurrency(null);
    setShowForm(true);
  };

  const handleEdit = (orgCurrency) => {
    setEditingOrgCurrency(orgCurrency);
    setShowForm(true);
  };

  const handleCancel = () => {
    setShowForm(false);
    setEditingOrgCurrency(null);
  };

  const handleSuccess = () => {
    setShowForm(false);
    setEditingOrgCurrency(null);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
            Currency Setup
          </h1>
          <p className="text-slate-600 dark:text-slate-400 mt-1">
            Manage currencies for your organization with custom exchange rates
          </p>
        </div>
        <div className="flex items-center space-x-2 text-slate-600 dark:text-slate-400">
          <Icon icon="heroicons:building-office" className="w-5 h-5" />
          <span className="text-sm">Organization Settings</span>
        </div>
      </div>

      {/* Content */}
      {showForm ? (
        <CurrencyAssignmentForm
          orgCurrency={editingOrgCurrency}
          onCancel={handleCancel}
          onSuccess={handleSuccess}
        />
      ) : (
        <OrganizationCurrencyList
          onEdit={handleEdit}
          onAdd={handleAdd}
        />
      )}
    </div>
  );
};

export default CurrencySetup;
