import React, { useState } from "react";
import { Icon } from "@iconify/react";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import OrganizationCurrencyList from "./OrganizationCurrencyList";
import CurrencyAssignmentForm from "./CurrencyAssignmentForm";

const CurrencySetup = () => {
  const [showForm, setShowForm] = useState(false);
  const { data: orgCurrenciesData, refetch } = useGetApiQuery("admin/org-currencies");

  const assignedCurrencies = orgCurrenciesData || [];

  const handleAdd = () => {
    setShowForm(true);
  };

  const handleEdit = (orgCurrency) => {
    // For editing individual currency assignments, we'll handle this in the list component
    console.log("Edit currency:", orgCurrency);
  };

  const handleCancel = () => {
    setShowForm(false);
  };

  const handleSuccess = () => {
    setShowForm(false);
    refetch(); // Refresh the data after successful assignment
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
            Currency Setup
          </h1>
          <p className="text-slate-600 dark:text-slate-400 mt-1">
            Manage currencies for your organization with custom exchange rates
          </p>
        </div>
        <div className="flex items-center space-x-2 text-slate-600 dark:text-slate-400">
          <Icon icon="heroicons:building-office" className="w-5 h-5" />
          <span className="text-sm">Organization Settings</span>
        </div>
      </div>

      {/* Content */}
      {showForm ? (
        <CurrencyAssignmentForm
          assignedCurrencies={assignedCurrencies}
          onCancel={handleCancel}
          onSuccess={handleSuccess}
        />
      ) : (
        <OrganizationCurrencyList
          onEdit={handleEdit}
          onAdd={handleAdd}
        />
      )}
    </div>
  );
};

export default CurrencySetup;
