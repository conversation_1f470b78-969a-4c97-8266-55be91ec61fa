import React, { useState, useEffect } from "react";
import { useGetApiQuery, usePostApiMutation, useUpdateApiMutation } from "@/store/api/master/commonSlice";
import Card from "@/components/ui/Card";
import { Icon } from "@iconify/react";
import { toast } from "react-toastify";
import Loading from "@/components/Loading";

const CurrencyAssignmentForm = ({ orgCurrency, onCancel, onSuccess }) => {
  const { data: currenciesData, isLoading: loadingCurrencies } = useGetApiQuery("admin/currencies");
  const [postApi, { isLoading: isAssigning }] = usePostApiMutation();
  const [updateApi, { isLoading: isUpdating }] = useUpdateApiMutation();
  
  const [formData, setFormData] = useState({
    currency_id: "",
    is_default: false,
    is_active: true,
    custom_exchange_rate: "",
  });
  
  const [errors, setErrors] = useState({});
  const isEditing = !!orgCurrency;
  const isLoading = isAssigning || isUpdating;
  const currencies = currenciesData?.data || [];

  useEffect(() => {
    if (orgCurrency) {
      setFormData({
        currency_id: orgCurrency.currency_id || "",
        is_default: orgCurrency.is_default || false,
        is_active: orgCurrency.is_active || true,
        custom_exchange_rate: orgCurrency.custom_exchange_rate || "",
      });
    }
  }, [orgCurrency]);

  const validateForm = () => {
    const newErrors = {};

    // Currency selection validation
    if (!formData.currency_id) {
      newErrors.currency_id = "Please select a currency";
    }

    // Custom exchange rate validation
    if (formData.custom_exchange_rate) {
      const rate = parseFloat(formData.custom_exchange_rate);
      if (isNaN(rate) || rate < 0) {
        newErrors.custom_exchange_rate = "Exchange rate must be a positive number";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const payload = {
        ...formData,
        custom_exchange_rate: formData.custom_exchange_rate ? parseFloat(formData.custom_exchange_rate) : null,
      };

      if (isEditing) {
        await updateApi({
          end_point: `admin/org-currencies/${orgCurrency.id}`,
          body: payload
        }).unwrap();
      } else {
        await postApi({
          end_point: "admin/org-currencies/assign",
          body: payload
        }).unwrap();
      }
      
      onSuccess();
    } catch (error) {
      if (error?.data?.errors) {
        setErrors(error.data.errors);
        Object.values(error.data.errors)
          .flat()
          .forEach((msg) => toast.error(msg));
      } else {
        toast.error(error?.data?.message || "An error occurred");
      }
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const getSelectedCurrency = () => {
    return currencies.find(c => c.id === parseInt(formData.currency_id));
  };

  if (loadingCurrencies) return <Loading />;

  return (
    <Card>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
          {isEditing ? "Update Currency Assignment" : "Assign Currency"}
        </h2>
        <button
          onClick={onCancel}
          className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
        >
          <Icon icon="heroicons:x-mark" className="w-6 h-6" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Currency Selection */}
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            Select Currency *
          </label>
          <select
            name="currency_id"
            value={formData.currency_id}
            onChange={handleChange}
            disabled={isEditing}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
              errors.currency_id 
                ? "border-red-500 focus:ring-red-500" 
                : "border-slate-300 dark:border-slate-600"
            } dark:bg-slate-800 dark:text-white ${isEditing ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <option value="">Select a currency...</option>
            {currencies.map((currency) => (
              <option key={currency.id} value={currency.id}>
                {currency.name} ({currency.code}) - {currency.symbol}
              </option>
            ))}
          </select>
          {errors.currency_id && (
            <p className="mt-1 text-sm text-red-500">{errors.currency_id}</p>
          )}
          {isEditing && (
            <p className="mt-1 text-xs text-slate-500 dark:text-slate-400">
              Currency cannot be changed when editing
            </p>
          )}
        </div>

        {/* Currency Info Display */}
        {formData.currency_id && getSelectedCurrency() && (
          <div className="p-4 bg-slate-50 dark:bg-slate-700 rounded-lg">
            <h4 className="text-sm font-medium text-slate-900 dark:text-white mb-2">Currency Information</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-slate-600 dark:text-slate-400">Name:</span>
                <span className="ml-2 text-slate-900 dark:text-white">{getSelectedCurrency().name}</span>
              </div>
              <div>
                <span className="text-slate-600 dark:text-slate-400">Code:</span>
                <span className="ml-2 text-slate-900 dark:text-white font-mono">{getSelectedCurrency().code}</span>
              </div>
              <div>
                <span className="text-slate-600 dark:text-slate-400">Symbol:</span>
                <span className="ml-2 text-slate-900 dark:text-white font-mono text-lg">{getSelectedCurrency().symbol}</span>
              </div>
              <div>
                <span className="text-slate-600 dark:text-slate-400">Base Rate:</span>
                <span className="ml-2 text-slate-900 dark:text-white font-mono">{getSelectedCurrency().exchange_rate}</span>
              </div>
            </div>
          </div>
        )}


        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-slate-200 dark:border-slate-700">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isLoading && <Icon icon="heroicons:arrow-path" className="w-4 h-4 animate-spin" />}
            <span>{isEditing ? "Update Assignment" : "Assign Currency"}</span>
          </button>
        </div>
      </form>
    </Card>
  );
};

export default CurrencyAssignmentForm;
