import React, { useState, useEffect } from "react";
import { useGetApiQuery, usePostApiMutation } from "@/store/api/master/commonSlice";
import Card from "@/components/ui/Card";
import { Icon } from "@iconify/react";
import { toast } from "react-toastify";
import Loading from "@/components/Loading";

const CurrencyAssignmentForm = ({ assignedCurrencies, onCancel, onSuccess }) => {
  const { data: currenciesData, isLoading: loadingCurrencies } = useGetApiQuery("admin/currencies");
  const [postApi, { isLoading: isAssigning }] = usePostApiMutation();

  const [selectedCurrencies, setSelectedCurrencies] = useState([]);
  const [errors, setErrors] = useState({});
  const isLoading = isAssigning;

  const currencies = currenciesData?.data || [];

  console.log(assignedCurrencies);

  // ✅ Initialize selected currencies from assignedCurrencies once currencies are loaded
  useEffect(() => {
    if (currencies.length > 0 && assignedCurrencies?.length > 0) {
      const assignedIds = assignedCurrencies.map((ac) => ac.currency_id);
      setSelectedCurrencies(assignedIds);
    }
  }, [currencies, assignedCurrencies]);

  const validateForm = () => {
    const newErrors = {};
    if (selectedCurrencies.length === 0) {
      newErrors.currencies = "Please select at least one currency";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      const payload = {
        currency_ids: selectedCurrencies,
      };

      await postApi({
        end_point: "admin/org-currencies/assign",
        body: payload,
      }).unwrap();

      onSuccess();
    } catch (error) {
      if (error?.data?.errors) {
        setErrors(error.data.errors);
        Object.values(error.data.errors)
          .flat()
          .forEach((msg) => toast.error(msg));
      } else {
        toast.error(error?.data?.message || "An error occurred");
      }
    }
  };

  const handleCurrencyToggle = (currencyId) => {
    setSelectedCurrencies((prev) =>
      prev.includes(currencyId)
        ? prev.filter((id) => id !== currencyId)
        : [...prev, currencyId]
    );

    if (errors.currencies) {
      setErrors((prev) => ({ ...prev, currencies: "" }));
    }
  };

  if (loadingCurrencies) return <Loading />;

  const assignedCurrencyIds = assignedCurrencies?.map((ac) => ac.currency_id) || [];

  return (
    <Card>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
          Assign Currencies
        </h2>
        <button
          onClick={onCancel}
          className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
        >
          <Icon icon="heroicons:x-mark" className="w-6 h-6" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Currency Selection */}
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-4">
            Select Currencies <span className="text-red-500">*</span>
          </label>

          <div className="grid grid-cols-4 md:grid-cols-6 gap-4">
            {currencies.map((currency) => (
              <div
                key={currency.id}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedCurrencies.includes(currency.id)
                    ? "border-primary-500 bg-primary-50 dark:bg-primary-900/20"
                    : "border-slate-200 dark:border-slate-600 hover:border-slate-300 dark:hover:border-slate-500"
                }`}
                onClick={() => handleCurrencyToggle(currency.id)}
              >
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={selectedCurrencies.includes(currency.id)}
                    onChange={(e) => {
                      e.stopPropagation(); // Prevent toggle from both card and checkbox
                      handleCurrencyToggle(currency.id);
                    }}
                    className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500"
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-sm font-medium text-slate-900 dark:text-white">
                          {currency.name}
                        </h4>
                        <p className="text-xs text-slate-600 dark:text-slate-400">
                          {currency.code} - {currency.symbol}
                        </p>
                      </div>
                    </div>
                    {assignedCurrencyIds.includes(currency.id) && (
                      <div className="mt-2">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                          Currently Assigned
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {errors.currencies && (
            <p className="mt-2 text-sm text-red-500">{errors.currencies}</p>
          )}

          <p className="mt-2 text-xs text-slate-500 dark:text-slate-400">
            Select currencies to assign to your organization. You can assign multiple currencies at once.
          </p>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-slate-200 dark:border-slate-700">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isLoading || selectedCurrencies.length === 0}
            className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isLoading && <Icon icon="heroicons:arrow-path" className="w-4 h-4 animate-spin" />}
            <span>
              Assign {selectedCurrencies.length > 0 ? `${selectedCurrencies.length} ` : ''}
              Currenc{selectedCurrencies.length === 1 ? 'y' : 'ies'}
            </span>
          </button>
        </div>
      </form>
    </Card>
  );
};

export default CurrencyAssignmentForm;
