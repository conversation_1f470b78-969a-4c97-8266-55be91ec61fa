import React, { useState, useEffect } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useDispatch } from "react-redux";
import { setEditData, setEditShowModal, setShowModal } from "@/features/commonSlice";
import {
  useGetApiQuery,
} from "@/store/api/master/commonSlice";
import CreatePaymentGateway from "./create";
import EditPaymentGateway from "./edit";
import DeletePaymentGateway from "./delete";

const PaymentGateway = () => {
  const dispatch = useDispatch();
  const [apiParam, setApiParam] = useState("");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);
  const [tableData, setTableData] = useState([]);

  // Fetch payment gateways
  const {
    data,
    isLoading,
    isFetching,
    refetch
  } = useGetApiQuery(`admin/organization-payment-gateways${apiParam}`);

  // Format data for table
  useEffect(() => {
    if (data) {
      try {
        const formattedData = data.map((item) => ({
          id: item.id,
          name: item.payment_gateway?.name || "N/A",
          status: (
            <Badge
              className={item.is_active ? "bg-success-500" : "bg-danger-500"}
              label={item.is_active ? "Active" : "Inactive"}
            />
          ),
          created_at: new Date(item.created_at).toLocaleDateString(),
          // Store only the data we need, not the entire item
          raw: {
            id: item.id,
            payment_gateway_id: item.payment_gateway_id,
            is_active: item.is_active,
            credentials: item.credentials,
            created_at: item.created_at
          },
        }));
        setTableData(formattedData);
      } catch (error) {
        console.error("Error formatting table data:", error);
      }
    }
  }, [data]);

  // Table columns
  const columns = [
    {
      label: "ID",
      field: "id",
    },
    {
      label: "Gateway Name",
      field: "name",
    },
    {
      label: "Status",
      field: "status",
    },
    {
      label: "Created At",
      field: "created_at",
    },
    {
      label: "Actions",
      field: "",
    },
  ];

  // Table actions
  const actions = [
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (index) => {
        const selectedItem = tableData[index].raw;
        dispatch(setEditData(selectedItem));
        dispatch(setEditShowModal(true));
      },
    },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (index) => {
        const selectedItem = tableData[index].raw;
        setDeleteData(selectedItem);
        setShowDeleteModal(true);
      },
    },
  ];

  // Handle page change
  const changePage = (page) => {
    setApiParam(page);
  };

  // No need to get modal state here as BasicTablePage handles it

  return (
    <div>
      <BasicTablePage
        loading={isLoading || isFetching}
        title="Payment Gateways"
        createButton="Add Payment Gateway"
        createButtonAction={() => dispatch(setShowModal(true))}
        createPage={<CreatePaymentGateway />}
        editPage={<EditPaymentGateway />}
        actions={actions}
        columns={columns}
        data={tableData}
        changePage={changePage}
        currentPage={data?.current_page || 1}
        totalPages={data?.last_page || 1}
        setFilter={setApiParam}
      />

      <DeletePaymentGateway
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
        refetch={refetch}
      />
    </div>
  );
};

export default PaymentGateway;