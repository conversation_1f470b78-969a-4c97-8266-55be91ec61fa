import React, { useState, useEffect } from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { Formik, Form, FieldArray } from "formik";
import * as Yup from "yup";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import {
  useGetApiQuery,
  usePostApiMutation,
} from "@/store/api/master/commonSlice";

import Switch from "@/components/ui/Switch";
import InputField from "@/components/ui/InputField";
import { toast } from "react-toastify";

const CreatePaymentGateway = () => {
  const dispatch = useDispatch();
  const { showModal } = useSelector((state) => state.commonReducer);
  const [postApi, { isLoading }] = usePostApiMutation();
  const [paymentGateways, setPaymentGateways] = useState([]);
  const [selectedGateway, setSelectedGateway] = useState(null);
  const [isActive, setIsActive] = useState(true);

  // Fetch available payment gateways
  const { data: gatewaysData } = useGetApiQuery("admin/payment-gateways");

  console.log(gatewaysData);

  useEffect(() => {
    if (gatewaysData) {
      // Handle different API response structures
      const gatewayList = gatewaysData.data || gatewaysData;

      if (!gatewayList || !Array.isArray(gatewayList)) {
        console.error("Invalid gateway data format:", gatewaysData);
        return;
      }

      const options = gatewayList.map((gateway) => {
        // Parse credential_format if it exists
        let credentialFields = [];
        let credentialDefaults = {};

        if (gateway.credential_format) {
          try {
            credentialDefaults = JSON.parse(gateway.credential_format);
            credentialFields = Object.keys(credentialDefaults);
          } catch (error) {
            console.error("Error parsing credential_format:", error);
          }
        }

        return {
          value: gateway.id,
          label: gateway.name,
          type: gateway.type,
          logo: gateway.logo,
          description: gateway.short_description,
          credentials: credentialFields,
          credentialDefaults: credentialDefaults
        };
      });

      setPaymentGateways(options);
      console.log("Payment Gateways:", options);
    }
  }, [gatewaysData]);

  // Handle gateway selection
  const handleGatewayChange = (e, setFieldValue) => {
    const gatewayId = e.target.value;
    const gateway = paymentGateways.find((g) => g.value == gatewayId);
    setSelectedGateway(gateway);
    setFieldValue("payment_gateway_id", gatewayId);

    // Initialize credential fields with defaults if available
    if (gateway && gateway.credentials) {
      const initialCredentials = {};
      gateway.credentials.forEach(field => {
        // Use default values from credential_format if available
        initialCredentials[field] = gateway.credentialDefaults[field] || "";
      });
      setFieldValue("credentials", initialCredentials);
    }
  };

  // Form validation schema
  const validationSchema = Yup.object().shape({
    payment_gateway_id: Yup.string().required("Payment gateway is required"),
    credentials: Yup.object().required("Credentials are required"),
  });

  // Initial form values
  const initialValues = {
    payment_gateway_id: "",
    credentials: {},
    is_active: true,
  };

  // Handle form submission
  const handleSubmit = async (values) => {
    try {
      // Add is_active value from state
      values.is_active = isActive;

      const response = await postApi({
        end_point: "admin/organization-payment-gateways",
        body: values,
      });

      if (response.data) {
        dispatch(setShowModal(false));
      } else if (response.error) {
      }
    } catch (error) {
      toast.error("An error occurred while adding payment gateway");
      console.error(error);
    }
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={() => dispatch(setShowModal(false))}
      title="Add Payment Gateway"
      className="max-w-2xl"
    >
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ values, errors, touched, setFieldValue }) => (
          <Form className="space-y-4">
            {/* Payment Gateway Selection */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-3">
                Select Payment Gateway
              </label>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {paymentGateways.map((gateway) => (
                  <div
                    key={gateway.value}
                    className={`
                      relative p-4 border rounded-lg cursor-pointer transition-all
                      ${values.payment_gateway_id == gateway.value
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-slate-200 hover:border-slate-300'
                      }
                    `}
                    onClick={() => {
                      // Create a mock event object to maintain compatibility with handleGatewayChange
                      const mockEvent = { target: { value: gateway.value } };
                      handleGatewayChange(mockEvent, setFieldValue);
                    }}
                  >
                    <div className="flex items-center gap-3">
                      {/* Radio button */}
                      <div className={`
                        w-5 h-5 rounded-full border-2 flex items-center justify-center
                        ${values.payment_gateway_id == gateway.value
                          ? 'border-primary-500'
                          : 'border-slate-300'
                        }
                      `}>
                        {values.payment_gateway_id == gateway.value && (
                          <div className="w-3 h-3 rounded-full bg-primary-500"></div>
                        )}
                      </div>

                      {/* Gateway logo */}
                      <div className="flex-grow flex items-center gap-3">
                        {gateway.logo ? (
                          <div className="w-10 h-10 flex-shrink-0">
                            <img
                              src={import.meta.env.VITE_ASSET_HOST_URL + gateway.logo}
                              alt={gateway.label}
                              className="w-full h-full object-contain"
                            />
                          </div>
                        ) : (
                          <div className="w-10 h-10 bg-slate-100 rounded-md flex items-center justify-center flex-shrink-0">
                            <span className="text-slate-400 text-xs">{gateway.label.substring(0, 2)}</span>
                          </div>
                        )}

                        <div>
                          <div className="font-medium text-slate-800">{gateway.label}</div>
                          {gateway.description && (
                            <div className="text-xs text-slate-500 line-clamp-1">{gateway.description}</div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {errors.payment_gateway_id && touched.payment_gateway_id && (
                <div className="text-danger-500 text-sm mt-2">
                  {errors.payment_gateway_id}
                </div>
              )}
            </div>

            {/* Selected Gateway Information */}
            {selectedGateway && (
              <div className="bg-primary-50 p-5 rounded-lg border border-primary-100 mb-4">
                <div className="flex items-center gap-4 mb-3">
                  <div className="w-10 h-10 bg-white rounded-full p-1.5 flex items-center justify-center shadow-sm">
                    {selectedGateway.logo ? (
                      <img
                        src={import.meta.env.VITE_ASSET_HOST_URL + selectedGateway.logo}
                        alt={selectedGateway.label}
                        className="w-full h-full object-contain"
                      />
                    ) : (
                      <div className="w-full h-full bg-primary-100 rounded-full flex items-center justify-center">
                        <span className="text-primary-600 font-medium">{selectedGateway.label.substring(0, 2)}</span>
                      </div>
                    )}
                  </div>
                  <div className="flex-grow">
                    <h3 className="text-base font-medium text-slate-800 mb-0.5">
                      {selectedGateway.label}
                    </h3>
                    <div className="flex items-center gap-2">
                      <span className="px-2 py-0.5 bg-primary-100 text-primary-700 text-xs rounded-full">
                        {selectedGateway.type}
                      </span>
                      {selectedGateway.description && (
                        <span className="text-sm text-slate-500">
                          {selectedGateway.description}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="text-sm text-slate-600 bg-white p-3 rounded border border-slate-200">
                  <p className="mb-0">
                    <span className="font-medium">Note:</span> Please enter the required credentials below to connect to this payment gateway.
                    These credentials are typically found in your {selectedGateway.label} developer dashboard.
                  </p>
                </div>
              </div>
            )}

            {/* Credentials Fields */}
            {selectedGateway && selectedGateway.credentials && selectedGateway.credentials.length > 0 && (
              <div className="bg-white p-5 rounded-lg border border-slate-200 shadow-sm">
                <h3 className="text-base font-medium text-slate-800 mb-4 flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-primary-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                  </svg>
                  Gateway Credentials
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                  {selectedGateway.credentials.map((field) => {
                    const isSecret = field.includes("key") || field.includes("secret") || field.includes("password") || field.includes("token");
                    const fieldLabel = field.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());

                    return (
                      <div key={field} className="relative">
                        <InputField
                          label={fieldLabel}
                          name={`credentials.${field}`}
                          type={isSecret ? "password" : "text"}
                          placeholder={`Enter ${field.replace(/_/g, " ")}`}
                          value={values.credentials[field] || ""}
                          onChange={(e) => {
                            setFieldValue(`credentials.${field}`, e.target.value);
                          }}
                        />
                        {isSecret && (
                          <div className="flex items-center gap-1 text-xs text-amber-600 mt-1">
                            <svg xmlns="http://www.w3.org/2000/svg" className="w-3.5 h-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                            </svg>
                            These are sensitive fields. Keep them secure.
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}



            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-slate-200">
              <Button
                text="Cancel"
                className="btn-outline-dark"
                onClick={() => dispatch(setShowModal(false))}
                type="button"
              />
              <Button
                text="Add Gateway"
                className="btn-primary"
                type="submit"
                isLoading={isLoading}
                disabled={isLoading}
              />
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default CreatePaymentGateway;
