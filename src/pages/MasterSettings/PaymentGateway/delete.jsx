import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";
import { useDeleteApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";

const DeletePaymentGateway = ({ showDeleteModal, setShowDeleteModal, data, refetch }) => {
  const [deleteApi, { isLoading }] = useDeleteApiMutation();

  // Handle delete confirmation
  const handleDelete = async () => {
    if (!data) return;

    try {
      let formData = new FormData();
      const response = await deleteApi({
        end_point: `admin/organization-payment-gateways/${data.id}`,
        body: {}
      });

      if (response.data) {
        setShowDeleteModal(false);
        if (refetch) refetch();
      } else if (response.error) {
        toast.error(response.error.data?.message || "Failed to delete payment gateway");
      }
    } catch (error) {
      toast.error("An error occurred while deleting payment gateway");
      console.error(error);
    }
  };

  if (!data) return null;

  return (
    <Modal
      activeModal={showDeleteModal}
      onClose={() => setShowDeleteModal(false)}
      title="Delete Payment Gateway"
      titleClass="text-danger-500"
      className="max-w-2xl mx-auto"
      centered
      noFade
    >
      <div className="text-center justify-center mb-5">
      <div className="flex justify-center mb-4 text-danger-500">
        <Icon icon="heroicons-outline:exclamation-circle" className="text-5xl" />
      </div>
        <div className="text-xl font-medium mb-2">Are you sure?</div>
        <div className="text-slate-500">
          Do you really want to delete this payment gateway? This process cannot be undone.
        </div>
      </div>
      <div className="flex justify-center gap-3">
        <Button
          text="Cancel"
          className="btn-outline-dark"
          onClick={() => setShowDeleteModal(false)}
        />
        <Button
          text="Yes, Delete"
          className="btn-danger"
          onClick={handleDelete}
          isLoading={isLoading}
          disabled={isLoading}
        />
      </div>
    </Modal>
  );
};

export default DeletePaymentGateway;
