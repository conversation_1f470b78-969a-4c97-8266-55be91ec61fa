import React, { useState, useEffect } from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal } from "@/features/commonSlice";
import {
  useGetApiQuery,
  useUpdateApiMutation,
} from "@/store/api/master/commonSlice";
import InputField from "@/components/ui/InputField";
import Switch from "@/components/ui/Switch";
import { toast } from "react-toastify";

const EditPaymentGateway = () => {
  const dispatch = useDispatch();
  const { showEditModal, editData } = useSelector((state) => state.commonReducer);
  const [updateApi, { isLoading }] = useUpdateApiMutation();
  const [isActive, setIsActive] = useState(false);
  const [gatewayDetails, setGatewayDetails] = useState(null);

  // Fetch gateway details
  const { data: gatewayData, isLoading: isGatewayLoading } = useGetApiQuery(
    editData ? `admin/payment-gateways/${editData.payment_gateway_id}` : null,
    { skip: !editData }
  );

  useEffect(() => {
    if (editData) {
      setIsActive(editData.is_active);
    }
  }, [editData]);

  useEffect(() => {
    // Handle different API response structures
    if (gatewayData) {
      // The API might return data directly or nested in a data property
      const gateway = gatewayData.data || gatewayData;

      // Parse credential_format if it exists
      let credentialFields = [];
      let credentialDefaults = {};

      if (gateway.credential_format) {
        try {
          credentialDefaults = JSON.parse(gateway.credential_format);
          credentialFields = Object.keys(credentialDefaults);
        } catch (error) {
          console.error("Error parsing credential_format:", error);
        }
      }

      // Set gateway details with parsed credential information
      setGatewayDetails({
        ...gateway,
        credentials: credentialFields,
        credentialDefaults: credentialDefaults
      });
    }
  }, [gatewayData]);

  // Form validation schema
  const validationSchema = Yup.object().shape({
    credentials: Yup.object().required("Credentials are required"),
  });

  // Initial form values
  const getInitialValues = () => {
    if (!editData) return { credentials: {} };

    return {
      credentials: editData.credentials || {},
    };
  };

  // Handle form submission
  const handleSubmit = async (values) => {
    try {
      const changedValues = {};
      let credentialsChanged = false;
  
      if (editData.credentials) {
        credentialsChanged = Object.keys(values.credentials).some(
          key => values.credentials[key] !== editData.credentials[key]
        );
  
        const originalKeys = Object.keys(editData.credentials || {});
        const newKeys = Object.keys(values.credentials || {});
  
        if (newKeys.length !== originalKeys.length) {
          credentialsChanged = true;
        }
      } else if (Object.keys(values.credentials).length > 0) {
        credentialsChanged = true;
      }
  
      if (credentialsChanged) {
        changedValues.credentials = values.credentials;
      }
  
      if (isActive !== editData.is_active) {
        changedValues.is_active = isActive;
      }
  
      if (Object.keys(changedValues).length === 0) {
        toast.info("No changes detected");
        dispatch(setEditShowModal(false));
        return;
      }
  
      // Construct FormData
      const formData = new FormData();
  
      // Flatten credentials if changed
      if (changedValues.credentials) {
        Object.entries(changedValues.credentials).forEach(([key, value]) => {
          formData.append(`credentials[${key}]`, value);
        });
      }
  
      // Append other changed values
      Object.entries(changedValues).forEach(([key, value]) => {
        if (key !== "credentials") {
          formData.append(key, value);
        }
      });
  
      const response = await updateApi({
        end_point: `admin/organization-payment-gateways/${editData.id}`,
        body: formData,
        is_form_data: true, // if your API wrapper requires this
      });
  
      if (response.data) {
        dispatch(setEditShowModal(false));
      } else if (response.error) {
        toast.error(response.error.data?.message || "Failed to update payment gateway");
      }
    } catch (error) {
      toast.error("An error occurred while updating payment gateway");
      console.error(error);
    }
  };
  
  // Show loading or return null if data isn't ready
  if (!editData) {
    return null;
  }

  if (isGatewayLoading) {
    return (
      <Modal
        activeModal={showEditModal}
        onClose={() => dispatch(setEditShowModal(false))}
        title="Loading Gateway Details"
        className="max-w-2xl"
      >
        <div className="flex justify-center items-center p-6">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>
      </Modal>
    );
  }

  if (!gatewayDetails) {
    return null;
  }

  return (
    <Modal
      activeModal={showEditModal}
      onClose={() => dispatch(setEditShowModal(false))}
      title={`Edit ${gatewayDetails.name} Gateway`}
      className="max-w-2xl"
    >
      <Formik
        initialValues={getInitialValues()}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ values, errors, touched, setFieldValue }) => (
          <Form className="space-y-4">
            {/* Gateway Information */}
            <div className="bg-slate-50 p-4 rounded-lg border border-slate-200 mb-4">
              <div className="flex items-start gap-4">
                {gatewayDetails.logo && (
                  <div className="flex-shrink-0 w-12 h-12">
                    <img
                      src={import.meta.env.VITE_ASSET_HOST_URL + gatewayDetails.logo}
                      alt={gatewayDetails.name}
                      className="w-full h-full object-contain"
                    />
                  </div>
                )}
                <div className="flex-grow">
                  <h3 className="text-base font-medium text-slate-800 mb-1">
                    {gatewayDetails.name} ({gatewayDetails.type})
                  </h3>
                  {gatewayDetails.short_description && (
                    <p className="text-sm text-slate-600 mb-0">
                      {gatewayDetails.short_description}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Credentials Fields */}
            {gatewayDetails.credentials && gatewayDetails.credentials.length > 0 && (
              <div className="bg-slate-50 p-4 rounded-lg border border-slate-200">
                <h3 className="text-sm font-medium text-slate-700 mb-3">
                  Gateway Credentials
                </h3>
                <div className="space-y-3">
                  {gatewayDetails.credentials.map((field) => {
                    const isSecret = field.includes("key") || field.includes("secret") || field.includes("password") || field.includes("token");
                    const fieldLabel = field.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());

                    return (
                      <div key={field} className="relative">
                        <InputField
                          label={fieldLabel}
                          name={`credentials.${field}`}
                          type={isSecret ? "password" : "text"}
                          placeholder={`Enter ${field.replace(/_/g, " ")}`}
                          value={values.credentials[field] || ""}
                          onChange={(e) => {
                            setFieldValue(`credentials.${field}`, e.target.value);
                          }}
                        />
                        {isSecret && (
                          <div className="text-xs text-slate-500 mt-1">
                            This is a sensitive field. Keep it secure.
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Status Toggle */}
            <div className="flex items-center justify-between bg-slate-50 p-3 rounded-lg border border-slate-200">
              <label className="block text-sm font-medium">Active Status</label>
              <Switch
                activeClass="bg-success-500"
                value={isActive}
                onChange={() => setIsActive(!isActive)}
              />
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-slate-200">
              <Button
                text="Cancel"
                className="btn-outline-dark"
                onClick={() => dispatch(setEditShowModal(false))}
                type="button"
              />
              <Button
                text="Update Gateway"
                className="btn-primary"
                type="submit"
                isLoading={isLoading}
                disabled={isLoading}
              />
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default EditPaymentGateway;
