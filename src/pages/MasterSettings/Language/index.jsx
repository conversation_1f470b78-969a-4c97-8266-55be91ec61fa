import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Modal from "@/components/ui/Modal";
import { useGetApiQuery, usePostApiMutation, useUpdateApiMutation } from "@/store/api/master/commonSlice";
import { setShowModal, setEditData } from "@/features/commonSlice";
import { useDispatch, useSelector } from "react-redux";
import Delete from "./Delete";

const Language = () => {
  const { data: apiData, isLoading, isFetching } = useGetApiQuery("admin/label-translations");
  const [postApi] = usePostApiMutation();
  const [updateApi] = useUpdateApiMutation();

  const [formData, setFormData] = useState({ label_key: "", label_value: "" });
  const dispatch = useDispatch();
  const { showModal,  editData } = useSelector((state) => state.commonReducer);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);
  const [editModal, setEditModal] = useState(false);

  const handleModalToggle = () => {
    dispatch(setShowModal(!showModal));
    setFormData({ label_key: "", label_value: "" }); // reset
  };

  const handleEditModalToggle = () => {
    setEditModal(!editModal);
    setFormData({ label_key: "", label_value: "" }); 
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async () => {
    if (!formData.label_key || !formData.label_value) {
      alert("Both fields are required");
      return;
    }

    if (editModal && editData) {
        let formPayload = new FormData();
        formPayload.append("label_key", formData.label_key);
        formPayload.append("label_value", formData.label_value);
      // Update existing translation
      const response = await updateApi({
        end_point: `admin/label-translations/${editData.id}`,
        body: formPayload,
      });
      console.log("Update response:", response);
      setEditModal(false);
    } else {
      // Create new translation
      const response = await postApi({
        end_point: "admin/label-translations",
        body: formData,
      });
      console.log("Submit response:", response);
      handleModalToggle();
    }
  };

  const columns = [
    { label: "Label Key", field: "label_key" },
    { label: "Label Value", field: "label_value" },
    {
      label: "Created At",
      field: "created_at",
    },
    {
      label: "Updated At",
      field: "updated_at",
    },
    {
      label: "Actions", field: "",
    },
  ];

  const tableData = apiData?.data?.map((item) => ({
    ...item,
    created_at: new Date(item.created_at).toLocaleDateString(),
    updated_at: new Date(item.updated_at).toLocaleDateString(),
  })) || [];

  const actions = [
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        const itemToEdit = apiData.data[val];
        dispatch(setEditData(itemToEdit));
        setFormData({
          label_key: itemToEdit.label_key,
          label_value: itemToEdit.label_value
        });
        setEditModal(true);
      },
    },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(apiData.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  return (
    <>
      <BasicTablePage
        createButton="Add Translation"
        loading={isLoading || isFetching}
        title="Translation List"
        actions={actions}
        columns={columns}
        data={tableData}
        currentPage={apiData?.current_page}
        totalPages={Math.ceil(apiData?.total / apiData?.per_page)}
      />

      {/* Add New Translation Modal */}
      {showModal && (
        <Modal
          activeModal={showModal}
          onClose={handleModalToggle}
          title="Add New Translation"
          className="max-w-md"
        >
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Label Key</label>
              <input
                type="text"
                name="label_key"
                value={formData.label_key}
                onChange={handleChange}
                className="w-full border px-3 py-2 rounded"
                placeholder="e.g. student"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Label Value</label>
              <input
                type="text"
                name="label_value"
                value={formData.label_value}
                onChange={handleChange}
                className="w-full border px-3 py-2 rounded"
                placeholder="e.g. Participant"
              />
            </div>
            <div className="flex justify-end gap-2">
              <button
                className="bg-gray-300 text-gray-800 px-4 py-2 rounded hover:bg-gray-400"
                onClick={handleModalToggle}
              >
                Cancel
              </button>
              <button
                className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
                onClick={handleSubmit}
              >
                Save
              </button>
            </div>
          </div>
        </Modal>
      )}

      {/* Edit Translation Modal */}
      {editModal && (
        <Modal
          activeModal={editModal}
          onClose={handleEditModalToggle}
          title="Edit Translation"
          className="max-w-md"
        >
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Label Key</label>
              <input
                type="text"
                name="label_key"
                value={formData.label_key}
                onChange={handleChange}
                className="w-full border px-3 py-2 rounded"
                placeholder="e.g. student"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Label Value</label>
              <input
                type="text"
                name="label_value"
                value={formData.label_value}
                onChange={handleChange}
                className="w-full border px-3 py-2 rounded"
                placeholder="e.g. Participant"
              />
            </div>
            <div className="flex justify-end gap-2">
              <button
                className="bg-gray-300 text-gray-800 px-4 py-2 rounded hover:bg-gray-400"
                onClick={handleEditModalToggle}
              >
                Cancel
              </button>
              <button
                className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
                onClick={handleSubmit}
              >
                Update
              </button>
            </div>
          </div>
        </Modal>
      )}

      {showDeleteModal && 
        <Delete showDeleteModal={showDeleteModal} setShowDeleteModal={setShowDeleteModal} data={deleteData} />
      }
    </>
  );
};

export default Language;
