import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Select from "@/components/ui/Select";
import Card from "@/components/ui/Card";
import Create from "./create";
import Edit from "./edit";
import Delete from "./delete";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";

import avatar from "@/assets/images/avatar/av-1.svg";
import { useNavigate } from "react-router-dom";
import ChangePasswordModal from "@/components/partials/common-modals/ChangePasswordModal";
// import { useParams } from "react-router-dom";



const TableHeaderExtra = ({filterType}) => {

  const filterUserType = (val) => {
    filterType(val);
  };

  return <>
  <Select
    className="w-52"
    defaultValue={{ label: "All", value: "All" }}
    placeholder="Select User Type"
    options={[
      { label: "All", value: "All" },
      // { label: "System Admin", value: "SystemAdmin" },
      // { label: "Super Admin", value: "SuperAdmin" },
      { label: "Organization Admin", value: "OrganizationAdmin" },
      { label: "Mentor", value: "Mentor" },
      { label: "Student", value: "Student" },
      // { label: "Other", value: "Other" },
    ]}
    name="user_type"
    onChange={(e) => {
      filterUserType(e.target.value);
    }}
  />
</>;
};

const index = () => {
  const [showModal, setShowModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [filter, setFilter] = useState("");
  const [apiParam, setApiParam] = useState("");
  const dispatch = useDispatch();
  const url = `admin/users${apiParam}`;
  const {data, isLoading, isFetching} = useGetApiQuery(url);
  const changePage = (val) => {
    console.log(apiParam);
    console.log(val);

    if (apiParam) {

      const newParam = apiParam.includes("&") ? apiParam.split("&")[0] : apiParam;
      setApiParam(newParam + val.replace("?", "&"));

    } else {
      setApiParam(val);
    }
  };

  const navigate = useNavigate();

  const filterType = (val)  =>  {
    setApiParam(`?user_type=${val}`);

  }
  // console.log("user:", data);
  const columns = [
    {
      label: "SL",
      field: "id",
    },
    {
      label: "Name",
      field: "name",
    },
    {
      label: "User Name",
      field: "username",
    },
    {
      label: "Email",
      field: "email",
    },
    {
      label: "Contact Number",
      field: "contact_no",
    },
    {
      label: "User Type",
      field: "user_type",
    },
    {
      label: "Status",
      field: "status",
    },
    {
      label: "Action",
      field: "",
    },
  ];
  const handleIndexImageNameClick = (id) => {
    const selectedMentor = data?.data.find((item) => item.id === id);
    navigate(`/mentor-details/${id}`, { state: { mentor: selectedMentor } });
  };

  const tableData = data?.data?.map((item, index) => {
    return {
      id: index + 1,
      name: (
        <button
          type="button"
          onClick={() => handleIndexImageNameClick(item.id)}
          className="flex items-center bg-transparent border-none cursor-pointer p-0"
        >
          <img
            src={
              item.image
                ? import.meta.env.VITE_ASSET_HOST_URL + item.image
                : avatar
            }
            className="rounded-full w-8 h-8 mr-2"
            alt="avatar"
          />
          <span className="hover:text-primary-500 hover:underline">
            {item.name}
          </span>
        </button>
      ),
      username: item.username,
      email: item.email,
      contact_no: item.contact_no,
      user_type: item.user_type,
      status: (
        <Badge
          className={
            item.is_active
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {item.is_active ? "Active" : "Inactive"}{" "}
        </Badge>
      ),
    };
  });

  const actions = [
    {
      name: "Edit",
      icon: "lucide:edit",
      onClick: (val) => {
        dispatch(setEditData(data.data[val]));
        dispatch(setEditShowModal(true));
      },
    },
    {
      name: "Change Password",
      icon: "heroicons-outline:key",
      onClick: (val) => {
        setSelectedUser(data.data[val]);
        setShowPasswordModal(true);
      },
    },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  const handleSubmit = () => {
    setShowModal(false);
  };

  const createPage = <Create />;
  const editPage = <Edit />;

  return (
    <>

      <div>


        <BasicTablePage
          loading={isLoading || isFetching}
          tableHeaderExtra={<TableHeaderExtra filterType={filterType}/>}
          title="User List"
          createButton="Create New User"
          createPage={createPage}
          editPage={editPage}
          actions={actions}
          columns={columns}
          data={tableData}
          changePage={changePage}
          currentPage={data?.current_page}
          submitForm={handleSubmit}
          totalPages={Math.ceil(data?.total / data?.per_page)}
          filter={filter}
          setFilter={setApiParam}
        />

        <Delete
          showDeleteModal={showDeleteModal}
          setShowDeleteModal={setShowDeleteModal}
          data={deleteData}
        />

        {selectedUser && (
          <ChangePasswordModal
            showModal={showPasswordModal}
            setShowModal={setShowPasswordModal}
            userId={selectedUser.id}
            userName={selectedUser.name}
          />
        )}
      </div>
    </>
  );
};

export default index;
