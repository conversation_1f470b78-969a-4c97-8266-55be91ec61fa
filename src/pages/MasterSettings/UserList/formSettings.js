import React from "react";
import * as yup from "yup";

export const initialValues = {
  name: "",
  username: "",
  email: "",
  contact_no: "",
  user_type: "",
  password: "",
  is_active: "",
};

export const validationSchema = yup.object({
  name: yup
    .string()
    .max(50, "Should not be more than 50 characters")
    .min(3, "Should not be less than 3 characters")
    .required("Name is Required"),

  username: yup
    .string()
    .max(50, "Should not be more than 50 characters")
    .min(3, "Should not be less than 3 characters")
    .nullable(),

  email: yup.string().email().required("Email is Required"),
  contact_no: yup
    .string()
    .matches(
      /^(\+?[0-9]{1,4})?0?\d{6,14}$/,
      "Contact number must be a valid number"
    )
    .required("Contact number is required"),


  password: yup
    .string()
    .required("Password is required")
    .min(8, "Password must be at least 8 characters")
    .max(20, "Password must not exceed 20 characters"),
});

export const editValidationSchema = yup.object({
  name: yup
    .string()
    .max(50, "Should not be more than 50 characters")
    .min(3, "Should not be less than 3 characters")
    .required("Name is Required"),

  username: yup
    .string()
    .max(50, "Should not be more than 50 characters")
    .min(3, "Should not be less than 3 characters")
    .nullable(),

  email: yup.string().nullable().email("Invalid email address"),
  
  contact_no: yup
    .string()
    .matches(
      /^(\+?[0-9]{1,4})?0?\d{6,14}$/,
      "Contact number must be a valid number"
    )
    .required("Contact number is required"),
});
