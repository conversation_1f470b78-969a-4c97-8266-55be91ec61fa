import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import Select from "@/components/ui/Select";
import { Formik, Form, Field } from "formik";
import { editValidationSchema } from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal } from "@/features/commonSlice";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import { useParams } from "react-router-dom";

const edit = () => {
  const [updateApi, { isLoading, isError, error, isSuccess }] =
    useUpdateApiMutation();
  const dispatch = useDispatch();
  const { showEditModal } = useSelector((state) => state.commonReducer);
  const { editData } = useSelector((state) => state.commonReducer);
  const [isActive, setIsActive] = useState(editData.is_active);
  const { id } = useParams();

  const onSubmit = async (values, { resetForm, setErrors }) => {
    let formData = new FormData();
    formData.append("name", values.name);
    formData.append("username", values.username || '');
    formData.append("email", values.email || '');
    formData.append("contact_no", values.contact_no);
    formData.append("user_type", values.user_type);
    formData.append("is_active", isActive ? 1 : 0);
    values.id = id;
    values.is_active = isActive;
    const response = await updateApi({
      end_point: "admin/users/" + editData.id,
      body: formData,
    });
    if (response.error) {
      setErrors(response.error.data.errors);
    } else if (response.data) {
      dispatch(setEditShowModal(false));
    }
  };
  return (
    <Modal
      activeModal={showEditModal}
      onClose={() => dispatch(setEditShowModal(false))}
      title="Update User"
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setEditShowModal(false))}
        />
      }
    >
      <Formik
        validationSchema={editValidationSchema}
        initialValues={editData}
        onSubmit={onSubmit}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          handleSubmit,
          setFieldValue,
          isSubmitting,
        }) => (
          <Form>
            <>
              <div className="grid md:grid-cols-2 gap-4">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  placeholder="Enter Name"
                  required
                />

                { editData.user_type !== "Mentor" &&
                editData.user_type !== "Student" &&
                editData.user_type !== "Other" ? (
                  <InputField
                    label="User Name"
                    name="username"
                    type="text"
                    placeholder="Enter User Name"
                    required
                  />
              ): ''}
                <InputField
                  label="Email Address"
                  name="email"
                  type="email"
                  placeholder="Enter Email Address"
                  required
                />
                <NumberInput
                  defaultValue={editData.contact_no || ""}
                  label="Contact Number"
                  name="contact_no"
                  placeholder="Enter Contact Number"
                  required
                />
                {editData.user_type !== "Mentor" &&
                editData.user_type !== "Student" &&
                editData.user_type !== "Other" ? (
                  <>
                    <Select
                      defaultValue={editData.user_type}
                      label="User Type"
                      placeholder="Select User Type"
                      options={[
                        { label: "SystemAdmin", value: "SystemAdmin" },
                        { label: "SuperAdmin", value: "SuperAdmin" },
                        {
                          label: "OrganizationAdmin",
                          value: "OrganizationAdmin",
                        },
                        { label: "Mentor", value: "Mentor" },
                        { label: "Student", value: "Student" },
                        { label: "Other", value: "Other" },
                      ]}
                      name="user_type"
                      onChange={(e) => {
                        setFieldValue("user_type", e.target.value);
                      }}
                    />
                  </>
                ) : (
                  ""
                )}

                {/* <InputField
                  label="Password"
                  name="password"
                  type="password"
                  placeholder="Enter Password"
                  required
                /> */}
                <div className="mt-10 ms-5">
                  <Switch
                    label="Active"
                    activeClass="bg-success-500"
                    value={isActive}
                    name="is_active"
                    onChange={() => setIsActive(!isActive)}
                  />
                </div>
              </div>
            </>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default edit;
