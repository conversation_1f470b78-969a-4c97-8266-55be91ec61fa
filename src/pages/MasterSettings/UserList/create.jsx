import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import Select from "@/components/ui/Select";
import { Formik, Form, Field } from "formik";
import { initialValues, validationSchema } from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";

const create = () => {
  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();
  const dispatch = useDispatch();

  const [isActive, setIsActive] = useState(true);
  const { showModal } = useSelector((state) => state.commonReducer);

  const onSubmit = async (values, { resetForm, setErrors }) => {
    let formData = new FormData();
    formData.append("name", values.name);
    formData.append("username", values.username);
    formData.append("email", values.email);
    formData.append("contact_no", values.contact_no);
    formData.append("user_type", values.user_type);
    formData.append("password", values.password);
    formData.append("is_active", isActive ? 1 : 0);

    const response = await postApi({
      end_point: "admin/users",
      body: formData,
    });

    if (response.error) {
      setErrors(response.error.data.errors);
    } else if (response.data) {
      resetForm();
      dispatch(setShowModal(false));
    }
  };
  return (
    <Modal
      activeModal={showModal}
      onClose={() => dispatch(setShowModal(false))}
      title="Add New User"
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setShowModal(false))}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={initialValues}
        onSubmit={onSubmit}
      >
        {({ values, errors, setFieldValue }) => (
          <Form>
            <>
              <div className="grid md:grid-cols-2 gap-4">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  placeholder="Enter Name"
                  required
                />
                <InputField
                  label="User Name"
                  name="username"
                  type="text"
                  placeholder="Enter User Name"
                />
                <InputField
                  label="Email Address"
                  name="email"
                  type="email"
                  placeholder="Enter Email Address"
                  required
                />
                <NumberInput
                  label="Contact Number"
                  name="contact_no"
                  placeholder="Enter Contact Number"
                  required
                />
                <div>

                <label
                    htmlFor="user_type"
                    className="block text-[#1D1D1F] text-base font-medium mb-2"
                >
                    User Type  <span className="text-red-500">*</span>
                </label>
                  <Select
                    defaultValue=""
                    label=""
                    placeholder="Select User Type"
                    options={[
                      { label: "SystemAdmin", value: "SystemAdmin" },
                      { label: "SuperAdmin", value: "SuperAdmin" },
                      {
                        label: "OrganizationAdmin",
                        value: "OrganizationAdmin",
                      },
                      { label: "Mentor", value: "Mentor" },
                      { label: "Student", value: "Student" },
                      { label: "Other", value: "Other" },
                    ]}
                    name="user_type"
                    onChange={(e) => {
                      setFieldValue("user_type", e.target.value);
                    }}
                  />
                </div>
                <InputField
                  label="Password"
                  name="password"
                  type="password"
                  placeholder="Enter Password"
                  required
                />
        
              </div>
            </>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default create;
