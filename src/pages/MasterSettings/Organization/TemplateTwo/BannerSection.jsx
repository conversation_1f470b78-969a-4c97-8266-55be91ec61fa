import React, { useState } from "react";
import { Icon } from "@iconify/react";
// Removed ReactPlayer import as we're replacing it with iframe
import worldGif from "@/assets/images/all-img/world.gif";
import Tooltip from "@/components/ui/Tooltip";
import { useNavigate } from "react-router-dom";

const BannerSection = ({
  organization,
  setFieldValue,
  values,
  errors,
  touched,
  setFieldTouched,
  handleSubmit,
}) => {
  // Image Upload States
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageInput, setShowImageInput] = useState(false);
  const [tempFile, setTempFile] = useState(null);

  // Video Upload States
  const [showVideoInput, setShowVideoInput] = useState(false);
  const [tempVideoURL, setTempVideoURL] = useState("");

  // Handle Image File Selection
  const handleImageFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setTempFile(file);
      setSelectedImage(URL.createObjectURL(file));
    }
  };

  const navigate = useNavigate();
  // Save Image
  const handleImageSave = () => {
    if (tempFile) {
      setFieldValue("banner", tempFile);
      handleSubmit();
      handleImageCancel();
    }
  };

  // Cancel Image Upload
  const handleImageCancel = () => {
    setShowImageInput(false);
    setSelectedImage(null);
    setTempFile(null);
  };

  // Stats Section States
  const [showStatsInput, setShowStatsInput] = useState(false);

  const handleStatsCancel = () => {
    setShowStatsInput(false);
  };

  // Show Stats Input Overlay
  const handleStatsEditClick = () => {
    setShowStatsInput(true);
  };

  // Handle Video URL Input Change
  const handleVideoChange = (e) => {
    setTempVideoURL(e.target.value);
  };

  // Save Video URL
  const handleVideoSave = () => {
    if (tempVideoURL.trim()) {
      setFieldValue("promotional_video", tempVideoURL.trim());
      handleSubmit();
      handleVideoCancel();
    }
  };

  // Cancel Video Editing
  const handleVideoCancel = () => {
    setShowVideoInput(false);
    setTempVideoURL("");
  };

  // Show Video Input Overlay
  const handleVideoEditClick = () => {
    setShowVideoInput(true);
    setTempVideoURL(values?.promotional_video || "");
  };

  // Determine Image Source
  const imageSrc =
    selectedImage ||
    (values?.banner
      ? URL.createObjectURL(values.banner)
      : `${import.meta.env.VITE_ASSET_HOST_URL}${organization?.banner}`);

  // Helper function to convert YouTube URLs to embed URLs
  const getEmbedUrl = (url) => {
    if (!url) return "";
    const youtubeMatch = url.match(
      /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})/
    );
    const shortYoutubeMatch = url.match(
      /(?:https?:\/\/)?(?:www\.)?youtu\.be\/([a-zA-Z0-9_-]{11})/
    );

    let videoId = "";
    if (youtubeMatch && youtubeMatch[1]) {
      videoId = youtubeMatch[1];
    } else if (shortYoutubeMatch && shortYoutubeMatch[1]) {
      videoId = shortYoutubeMatch[1];
    }

    if (videoId) {
      return `https://www.youtube.com/embed/${videoId}?autoplay=1&mute=1&loop=1&controls=0&playlist=${videoId}`;
    }

    // For non-YouTube URLs, return as is or handle accordingly
    return url;
  };

  // Determine Video URL for iframe
  const videoSrc =
    organization?.promotional_video ||
    "https://www.youtube.com/watch?v=88jH_04zmIw";

  const embedVideoUrl = getEmbedUrl(videoSrc);

  return (
    <div className="grid grid-cols-12 gap-5">
      {/* Left Column: Professor Image and Image Upload */}
      <div className="col-span-6 flex flex-col">
        {/* Professor Image */}

        <Tooltip content="Set a few Mentors as Featured after you have added them">
          <div
            className="rounded-lg w-68 h-72 relative bg-blue-500 cursor-pointer mb-4"
            onClick={() => navigate("/mentor-list")}
          >
            <h3 className="text-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
              Featured Mentors
            </h3>
          </div>
        </Tooltip>

        {/* Image Upload Section */}
        <div className="w-full rounded-lg relative shadow-lg">
          {/* Current Image */}
          <div className="relative w-full h-64 rounded shadow group">
            <img
              src={imageSrc}
              alt="Hero Background"
              className="w-full h-full object-cover rounded"
            />

            {/* Image Upload Overlay */}
            <div
              className={`absolute inset-0 bg-black bg-opacity-50 text-white text-2xl flex items-center justify-center transition-all duration-700 ease-in-out ${showImageInput
                ? "opacity-100"
                : "opacity-0 group-hover:opacity-100"
                }`}
            >
              {!showImageInput ? (
                <button
                  onClick={() => setShowImageInput(true)}
                  className="font-semibold text-xs cursor-pointer border border-[#4669FA] rounded-md text-[#4669FA] bg-white px-4 py-2 hover:bg-[#4669FA] hover:text-white"
                  aria-label="Add Photo"
                >
                  Add Photo
                </button>
              ) : (
                <div className="flex flex-col items-center gap-4 w-full px-4">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageFileChange}
                    className="w-full text-sm text-gray-700 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:bg-gray-300 hover:file:bg-gray-100 border-2 bg-white rounded-md"
                    aria-label="Select Banner Image"
                  />
                  <div className="flex gap-2">
                    <button
                      onClick={handleImageCancel}
                      className="font-semibold text-xs cursor-pointer border border-[#4669FA] rounded-md text-[#4669FA] bg-white px-4"
                      aria-label="Cancel Banner Upload"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleImageSave}
                      className="font-semibold text-xs cursor-pointer rounded-md text-white py-1 bg-[#4669FA] px-4"
                      aria-label="Save Banner Image"
                    >
                      Save
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Error Message for Image */}
          {errors.banner && touched.banner && (
            <div className="text-red-500 text-xs mt-1">{errors.banner}</div>
          )}

          {/* Upload Icon (Visible on Hover) */}
          <div className="absolute top-2 right-2 hidden group-hover:flex">
            <Icon
              icon="heroicons:arrow-up-tray"
              className="w-6 h-6 text-gray-500"
            />
          </div>
        </div>
      </div>

      {/* Right Column: Video Section and Stats */}
      <div className="col-span-6 flex flex-col h-full">
        {/* Video Section */}
        <div className="p-10"></div>
        <div className="flex-1 w-full relative flex items-center justify-center shadow-lg border-2 border-sky-50 hover:border-gray-600 group cursor-pointer mb-5">
          {/* Iframe for Video */}
          <iframe
            src={embedVideoUrl}
            title="Promotional Video"
            allow="autoplay; encrypted-media"
            allowFullScreen
            className="rounded-lg w-full h-full"
          ></iframe>

          {/* Video Upload Overlay */}
          <div
            className={`absolute inset-0 bg-black bg-opacity-50 text-white text-2xl flex items-center justify-center transition-all duration-700 ease-in-out ${showVideoInput
              ? "opacity-100"
              : "opacity-0 group-hover:opacity-100"
              }`}
          >
            {!showVideoInput ? (
              <button
                onClick={handleVideoEditClick}
                className="font-semibold text-xs cursor-pointer border border-[#4669FA] rounded-md text-[#4669FA] bg-white px-4 py-2 hover:bg-[#4669FA] hover:text-white"
                aria-label="Edit Promotional Video"
              >
                Update Promotional Video URL(Youtube url)
              </button>
            ) : (
              <div className="flex flex-col items-center gap-4 w-full px-4">
                <input
                  type="url"
                  placeholder="Enter video URL"
                  value={tempVideoURL}
                  onChange={handleVideoChange}
                  className="w-full text-sm text-gray-700 px-4 py-2 rounded bg-white placeholder-gray-400"
                  aria-label="Promotional Video URL"
                />
                <div className="flex gap-2">
                  <button
                    onClick={handleVideoCancel}
                    className="font-semibold text-xs cursor-pointer border border-[#4669FA] rounded-md text-[#4669FA] bg-white px-4"
                    aria-label="Cancel Video Edit"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleVideoSave}
                    className="font-semibold text-xs cursor-pointer rounded-md text-white py-1 bg-[#4669FA] px-4"
                    aria-label="Save Promotional Video"
                  >
                    Save
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Stats Section */}
        <div className="relative w-full h-32 rounded-lg shadow-lg border bg-gray-200 group cursor-pointer">
          <img
            src={worldGif}
            alt="Rotating Globe"
            className="w-full h-full object-cover rounded-lg"
          />
          <div
            className={`absolute inset-0 bg-gray-200 opacity-75 rounded-lg flex items-center justify-center transition-all duration-700 ease-in-out ${showStatsInput
              ? "opacity-100"
              : "opacity-0 group-hover:opacity-100"
              }`}
            onClick={!showStatsInput ? handleStatsEditClick : undefined}
          >
            {!showStatsInput ? (
              <span className="text-center text-lg font-semibold text-sky-700">
                <span className="text-2xl sm:text-4xl">
                  Total Students
                </span>
                <br />
                {values.custom_student_number || 0} Worldwide Users
              </span>
            ) : (
              <div className="flex flex-col items-center gap-4 w-full px-4">
                <input
                  type="text"
                  placeholder="Enter total students"
                  maxLength={8}
                  value={values.custom_student_number || ""}
                  onChange={(e) => {
                    const newValue = e.target.value;
                    if (newValue === '') {
                      setFieldValue("custom_student_number", '');
                    } else if (/^[1-9]\d*$/.test(newValue) && newValue.length <= 8) {
                      setFieldValue("custom_student_number", newValue);
                    } else if (newValue === '0') {
                      setFieldValue("custom_student_number", newValue);
                      setFieldTouched("custom_student_number", true);
                    }
                  }}
                  onBlur={() => setFieldTouched("custom_student_number", true)}
                  className={`w-full text-sm text-gray-700 px-4 py-2 rounded bg-white placeholder-gray-400 ${
                    errors.custom_student_number && touched.custom_student_number 
                      ? "border-2 border-red-500" 
                      : ""
                  }`}
                  aria-label="Total Students"
                />
                {errors.custom_student_number && touched.custom_student_number && (
                  <div className="text-red-500 text-xs mt-1 absolute">
                    {errors.custom_student_number}
                  </div>
                )}
                <div className="flex gap-2">
                  <button
                    onClick={handleStatsCancel}
                    className="font-semibold text-xs cursor-pointer border border-[#4669FA] rounded-md text-[#4669FA] bg-white px-4"
                    aria-label="Cancel Edit Total Students"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => {
                      if (!/^\d+$/.test(values.custom_student_number || "")) {
                        setFieldTouched("custom_student_number", true);
                        return;
                      }
                      handleSubmit();
                      handleStatsCancel();
                    }}
                    className="font-semibold text-xs cursor-pointer rounded-md text-white py-1 bg-[#4669FA] px-4"
                    aria-label="Save Total Students"
                  >
                    Save
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BannerSection;
