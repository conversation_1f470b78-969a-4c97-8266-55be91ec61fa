import React, { useState, useRef } from "react";
import { useSelector } from "react-redux";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import heroInstructorIcon from "@/assets/images/all-img/heroInstructorIcon.png";

const InstructorCounter = ({ organization }) => {
  const { user } = useSelector((state) => state.auth);
  const [updateApi] = useUpdateApiMutation();

  // Instructor count editing states
  const [isEditingInstructorCount, setIsEditingInstructorCount] = useState(false);
  const [instructorCountValue, setInstructorCountValue] = useState(
    organization?.custom_instructor_number ? String(organization?.custom_instructor_number) : "80"
  );
  const [isSavingInstructorCount, setIsSavingInstructorCount] = useState(false);
  const [instructorCountError, setInstructorCountError] = useState("");
  
  const instructorCountInputRef = useRef(null);

  return (
    <div className="flex items-center justify-center flex-col gap-2 relative group">
          <img className="max-sm:w-10 mx-auto" src={heroInstructorIcon} alt="" />
          <div className="relative">
            {isEditingInstructorCount ? (
              <div className="flex items-center">
                <input
                  ref={instructorCountInputRef}
                  type="text"
                  min="1"
                  value={instructorCountValue}
                  onChange={(e) => {
                    // Remove non-numeric characters and leading zeros
                    const value = e.target.value.replace(/^0+|[^0-9]/g, '');
                    setInstructorCountValue(value);
                    if (instructorCountError) setInstructorCountError("");
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      // Validate input
                      if (instructorCountValue === '' || instructorCountValue === '0') {
                        setInstructorCountError("Please enter a valid number greater than 0");
                        return;
                      }

                      // Check if the value is a valid number
                      if (isNaN(Number(instructorCountValue))) {
                        setInstructorCountError("Please enter a valid number");
                        return;
                      }

                      const submitInstructorCount = async () => {
                        setIsSavingInstructorCount(true);
                        try {
                          const formData = new FormData();
                          formData.append("custom_instructor_number", instructorCountValue);

                          await updateApi({
                            end_point: `admin/organizations/${user?.organization_id}`,
                            body: formData
                          }).unwrap();

                          setIsEditingInstructorCount(false);
                          setInstructorCountError("");
                        } catch (err) {
                          console.error("Failed to update instructor count:", err);
                          setInstructorCountError("Failed to update. Please try again.");
                        } finally {
                          setIsSavingInstructorCount(false);
                        }
                      };

                      submitInstructorCount();
                    } else if (e.key === 'Escape') {
                      setIsEditingInstructorCount(false);
                      setInstructorCountValue(organization?.custom_instructor_number ? String(organization?.custom_instructor_number) : "80");
                      setInstructorCountError("");
                    }
                  }}
                  className="w-24 text-center text-2xl max-sm:text-xl font-semibold text-blue-600 bg-transparent outline-none border-b border-gray-400 py-1 transition-all duration-200 ease-in-out focus:border-blue-500"
                  autoFocus
                />
                <span className="text-2xl max-sm:text-xl font-semibold text-blue-600">+</span>
                <span className="text-blue-600 ml-1">Instructors</span>

                {/* Check icon */}
                <button
                  onClick={async () => {
                    // Validate input
                    if (instructorCountValue === '' || instructorCountValue === '0') {
                      setInstructorCountError("Please enter a valid number greater than 0");
                      return;
                    }

                    // Check if the value is a valid number
                    if (isNaN(Number(instructorCountValue))) {
                      setInstructorCountError("Please enter a valid number");
                      return;
                    }

                    setIsSavingInstructorCount(true);
                    try {
                      const formData = new FormData();
                      formData.append("custom_instructor_number", instructorCountValue);

                      await updateApi({
                        end_point: `admin/organizations/${user?.organization_id}`,
                        body: formData
                      }).unwrap();

                      setIsEditingInstructorCount(false);
                      setInstructorCountError("");
                    } catch (err) {
                      console.error("Failed to update instructor count:", err);
                      setInstructorCountError("Failed to update. Please try again.");
                    } finally {
                      setIsSavingInstructorCount(false);
                    }
                  }}
                  className="ml-2 text-green-500 hover:text-green-700"
                  disabled={instructorCountValue === '' || instructorCountValue === '0' || isSavingInstructorCount}
                  aria-label="Submit Instructor Count"
                >
                  <Icon icon="heroicons-outline:check" className="w-5 h-5" />
                </button>
              </div>
            ) : (
              <div className="flex items-center">
                <p
                  className="text-blue-600 cursor-pointer"
                  onClick={() => {
                    setIsEditingInstructorCount(true);
                    setInstructorCountValue(organization?.custom_instructor_number ? String(organization?.custom_instructor_number) : "80");
                  }}
                >
                  <span className="text-2xl max-sm:text-xl font-semibold">
                    {organization?.custom_instructor_number ? organization.custom_instructor_number : "80"}+
                  </span>{" "}
                  Instructors
                </p>

                {/* Pencil icon */}
                <button
                  onClick={() => {
                    setIsEditingInstructorCount(true);
                    setInstructorCountValue(organization?.custom_instructor_number ? String(organization?.custom_instructor_number) : "80");
                  }}
                  className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 bg-blue-50 hover:bg-blue-100 rounded-full"
                  aria-label="Edit Instructor Count"
                >
                  <Icon icon="heroicons-outline:pencil" className="text-blue-600 w-4 h-4" />
                </button>
              </div>
            )}

            {instructorCountError && (
              <div className="text-red-500 text-xs mt-1">{instructorCountError}</div>
            )}
          </div>
    </div>
  );
};

export default InstructorCounter;
