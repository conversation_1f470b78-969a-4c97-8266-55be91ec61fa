import React, { useState } from "react";
import useFooterType from "@/hooks/theme/useFooterType";
import { useTranslation } from "react-i18next";
import { Icon } from "@iconify/react";
import googlePlay from "./googleplay.svg";
import appstore from "./appstore.svg";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { toast } from "react-toastify";
import { useGetMenuListQuery } from "@/store/api/master/menuSlice";

const ASSET_URL = import.meta.env.VITE_ASSET_HOST_URL;



const Footer = ({ organization, setFieldValue, values, errors, touched, handleSubmit, setFieldTouched }) => {
    const { t } = useTranslation();
    const { data: menuData } = useGetMenuListQuery({ footer: 1, pagination: 0 });

    const [editingLink, setEditingLink] = useState(null);
    const [tempLinkValue, setTempLinkValue] = useState("");

    // State for editable address, email, and contact number
    const [editingField, setEditingField] = useState(null);
    const [tempAddressValue, setTempAddressValue] = useState("");
    const [tempEmailValue, setTempEmailValue] = useState("");
    const [tempContactValue, setTempContactValue] = useState("");

    const handleEditClick = (type) => {
        setEditingLink(type);
        switch (type) {
            case 'facebook':
                setTempLinkValue(values.facebook || "");
                break;
            case 'twitter':
                setTempLinkValue(values.twitter || "");
                break;
            case 'linkedin':
                setTempLinkValue(values.linkedin || "");
                break;
            case 'youtube':
                setTempLinkValue(values.youtube || "");
                break;
            case 'website':
                setTempLinkValue(values.website || "");
                break;
            default:
                setTempLinkValue("");
        }
    };

    const handleCancel = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setEditingLink(null);
        setTempLinkValue("");
    };

    // Function to validate URL
    const isValidUrl = (url) => {
        if (!url || url.trim() === "") return false;
        try {
            new URL(url);
            return true;
        } catch (e) {
            return false;
        }
    };

    const handleSave = (e) => {
        e.preventDefault();
        e.stopPropagation();

        if (editingLink) {
            // Check if the link is empty
            if (!tempLinkValue || tempLinkValue.trim() === "") {
                toast.error("Link cannot be empty");
                return;
            }

            // Check if the link is a valid URL
            if (!isValidUrl(tempLinkValue)) {
                toast.error("Please enter a valid URL");
                return;
            }

            // If validation passes, update the field value
            switch (editingLink) {
                case 'facebook':
                    setFieldValue("facebook", tempLinkValue);
                    break;
                case 'twitter':
                    setFieldValue("twitter", tempLinkValue);
                    break;
                case 'linkedin':
                    setFieldValue("linkedin", tempLinkValue);
                    break;
                case 'youtube':
                    setFieldValue("youtube", tempLinkValue);
                    break;
                case 'website':
                    setFieldValue("website", tempLinkValue);
                    break;
            }

            // Submit the form and reset the state
            handleSubmit();
            setEditingLink(null);
            setTempLinkValue("");
            // toast.success("Link updated successfully");
        }
    };

    const handleInputChange = (e) => {
        setTempLinkValue(e.target.value);
    };

    const [showLogoInput, setShowLogoInput] = useState(false);
    const [selectedLogo, setSelectedLogo] = useState(null);
    const [tempLogoFile, setTempLogoFile] = useState(null);

    const handleLogoFileChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setTempLogoFile(file);
            setSelectedLogo(URL.createObjectURL(file));
        }
    };

    const handleLogoSave = () => {
        if (tempLogoFile) {
            setFieldValue("footer_logo", tempLogoFile);
            handleSubmit();
            handleLogoCancel();
        } else {
            // If no file is selected, just close the input without sending footer_logo
            handleLogoCancel();
        }
    };

    const handleLogoCancel = () => {
        setShowLogoInput(false);
        setSelectedLogo(null);
        setTempLogoFile(null);
    };

    // Functions for editable address
    const handleEditAddress = () => {
        setEditingField('address');
        setTempAddressValue(values.address || organization?.address || "");
    };

    const handleAddressCancel = () => {
        setEditingField(null);
        setTempAddressValue("");
    };

    const handleAddressSave = () => {
        console.log(tempAddressValue);
        // Validate address (non-empty)
        // if (!tempAddressValue.trim()) {
        //     toast.error("Address cannot be empty");
        //     return;
        // }

        setFieldValue("address", tempAddressValue.trim());
        handleSubmit();
        setEditingField(null);
    };

    const handleAddressChange = (e) => {
        setTempAddressValue(e.target.value);
    };

    // Functions for editable email
    const handleEditEmail = () => {
        setEditingField('email');
        setTempEmailValue(values.email || organization?.email || "");
    };

    const handleEmailCancel = () => {
        setEditingField(null);
        setTempEmailValue("");
    };

    const isValidEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const handleEmailSave = () => {
        // Validate email (non-empty and valid format)
        if (!tempEmailValue.trim()) {
            toast.error("Email cannot be empty");
            return;
        }

        if (!isValidEmail(tempEmailValue.trim())) {
            toast.error("Please enter a valid email address");
            return;
        }

        setFieldValue("email", tempEmailValue.trim());
        handleSubmit();
        setEditingField(null);
    };

    const handleEmailChange = (e) => {
        setTempEmailValue(e.target.value);
    };

    // Functions for editable contact number
    const handleEditContact = () => {
        setEditingField('contact_no');
        setTempContactValue(values.contact_no || organization?.contact_no || "");
    };

    const handleContactCancel = () => {
        setEditingField(null);
        setTempContactValue("");
    };

    const isValidPhoneNumber = (phone) => {
        // International phone number validation (allows digits, spaces, dashes, plus sign, and parentheses)
        const phoneRegex = /^[+]?[\d\s\-()+]+$/;
        return phoneRegex.test(phone) && phone.trim().length >= 3;
    };

    const handleContactSave = () => {
        // Validate contact number (non-empty and valid format)
    
        if (tempContactValue && !isValidPhoneNumber(tempContactValue.trim())) {
            toast.error("Please enter a valid contact number");
            return;
        }

        setFieldValue("contact_no", tempContactValue.trim() || "");
        handleSubmit();
        setEditingField(null);
    };

    const handleContactChange = (e) => {
        setTempContactValue(e.target.value);
    };

    return (
        <div>
            <div className="relative bg-gradient-to-t from-[#CAE5FE] to-[#E4F2FF]">
                <div className="container py-10 ">
                    {/* <footer className={className + " " + footerClassName()}> */}
                    <footer className="">
                        <div className="px-6 py-4 relative">
                            <div className="relative grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 items-start gap-4 max-sm:gap-8">
                                <div className="col-span-1 flex flex-col items-center md:items-start h-full justify-between">
                                    <div className="relative cursor-pointer">
                                        {/* Show footer_logo if available, fall back to logo, or show organization name as text */}
                                        {selectedLogo || (values?.footer_logo && typeof values.footer_logo !== 'string') ? (
                                            <img
                                                src={selectedLogo || URL.createObjectURL(values.footer_logo)}
                                                alt="Logo"
                                                className="h-[80px] w-auto"
                                            />
                                        ) : organization?.footer_logo ? (
                                            <img
                                                src={ASSET_URL + organization.footer_logo}
                                                alt="Logo"
                                                className="h-[80px] w-auto"
                                            />
                                        ) : organization?.logo ? (
                                            <img
                                                src={ASSET_URL + organization.logo}
                                                alt="Logo"
                                                className="h-[80px] w-auto"
                                            />
                                        ) : (
                                            <div className="h-[80px] flex items-center justify-center">
                                                <h2 className="text-xl font-bold text-gray-800">{organization?.name || "Organization Name"}</h2>
                                            </div>
                                        )}

                                        {/* Logo Upload Overlay */}
                                        <div
                                            className={`absolute inset-0 bg-black bg-opacity-50 text-white flex items-center justify-center transition-all duration-700 ease-in-out ${showLogoInput
                                                ? "opacity-100"
                                                : "opacity-0 group-hover:opacity-100"
                                                }`}
                                        >
                                            {!showLogoInput ? (
                                                <button
                                                    onClick={() => setShowLogoInput(true)}
                                                    className="font-semibold text-xs cursor-pointer border border-[#4669FA] rounded-md text-[#4669FA] bg-transparent px-4 py-2 hover:bg-[#4669FA] hover:text-white"
                                                    aria-label="Change Footer Logo"
                                                >
                                                    Change Footer Logo
                                                </button>
                                            ) : (
                                                <div className="flex flex-col items-center gap-4 w-full px-4" style={{ minWidth: "300px" }}>
                                                    <input
                                                        type="file"
                                                        accept="image/*"
                                                        onChange={handleLogoFileChange}
                                                        className="w-full text-sm text-gray-700 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:bg-gray-300 hover:file:bg-gray-100 border-2 bg-white rounded-md"
                                                        aria-label="Select Logo Image"
                                                    />
                                                    <div className="flex gap-2">
                                                        <button
                                                            onClick={handleLogoCancel}
                                                            className="font-semibold text-xs cursor-pointer border border-[#4669FA] rounded-md text-[#4669FA] bg-white px-4"
                                                            aria-label="Cancel Logo Upload"
                                                        >
                                                            Cancel
                                                        </button>
                                                        <button
                                                            onClick={handleLogoSave}
                                                            className="font-semibold text-xs cursor-pointer rounded-md text-white py-1 bg-[#4669FA] px-4"
                                                            aria-label="Save Logo Image"
                                                            disabled={!tempLogoFile}
                                                        >
                                                            Save
                                                        </button>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                    <h3 className="text-lg text-gray-700 mb-3">Follow Us On</h3>
                                    <div className="flex space-x-4">
                                        {/* Facebook Link */}
                                        <div className="relative group">
                                            {editingLink === 'facebook' ? (
                                                <div
                                                    className="absolute -top-16 -left-2 bg-white p-3 rounded-lg shadow-lg z-10 w-64"
                                                    onClick={(e) => e.stopPropagation()}
                                                >
                                                    <div className="relative">
                                                        <input
                                                            type="url"
                                                            value={tempLinkValue}
                                                            onChange={handleInputChange}
                                                            className={`border ${!tempLinkValue.trim() || !isValidUrl(tempLinkValue) ? 'border-red-500' : 'border-gray-300'} rounded p-2 text-sm w-full mb-2`}
                                                            placeholder="https://www.facebook.com/yourpage"
                                                            required
                                                        />
                                                    </div>
                                                    <div className="flex justify-end space-x-2">
                                                        <button
                                                            type="button"
                                                            onClick={handleCancel}
                                                            className="font-semibold text-xs cursor-pointer border border-[#4669FA] rounded-md text-[#4669FA] bg-white px-3 py-1"
                                                        >
                                                            Cancel
                                                        </button>
                                                        <button
                                                            type="button"
                                                            onClick={handleSave}
                                                            className={`font-semibold text-xs rounded-md text-white py-1 px-3 ${!tempLinkValue.trim() || !isValidUrl(tempLinkValue) ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#4669FA] cursor-pointer'}`}
                                                            disabled={!tempLinkValue.trim() || !isValidUrl(tempLinkValue)}
                                                        >
                                                            Save
                                                        </button>
                                                    </div>
                                                </div>
                                            ) : null}
                                            <a
                                                href={values.facebook || "#"}
                                                target="_blank"
                                                className={`bg-white border border-sky-600 p-2 rounded group flex items-center justify-center ${!values.facebook ? 'opacity-50' : ''}`}
                                                onClick={!values.facebook ? (e) => e.preventDefault() : undefined}
                                            >
                                                <Icon
                                                    icon="ri:facebook-fill"
                                                    className="h-6 w-6 text-gray-600 group-hover:opacity-0 transition-opacity duration-300 ease-in-out"
                                                />
                                                <Icon
                                                    icon="logos:facebook"
                                                    className="h-6 w-6 text-gray-600 opacity-0 group-hover:opacity-100 absolute transition-opacity duration-300 ease-in-out"
                                                />
                                            </a>
                                            {!editingLink && (
                                                <button
                                                    onClick={() => handleEditClick('facebook')}
                                                    className="absolute -top-2 -right-2 p-1 rounded-full bg-blue-50 text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"
                                                    title="Edit Facebook Link"
                                                >
                                                    <Icon icon="heroicons-outline:pencil" className="h-3 w-3" />
                                                </button>
                                            )}
                                        </div>

                                        {/* Twitter Link */}
                                        <div className="relative group">
                                            {editingLink === 'twitter' ? (
                                                <div
                                                    className="absolute -top-16 -left-2 bg-white p-3 rounded-lg shadow-lg z-10 w-64"
                                                    onClick={(e) => e.stopPropagation()}
                                                >
                                                    <div className="relative">
                                                        <input
                                                            type="url"
                                                            value={tempLinkValue}
                                                            onChange={handleInputChange}
                                                            className={`border ${!tempLinkValue.trim() || !isValidUrl(tempLinkValue) ? 'border-red-500' : 'border-gray-300'} rounded p-2 text-sm w-full mb-2`}
                                                            placeholder="https://twitter.com/yourhandle"
                                                            required
                                                        />
                                                    </div>
                                                    <div className="flex justify-end space-x-2">
                                                        <button
                                                            type="button"
                                                            onClick={handleCancel}
                                                            className="font-semibold text-xs cursor-pointer border border-[#4669FA] rounded-md text-[#4669FA] bg-white px-3 py-1"
                                                        >
                                                            Cancel
                                                        </button>
                                                        <button
                                                            type="button"
                                                            onClick={handleSave}
                                                            className={`font-semibold text-xs rounded-md text-white py-1 px-3 ${!tempLinkValue.trim() || !isValidUrl(tempLinkValue) ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#4669FA] cursor-pointer'}`}
                                                            disabled={!tempLinkValue.trim() || !isValidUrl(tempLinkValue)}
                                                        >
                                                            Save
                                                        </button>
                                                    </div>
                                                </div>
                                            ) : null}
                                            <a
                                                href={values.twitter || "#"}
                                                target="_blank"
                                                className={`bg-white border border-sky-600 p-2 rounded group flex items-center justify-center ${!values.twitter ? 'opacity-50' : ''}`}
                                                onClick={!values.twitter ? (e) => e.preventDefault() : undefined}
                                            >
                                                <Icon
                                                    icon="mdi:twitter"
                                                    className="h-6 w-6 text-gray-600 group-hover:opacity-0 transition-opacity duration-300 ease-in-out"
                                                />
                                                <Icon
                                                    icon="logos:twitter"
                                                    className="h-6 w-6 text-gray-600 opacity-0 group-hover:opacity-100 absolute transition-opacity duration-300 ease-in-out"
                                                />
                                            </a>
                                            {!editingLink && (
                                                <button
                                                    onClick={() => handleEditClick('twitter')}
                                                    className="absolute -top-2 -right-2 p-1 rounded-full bg-blue-50 text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"
                                                    title="Edit Twitter Link"
                                                >
                                                    <Icon icon="heroicons-outline:pencil" className="h-3 w-3" />
                                                </button>
                                            )}
                                        </div>

                                        {/* LinkedIn Link */}
                                        <div className="relative group">
                                            {editingLink === 'linkedin' ? (
                                                <div
                                                    className="absolute -top-16 -left-2 bg-white p-3 rounded-lg shadow-lg z-10 w-64"
                                                    onClick={(e) => e.stopPropagation()}
                                                >
                                                    <div className="relative">
                                                        <input
                                                            type="url"
                                                            value={tempLinkValue}
                                                            onChange={handleInputChange}
                                                            className={`border ${!tempLinkValue.trim() || !isValidUrl(tempLinkValue) ? 'border-red-500' : 'border-gray-300'} rounded p-2 text-sm w-full mb-2`}
                                                            placeholder="https://www.linkedin.com/company/yourcompany"
                                                            required
                                                        />
                                                    </div>
                                                    <div className="flex justify-end space-x-2">
                                                        <button
                                                            type="button"
                                                            onClick={handleCancel}
                                                            className="font-semibold text-xs cursor-pointer border border-[#4669FA] rounded-md text-[#4669FA] bg-white px-3 py-1"
                                                        >
                                                            Cancel
                                                        </button>
                                                        <button
                                                            type="button"
                                                            onClick={handleSave}
                                                            className={`font-semibold text-xs rounded-md text-white py-1 px-3 ${!tempLinkValue.trim() || !isValidUrl(tempLinkValue) ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#4669FA] cursor-pointer'}`}
                                                            disabled={!tempLinkValue.trim() || !isValidUrl(tempLinkValue)}
                                                        >
                                                            Save
                                                        </button>
                                                    </div>
                                                </div>
                                            ) : null}
                                            <a
                                                href={values.linkedin || "#"}
                                                target="_blank"
                                                className={`bg-white border border-sky-600 p-2 rounded group flex items-center justify-center ${!values.linkedin ? 'opacity-50' : ''}`}
                                                onClick={!values.linkedin ? (e) => e.preventDefault() : undefined}
                                            >
                                                <Icon
                                                    icon="fa-brands:linkedin"
                                                    className="h-6 w-6 text-gray-600 group-hover:opacity-0 transition-opacity duration-300 ease-in-out"
                                                />
                                                <Icon
                                                    icon="devicon:linkedin"
                                                    className="h-6 w-6 text-gray-600 opacity-0 group-hover:opacity-100 absolute transition-opacity duration-300 ease-in-out"
                                                />
                                            </a>
                                            {!editingLink && (
                                                <button
                                                    onClick={() => handleEditClick('linkedin')}
                                                    className="absolute -top-2 -right-2 p-1 rounded-full bg-blue-50 text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"
                                                    title="Edit LinkedIn Link"
                                                >
                                                    <Icon icon="heroicons-outline:pencil" className="h-3 w-3" />
                                                </button>
                                            )}
                                        </div>

                                        {/* YouTube Link */}
                                        <div className="relative group">
                                            {editingLink === 'youtube' ? (
                                                <div
                                                    className="absolute -top-16 -left-2 bg-white p-3 rounded-lg shadow-lg z-10 w-64"
                                                    onClick={(e) => e.stopPropagation()}
                                                >
                                                    <div className="relative">
                                                        <input
                                                            type="url"
                                                            value={tempLinkValue}
                                                            onChange={handleInputChange}
                                                            className={`border ${!tempLinkValue.trim() || !isValidUrl(tempLinkValue) ? 'border-red-500' : 'border-gray-300'} rounded p-2 text-sm w-full mb-2`}
                                                            placeholder="https://www.youtube.com/@yourchannel"
                                                            required
                                                        />
                                                    </div>
                                                    <div className="flex justify-end space-x-2">
                                                        <button
                                                            type="button"
                                                            onClick={handleCancel}
                                                            className="font-semibold text-xs cursor-pointer border border-[#4669FA] rounded-md text-[#4669FA] bg-white px-3 py-1"
                                                        >
                                                            Cancel
                                                        </button>
                                                        <button
                                                            type="button"
                                                            onClick={handleSave}
                                                            className={`font-semibold text-xs rounded-md text-white py-1 px-3 ${!tempLinkValue.trim() || !isValidUrl(tempLinkValue) ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#4669FA] cursor-pointer'}`}
                                                            disabled={!tempLinkValue.trim() || !isValidUrl(tempLinkValue)}
                                                        >
                                                            Save
                                                        </button>
                                                    </div>
                                                </div>
                                            ) : null}
                                            <a
                                                href={values.youtube || "#"}
                                                target="_blank"
                                                className={`bg-white border border-sky-600 p-2 rounded group flex items-center justify-center ${!values.youtube ? 'opacity-50' : ''}`}
                                                onClick={!values.youtube ? (e) => e.preventDefault() : undefined}
                                            >
                                                <Icon
                                                    icon="mingcute:youtube-fill"
                                                    className="h-6 w-6 text-gray-600 group-hover:opacity-0 transition-opacity duration-300 ease-in-out"
                                                />
                                                <Icon
                                                    icon="logos:youtube-icon"
                                                    className="h-7 w-6 text-gray-600 opacity-0 group-hover:opacity-100 absolute transition-opacity duration-300 ease-in-out"
                                                />
                                            </a>
                                            {!editingLink && (
                                                <button
                                                    onClick={() => handleEditClick('youtube')}
                                                    className="absolute -top-2 -right-2 p-1 rounded-full bg-blue-50 text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"
                                                    title="Edit YouTube Link"
                                                >
                                                    <Icon icon="heroicons-outline:pencil" className="h-3 w-3" />
                                                </button>
                                            )}
                                        </div>

                                        {/* Website Link */}
                                        <div className="relative group">
                                            {editingLink === 'website' ? (
                                                <div
                                                    className="absolute -top-16 -left-2 bg-white p-3 rounded-lg shadow-lg z-10 w-64"
                                                    onClick={(e) => e.stopPropagation()}
                                                >
                                                    <div className="relative">
                                                        <input
                                                            type="url"
                                                            value={tempLinkValue}
                                                            onChange={handleInputChange}
                                                            className={`border ${!tempLinkValue.trim() || !isValidUrl(tempLinkValue) ? 'border-red-500' : 'border-gray-300'} rounded p-2 text-sm w-full mb-2`}
                                                            placeholder="https://www.yourwebsite.com"
                                                            required
                                                        />
                                                    </div>
                                                    <div className="flex justify-end space-x-2">
                                                        <button
                                                            type="button"
                                                            onClick={handleCancel}
                                                            className="font-semibold text-xs cursor-pointer border border-[#4669FA] rounded-md text-[#4669FA] bg-white px-3 py-1"
                                                        >
                                                            Cancel
                                                        </button>
                                                        <button
                                                            type="button"
                                                            onClick={handleSave}
                                                            className={`font-semibold text-xs rounded-md text-white py-1 px-3 ${!tempLinkValue.trim() || !isValidUrl(tempLinkValue) ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#4669FA] cursor-pointer'}`}
                                                            disabled={!tempLinkValue.trim() || !isValidUrl(tempLinkValue)}
                                                        >
                                                            Save
                                                        </button>
                                                    </div>
                                                </div>
                                            ) : null}
                                            <a
                                                href={values.website || "#"}
                                                target="_blank"
                                                className={`bg-white border border-sky-600 p-2 rounded group flex items-center justify-center ${!values.website ? 'opacity-50' : ''}`}
                                                onClick={!values.website ? (e) => e.preventDefault() : undefined}
                                            >
                                                <Icon
                                                    icon="mdi:web"
                                                    className="h-6 w-6 text-gray-600 group-hover:opacity-0 transition-opacity duration-300 ease-in-out"
                                                />
                                                <Icon
                                                    icon="logos:chrome"
                                                    className="h-6 w-6 text-gray-600 opacity-0 group-hover:opacity-100 absolute transition-opacity duration-300 ease-in-out"
                                                />
                                            </a>
                                            {!editingLink && (
                                                <button
                                                    onClick={() => handleEditClick('website')}
                                                    className="absolute -top-2 -right-2 p-1 rounded-full bg-blue-50 text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"
                                                    title="Edit Website Link"
                                                >
                                                    <Icon icon="heroicons-outline:pencil" className="h-3 w-3" />
                                                </button>
                                            )}
                                        </div>
                                    </div>
                                </div>
                                <div className="flex flex-col items-center md:items-start gap-3">
                                    <h5 className="font-medium mb-2 text-xl text-gray-700">
                                        Contact Us
                                    </h5>
                                    {/* Editable Address */}
                                    <div className="flex gap-2 relative group">
                                        <Icon icon="bx:map" className="h-6 w-6 mt-1 text-sky-600 flex-shrink-0" />
                                        {editingField === 'address' ? (
                                            <div className="absolute -top-16 -left-2 bg-white p-3 rounded-lg shadow-lg z-10 w-64">
                                                <div className="relative">
                                                    <textarea
                                                        value={tempAddressValue}
                                                        onChange={handleAddressChange}
                                                        className={`border ${!tempAddressValue.trim() ? 'border-red-500' : 'border-gray-300'} rounded p-2 text-sm w-full mb-2`}
                                                        placeholder="Enter your address"
                                                        rows={3}
                                                        style={{ resize: "none" }}
                                                        required
                                                    />
                                                </div>
                                                <div className="flex justify-end space-x-2">
                                                    <button
                                                        type="button"
                                                        onClick={handleAddressCancel}
                                                        className="font-semibold text-xs cursor-pointer border border-[#4669FA] rounded-md text-[#4669FA] bg-white px-3 py-1"
                                                    >
                                                        Cancel
                                                    </button>
                                                    <button
                                                        type="button"
                                                        onClick={handleAddressSave}
                                                        className={`font-semibold text-xs rounded-md text-white py-1 px-3 ${!tempAddressValue.trim() ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#4669FA] cursor-pointer'}`}
                                                        disabled={!tempAddressValue.trim()}
                                                    >
                                                        Save
                                                    </button>
                                                </div>
                                            </div>
                                        ) : (
                                            <p className="items-center">
                                                {values?.address || organization?.address || "--"}
                                            </p>
                                        )}
                                        {!editingField && (
                                            <button
                                                onClick={handleEditAddress}
                                                className="absolute -top-2 -right-2 p-1 rounded-full bg-blue-50 text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"
                                                title="Edit Address"
                                            >
                                                <Icon icon="heroicons-outline:pencil" className="h-3 w-3" />
                                            </button>
                                        )}
                                    </div>

                                    {/* Editable Contact Number */}
                                    <div className="flex items-center relative group">
                                        <Icon
                                            icon="mdi:phone"
                                            className="h-6 w-6 mr-2 text-sky-600 flex-shrink-0"
                                        />
                                        {editingField === 'contact_no' ? (
                                            <div className="absolute -top-16 -left-2 bg-white p-3 rounded-lg shadow-lg z-10 w-64">
                                                <div className="relative">
                                                    <input
                                                        type="tel"
                                                        value={tempContactValue}
                                                        onChange={handleContactChange}
                                                        className={`border ${!tempContactValue.trim() || !isValidPhoneNumber(tempContactValue) ? 'border-red-500' : 'border-gray-300'} rounded p-2 text-sm w-full mb-2`}
                                                        placeholder="Enter your contact number"
                                                    />
                                                </div>
                                                <div className="flex justify-end space-x-2">
                                                    <button
                                                        type="button"
                                                        onClick={handleContactCancel}
                                                        className="font-semibold text-xs cursor-pointer border border-[#4669FA] rounded-md text-[#4669FA] bg-white px-3 py-1"
                                                    >
                                                        Cancel
                                                    </button>
                                                    <button
                                                        type="button"
                                                        onClick={handleContactSave}
                                                        className={`font-semibold text-xs rounded-md text-white py-1 px-3 bg-[#4669FA] cursor-pointer`}
                                                        // disabled={!tempContactValue.trim() || !isValidPhoneNumber(tempContactValue)}
                                                    >
                                                        Save
                                                    </button>
                                                </div>
                                            </div>
                                        ) : (
                                            <span>
                                                {organization?.hotline_number ? organization?.hotline_number + " | " : ""}
                                                {values?.contact_no || organization?.contact_no || "--"}
                                            </span>
                                        )}
                                        {!editingField && (
                                            <button
                                                onClick={handleEditContact}
                                                className="absolute -top-2 -right-2 p-1 rounded-full bg-blue-50 text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"
                                                title="Edit Contact Number"
                                            >
                                                <Icon icon="heroicons-outline:pencil" className="h-3 w-3" />
                                            </button>
                                        )}
                                    </div>

                                    {/* Editable Email */}
                                    <div className="flex items-center relative group">
                                        <Icon
                                            icon="fluent:mail-16-regular"
                                            className="h-6 w-6 mr-2 text-sky-600 flex-shrink-0"
                                        />
                                        {editingField === 'email' ? (
                                            <div className="absolute -top-16 -left-2 bg-white p-3 rounded-lg shadow-lg z-10 w-64">
                                                <div className="relative">
                                                    <input
                                                        type="email"
                                                        value={tempEmailValue}
                                                        onChange={handleEmailChange}
                                                        className={`border ${!tempEmailValue.trim() || !isValidEmail(tempEmailValue) ? 'border-red-500' : 'border-gray-300'} rounded p-2 text-sm w-full mb-2`}
                                                        placeholder="Enter your email"
                                                        required
                                                    />
                                                </div>
                                                <div className="flex justify-end space-x-2">
                                                    <button
                                                        type="button"
                                                        onClick={handleEmailCancel}
                                                        className="font-semibold text-xs cursor-pointer border border-[#4669FA] rounded-md text-[#4669FA] bg-white px-3 py-1"
                                                    >
                                                        Cancel
                                                    </button>
                                                    <button
                                                        type="button"
                                                        onClick={handleEmailSave}
                                                        className={`font-semibold text-xs rounded-md text-white py-1 px-3 ${!tempEmailValue.trim() || !isValidEmail(tempEmailValue) ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#4669FA] cursor-pointer'}`}
                                                        disabled={!tempEmailValue.trim() || !isValidEmail(tempEmailValue)}
                                                    >
                                                        Save
                                                    </button>
                                                </div>
                                            </div>
                                        ) : (
                                            <span>{values?.email || organization?.email || "--"}</span>
                                        )}
                                        {!editingField && (
                                            <button
                                                onClick={handleEditEmail}
                                                className="absolute -top-2 -right-2 p-1 rounded-full bg-blue-50 text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"
                                                title="Edit Email"
                                            >
                                                <Icon icon="heroicons-outline:pencil" className="h-3 w-3" />
                                            </button>
                                        )}
                                    </div>
                                </div>
                                <div className="col-span-1 flex flex-col items-center md:items-start gap-3">
                                    <h5 className="font-medium mb-2 text-xl text-gray-700">
                                        More Pages
                                    </h5>
                                    {menuData && menuData?.length > 0 ? (
                                        menuData.map((menuItem) => (
                                            <Link
                                                key={menuItem.id}
                                                to={menuItem?.slug}
                                                className="mb-1"
                                                onClick={(e) => e.preventDefault()}
                                            >
                                                {menuItem.name}
                                            </Link>
                                        ))
                                    ) : (
                                        <>
                                            <Link to="/under-construction" className="mb-1">
                                                Privacy Policy
                                            </Link>
                                            <Link to="/under-construction" className="mb-1">
                                                Terms & Conditions
                                            </Link>
                                            <Link to="/under-construction" className="mb-1">
                                                Contact Us
                                            </Link>
                                            <Link to="/under-construction">Support Center</Link>
                                        </>
                                    )}
                                </div>
                                <div className="col-span-1 flex flex-col items-center md:items-start space-y-2">
                                    <h5 className="font-medium mb-2 text-xl text-gray-700">
                                        Install App
                                    </h5>
                                    <div className="flex space-x-2">
                                        <Link to="#">
                                            <img src={appstore} alt="App Store" className="w-32" />
                                        </Link>
                                        <Link to="#">
                                            <img
                                                src={googlePlay}
                                                alt="Google Play"
                                                className="w-32"
                                            />
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </footer>
                </div>
            </div>
            <div
                style={{
                    background:
                        "linear-gradient(90deg, #0C5483 0%, #40A5E7 52.63%, #003456 100%)",
                }}
                className="flex justify-center items-center bg-sky-600"
            >
                <p className="text-white py-3">All right reserved @ BacBon LTD. 2024</p>
            </div>
        </div>
    );
};

export default Footer;
