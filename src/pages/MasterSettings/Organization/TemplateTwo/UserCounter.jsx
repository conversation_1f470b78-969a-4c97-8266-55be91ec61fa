import React, { useState, useRef } from "react";
import { useSelector } from "react-redux";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import heroUserIcon from "@/assets/images/all-img/heroUserIcon.png";

const UserCounter = ({ organization }) => {
  const { user } = useSelector((state) => state.auth);
  const [updateApi] = useUpdateApiMutation();

  // User count editing states
  const [isEditingUserCount, setIsEditingUserCount] = useState(false);
  const [userCountValue, setUserCountValue] = useState(
    organization?.custom_user_number ? String(organization?.custom_user_number) : "--"
  );
  const [isSavingUserCount, setIsSavingUserCount] = useState(false);
  const [userCountError, setUserCountError] = useState("");
  
  const userCountInputRef = useRef(null);

  return (
    <div className="flex items-center justify-center flex-col gap-2 relative group">
          <img className="max-sm:w-10 mx-auto" src={heroUserIcon} alt="" />
          <div className="relative">
            {isEditingUserCount ? (
              <div className="flex items-center">
                <input
                  ref={userCountInputRef}
                  type="text"
                  value={userCountValue}
                  onChange={(e) => {
                    // Remove non-numeric characters
                    const value = e.target.value.replace(/[^0-9]/g, '');
                    setUserCountValue(value);
                    if (userCountError) setUserCountError("");
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      // Validate input
                      if (userCountValue === '' || userCountValue === '0') {
                        setUserCountError("Please enter a valid number greater than 0");
                        return;
                      }

                      // Check if the value is a valid number
                      if (isNaN(Number(userCountValue))) {
                        setUserCountError("Please enter a valid number");
                        return;
                      }

                      const submitUserCount = async () => {
                        setIsSavingUserCount(true);
                        try {
                          const formData = new FormData();
                          formData.append("custom_user_number", userCountValue);

                          await updateApi({
                            end_point: `admin/organizations/${user?.organization_id}`,
                            body: formData
                          }).unwrap();

                          setIsEditingUserCount(false);
                          setUserCountError("");
                        } catch (err) {
                          console.error("Failed to update user count:", err);
                          setUserCountError("Failed to update. Please try again.");
                        } finally {
                          setIsSavingUserCount(false);
                        }
                      };

                      submitUserCount();
                    } else if (e.key === 'Escape') {
                      setIsEditingUserCount(false);
                      setUserCountValue(organization?.custom_user_number ? String(organization?.custom_user_number) : "13000");
                      setUserCountError("");
                    }
                  }}
                  className="w-24 text-center text-2xl max-sm:text-xl font-semibold text-blue-600 bg-transparent outline-none border-b border-gray-400 py-1 transition-all duration-200 ease-in-out focus:border-blue-500"
                  autoFocus
                />
                <span className="text-2xl max-sm:text-xl font-semibold text-blue-600">+</span>
                <span className="text-blue-600 ml-1">Users</span>

                {/* Check icon */}
                <button
                  onClick={async () => {
                    // Validate input
                    if (userCountValue === '' || userCountValue === '0') {
                      setUserCountError("Please enter a valid number greater than 0");
                      return;
                    }

                    // Check if the value is a valid number
                    if (isNaN(Number(userCountValue))) {
                      setUserCountError("Please enter a valid number");
                      return;
                    }

                    setIsSavingUserCount(true);
                    try {
                      const formData = new FormData();
                      formData.append("custom_user_number", userCountValue);

                      await updateApi({
                        end_point: `admin/organizations/${user?.organization_id}`,
                        body: formData
                      }).unwrap();

                      setIsEditingUserCount(false);
                      setUserCountError("");
                    } catch (err) {
                      console.error("Failed to update user count:", err);
                      setUserCountError("Failed to update. Please try again.");
                    } finally {
                      setIsSavingUserCount(false);
                    }
                  }}
                  className="ml-2 text-green-500 hover:text-green-700"
                  disabled={userCountValue === '' || userCountValue === '0' || isSavingUserCount}
                  aria-label="Submit User Count"
                >
                  <Icon icon="heroicons-outline:check" className="w-5 h-5" />
                </button>
              </div>
            ) : (
              <div className="flex items-center">
                <p
                  className="text-blue-600 cursor-pointer"
                  onClick={() => {
                    setIsEditingUserCount(true);
                    setUserCountValue(organization?.custom_user_number ? String(organization?.custom_user_number) : "13000");
                  }}
                >
                  <span className="text-2xl max-sm:text-xl font-semibold">
                    {organization?.custom_user_number ? organization.custom_user_number : "13000"}+
                  </span>{" "}
                  Users
                </p>

                {/* Pencil icon */}
                <button
                  onClick={() => {
                    setIsEditingUserCount(true);
                    setUserCountValue(organization?.custom_user_number ? String(organization?.custom_user_number) : "13000");
                  }}
                  className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 bg-blue-50 hover:bg-blue-100 rounded-full"
                  aria-label="Edit User Count"
                >
                  <Icon icon="heroicons-outline:pencil" className="text-blue-600 w-4 h-4" />
                </button>
              </div>
            )}

            {userCountError && (
              <div className="text-red-500 text-xs mt-1">{userCountError}</div>
            )}
          </div>
    </div>
  );
};

export default UserCounter;
