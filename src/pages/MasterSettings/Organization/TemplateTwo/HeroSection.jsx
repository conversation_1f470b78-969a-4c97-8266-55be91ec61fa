import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { Icon } from "@iconify/react/dist/iconify.js";
import HeadlineEditor from "./HeadlineEditor";
import BannerEditor from "./BannerEditor";
import CourseCounter from "./CourseCounter";
import UserCounter from "./UserCounter";
import BookCounter from "./BookCounter";
import InstructorCounter from "./InstructorCounter";
import heroBgImg from "@/assets/images/all-img/heroBg1.png";
import heroMoveIcon from "@/assets/images/all-img/heroMoveIcon.png";
import StudentCounter from "./StudentCounter";

const HeroSection = ({ organization }) => {
  const [count, setCount] = useState(0);

  // Fallback for invalid or undefined studentsCount
  const targetCount = Number(organization.students_count) || 0;
  const steps = 10;
  const incrementValue = Math.ceil(targetCount / steps);
  const [transformStyle, setTransformStyle] = useState("");

  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const translateY = scrollY * 0.5;


      setTransformStyle(`translateY(${translateY}px)`);
    };

    window.addEventListener("scroll", handleScroll);


    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  useEffect(() => {
    if (targetCount === 0) return;

    const interval = setInterval(() => {
      setCount((prev) => {
        if (prev + incrementValue >= targetCount) {
          clearInterval(interval);
          return targetCount;
        }
        return prev + incrementValue;
      });
    }, 100);

    return () => clearInterval(interval);
  }, [incrementValue, targetCount]);

  return (
    <div>
      <div className="py-10  relative overflow-hidden">
        {/* Background Images */}
        <img
          src={heroBgImg}
          className="max-sm:opacity-25 absolute left-0 top-0 z-0 h-full object-cover"
          alt=""
        />
        <img
          src={heroMoveIcon}
          style={{ transform: transformStyle }}
          className="max-sm:opacity-50 absolute right-0 bottom-0 z-0 transition-transform duration-75"
          alt="Hero Icon"
        />

        {/* Content Container */}
        <div className="flex items-center">
          <div className="container mx-auto flex flex-col lg:flex-row items-center gap-5 z-10 py-10">
            {/* Left Column */}
            <div className="flex-1 space-y-5 lg:text-left">
              <HeadlineEditor organization={organization} />
              <div className="flex items-center gap-5 max-sm:gap-2">
                <Link
                  to="/courses"
                  className="sm:w-52 px-3 max-sm:py-2 py-3 text-center hover:text-sky-600 rounded-full border border-sky-600 flex items-center justify-center hover:bg-gray-100 hover:gap-2 gap-5 bg-sky-700 text-white duration-300"
                >
                  Explore Courses
                  <Icon icon="line-md:arrow-right" className="text-lg" />
                </Link>
              </div>
            </div>

            <BannerEditor organization={organization} />
          </div>
        </div>
      </div>

      <div className="relative z-20 flex items-center justify-center my-6 mx-5">
        <div className="flex items-center justify-around container bg-white rounded-xl absolute p-8 border border-blue-100 text-lg max-sm:text-base max-sm:text-center shadow-lg">
          <CourseCounter organization={organization} count={count} />
          {/* <UserCounter organization={organization} /> */}
          <StudentCounter organization={organization} />
          <BookCounter organization={organization} />
          <InstructorCounter organization={organization} />
        </div>
      </div>
      <div className="h-16"></div>
    </div>
  );
};

export default HeroSection;
