import React, { useState, useRef } from "react";
import { useSelector } from "react-redux";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import heroCourseIcon from "@/assets/images/all-img/heroCourseIcon.png";

const CourseCounter = ({ organization, count }) => {
  const { user } = useSelector((state) => state.auth);
  const [updateApi] = useUpdateApiMutation();

  // Course count editing states
  const [isEditingCourseCount, setIsEditingCourseCount] = useState(false);
  const [courseCountValue, setCourseCountValue] = useState(
    organization?.custom_course_number ? String(organization?.custom_course_number) : String(count)
  );
  const [isSavingCourseCount, setIsSavingCourseCount] = useState(false);
  const [courseCountError, setCourseCountError] = useState("");
  
  const courseCountInputRef = useRef(null);

  return (
    <div className="flex items-center justify-center flex-col gap-2 relative group">
          <img className="max-sm:w-10 mx-auto" src={heroCourseIcon} alt="" />
          <div className="relative">
            {isEditingCourseCount ? (
              <div className="flex items-center">
                <input
                  ref={courseCountInputRef}
                  type="text"
                  min="1"
                  value={courseCountValue}
                  onChange={(e) => {
                    const value = e.target.value.replace(/^0+|[^0-9]/g, '');
                    setCourseCountValue(value);
                    if (courseCountError) setCourseCountError("");
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      if (courseCountValue === '' || courseCountValue === '0') {
                        setCourseCountError("Please enter a valid number greater than 0");
                        return;
                      }

                      // Check if the value is a valid number
                      if (isNaN(Number(courseCountValue))) {
                        setCourseCountError("Please enter a valid number");
                        return;
                      }

                      const submitCourseCount = async () => {
                        setIsSavingCourseCount(true);
                        try {
                          const formData = new FormData();
                          formData.append("custom_course_number", courseCountValue);

                          await updateApi({
                            end_point: `admin/organizations/${user?.organization_id}`,
                            body: formData
                          }).unwrap();

                          setIsEditingCourseCount(false);
                          setCourseCountError("");
                        } catch (err) {
                          console.error("Failed to update course count:", err);
                          setCourseCountError("Failed to update. Please try again.");
                        } finally {
                          setIsSavingCourseCount(false);
                        }
                      };

                      submitCourseCount();
                    } else if (e.key === 'Escape') {
                      setIsEditingCourseCount(false);
                      setCourseCountValue(organization?.custom_course_number ? String(organization?.custom_course_number) : String(count));
                      setCourseCountError("");
                    }
                  }}
                  className="w-16 text-center text-2xl max-sm:text-xl font-semibold text-blue-600 bg-transparent outline-none border-b border-gray-400 py-1 transition-all duration-200 ease-in-out focus:border-blue-500"
                  autoFocus
                />
                <span className="text-2xl max-sm:text-xl font-semibold text-blue-600">+</span>
                <span className="text-blue-600 ml-1">Courses</span>

                {/* Check icon */}
                <button
                  onClick={async () => {
                    // Validate input
                    if (courseCountValue === '' || courseCountValue === '0') {
                      setCourseCountError("Please enter a valid number greater than 0");
                      return;
                    }

                    // Check if the value is a valid number
                    if (isNaN(Number(courseCountValue))) {
                      setCourseCountError("Please enter a valid number");
                      return;
                    }

                    setIsSavingCourseCount(true);
                    try {
                      const formData = new FormData();
                      formData.append("custom_course_number", courseCountValue);

                      await updateApi({
                        end_point: `admin/organizations/${user?.organization_id}`,
                        body: formData
                      }).unwrap();

                      setIsEditingCourseCount(false);
                      setCourseCountError("");
                    } catch (err) {
                      console.error("Failed to update course count:", err);
                      setCourseCountError("Failed to update. Please try again.");
                    } finally {
                      setIsSavingCourseCount(false);
                    }
                  }}
                  className="ml-2 text-green-500 hover:text-green-700"
                  disabled={courseCountValue === '' || courseCountValue === '0' || isSavingCourseCount}
                  aria-label="Submit Course Count"
                >
                  <Icon icon="heroicons-outline:check" className="w-5 h-5" />
                </button>
              </div>
            ) : (
              <div className="flex items-center">
                <p
                  className="text-blue-600 cursor-pointer"
                  onClick={() => {
                    setIsEditingCourseCount(true);
                    setCourseCountValue(organization?.custom_course_number ? String(organization?.custom_course_number) : String(count));
                  }}
                >
                  <span className="text-2xl max-sm:text-xl font-semibold">
                    {organization?.custom_course_number ? organization.custom_course_number : count}+
                  </span>{" "}
                  Courses
                </p>

                {/* Pencil icon */}
                <button
                  onClick={() => {
                    setIsEditingCourseCount(true);
                    setCourseCountValue(organization?.custom_course_number ? String(organization?.custom_course_number) : String(count));
                  }}
                  className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 bg-blue-50 hover:bg-blue-100 rounded-full"
                  aria-label="Edit Course Count"
                >
                  <Icon icon="heroicons-outline:pencil" className="text-blue-600 w-4 h-4" />
                </button>
              </div>
            )}

            {courseCountError && (
              <div className="text-red-500 text-xs mt-1">{courseCountError}</div>
            )}
          </div>
    </div>
  );
};

export default CourseCounter;
