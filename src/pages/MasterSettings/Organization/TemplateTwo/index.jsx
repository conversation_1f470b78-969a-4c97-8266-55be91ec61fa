import React, { useState, useEffect } from "react";
import { Formik, Form } from "formik";
import { useDispatch, useSelector } from "react-redux";
import {
  useGetApiQuery,
  useUpdateApiMutation,
} from "@/store/api/master/commonSlice";
import { validationSchema } from "./formSettings";
import EditHeader from "./EditHeader";
import OrganizationInfo from "./OrganizationInfo";
import BannerSection from "./BannerSection";
import ContactInfo from "./ContactInfo";
import HeroSection from "./HeroSection";
import HeroBg from "@/assets/images/all-img/websiteSettings.png";
import Footer from "./Footer";
import { toast } from "react-toastify";

const TemplateTwo = ({ organizationData, user }) => {
  const [updateApi, { isLoading }] = useUpdateApiMutation();
  const [showTemplateList, setShowTemplateList] = useState(false);
  const [originalData, setOriginalData] = useState(null);

  useEffect(() => {
    if (organizationData) {
      console.log("Organization data received:", organizationData);
      setOriginalData(organizationData);
    }
  }, [organizationData]);

  const organization = { ...organizationData };

  const initialValues = {
    name: organization?.name || "",
    details: organization?.details || "",
    address: organization?.address || "",
    email: organization?.email || "",
    contact_no: organization?.contact_no || "",
    contact_person: organization?.contact_person || "",
    is_active: organization?.is_active || false,
    contact_number: organization?.contact_number || "",
    hotline_number: organization?.hotline_number || "",
    custom_student_number: organization?.custom_student_number || "",
    facebook: organization?.facebook || "",
    twitter: organization?.twitter || "",
    linkedin: organization?.linkedin || "",
    youtube: organization?.youtube || "",
    website: organization?.website || "",
    footer_logo: organization?.footer_logo || null,
  };

  const onSubmit = async (values, { resetForm, setErrors }) => {
    console.log("Submitting values:", values);
    try {
      const formData = new FormData();

      // Helper function to check if a field has changed
      const hasChanged = (key) => {
        // For file objects, check if it's a new file
        if (key === 'footer_logo') {
          return values[key] && typeof values[key] !== 'string';
        }

        // For boolean values, convert to 1/0 for comparison
        if (typeof values[key] === 'boolean') {
          const valueAsNumber = values[key] ? 1 : 0;
          const originalAsNumber = originalData[key] ? 1 : 0;
          return valueAsNumber !== originalAsNumber;
        }

        // Default string comparison
        return values[key] !== originalData[key];
      };

      // Get all fields from values
      const allFields = Object.keys(values);

      // Only append fields that have changed
      allFields.forEach(key => {
        if (hasChanged(key)) {
          // Special handling for file uploads
          if (key === 'footer_logo' && typeof values[key] !== 'string') {
            formData.append(key, values[key]);
          }
          // Special handling for boolean values
          else if (typeof values[key] === 'boolean') {
            formData.append(key, values[key] ? '1' : '0');
          }
          // Handle all other fields
          else if (values[key] !== undefined && values[key] !== null) {
            formData.append(key, values[key]);
          }
        }
      });

      // Log what's being sent (for debugging)
      console.log("Sending only changed fields:");
      for (let [key, value] of formData.entries()) {
        console.log(`${key}: ${value instanceof File ? 'File object' : value}`);
      }

      // If no fields have changed, show a message and return
      if ([...formData.entries()].length === 0) {
        toast.info("No changes detected. Nothing to update.");
        return;
      }

      const endpoint = `admin/organizations/${user?.organization_id}`;

      await updateApi({
        end_point: endpoint,
        body: formData,
      }).unwrap();



    } catch (error) {
      console.error("Update error:", error);
      if (error?.data?.errors) {
        setErrors(error.data.errors);
        const firstErrorKey = Object.keys(error.data.errors)[0];
        const firstErrorMessage = error.data.errors[firstErrorKey][0];
        toast.error(firstErrorMessage || "Validation error");
      } else {
        toast.error(error?.data?.message || "An unexpected error occurred. Please try again.");
      }
    }
  };

  return (
    <div className="w-full">
      <div
        className="relative min-h-[600px] overflow-hidden bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url(${HeroBg})`,
          backgroundBlendMode: "overlay",
        }}
      >
        <Formik
          initialValues={initialValues}
          onSubmit={onSubmit}
          validationSchema={validationSchema}
          enableReinitialize={true}
        >
          {({ values, setFieldValue, errors, touched, handleSubmit, setFieldTouched }) => (
            <Form className="">
              <EditHeader
                organization={organization}
                setFieldValue={setFieldValue}
                values={values}
                errors={errors}
                touched={touched}
                handleSubmit={handleSubmit}
                isLoading={isLoading}
              />

              <div className="px-4">
                <HeroSection organization={organization} />
              </div>

              <div className="px-4">
                <div className="pt-4">
                  <Footer
                    organization={organization}
                    setFieldValue={setFieldValue}
                    values={values}
                    errors={errors}
                    touched={touched}
                    handleSubmit={handleSubmit}
                    setFieldTouched={setFieldTouched}
                    isLoading={isLoading}
                  />
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default TemplateTwo;