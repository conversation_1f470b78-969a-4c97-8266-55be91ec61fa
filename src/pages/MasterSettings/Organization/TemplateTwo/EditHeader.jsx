import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import { Phone, Pencil, Check, Search, Bell, User } from "lucide-react";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
const EditHeader = ({
  organization,
  setFieldValue,
  values,
  errors,
  touched,
  handleSubmit,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const [updateApi, { isLoading }] = useUpdateApiMutation();
  const handleBlur = (e) => {
    if (isSubmitting) return;
    setIsEditing(false);
  };

  const handleFocus = () => {
    setIsEditing(true);
  };

  const handleLogoChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      setFieldValue("logo", file);

      let formData = new FormData();
      formData.append("logo", file);
      const response = await updateApi({
        end_point: `admin/organizations/${organization?.id}`,
        body: formData,
      });
      
    }

  };

  const handlePhoneSubmit = () => {
    handleSubmit();
    setIsEditing(false);
    setIsSubmitting(false);
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handlePhoneSubmit();
    }
  };

  useEffect(() => {
    let objectUrl;
    if (values.logo) {
      objectUrl = URL.createObjectURL(values.logo);
    }
    return () => {
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    };
  }, [values.logo]);

  return (
    <header className="bg-[#13497C] text-white shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16 items-center">
          {/* Left section - Logo and Navigation */}
          <div className="flex items-center space-x-8">
            {/* Logo */}
            <div className="flex-shrink-0">
              <div className="relative">
                <input
                  type="file"
                  accept="image/*"
                  className="hidden"
                  id="lms-logo"
                  onChange={handleLogoChange}
                />
                <label htmlFor="lms-logo" className="cursor-pointer">
                  <div
                    className={`relative group ${
                      errors.logo && touched.logo ? "ring-2 ring-red-500 rounded" : ""
                    }`}
                  >
                    <img
                      src={
                        values?.logo
                          ? URL.createObjectURL(values.logo)
                          : `${import.meta.env.VITE_ASSET_HOST_URL}${
                              organization?.logo
                            }`
                      }
                      alt="Organization Logo"
                      className="h-10 w-auto object-contain"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded flex items-center justify-center">
                      <Icon
                        icon="mdi:camera"
                        className="w-5 h-5 text-white opacity-0 group-hover:opacity-100"
                      />
                    </div>
                  </div>
                </label>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-8">
              <a
                href="#"
                className="text-white hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Home
              </a>
              <a
                href="#"
                className=" text-white hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Courses
              </a>
              <a
                href="#"
                className=" text-white hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Categories
              </a>
              <a
                href="#"
                className=" text-white hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Resources
              </a>
            </nav>
          </div>



          {/* Right section - Actions and user */}
          <div className="flex items-center space-x-4">
            {/* Mobile search button */}
            <button
              type="button"
              className="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
            >
              <Search className="h-5 w-5" />
            </button>

            {/* Hotline Number */}
            <div className="hidden md:flex items-center space-x-2 relative group">
              <div className="relative flex-1">
                <input
                  type="text"
                  id="phone-input"
                  value={
                    isEditing
                      ? values.hotline_number
                      : organization?.hotline_number
                  }
                  onChange={(e) =>
                    setFieldValue("hotline_number", e.target.value)
                  }
                  onFocus={handleFocus}
                  onBlur={handleBlur}
                  onKeyDown={handleKeyDown}
                  placeholder="Hotline Number"
                  className={`
                    w-full bg-transparent outline-none text-sm pl-2 pr-8
                    transition-all duration-200 ease-in-out
                    ${isEditing ? "border-b border-gray-400 py-1" : ""}
                    focus:border-blue-500
                  `}
                />
                {errors.hotline_number && touched.hotline_number && (
                  <div className="absolute top-full left-0 text-xs text-red-500 mt-1">
                    {errors.hotline_number}
                  </div>
                )}
                {/* Edit/Submit buttons */}
                <div className="absolute right-0 top-1/2 -translate-y-1/2 flex space-x-1">
                  {!isEditing && (
                    <button
                      onClick={handleFocus}
                      className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                      aria-label="Edit Hotline Number"
                    >
                      <Pencil className="w-4 h-4 text-gray-500 hover:text-gray-700" />
                    </button>
                  )}
                  {isEditing && (
                    <button
                      onMouseDown={() => setIsSubmitting(true)}
                      onClick={handlePhoneSubmit}
                      className="text-green-500 hover:text-green-700"
                      aria-label="Submit Hotline Number"
                    >
                      <Check className="w-5 h-5" />
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* Notifications */}
            <button
              type="button"
              className="p-2 rounded-full text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 relative"
            >
              <Bell className="h-5 w-5" />
              <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></span>
            </button>

            {/* User dropdown */}
            <div className="ml-4 relative flex-shrink-0">
              <div>
                <button
                  type="button"
                  className="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  id="user-menu"
                  aria-expanded="false"
                  aria-haspopup="true"
                >
                  <span className="sr-only">Open user menu</span>
                  <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                    <User className="h-5 w-5" />
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

    </header>
  );
};

export default EditHeader;