import React from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetOrganizationListQuery } from "@/store/api/master/masterSettingOrganizationListSlice";

const index = () => {
  const res = useGetOrganizationListQuery();
  console.log(res.data);
  const data = res || [];
 
  const columns = [
    {
      label: "Logo",
      field: "logo",
    },
    {
      label: "Name",
      field: "name",
    },
    {
      label: "Slug",
      field: "slug",
    },
    {
      label: "Email",
      field: "email",
    },
    {
      label: "Contact No & Contact Person",
      field: "contact_no",
    },
    {
      label: "Status",
      field: "status",
    },
    {
      label: "Action",
      field: "",
    },
  ];
  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      logo: <img src={item.logo} alt="" className="h-10 w-10 object-cover"/>,
      name: item.name,
      slug:item.slug,
      email:item.email,
      contact_no:(
        <div>
          {item.contact_no} <br /> {item.contact_person}
        </div>
      ),
      status: (
        <Badge
          className={
            item.is_active
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {" "}
          {item.is_active ? "Active" : "Inactive"}{" "}
        </Badge>
      ),
    };
  });

  const actions = [
    {
      name: "view",
      icon: "heroicons-outline:eye",
      onClick: (val) => {
        console.log(val);
      },
    },
    {
      name: "edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        console.log(val);
      },
    },
    {
      name: "delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        console.log(val);
      },
    },
  ];
  const changePage = (item) => {
    console.log(item);
  };

  return (
    <div>
        <BasicTablePage
          title="Organization List"
          createButton="Add New Organization"
          actions={actions}
          columns={columns}
          data={tableData}
          changePage={changePage}
          currentPage={data?.current_page}
          totalPages={Math.ceil(data?.total / data?.per_page)}
        />
    </div>
  );
};

export default index;
