  import React, { useState } from "react";
  import TemplateOne from "./TemplateOne";
  import TemplateTwo from "./TemplateTwo";
  import { useGetApiQuery } from "@/store/api/master/commonSlice";
  import { useSelector } from "react-redux";
  import Button from "@/components/ui/Button";
  import ChooseTemplate from "./ChooseTemplate";

  const UpdateOrganization = () => {

    const [showTemplateList, setShowTemplateList] = useState(false);
    const { user } = useSelector((state) => state.auth);

    const {
        data: organizationData,
        isLoading: isOrgLoading,
        isError: isOrgError,
    } = useGetApiQuery(`admin/organizations/${user?.organization_id}`);
    console.log(organizationData);

    // Handle loading and error states
    if (isOrgLoading) {
        return (
          <div className="flex justify-center items-center h-screen">
            Loading...
          </div>
        );
      }

      if (isOrgError || !organizationData) {
        return (
          <div className="flex justify-center items-center h-screen">
            Error loading organization data.
          </div>
        );
      }
  return (
    <div>
    <div className="flex justify-between items-center p-4">
      <div className="text-3xl font-semibold">
        {organizationData?.name}
      </div>
      <Button className="btn btn-primary" onClick={() => setShowTemplateList(true)}>Change Template</Button>
    </div>

    {showTemplateList ? <ChooseTemplate setShowTemplateList={setShowTemplateList} organization={organizationData} /> :
    <div>
      {organizationData?.template_id == 1 ? (
        <TemplateOne organizationData={organizationData} user={user} />
      ) : organizationData?.template_id == 2 ? (
        <TemplateTwo organizationData={organizationData} user={user} />
      ) : (
        <div className="flex justify-center items-center h-screen">
          Template {organizationData?.template_id} is not supported.
        </div>
      )}
    </div> }

    </div>
  );
  };

  export default UpdateOrganization;