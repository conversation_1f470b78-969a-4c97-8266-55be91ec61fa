import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Fileinput from "@/components/ui/Fileinput";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import Textarea from "@/components/ui/Textarea";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { Formik, Form, Field } from 'formik';
import { initialValues, validationSchema} from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import { useOrganizationCreateOrUpdateMutation } from "@/store/api/master/masterSettingOrganizationListSlice";
const CreateOrganization = () => {
    const [organizationCreateOrUpdate, { isLoading, isError, error, isSuccess }] = useOrganizationCreateOrUpdateMutation();
    const dispatch = useDispatch()
    const { showModal } = useSelector((state) => state.commonReducer);
    const [checkedAuthentication, setIsAuthentication] = useState(false);
    const [checkedContnent, setIsContnent] = useState(false);

    const onSubmit = async (values, { resetForm }) => {
        console.log(values);
        
        let formData = new FormData();
        formData.append("name", values.name);
        formData.append("slug", values.slug);
        formData.append("details", values.details || '');
        formData.append("address", values.address || '');
        formData.append("email", values.email || '');
        formData.append("contact_no", values.contact_no || '');
        formData.append("contact_person", values.contact_person || '');
        formData.append("contact_number", values.contact_number || '');
        formData.append("hotline_number", values.hotline_number || '');
        formData.append("menu_position", values.menu_position || '');
        formData.append("host_url", values.host_url);
        formData.append("color_theme", values.color_theme);
        formData.append("logo", values.logo || '');
        formData.append("banner", values.banner || '');
        formData.append("is_active", isActive ? 1 : 0);

        const response = await organizationCreateOrUpdate(formData);
        console.log(response);
        dispatch(setShowModal(false));
    }
    return (
    <Modal
    activeModal={showModal}
    onClose={() => dispatch(setShowModal(false))}
    title="Create Organization"
    className="max-w-5xl"
    footer={
        <Button
            text="Close"
            btnClass="btn-primary"
            onClick={() => dispatch(setShowModal(false))}
        />
        }
    >        
    <Formik 
    validationSchema={validationSchema}
    initialValues={initialValues}
    onSubmit={onSubmit}>
    {({ values,
        errors,
        touched,
        handleChange,
        handleBlur,
        handleSubmit,
        setFieldValue,
     }) => (
      <Form>
        <div>
          <div className="grid md:grid-cols-2 gap-4">
              <InputField
              label="Organization Name"
              name="name"
              type="text"
              placeholder="Organization Name"
              required
              />
              <InputField
              label="Email"
              name="email"
              type="text"
              placeholder="Email"
              required
              />
              <div>
                  <label className="block text-[#1D1D1F] text-base font-medium mb-2">Logo
                  <span className="text-red-500 ml-1">*</span>
                  </label>
                  <Fileinput
                  name="logo"
                  accept="image/*"
                  type="file"
                  placeholder="Logo"
                  preview={true}
                  selectedFile={values.logo}
                  onChange={(e) => {
                  setFieldValue("logo", e.currentTarget.files[0]);
                  }}
                  />
              </div>
            
              <div>
                  <label className="block text-[#1D1D1F] text-base font-medium mb-2">Banner</label>
                  <Fileinput
                  name="banner"
                  accept="image/*"
                  type="file"
                  placeholder="Banner"
                  preview={true}
                  selectedFile={values.banner}
                  onChange={(e) => {
                    setFieldValue("banner", e.currentTarget.files[0]);
                  }}
                  />
              </div>

              <InputField
                label="Host URL"
                name="host_url"
                type="text"
                placeholder="Enter Host URL"
                required
              />
              <InputField
              label="Contact Person"
              name="contact_person"
              type="text"
              placeholder="Contact Person"
              />
              <NumberInput
              label="Contact No"
              name="contact_no"
              placeholder="Contact No"
              onChange={(e) => setFieldValue('contact_no', e.target.value)}
              />
            
              <NumberInput
              label="Hotline No"
              name="hotline_number"
              placeholder="Hotline No"
              onChange={(e) => setFieldValue('hotline_number', e.target.value)}
              />
            
              <InputField
              label="Organization Slug"
              name="slug"
              type="text"
              placeholder="Organization Slug"
              />
              
              <InputField
              label="Address"
              name="address"
              type="text"
              placeholder="Address"
              />

          </div>
          <div className="grid md:grid-cols-1 ">
              <div>
                <label className="block text-[#1D1D1F] text-base font-medium py-3">
                  Details
                </label>
                <Textarea
                  placeholder="Details"
                  name="details"
                  onChange={(e) => {
                    setFieldValue("details", e.target.value);
                  }}
                />
              </div>
          </div>

          <div className="mt-4">
              <div className="grid grid-4-md gap-4">
              <Switch
                label="Active"
                activeClass="bg-success-500"
                value={values.is_active}
                name="is_active"
                onChange={(e) => {
                  setFieldValue('is_active', e.target.value)
                }}
              />
          
              </div>
          </div>
          <div className="mb-4">
          </div>
      </div>
          <div className="ltr:text-right rtl:text-left mt-5">
              <Button isLoading={isLoading}
                  type="submit"
                  className="btn text-center btn-primary"
              >
                  Submit
              </Button>
              </div>
        
        </Form>
        )}
        </Formik>
        </Modal>
    );
}

export default CreateOrganization;
