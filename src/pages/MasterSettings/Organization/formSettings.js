import React from "react";
import * as yup from "yup";


export const initialValues = { 
    name: "",
    details: "",
    address: "",
    email: "",
    contact_no: "",
    contact_person: "",
    is_active: "",
    contact_number: "",
    hotline_number: ""
};
export const validationSchema =  yup.object({
    // name: yup.string()
    // .max(50, "Should not be more than 50 characters")
    // .min(3, "Should not be less than 3 characters")
    // .required("Organization Name is Required"),
    // details: yup.string()
    // .max(255, "Should not be more than 255 characters")
    // .min(3, "Should not be less than 3 characters"),
    // address: yup.string().required("Address is Required"),
    // email: yup.string().email().required("Email is Required"),
    // contact_no: yup
    //   .string()
    //   .matches(
    //     /^(?:\+88|88)?(01[3-9]\d{8})$/,
    //     "Contact number must be a valid Bangladeshi mobile number"
    //   )
    //   .required("Contact number is required"),
    // contact_person: yup.string()
    // .max(50, "Should not be more than 50 characters")
    // .min(3, "Should not be less than 3 characters")
    // .required("Contact Person is Required"),
    // host_url: yup.string()
    // // .url("Invalid URL")
    // .required("Host Url is Required"),
    
})


