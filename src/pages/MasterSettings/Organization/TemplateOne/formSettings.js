import * as Yup from 'yup';

export const validationSchema = Yup.object().shape({
  hotline_number: Yup.string()
    .matches(/^[+]?[\d\s\-()+]+$/, "Phone number can contain digits, spaces, dashes, plus sign, and parentheses")
    .min(3, "Phone number must be at least 3 digits")
    .max(20, "Phone number cannot exceed 20 characters"),
  custom_student_number: Yup.string()
    .matches(/^(0|[1-9]\d*)$/, "Student number must be 0 or a positive number without leading zeros")
    .matches(/^\d+$/, "Student number must contain only numbers")
    .max(8, "Student number cannot exceed 8 digits"),
});
