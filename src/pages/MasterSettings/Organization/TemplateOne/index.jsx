import React, { useState, useEffect } from "react";
import { Formik, Form } from "formik";
import { useDispatch, useSelector } from "react-redux";
import {
  useGetApiQuery,
  useUpdateApiMutation,
} from "@/store/api/master/commonSlice";
import { validationSchema } from "./formSettings";
import EditHeader from "./EditHeader";
import OrganizationInfo from "./OrganizationInfo";
import BannerSection from "./BannerSection";
import ContactInfo from "./ContactInfo";
import HeroSection from "./HeroSection";
import HeroBg from "@/assets/images/all-img/websiteSettings.png";
import Footer from "./Footer";
import { toast } from "react-toastify";

const TemplateOne = ({organizationData, user}) => {
  const [updateApi, { isLoading }] = useUpdateApiMutation();
  const [showTemplateList, setShowTemplateList] = useState(false);



  const dispatch = useDispatch();

  // State to store original organization data for comparison
  const [originalData, setOriginalData] = useState(null);

  useEffect(() => {
    if (organizationData) {
      setOriginalData(organizationData);
    }
  }, [organizationData]);



  const organization = { ...organizationData };

  const initialValues = {
    name: organization?.name || "",
    details: organization?.details || "",
    address: organization?.address || "",
    email: organization?.email || "",
    contact_no: organization?.contact_no || "",
    contact_person: organization?.contact_person || "",
    is_active: organization?.is_active || false,
    contact_number: organization?.contact_number || "",
    hotline_number: organization?.hotline_number || "",
    custom_student_number: organization?.custom_student_number || 0,
    facebook: organization?.facebook || "",
    twitter: organization?.twitter || "",
    linkedin: organization?.linkedin || "",
    youtube: organization?.youtube || "",
    website: organization?.website || "",
    footer_logo: organization?.footer_logo || null,
  };

  const onSubmit = async (values, { resetForm, setErrors }) => {
    try {
      const formData = new FormData();

      Object.keys(values).forEach((key) => {
        // Skip undefined or null values
        if (values[key] === undefined || values[key] === null) {
          return;
        }

        // Skip email if unchanged
        if (key === "email" && originalData.email === values.email) {
          return;
        }

        // Skip footer_logo if it's a string (not a file)
        if (key === "footer_logo" && typeof values[key] === 'string') {
          return;
        }

        // Skip social media links if unchanged
        const socialMediaLinks = ['facebook', 'twitter', 'linkedin', 'youtube', 'website'];
        if (socialMediaLinks.includes(key) && values[key] === (originalData[key] || "")) {
          return;
        }

        // Always include hotline_number
        if (key === "hotline_number") {
          formData.append(key, values[key]);
          console.log("Adding hotline_number to form data:", values[key]);
        }

        // Convert boolean to string if necessary
        if (typeof values[key] === "boolean") {
          formData.append(key, values[key] ? "1" : "0");
        } else {
          formData.append(key, values[key]);
        }
      });

      // Log FormData contents for debugging
      console.log("FormData contents:");
      for (let [key, value] of formData.entries()) {
        console.log(key, value);
      }

      const response = await updateApi({
        end_point: `admin/organizations/${user?.organization_id}`,
        body: formData,
      }).unwrap();

      console.log("API response:", response);

    } catch (error) {
      if (error?.data?.errors) {
        setErrors(error.data.errors);
        const firstErrorKey = Object.keys(error.data.errors)[0];
        const firstErrorMessage = error.data.errors[firstErrorKey][0];
        toast.error(firstErrorMessage || "Validation error");
      } else {
        console.error("An unexpected error occurred:", error);
        toast.error(error?.data?.message || "An unexpected error occurred. Please try again.");
      }
    }
  };

  return (
    <div className="w-full">
        <div
          className="relative min-h-[600px] overflow-hidden bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url(${HeroBg})`,
            backgroundBlendMode: "overlay",
          }}
        >
          <Formik
            initialValues={initialValues}
            onSubmit={onSubmit}
            validationSchema={validationSchema}
            enableReinitialize={true} // Allow Formik to reinitialize when initialValues change
          >
            {({ values, setFieldValue, errors, touched, handleSubmit, setFieldTouched }) => (
              console.log(errors),
              <Form className="">
                <EditHeader
                  organization={organization}
                  setFieldValue={setFieldValue}
                  values={values}
                  errors={errors}
                  touched={touched}
                  handleSubmit={handleSubmit}
                />

                <div className="grid grid-cols-2 gap-4 pt-6 px-4">
                  <OrganizationInfo
                    organization={organization}
                    setFieldValue={setFieldValue}
                    values={values}
                    errors={errors}
                    touched={touched}
                    handleSubmit={handleSubmit}
                  />
                  <BannerSection
                    organization={organization}
                    setFieldValue={setFieldValue}
                    values={values}
                    errors={errors}
                    touched={touched}
                    handleSubmit={handleSubmit}
                  />
                </div>

                <div className="px-4">
                  {/* <ContactInfo
                    organization={organization}
                    setFieldValue={setFieldValue}
                    values={values}
                    errors={errors}
                    touched={touched}
                    handleSubmit={handleSubmit}
                  /> */}
                  <div className="pt-4">
                    <Footer
                      organization={organization}
                      setFieldValue={setFieldValue}
                      values={values}
                      errors={errors}
                      touched={touched}
                      handleSubmit={handleSubmit}
                      setFieldTouched={setFieldTouched}
                    />
                  </div>
                </div>

                <div className="w-full p-4 flex justify-end"></div>
              </Form>
            )}
          </Formik>
        </div>

    </div>
  );
};

export default TemplateOne;
