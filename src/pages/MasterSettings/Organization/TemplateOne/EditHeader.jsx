import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import { Phone, Pencil, Check } from "lucide-react";
import * as Yup from "yup";

const EditHeader = ({
  organization,
  setFieldValue,
  values,
  errors,
  touched,
  handleSubmit,
}) => {
  const [isEditing, setIsEditing] = useState(false); // State to track edit mode
  const [isSubmitting, setIsSubmitting] = useState(false); // State to track if submitting

  // Handle input blur event
  const handleBlur = (e) => {
    if (isSubmitting) {
      // Prevent setting isEditing to false if submitting
      return;
    }
    setIsEditing(false);
  };

  // Handle input focus event
  const handleFocus = () => {
    setIsEditing(true);
  };

  // Handle logo change and submit the form
  const handleLogoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFieldValue("logo", file);
      handleSubmit(); // Submit the form after setting the logo
    }
  };

  // Handle phone number submission
  const handlePhoneSubmit = () => {
    handleSubmit(); // Submit the form
    setIsEditing(false); // Exit edit mode
    setIsSubmitting(false); // Reset submitting flag
  };

  // Handle pressing Enter key to submit the phone number
  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handlePhoneSubmit();
      setIsEditing(false);
      setIsSubmitting(false);
    }
  };

  // Clean up object URLs to avoid memory leaks
  useEffect(() => {
    let objectUrl;
    if (values.logo) {
      objectUrl = URL.createObjectURL(values.logo);
    }
    return () => {
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    };
  }, [values.logo]);

  return (
    <div className="w-full">
      {/* Main navigation */}
      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-screen-xl mx-auto px-4 py-3">
          <div className="flex justify-between items-center">
            {/* Logo Section */}
            <div className="flex items-center space-x-4">
              <div className="relative">
                <input
                  type="file"
                  accept="image/*"
                  className="hidden"
                  id="lms-logo"
                  onChange={handleLogoChange} // Updated handler
                />
                <label htmlFor="lms-logo">
                  <div
                    className={`relative group cursor-pointer ${
                      errors.logo && touched.logo ? "ring-2 ring-red-500" : ""
                    }`}
                  >
                    <img
                      src={
                        values?.logo
                          ? URL.createObjectURL(values.logo)
                          : `${import.meta.env.VITE_ASSET_HOST_URL}${
                              organization?.logo
                            }`
                      }
                      alt="Organization Logo"
                      className="h-[40px] w-[130px] object-contain"
                    />
                    <div className="absolute inset-0 bg-gray-600 bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                      <Icon
                        icon="mdi:camera"
                        className="w-5 h-5 text-white opacity-0 group-hover:opacity-100"
                      />
                    </div>
                  </div>
                </label>
              </div>
            </div>

            {/* Navigation Links */}
            <div className="flex items-center space-x-6">
              <a href="#" className="text-gray-700 hover:text-primary-500">
                Home
              </a>
              <a href="#" className="text-gray-700 hover:text-primary-500">
                Categories
              </a>
              <a href="#" className="text-gray-700 hover:text-primary-500">
                Courses
              </a>
              <a href="#" className="text-gray-700 hover:text-primary-500">
                More
              </a>
            </div>

            {/* Right Section */}
            <div className="flex items-center space-x-4">
          

              {/* Hotline Number */}
              <div className="flex items-center space-x-2 relative group">
                <Phone className="w-4 h-4 text-gray-600" />
                <div className="relative flex-1">
                  <input
                    type="text"
                    id="phone-input"
                    value={
                      isEditing
                        ? values.hotline_number
                        : organization?.hotline_number
                    }
                    onChange={(e) =>
                      setFieldValue("hotline_number", e.target.value)
                    }
                    onFocus={handleFocus}
                    onBlur={handleBlur}
                    onKeyDown={handleKeyDown} // Handle Enter key
                    placeholder="Hotline Number"
                    className={`
                      w-full bg-transparent outline-none text-sm pl-2
                      transition-all duration-200 ease-in-out
                      ${isEditing ? "border border-gray-300 rounded py-1" : ""}
                      focus:border-blue-500 focus:ring-1 focus:ring-blue-500
                    `}
                  />
                  {errors.hotline_number && touched.hotline_number && (
                    <div className="text-sm text-red-500">
                      {errors.hotline_number}
                    </div>
                  )}
                  {/* Pencil Icon */}
                  {!isEditing && (
                    <button
                      onClick={handleFocus}
                      className={`
                        absolute right-8 top-1/2 -translate-y-1/2
                        opacity-0 group-hover:opacity-100
                        transition-opacity duration-200 ease-in-out
                      `}
                      aria-label="Edit Hotline Number"
                    >
                      <Pencil className="w-4 h-4 text-gray-600 hover:text-gray-800" />
                    </button>
                  )}
                  {/* Check Icon */}
                  {isEditing && (
                    <button
                      onMouseDown={() => setIsSubmitting(true)} // Set submitting flag on mouse down
                      onClick={handlePhoneSubmit}
                      className={`
                        absolute right-2 top-1/2 -translate-y-1/2
                        opacity-0 group-hover:opacity-100
                        transition-opacity duration-200 ease-in-out
                      `}
                      aria-label="Submit Hotline Number"
                    >
                      <Check className="w-6 h-6 text-green-600 hover:text-green-800" />
                    </button>
                  )}
                </div>
              </div>

              {/* Notifications Icon */}
              <Icon
                icon="mdi:bell"
                className="w-5 h-5 text-gray-600 cursor-pointer hover:text-gray-800"
                aria-label="Notifications"
              />

              {/* User Avatar */}
              <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                <Icon
                  icon="mdi:account"
                  className="w-5 h-5 text-gray-600"
                  aria-label="User Account"
                />
              </div>
            </div>
          </div>
        </div>
      </nav>
    </div>
  );
};

export default EditHeader;
