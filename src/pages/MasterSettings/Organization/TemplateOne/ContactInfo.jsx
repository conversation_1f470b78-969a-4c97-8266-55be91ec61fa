// ContactInfo.jsx
import React, { useState, useRef, useEffect } from "react";
import { FiEdit2, FiSave, FiX } from "react-icons/fi"; // Icons for better UI

const ContactInfo = ({
  organization,
  setFieldValue,
  errors,
  touched,
  handleSubmit,
}) => {
  // State to manage edit mode for each field
  const [isEditingAddress, setIsEditingAddress] = useState(false);
  const [isEditingDetails, setIsEditingDetails] = useState(false);

  // Local state to manage temporary values
  const [tempAddress, setTempAddress] = useState(organization?.address || "");
  const [tempDetails, setTempDetails] = useState(organization?.details || "");

  // Refs for textareas to manage focus
  const addressRef = useRef(null);
  const detailsRef = useRef(null);

  // Effect to focus textarea when entering edit mode
  useEffect(() => {
    if (isEditingAddress && addressRef.current) {
      addressRef.current.focus();
    }
  }, [isEditingAddress]);

  useEffect(() => {
    if (isEditingDetails && detailsRef.current) {
      detailsRef.current.focus();
    }
  }, [isEditingDetails]);

  // Handlers for Address
  const handleEditAddress = () => {
    setIsEditingAddress(true);
  };

  const handleCancelAddress = () => {
    setTempAddress(organization?.address || "");
    setIsEditingAddress(false);
  };

  const handleSaveAddress = () => {
    setFieldValue("address", tempAddress);
    setIsEditingAddress(false);
    handleSubmit(); // Trigger form submission
  };

  // Handlers for Details
  const handleEditDetails = () => {
    setIsEditingDetails(true);
  };

  const handleCancelDetails = () => {
    setTempDetails(organization?.details || "");
    setIsEditingDetails(false);
  };

  const handleSaveDetails = () => {
    setFieldValue("details", tempDetails);
    setIsEditingDetails(false);
    handleSubmit(); // Trigger form submission
  };

  return (
    <div className="grid grid-cols-2 gap-4 mt-4 p-2 border shadow-md rounded-md">
      {/* Address Section */}
      <div className="relative p-4 bg-white rounded-lg group">
        <h5 className="font-semibold text-sm mb-2">Address</h5>
        <textarea
          ref={addressRef}
          value={tempAddress}
          name="address"
          className={`w-full text-sm bg-transparent outline-none p-2 rounded 
            ${
              isEditingAddress
                ? "border-2 border-blue-500"
                : "border border-transparent"
            }
            ${errors.address && touched.address ? "border-red-500" : ""}
            transition-all duration-300`}
          spellCheck="false"
          rows={3}
          style={{ resize: "none" }}
          placeholder="Write your organization address here..."
          onChange={(e) => setTempAddress(e.target.value)}
          disabled={!isEditingAddress}
        />
        {errors.address && touched.address && (
          <div className="text-red-500 text-xs mt-1">{errors.address}</div>
        )}

        {/* Edit Button */}
        {!isEditingAddress && (
          <button
            type="button"
            onClick={handleEditAddress}
            className="absolute top-14 p-2 rounded-full bg-blue-50 right-4 text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            title="Edit Address"
          >
            <FiEdit2 size={18} />
          </button>
        )}

        {/* Save and Cancel Buttons */}
        {isEditingAddress && (
          <div className="flex space-x-2 mt-2 justify-end">
            <button
              type="button"
              onClick={handleCancelAddress}
              className="font-semibold text-xs cursor-pointer border border-[#4669FA] rounded-md text-[#4669FA] bg-white px-4 transition-colors duration-300"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleSaveAddress}
              className="font-semibold text-xs cursor-pointer  rounded-md text-white py-1 bg-[#4669FA] px-4 transition-colors duration-300"
            >
              Save
            </button>
          </div>
        )}
      </div>

      {/* Organization Description Section */}
      <div className="relative p-4 bg-white rounded-lg group">
        <h5 className="font-semibold text-sm mb-2">Organization Description</h5>
        <textarea
          ref={detailsRef}
          value={tempDetails}
          name="details"
          className={`w-full text-sm bg-transparent outline-none p-2 rounded 
            ${
              isEditingDetails
                ? "border-2 border-blue-500"
                : "border border-transparent"
            }
            ${errors.details && touched.details ? "border-red-500" : ""}
            transition-all duration-300`}
          spellCheck="false"
          rows={3}
          style={{ resize: "none" }}
          placeholder="Write your short description here..."
          onChange={(e) => setTempDetails(e.target.value)}
          disabled={!isEditingDetails}
        />
        {errors.details && touched.details && (
          <div className="text-red-500 text-xs mt-1">{errors.details}</div>
        )}

        {/* Edit Button */}
        {!isEditingDetails && (
          <button
            type="button"
            onClick={handleEditDetails}
            className="absolute top-14 p-2 rounded-full bg-blue-50 right-0 text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            title="Edit Description"
          >
            <FiEdit2 size={18} />
          </button>
        )}

        {/* Save and Cancel Buttons */}
        {isEditingDetails && (
          <div className="flex space-x-2 mt-2 justify-end">
            <button
              type="button"
              onClick={handleCancelDetails}
              className="font-semibold text-xs cursor-pointer border border-[#4669FA] rounded-md text-[#4669FA] bg-white px-4 transition-colors duration-300"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleSaveDetails}
              className="font-semibold text-xs cursor-pointer  rounded-md text-white py-1 bg-[#4669FA] px-4 transition-colors duration-300"
            >
              Save
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContactInfo;
