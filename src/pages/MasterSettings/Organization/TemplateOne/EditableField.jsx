import React, { useEffect, useRef } from "react";
import PropTypes from "prop-types";
import { motion } from "framer-motion";
import { Pencil, Check, X } from "lucide-react";
import clsx from "clsx";

// Memoized ActionButtons to prevent unnecessary re-renders
const ActionButtons = React.memo(({ onCancel, onSave, isLoading }) => (
  <motion.div
    className="flex justify-end space-x-2 mt-3"
    // Removed initial and animate to prevent re-animation on re-renders
  >
    <motion.button
      type="button"
      onClick={onCancel}
      disabled={isLoading}
      className="font-semibold text-xs cursor-pointer  transition-all duration-200 border border-[#4669FA] rounded-md text-[#4669FA] bg-white px-4"
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      aria-label="Cancel"
    >
      {/* <X className="h-4 w-4" /> */}
      <span>Cancel</span>
    </motion.button>
    <motion.button
      type="button"
      onClick={onSave}
      disabled={isLoading}
      className="font-semibold text-xs cursor-pointer  transition-all duration-200  rounded-md text-white py-1 bg-[#4669FA] px-4"
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      aria-label="Save"
    >
      {/* <Check className="h-4 w-4" /> */}
      <span>Save</span>
    </motion.button>
  </motion.div>
));

ActionButtons.propTypes = {
  onCancel: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  isLoading: PropTypes.bool.isRequired,
};

const EditableField = ({
  isEditing,
  value,
  onChange,
  onEdit,
  onSave,
  onCancel,
  error,
  touched,
  fieldName,
  isHeadline,
  isLoading,
}) => {
  const textareaRef = useRef(null);

  // Function to adjust the textarea height based on content
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = "auto";
      // Set the height to match the content
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  };

  // Adjust textarea height and focus when entering edit mode
  useEffect(() => {
    if (isEditing) {
      adjustTextareaHeight();
      // Focus the textarea when entering edit mode
      textareaRef.current.focus();
    }
  }, [value, isEditing]);

  // Handle changes in the textarea
  const handleTextareaChange = (e) => {
    onChange(e);
    adjustTextareaHeight();
  };

  // Component for the Edit button
  const EditButton = () => (
    <motion.button
      type="button"
      onClick={onEdit}
      className="opacity-0 group-hover:opacity-100 absolute right-3 top-1/2 -translate-y-1/2 p-2 rounded-full bg-blue-50 hover:bg-blue-100 transition-all duration-200"
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      aria-label="Edit"
    >
      <Pencil className="h-4 w-4 text-blue-600" />
    </motion.button>
  );

  return (
    <motion.div
      className="group relative"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {isEditing ? (
        <div>
          <textarea
            ref={textareaRef}
            name={fieldName}
            value={value}
            onChange={handleTextareaChange}
            className={clsx(
              "w-full transition-all duration-200",
              "border-2 rounded-xl p-4",
              "focus:ring-2 focus:ring-blue-200 focus:border-blue-600",
              "bg-white shadow-sm",
              "resize-none overflow-hidden",
              isHeadline
                ? "text-2xl font-semibold text-[#1B69B3]"
                : "text-base min-h-[48px] text-gray-900",
              error && touched ? "border-red-300" : "border-blue-200"
            )}
            placeholder={`Enter ${fieldName}`}
            aria-label={fieldName}
            style={{ minHeight: "48px" }}
          />
          <ActionButtons
            onCancel={onCancel}
            onSave={onSave}
            isLoading={isLoading}
          />
          {error && touched && (
            <motion.div
              className="text-red-500 text-sm mt-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.2 }}
            >
              {error}
            </motion.div>
          )}
        </div>
      ) : (
        <motion.div
          className="relative p-4 border-2 border-transparent hover:border-blue-100 rounded-xl group transition-all duration-200 bg-white shadow-sm hover:shadow-md"
          whileHover={{ scale: 1.01 }}
        >
          {isHeadline ? (
            <h1 className="text-2xl font-semibold text-[#1B69B3] pr-12">
              {value || `No ${fieldName} provided.`}
            </h1>
          ) : (
            <p className="text-base text-gray-900 pr-12">
              {value || `No ${fieldName} provided.`}
            </p>
          )}
          <EditButton />
        </motion.div>
      )}
    </motion.div>
  );
};

EditableField.propTypes = {
  isEditing: PropTypes.bool.isRequired,
  value: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  onEdit: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  error: PropTypes.string,
  touched: PropTypes.bool,
  fieldName: PropTypes.string.isRequired,
  isHeadline: PropTypes.bool,
  isLoading: PropTypes.bool,
};

export default EditableField;
