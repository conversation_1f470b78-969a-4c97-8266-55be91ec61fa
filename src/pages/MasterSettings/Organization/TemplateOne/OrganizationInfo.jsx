import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import { useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import EditableField from './EditableField';

const OrganizationInfo = ({ organization, setFieldValue, values, errors, touched }) => {
  const [isEditingHeadline, setIsEditingHeadline] = useState(false);
  const [isEditingSubHeadline, setIsEditingSubHeadline] = useState(false);
  const [updateApi, { isLoading, error }] = useUpdateApiMutation();
  const { user } = useSelector(state => state.auth);
  const organizationId = user?.organization_id;

  useEffect(() => {
    if (!isEditingHeadline && organization?.headline !== values.headline) {
      setFieldValue('headline', organization?.headline || '');
    }
    if (!isEditingSubHeadline && organization?.sub_headline !== values.sub_headline) {
      setFieldValue('sub_headline', organization?.sub_headline || '');
    }
  }, [organization, isEditingHeadline, isEditingSubHeadline, setFieldValue, values.headline, values.sub_headline]);

  const handleEditClick = (field) => {
    if (field === 'headline') {
      setIsEditingHeadline(true);
    } else {
      setIsEditingSubHeadline(true);
    }
  };

  const handleCancel = (field) => {
    if (field === 'headline') {
      setFieldValue('headline', organization?.headline || '');
      setIsEditingHeadline(false);
    } else {
      setFieldValue('sub_headline', organization?.sub_headline || '');
      setIsEditingSubHeadline(false);
    }
  };

  const handleInputChange = (e, field) => {
    setFieldValue(field, e.target.value);
  };

  const handleSave = async (field) => {
    const value = values[field];
    if (value.trim() === '') {
      return;
    }
    
    try {
      const formData = new FormData();
      formData.append(field, value);
      
      await updateApi({
        end_point: `admin/organizations/${organizationId}`,
        body: formData
      }).unwrap();
      
      if (field === 'headline') {
        setIsEditingHeadline(false);
      } else {
        setIsEditingSubHeadline(false);
      }
    } catch (err) {
      console.error(`Failed to update ${field}:`, err);
    }
  };

  return (
    <motion.div 
      className="max-w-4xl mx-auto p-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="space-y-4">
        <EditableField
          isEditing={isEditingHeadline}
          value={values.headline}
          onChange={(e) => handleInputChange(e, 'headline')}
          onEdit={() => handleEditClick('headline')}
          onSave={() => handleSave('headline')}
          onCancel={() => handleCancel('headline')}
          error={errors.headline}
          touched={touched.headline}
          fieldName="headline"
          isHeadline={true}
          isLoading={isLoading}
        />

        <EditableField
          isEditing={isEditingSubHeadline}
          value={values.sub_headline}
          onChange={(e) => handleInputChange(e, 'sub_headline')}
          onEdit={() => handleEditClick('sub_headline')}
          onSave={() => handleSave('sub_headline')}
          onCancel={() => handleCancel('sub_headline')}
          error={errors.sub_headline}
          touched={touched.sub_headline}
          fieldName="sub-headline"
          isHeadline={false}
          isLoading={isLoading}
        />
      </div>
      
      {error && (
        <motion.div 
          className="mt-4 p-3 bg-red-50 text-red-600 rounded-lg text-sm"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          Error saving changes
        </motion.div>
      )}
    </motion.div>
  );
};

OrganizationInfo.propTypes = {
  organization: PropTypes.shape({
    headline: PropTypes.string,
    sub_headline: PropTypes.string
  }).isRequired,
  setFieldValue: PropTypes.func.isRequired,
  values: PropTypes.shape({
    headline: PropTypes.string,
    sub_headline: PropTypes.string
  }).isRequired,
  errors: PropTypes.object.isRequired,
  touched: PropTypes.object.isRequired,
};

export default OrganizationInfo;