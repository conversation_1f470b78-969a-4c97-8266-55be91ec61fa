import React, { useState } from "react";
import Mo<PERSON> from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Button from "@/components/ui/Button";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useDispatch } from "react-redux";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { Eye, EyeOff } from "lucide-react";

const ChangePassword = ({ showModal, setShowModal }) => {
  const dispatch = useDispatch();
  const [postApi, { isLoading }] = usePostApiMutation();

  const [showOld, setShowOld] = useState(false);
  const [showNew, setShowNew] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);

  const initialValues = {
    old_password: "",
    new_password: "",
    confirm_new_password: "",
  };

  const validationSchema = Yup.object({
    old_password: Yup.string().required("Old password is required"),
    new_password: Yup.string()
      .required("New password is required")
      .min(6, "Password must be at least 6 characters"),
    confirm_new_password: Yup.string()
      .oneOf([Yup.ref("new_password"), null], "Passwords must match")
      .required("Confirm your new password"),
  });

  const onSubmit = async (values, { resetForm }) => {
    const response = await postApi({
      end_point: "/admin/update-password",
      body: values,
    });

    console.log(response);

    if (response?.data?.status) {
      resetForm();
      setShowModal(false);
    }
  };

  const renderPasswordField = (
    label,
    name,
    value,
    handleChange,
    isVisible,
    toggleVisible
  ) => (
    <div className="relative">
      <InputField
        label={label}
        name={name}
        type={isVisible ? "text" : "password"}
        value={value}
        onChange={handleChange}
      />
      <button
        type="button"
        onClick={toggleVisible}
        className="absolute right-3 bottom-[10px] text-gray-500 hover:text-gray-700"
      >
        {isVisible ? <EyeOff size={18} /> : <Eye size={18} />}
      </button>
    </div>
  );

  return (
    <Modal
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      title="Change Password"
      className="max-w-xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowModal(false)}
        />
      }
    >
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
      >
        {({ handleChange, values }) => (
          <Form className="space-y-4 mt-4">
            {renderPasswordField(
              "Old Password",
              "old_password",
              values.old_password,
              handleChange,
              showOld,
              () => setShowOld(!showOld)
            )}
            {renderPasswordField(
              "New Password",
              "new_password",
              values.new_password,
              handleChange,
              showNew,
              () => setShowNew(!showNew)
            )}
            {renderPasswordField(
              "Confirm New Password",
              "confirm_new_password",
              values.confirm_new_password,
              handleChange,
              showConfirm,
              () => setShowConfirm(!showConfirm)
            )}

            <div className="text-right mt-6">
              <Button
                text="Change Password"
                type="submit"
                isLoading={isLoading}
                btnClass="btn btn-primary"
              />
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default ChangePassword;
