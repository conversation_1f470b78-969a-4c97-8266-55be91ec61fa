import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Fileinput from "@/components/ui/Fileinput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { Formik, Form, Field, ErrorMessage } from "formik";
import { initialValues, validationSchema } from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import Textarea from "@/components/ui/Textarea";
import Select from "@/components/ui/Select";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import {
  useGetDivisionListQuery,
  useGetDistrictListQuery,
  useGetCityListQuery,
  useGetAreaListQuery,
} from "@/store/api/master/commonSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";

const createMentor = () => {
  const [errors, setErrors] = useState([]);
  const [postApi, { isLoading }] = usePostApiMutation();
  const [divisionId, setDivisionId] = useState(null);
  const [districtId, setDistrictId] = useState(null);
  const [cityId, setCityId] = useState(null);
  const [isActive, setIsActive] = useState(false);
  const [isForeigner, setIsForeigner] = useState(false);
  const [isLifeCouch, setIsLifeCouch] = useState(false);
  const [isHostStaff, setIsHostStaff] = useState(false);
  const [isHostCertified, setIsHostCertified] = useState(false);
  const [isFeatured, setIsFeatured] = useState(false);

  const divisions = useGetDivisionListQuery()?.data;
  const districts = useGetDistrictListQuery(divisionId)?.data;
  const upazillas = useGetCityListQuery(districtId)?.data;
  const areas = useGetAreaListQuery(cityId)?.data;

  const dispatch = useDispatch();
  const { showModal } = useSelector((state) => state.commonReducer);

  const onSubmit = async (values, { resetForm }) => {
    // let formData = new FormData();
    // Object.keys(values).forEach((key) => {
    //   formData.append(key, values[key]);
    // });
    // formData.append("is_featured", isFeatured ? 1 : 0);
    // formData.append("is_foreigner", isForeigner ? 1 : 0);
    // formData.append("is_life_couch", isLifeCouch ? 1 : 0);
    // formData.append("is_host_staff", isHostStaff ? 1 : 0);
    // formData.append("is_host_certified", isHostCertified ? 1 : 0);
    // formData.append("is_active", isActive ? 1 : 0);

    const response = await postApi({
      end_point: "admin/mentor-create",
      body: values,
    });

    if (response.error) {
      setErrors(response.error?.data.errors || []);
    } else {
      resetForm();
      dispatch(setShowModal(false));
    }
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={() => dispatch(setShowModal(false))}
      title="Add New Mentor"
      className="max-w-3xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setShowModal(false))}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={{
          name: "",
          email: "",
          contact_no: "",
          password: "",
          is_active: 1,
        }}
        onSubmit={onSubmit}
      >
        {({ setFieldValue, values }) => (
          <Form>
            <div className="grid md:grid-cols-2 gap-4">
              <InputField
                label="Name"
                name="name"
                type="text"
                placeholder="Enter Name"
                required
                error={errors?.name}
              />
              <InputField
                required
                label="Email"
                name="email"
                type="email"
                placeholder="Enter Email"
                error={errors?.email}
              />
              <InputField
                label="Contact No"
                name="contact_no"
                type="text"
                placeholder="Enter Contact Number"
                error={errors?.contact_no}
              />

              <InputField
                required
                label="Password"
                name="password"
                type="text"
                placeholder="Enter Password"
                error={errors?.password}
              />
            </div>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default createMentor;

{
  /* <NumberInput label="Alternative Contact Number" name="alternative_contact_no" type="text" placeholder="Enter Alternative Contact Number" error={errors?.alternative_contact_no} />
              <InputField label="Education" name="education" type="text" placeholder="Enter Education" error={errors?.education} />
              <InputField label="Institute" name="institute" type="text" placeholder="Enter Institute" />
              <InputField label="Organization Slug" name="organization_slug" type="text" placeholder="Enter Organization Slug" />
              <InputField label="Referral Code" name="referral_code" type="text" placeholder="Enter Referral Code" />
              <InputField label="Referred Code" name="referred_code" type="text" placeholder="Enter Referred Code" />
              
              <Select
                label="Gender"
                placeholder="Select Gender"
                options={[
                  { label: "Male", value: "Male" },
                  { label: "Female", value: "Female" },
                  { label: "Others", value: "Others" },
                ]}
                name="gender"
                onChange={(e) => setFieldValue("gender", e.target.value)}
              />
              <Select
                label="Blood Group"
                placeholder="Select Blood Group"
                options={[
                  { label: "A+", value: "A+" },
                  { label: "A-", value: "A-" },
                  { label: "B+", value: "B+" },
                  { label: "B-", value: "B-" },
                  { label: "O+", value: "O+" },
                  { label: "O-", value: "O-" },
                  { label: "AB+", value: "AB+" },
                  { label: "AB-", value: "AB-" }
                ]}
                name="blood_group"
                onChange={(e) => setFieldValue("blood_group", e.target.value)}
              />
              <InputField label="Father Name" name="father_name" type="text" placeholder="Enter Father Name" error={errors?.father_name} />
              <InputField label="Mother Name" name="mother_name" type="text" placeholder="Enter Mother Name" error={errors?.mother_name} />
              <InputField label="Religion" name="religion" type="text" placeholder="Enter Religion" error={errors?.religion} />
              <Select
                label="Marital Status"
                placeholder="Select Marital Status"
                options={[
                  { label: "Married", value: "Married" },
                  { label: "Unmarried", value: "Unmarried" },
                  { label: "Divorced", value: "Divorced" },
                ]}
                name="marital_status"
                onChange={(e) => setFieldValue("marital_status", e.target.value)}
              />
              <DatePicker label="Date Of Birth" placeholder="YYYY-MM-DD" name="date_of_birth" error={errors?.date_of_birth} onChange={(e) => setFieldValue("date_of_birth", e)} />
              <InputField label="Profession" name="profession" type="text" placeholder="Enter Profession" error={errors?.profession} />
              <InputField label="NID Number" name="nid_no" type="text" placeholder="Enter NID Number" error={errors?.nid_no} />
              <InputField label="Birth Certificate Number" name="birth_certificate_no" type="text" placeholder="Enter Birth Certificate Number" error={errors?.birth_certificate_no} />
              <InputField label="Passport Number" name="passport_no" type="text" placeholder="Enter Passport Number" error={errors?.passport_no} />
              
              <Select
                label="Division"
                placeholder="Select Division"
                options={divisions?.map((item) => ({ label: item.name, value: item.id }))}
                name="division"
                onChange={(e) => {
                  setFieldValue("division_id", e.target.value);
                  setDivisionId(e.target.value);
                }}
              />
              <Select
                label="District"
                placeholder="Select District"
                options={districts?.map((item) => ({ label: item.name, value: item.id }))}
                name="district"
                onChange={(e) => {
                  setFieldValue("district_id", e.target.value);
                  setDistrictId(e.target.value);
                }}
              />
              <Select
                label="Thana/Upazila"
                placeholder="Select Thana/Upazila"
                options={upazillas?.map((item) => ({ label: item.name, value: item.id }))}
                name="city"
                onChange={(e) => {
                  setFieldValue("city_id", e.target.value);
                  setCityId(e.target.value);
                }}
              />
              <Select
                label="Area"
                placeholder="Select Area"
                options={areas?.map((item) => ({ label: item.name, value: item.id }))}
                name="area"
                onChange={(e) => setFieldValue("area_id", e.target.value)}
              />
            </div>
            <div className="grid md:grid-cols-2 gap-4 mt-3">
              <InputField label="Current Address" name="current_address" type="text" placeholder="Enter Current Address" />
              <InputField label="Permanent Address" name="permanent_address" type="text" placeholder="Enter Permanent Address" />
            </div>
            <div className="grid gmd:grid-cols-1 mt-3">
              <label className="block text-[#1D1D1F] text-base font-medium">Image</label>
              <Fileinput
                name="image"
                accept="image/*"
                type="file"
                placeholder="Image"
                preview={true}
                selectedFile={values.image}
                onChange={(e) => setFieldValue("image", e.currentTarget.files[0])}
              />
            </div>
            <div className="grid md:grid-cols-1 mt-3">
              <label className="block text-[#1D1D1F] text-base font-medium">Bio</label>
              <Textarea
                placeholder="Enter Bio"
                name="bio"
                onChange={(e) => setFieldValue("bio", e.target.value)}
              />
            </div>
            <div className="grid md:grid-cols-4 gap-4 py-5 mx-10">
              <Switch label="Active" activeClass="bg-success-500" value={isActive} name="is_active" onChange={() => setIsActive(!isActive)} />
              <Switch label="Foreigner" activeClass="bg-success-500" value={isForeigner} name="is_foreigner" onChange={() => setIsForeigner(!isForeigner)} />
              <Switch label="Life Couch" activeClass="bg-success-500" value={isLifeCouch} name="is_life_couch" onChange={() => setIsLifeCouch(!isLifeCouch)} />
              <Switch label="Host Staff" activeClass="bg-success-500" value={isHostStaff} name="is_host_staff" onChange={() => setIsHostStaff(!isHostStaff)} />
              <Switch label="Host Certified" activeClass="bg-success-500" value={isHostCertified} name="is_host_certified" onChange={() => setIsHostCertified(!isHostCertified)} />
              <Switch label="Featured" activeClass="bg-success-500" value={isFeatured} name="is_featured" onChange={() => setIsFeatured(!isFeatured)} />
            </div> */
}
