import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import moment from "moment";
import Icon from "@/components/ui/Icon";
import { useDispatch } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";
import avatar from "@/assets/images/avatar/av-1.svg";
import EditMentor from "./editMentor";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import { Mail, Phone, Award, MapPin, BookOpen, User, Calendar, Heart, Flag } from "lucide-react";

// Modern detail item with icon
const DetailItem = ({ label, value, icon }) => (
  <div className="flex items-start space-x-3 py-2">
    {icon && (
      <div className="flex-shrink-0 mt-1">
        {icon}
      </div>
    )}
    <div className="flex-1">
      <p className="text-sm text-gray-500 font-medium">{label}</p>
      <p className="text-base text-gray-800">{value || "---"}</p>
    </div>
  </div>
);

// Section card with title
const SectionCard = ({ title, icon, children }) => (
  <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
    <div className="border-b border-gray-100 px-5 py-3 flex items-center">
      {icon && <span className="text-primary-500 mr-2">{icon}</span>}
      <h3 className="text-base font-medium text-gray-800">{title}</h3>
    </div>
    <div className="p-5">{children}</div>
  </div>
);

const DetailsMentor = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams();

  const {
    data: mentor,
    isLoading,
  } = useGetApiQuery("admin/mentor-details/" + id);
  const handleEditMentorClick = () => {
    dispatch(setEditData(mentor));
    dispatch(setEditShowModal(true));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (!mentor) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <Icon icon="heroicons-outline:exclamation-circle" className="w-12 h-12 text-gray-400 mb-2" />
        <h2 className="text-xl font-medium text-gray-600">No teacher details available</h2>
        <button
          onClick={() => navigate("/mentor-list")}
          className="mt-4 px-4 py-2 text-sm font-medium text-primary-600 bg-primary-50 rounded-md hover:bg-primary-100"
        >
          Back to Teacher List
        </button>
      </div>
    );
  }

  return (
    <>
      {/* Header with action buttons */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Teacher Profile</h1>
        <div className="flex space-x-3">
          <button
            onClick={() => navigate("/mentor-list")}
            className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <Icon
              icon="material-symbols:arrow-back"
              className="w-4 h-4 mr-2"
            />
            Back to List
          </button>
          <button
            onClick={handleEditMentorClick}
            className="flex items-center px-4 py-2 text-sm font-medium text-white bg-primary-500 rounded-md hover:bg-primary-600"
          >
            <Icon
              icon="akar-icons:edit"
              className="w-4 h-4 mr-2"
            />
            Edit Profile
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left column - Profile card */}
        <div className="lg:col-span-1 space-y-6">
          {/* Profile Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
            <div className="p-6 flex flex-col items-center text-center">
              <div className="relative mb-4 group">
                <div className="h-32 w-32 rounded-full overflow-hidden ring-4 ring-white shadow-md">
                  <img
                    src={
                      mentor.image
                        ? import.meta.env.VITE_ASSET_HOST_URL + mentor.image
                        : avatar
                    }
                    alt={mentor.name || "Teacher"}
                    className="w-full h-full object-cover"
                  />
                </div>
                {mentor.is_featured && (
                  <div className="absolute -top-1 -right-1 bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                    Featured
                  </div>
                )}
              </div>

              <h2 className="text-xl font-bold text-gray-800 mb-1">
                {mentor.name}
              </h2>

              {/* {mentor.mentor_code && (
                <p className="text-sm text-gray-500 mb-3">
                  ID: {mentor.mentor_code}
                </p>
              )} */}

              <div className="flex flex-wrap items-center justify-center gap-2 mb-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  mentor.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {mentor.is_active ? 'Active' : 'Inactive'}
                </span>

                {/* {mentor.status && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {mentor.status}
                  </span>
                )} */}
              </div>

              <div className="w-full space-y-2 border-t border-gray-100 pt-4">
                {mentor.email && (
                  <div className="flex items-center text-sm">
                    <Mail size={16} className="text-gray-400 mr-2" />
                    <span className="text-gray-600">{mentor.email}</span>
                  </div>
                )}

                {mentor.contact_no && (
                  <div className="flex items-center text-sm">
                    <Phone size={16} className="text-gray-400 mr-2" />
                    <span className="text-gray-600">{mentor.contact_no}</span>
                  </div>
                )}

                {mentor.alternative_contact_no && (
                  <div className="flex items-center text-sm">
                    <Phone size={16} className="text-gray-400 mr-2" />
                    <span className="text-gray-600">{mentor.alternative_contact_no} (Alt)</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Status Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
            <div className="border-b border-gray-100 px-5 py-3">
              <h3 className="text-base font-medium text-gray-800">Status Information</h3>
            </div>
            <div className="p-5 space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Active Status</span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  mentor.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {mentor.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Featured Teacher</span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  mentor.is_featured ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {mentor.is_featured ? 'Yes' : 'No'}
                </span>
              </div>

              {/*
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Foreigner</span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  mentor.is_foreigner ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {mentor.is_foreigner ? 'Yes' : 'No'}
                </span>
              </div>

               <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Life Coach</span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  mentor.is_life_couch ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {mentor.is_life_couch ? 'Yes' : 'No'}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Host Staff</span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  mentor.is_host_staff ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {mentor.is_host_staff ? 'Yes' : 'No'}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Host Certified</span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  mentor.is_host_certified ? 'bg-teal-100 text-teal-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {mentor.is_host_certified ? 'Yes' : 'No'}
                </span>
              </div> */}

              {mentor.rating !== undefined && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Rating</span>
                  <span className="inline-flex items-center text-yellow-500">
                    {mentor.rating} <Icon icon="heroicons-solid:star" className="w-4 h-4 ml-1" />
                  </span>
                </div>
              )}

              {mentor.approval_date && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Approval Date</span>
                  <span className="text-sm text-gray-800">
                    {moment(mentor.approval_date).format("MMM D, YYYY")}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Referral Information */}
          {(mentor.referral_code || mentor.referred_code) && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
              <div className="border-b border-gray-100 px-5 py-3">
                <h3 className="text-base font-medium text-gray-800">Referral Information</h3>
              </div>
              <div className="p-5 space-y-3">
                {mentor.referral_code && (
                  <DetailItem
                    label="Referral Code"
                    value={mentor.referral_code}
                  />
                )}
                {mentor.referred_code && (
                  <DetailItem
                    label="Referred Code"
                    value={mentor.referred_code}
                  />
                )}
              </div>
            </div>
          )}
        </div>

        {/* Right column - Details sections */}
        <div className="lg:col-span-2 space-y-6">
          {/* Bio section */}
          {mentor.bio && (
            <SectionCard title="About" icon={<User size={18} />}>
              <p className="text-gray-700 whitespace-pre-line">{mentor.bio}</p>
            </SectionCard>
          )}

          {/* Education section */}
          <SectionCard title="Education & Professional Details" icon={<BookOpen size={18} />}>
            <div className="grid md:grid-cols-2 gap-6">
              <DetailItem
                icon={<Award size={18} className="text-gray-400" />}
                label="Education"
                value={mentor.education}
              />
              <DetailItem
                icon={<MapPin size={18} className="text-gray-400" />}
                label="Institute"
                value={mentor.institute}
              />
              {mentor.profession && (
                <DetailItem
                  icon={<Award size={18} className="text-gray-400" />}
                  label="Profession"
                  value={mentor.profession}
                />
              )}
              {mentor.host_rank_number && (
                <DetailItem
                  icon={<Award size={18} className="text-gray-400" />}
                  label="Host Rank Number"
                  value={mentor.host_rank_number}
                />
              )}
            </div>
          </SectionCard>

          {/* Address section */}
          {(mentor.current_address || mentor.permanent_address ||
            mentor.division_id || mentor.district_id || mentor.city_id || mentor.area_id) && (
            <SectionCard title="Address Information" icon={<MapPin size={18} />}>
              <div className="grid md:grid-cols-2 gap-6">
                {mentor.current_address && (
                  <DetailItem
                    icon={<MapPin size={18} className="text-gray-400" />}
                    label="Current Address"
                    value={mentor.current_address}
                  />
                )}
                {mentor.permanent_address && (
                  <DetailItem
                    icon={<MapPin size={18} className="text-gray-400" />}
                    label="Permanent Address"
                    value={mentor.permanent_address}
                  />
                )}
                {mentor.division_id && (
                  <DetailItem
                    icon={<MapPin size={18} className="text-gray-400" />}
                    label="Division"
                    value={mentor.division_id}
                  />
                )}
                {mentor.district_id && (
                  <DetailItem
                    icon={<MapPin size={18} className="text-gray-400" />}
                    label="District"
                    value={mentor.district_id}
                  />
                )}
                {mentor.city_id && (
                  <DetailItem
                    icon={<MapPin size={18} className="text-gray-400" />}
                    label="City"
                    value={mentor.city_id}
                  />
                )}
                {mentor.area_id && (
                  <DetailItem
                    icon={<MapPin size={18} className="text-gray-400" />}
                    label="Area"
                    value={mentor.area_id}
                  />
                )}
              </div>
            </SectionCard>
          )}

          {/* Personal details section */}
          <SectionCard title="Personal Information" icon={<User size={18} />}>
            <div className="grid md:grid-cols-2 gap-6">
              {mentor.date_of_birth && (
                <DetailItem
                  icon={<Calendar size={18} className="text-gray-400" />}
                  label="Date of Birth"
                  value={moment(mentor.date_of_birth).format("MMMM D, YYYY")}
                />
              )}
              {mentor.gender && (
                <DetailItem
                  icon={<User size={18} className="text-gray-400" />}
                  label="Gender"
                  value={mentor.gender}
                />
              )}
              {mentor.blood_group && (
                <DetailItem
                  icon={<Heart size={18} className="text-gray-400" />}
                  label="Blood Group"
                  value={mentor.blood_group}
                />
              )}
              {mentor.religion && (
                <DetailItem
                  icon={<Flag size={18} className="text-gray-400" />}
                  label="Religion"
                  value={mentor.religion}
                />
              )}
              {mentor.marital_status && (
                <DetailItem
                  icon={<User size={18} className="text-gray-400" />}
                  label="Marital Status"
                  value={mentor.marital_status}
                />
              )}
              {mentor.father_name && (
                <DetailItem
                  icon={<User size={18} className="text-gray-400" />}
                  label="Father's Name"
                  value={mentor.father_name}
                />
              )}
              {mentor.mother_name && (
                <DetailItem
                  icon={<User size={18} className="text-gray-400" />}
                  label="Mother's Name"
                  value={mentor.mother_name}
                />
              )}
            </div>
          </SectionCard>

          {/* ID Documents section */}
          {(mentor.nid_no || mentor.birth_certificate_no || mentor.passport_no) && (
            <SectionCard title="ID Documents" icon={<Award size={18} />}>
              <div className="grid md:grid-cols-2 gap-6">
                {mentor.nid_no && (
                  <DetailItem
                    icon={<Award size={18} className="text-gray-400" />}
                    label="NID / Social Security Number"
                    value={mentor.nid_no}
                  />
                )}
                {mentor.birth_certificate_no && (
                  <DetailItem
                    icon={<Award size={18} className="text-gray-400" />}
                    label="Birth Certificate No."
                    value={mentor.birth_certificate_no}
                  />
                )}
                {mentor.passport_no && (
                  <DetailItem
                    icon={<Award size={18} className="text-gray-400" />}
                    label="Passport Number"
                    value={mentor.passport_no}
                  />
                )}
              </div>
            </SectionCard>
          )}

          {/* System Information */}
          <SectionCard title="System Information" icon={<Icon icon="heroicons-outline:information-circle" className="w-5 h-5" />}>
            <div className="grid md:grid-cols-2 gap-6">
              {mentor.created_at && (
                <DetailItem
                  icon={<Calendar size={18} className="text-gray-400" />}
                  label="Created At"
                  value={moment(mentor.created_at).format("MMMM D, YYYY, h:mm A")}
                />
              )}
              {mentor.updated_at && (
                <DetailItem
                  icon={<Calendar size={18} className="text-gray-400" />}
                  label="Last Updated"
                  value={moment(mentor.updated_at).format("MMMM D, YYYY, h:mm A")}
                />
              )}
              
              
              
            </div>
          </SectionCard>
        </div>
      </div>

      {/* Edit Mentor Modal */}
      <EditMentor mentor={mentor} onClose={() => dispatch(setEditShowModal(false))} />
    </>
  );
};

export default DetailsMentor;
