import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery, useUpdateApiMutation } from "@/store/api/master/commonSlice";
import { initialValues, validationSchema } from "./formSettings";
import CreateMentor from "./createMentor";
import EditMentor from "./editMentor";
import { setEditShowModal, setEditData } from "@/features/commonSlice";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import avatar from "@/assets/images/avatar/av-1.svg";

import { useMentorUpdateMutation } from "@/store/api/master/mentorSlice";
import MentorDelete from "./MentorDelete";
import MentorBulkUpload from "./MentorBulkUpload";
import ChangePasswordModal from "@/components/partials/common-modals/ChangePasswordModal";
import MentorFeatureConfirmation from "./MentorFeatureConfirmation";

const index = () => {
  const [showModal, setShowModal] = useState(false);
  const [filter, setFilter] = useState("");
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [deleteData, setDeleteData] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showBulkModal, setShowBulkModal] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [showFeatureModal, setShowFeatureModal] = useState(false);
  const [selectedMentor, setSelectedMentor] = useState(null);
  // console.log(filter);
  const [apiParam, setApiParam] = useState("");


  const {
    data,
    isLoading: isMentorLoading,
    isFetching: isMentorFetching,
  } = useGetApiQuery("admin/all-mentor-list-admin" + apiParam);

  const changePage = (val) => {
    setApiParam(val);
  };

  const [mentorUpdate, { isLoading, isError, error, isSuccess }] =
    useMentorUpdateMutation();
    const [updateApi, { isLoading: isUpdating, error: updateError }] = useUpdateApiMutation();
  const columns = [
    {
      label: "Name",
      field: "name",
    },
    {
      label: "Mobile No",
      field: "contact_no",
    },
    {
      label: "Email",
      field: "email",
    },
    {
      label: "Status",
      field: "status",
    },
    {
      label: "Action",
      field: "",
    },
  ];

  const handleIndexImageNameClick = (id) => {
    const selectedMentor = data?.data.find((item) => item.id === id);
    navigate(`/mentor-details/${id}`, { state: { mentor: selectedMentor } });
    // navigate(`/mentor-dashboard/${id}`, { state: { mentor: selectedMentor } });
  };

  const tableData = data?.data?.map((item, index) => {
    return {
      name: (<button
        type="button"
        onClick={() => handleIndexImageNameClick(item.id)}
        className="relative flex items-center bg-transparent border-none cursor-pointer p-0 hover:bg-gray-50 rounded-lg p-2"
      >
        <img
          src={
            item.image
              ? import.meta.env.VITE_ASSET_HOST_URL + item.image
              : avatar
          }
          className="rounded-full w-10 h-10 mr-3 shadow-sm"
          alt="avatar"
        />
        <div className="flex flex-col">
          <span className="text-sm font-medium text-gray-800 hover:text-primary-500 flex items-center">
            {item.name}
          </span>
          {item.is_featured && (
          <span className="transform mt-1 px-3 py-1 text-xs font-bold text-white bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full shadow-md">
            Featured
          </span>
        )}
        </div>
      </button>

      ),
      contact_no: item.contact_no,
      email: <div>{item.email}</div>,
      status: (
        <Badge
          className={
            !item.is_active
              ? `bg-danger-500 text-white`
              : `bg-success-500 text-white`
          }
        >
          {item.is_active ? "Active" : "Inactive"}
        </Badge>
      ),
    };
  });

  const makeFeature = (mentor) => {
    setSelectedMentor(mentor);
    setShowFeatureModal(true);
  };
  const actions = [
    {
      name: "view",
      icon: "heroicons-outline:eye",
      onClick: (val) => {
        const selectedMentor = data?.data[val];
        navigate(`/mentor-details/${selectedMentor.id}`, {
          state: { mentor: selectedMentor },
        });
      },
    },
    {
      name: "Make Featured",
      icon: "heroicons-solid:star",
      onClick: (val) => {
        makeFeature(data.data[val]);
      },
    },
    {
      name: "edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        dispatch(setEditData(data.data[val]));
        dispatch(setEditShowModal(true));
      },
    },
    {
      name: "Change Password",
      icon: "heroicons-outline:key",
      onClick: (val) => {
        setSelectedUser(data.data[val]);
        setShowPasswordModal(true);
      },
    },
    {
      name: "delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];
  // const handleSubmit = () => {
  //   setShowModal(false);
  // };
  const createPage = <CreateMentor />;
  const editPage = <EditMentor />;

  return (
    <div>
      <BasicTablePage
        sampleFile="https://api.edupackbd.com/uploads/csv/mentor_informations.csv"
        loading={isMentorLoading || isMentorFetching || isLoading}
        title="Teacher List"
        createButton="Create New Teacher"
        setShowBulkModal={setShowBulkModal}
        createPage={createPage}
        editPage={editPage}
        actions={actions}
        columns={columns}
        data={tableData}
        changePage={changePage}
        currentPage={data?.current_page}
        totalPages={Math.ceil(data?.total / data?.per_page)}
        setFilter={setApiParam}
      />



      <MentorDelete
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />

      <MentorBulkUpload
        showBulkModal={showBulkModal}
        setShowBulkModal={setShowBulkModal}
      />

      {selectedUser && (
        <ChangePasswordModal
          showModal={showPasswordModal}
          setShowModal={setShowPasswordModal}
          userId={selectedUser.user_id}
          userName={selectedUser.name}
        />
      )}

      {selectedMentor && (
        <MentorFeatureConfirmation
          showModal={showFeatureModal}
          setShowModal={setShowFeatureModal}
          mentor={selectedMentor}
        />
      )}
    </div>
  );
};

export default index;
