import React from "react";
import * as yup from "yup";

export const initialValues = {
  name:"",
  email:"",
  contact_no:"",
  password:"",
  is_active: "",

  // organization_slug:"",
  // // username:"",
  // address:"",
  // education:"",
  // institute:"",
  // device_id:"",
  // referral_code:"",
  // referred_code:"",
  // alternative_contact_no:"",
  // gender:"",
  // bio:"",
  // father_name:"",
  // mother_name:"",
  // religion:"",
  // marital_status:"",
  // date_of_birth:"",
  // profession:"",
  // current_address:"",
  // permanent_address:"",
  // division_id:"",
  // district_id:"",
  // city_id:"",
  // area_id:"",
  // nid_no:"",
  // birth_certificate_no:"",
  // passport_no:"",
  // // intro_video:"",
  // // status:"",
  // // is_foreigner:"",
  // // is_life_couch:"",
  // // is_host_staff:"",
  // // is_host_certified:"",
  // // rating:"",
  // // approval_date:"",
  // host_rank_number:"",
  // blood_group:"",
  // is_featured:"",
};

export const validationSchema = yup.object({
  name: yup
    .string()
    .matches(
      /^([a-zA-Z. ]){2,30}$/,
      "Name should not contain numbers or special characters"
    )
    .max(50, "Should not be more than 50 characters")
    .min(3, "Should not be less than 3 characters")
    .required("Teacher Name is Required"),



  password: yup
    .string()
    .min(6, "Password must be at least 6 characters")
    // .matches(
    //   /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
    //   "Password must contain at least one uppercase letter, one lowercase letter, one number, one special character, and be at least 8 characters long"
    // )
    .required("Password is required"),

  email: yup.string().email().required("Email is required"),

  contact_no: yup
    .string()
    .matches(
      /^\+[1-9][0-9]{6,14}$/,
      "Contact number must be a valid mobile number"
    ),


  // education: yup
  //   .string()
  //   .max(151, "Should not be more than 151 characters")
  //   .min(3, "Should not be less than 3 characters"),

  // institute: yup
  //   .string()
  //   .max(151, "Should not be more than 151 characters")
  //   .min(3, "Should not be less than 3 characters"),

  // organization_slug: yup
  //   .string()
  //   .max(151, "Should not be more than 151 characters")
  //   .min(3, "Should not be less than 3 characters")
  //   .required("Organization Slug is Required"),

  // referral_code: yup
  //   .string()
  //   .max(30, "Should not be more than 30 characters")
  //   .min(3, "Should not be less than 3 characters")
  //   .required("Referral Code is Required"),

  // referred_code: yup
  //   .string()
  //   .max(30, "Should not be more than 30 characters")
  //   .min(3, "Should not be less than 3 characters")
  //   .required("Referred Code Name is Required"),

  // device_id: yup
  //   .string()
  //   .max(30, "Should not be more than 30 characters")
  //   .min(3, "Should not be less than 3 characters")
  //   .required("Organization Slug Name is Required"),

  // father_name: yup
  //   .string()
  //   .matches(
  //     /^([a-zA-Z ]){2,30}$/,
  //     "Name should not contain numbers or special characters"
  //   )
  //   .max(50, "Should not be more than 50 characters")
  //   .min(3, "Should not be less than 3 characters"),

  // mother_name: yup
  //   .string()
  //   .matches(
  //     /^([a-zA-Z ]){2,30}$/,
  //     "Name should not contain numbers or special characters"
  //   )
  //   .max(50, "Should not be more than 50 characters")
  //   .min(3, "Should not be less than 3 characters"),

  // religion: yup
  //   .string()
  //   .max(50, "Should not be more than 50 characters")
  //   .min(3, "Should not be less than 3 characters"),

  // profession: yup
  //   .string()
  //   .max(151, "Should not be more than 151 characters")
  //   .min(3, "Should not be less than 3 characters"),  
  // host_rank_number: yup
  //   .number()
  //   .required("Host Rank Number is Required"),
});



export const updateValidationSchema = yup.object({
  name: yup
    .string()
    // .matches(
    //   /^([a-zA-Z ]){2,30}$/,
    //   "Name should not contain numbers or special characters"
    // )
    .max(50, "Should not be more than 50 characters")
    .min(3, "Should not be less than 3 characters")
    .required("Teacher Name is Required"),



  // password: yup
  //   .string()
  //   .nullable()
  //   .min(6, "Password must be at least 6 characters"),

  email: yup.string().email().nullable(),

  contact_no: yup
    .string()
    .nullable()
    .matches(
      /^(\+?[0-9]{1,4})?0?\d{6,14}$/,
      "Contact number must be a valid number"
    ),


  // education: yup
  //   .string()
  //   .nullable()
  //   .max(151, "Should not be more than 151 characters")
  //   .min(3, "Should not be less than 3 characters"),

  // institute: yup
  //   .string()
  //   .nullable()
  //   .max(151, "Should not be more than 151 characters")
  //   .min(3, "Should not be less than 3 characters"),

  // father_name: yup
  //   .string()
  //   .nullable()
  //   .matches(
  //     /^([a-zA-Z ]){2,30}$/,
  //     "Name should not contain numbers or special characters"
  //   )
  //   .max(50, "Should not be more than 50 characters")
  //   .min(3, "Should not be less than 3 characters"),

  // mother_name: yup
  //   .string()
  //   .nullable()
  //   .matches(
  //     /^([a-zA-Z ]){2,30}$/,
  //     "Name should not contain numbers or special characters"
  //   )
  //   .max(50, "Should not be more than 50 characters")
  //   .min(3, "Should not be less than 3 characters"),

  // religion: yup
  //   .string()
  //   .nullable()
  //   .max(50, "Should not be more than 50 characters")
  //   .min(3, "Should not be less than 3 characters"),

  // profession: yup
  //   .string()
  //   .nullable()
  //   .max(151, "Should not be more than 151 characters")
  //   .min(3, "Should not be less than 3 characters"),
});

// link: yup.string().required("Link/URL is Required")
