import Button from "@/components/ui/Button";
import Fileinput from "@/components/ui/Fileinput";
import Modal from "@/components/ui/Modal";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { Form, Formik } from "formik";
import React, { useState } from "react";
import * as Yup from "yup";

// Validation schema
const validationSchema = Yup.object().shape({
    file: Yup.mixed()
      .required("A file is required")
      .test(
        "fileType",
        "Only Excel or CSV files are allowed",
        (value) =>
          value &&
          [
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // Excel (.xlsx)
            "application/vnd.ms-excel", // Excel (.xls)
            "text/csv", // CSV (.csv)
          ].includes(value.type)
      ),
  });

const MentorBulkUpload = ({ showBulkModal, setShowBulkModal }) => {
  const [postApi, { isLoading }] = usePostApiMutation();

  const handleSubmit = async (values) => {
    console.log(values);
    const formData = new FormData();
    formData.append("file", values.file);
    const response = await postApi({
      end_point: "/admin/import-mentor",
      body: formData,
    });
    console.log(response);
    setShowBulkModal(false);
  };

  return (
    <Modal
      activeModal={showBulkModal}
      onClose={() => setShowBulkModal(false)}
      title="Upload Bulk"
      className="max-w-3xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowBulkModal(false)}
        />
      }
    >
      <Formik
        initialValues={{ file: "" }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ values, setFieldValue, errors, touched }) => (
          <Form>
            <Fileinput
              name="file"
              accept=".xlsx,.xls,.csv"
              type="file"
              placeholder="Select Bulk"
              title="Bulk (Document)"
              selectedFile={values.file}
              onChange={(e) => {
                setFieldValue("file", e.target.files[0]);
              }}
            />
            {errors.file && touched.file && (
              <div className="text-red-500 text-sm mt-1">{errors.file}</div>
            )}

            <div className="flex justify-end mt-4">
              <Button
                text="Submit"
                type="submit"
                btnClass="btn-primary"
                isLoading={isLoading}
              />
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default MentorBulkUpload;
