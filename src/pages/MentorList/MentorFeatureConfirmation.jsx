import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";

const MentorFeatureConfirmation = ({ showModal, setShowModal, mentor }) => {
  const [updateApi, { isLoading }] = useUpdateApiMutation();

  const handleClose = () => {
    setShowModal(false);
  };

  const handleConfirm = async () => {
    try {
      let formData = new FormData();
      formData.append("is_featured", mentor.is_featured ? 0 : 1);
      
      const response = await updateApi({
        end_point: "admin/mentor-update/" + mentor.id,
        body: formData,
      });
      
      console.log(response);
      setShowModal(false);
    } catch (error) {
      console.error("Error updating featured status:", error);
    }
  };

  if (!mentor) return null;

  const actionText = mentor.is_featured ? "Remove from Featured" : "Make Featured";

  return (
    <Modal
      activeModal={showModal}
      onClose={handleClose}
      title={`Confirm ${actionText}`}
      className="max-w-md"
      centered
    >
      <div className="p-2">
        <h3 className="text-xl font-semibold text-center mb-4">
          Are you sure?
        </h3>
        <p className="text-center text-slate-600 mb-6">
          You are about to {mentor.is_featured ? "remove" : "make"}{" "}
          <span className="font-semibold">"{mentor.name}"</span>{" "}
          {mentor.is_featured ? "from" : "as"} a featured teacher.
        </p>

        <div className="flex justify-end gap-4">
          <Button
            onClick={handleClose}
            className="btn-outline-dark px-6"
            disabled={isLoading}
            text="Cancel"
          />
          <Button
            onClick={handleConfirm}
            className={mentor.is_featured ? "btn-warning px-6" : "btn-primary px-6"}
            isLoading={isLoading}
            text={actionText}
          />
        </div>
      </div>
    </Modal>
  );
};

export default MentorFeatureConfirmation;
