import React, { useState, useEffect } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { Formik, Form, useField } from "formik";
import { updateValidationSchema } from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import Textarea from "@/components/ui/Textarea";
import Select from "@/components/ui/Select";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import { useDropzone } from "react-dropzone";
import { X, Camera, Edit2 } from "lucide-react";
import Icon from "@/components/ui/Icon";
import moment from "moment";

import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import { setEditShowModal } from "@/features/commonSlice";
// Modern Image Uploader Component
const ImageUploader = ({ name, label, initialImage }) => {
  const [field, meta, helpers] = useField(name);
  const [preview, setPreview] = useState(null);
  const assetBaseURL = import.meta.env.VITE_ASSET_HOST_URL || '';

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif']
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        const file = acceptedFiles[0];
        helpers.setValue(file);
        const objectUrl = URL.createObjectURL(file);
        setPreview(objectUrl);
      }
    }
  });

  useEffect(() => {
    // Handle initial image from server
    if (initialImage && typeof initialImage === 'string') {
      setPreview(`${assetBaseURL}${initialImage}`);
    }

    // Cleanup function
    return () => {
      if (preview && preview.startsWith('blob:')) {
        URL.revokeObjectURL(preview);
      }
    };
  }, [initialImage, assetBaseURL]);

  const handleRemove = (e) => {
    e.stopPropagation();
    helpers.setValue(null);
    setPreview(null);
  };

  return (
    <div className="mb-6">
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {label}
        </label>
      )}

      <div
        {...getRootProps()}
        className={`relative cursor-pointer ${preview ? 'h-64 w-full' : 'h-48 border-2 border-dashed rounded-lg p-4 flex flex-col items-center justify-center'}
        ${isDragActive ? 'border-primary-500 bg-primary-50' : 'border-gray-300 hover:border-primary-500'}`}
      >
        <input {...getInputProps()} />

        {preview ? (
          <>
            <img
              src={preview}
              alt="Preview"
              className="h-full w-full object-cover rounded-lg"
            />
            <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    getRootProps().onClick(e);
                  }}
                  className="p-2 bg-white rounded-full text-gray-700 hover:text-primary-500"
                >
                  <Edit2 size={18} />
                </button>
                <button
                  type="button"
                  onClick={handleRemove}
                  className="p-2 bg-white rounded-full text-gray-700 hover:text-red-500"
                >
                  <X size={18} />
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="text-center">
            <Camera className="mx-auto h-12 w-12 text-gray-400" />
            <div className="mt-2">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {isDragActive ? 'Drop the image here' : 'Drag & drop an image here, or click to select'}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                PNG, JPG, GIF up to 5MB
              </p>
            </div>
          </div>
        )}
      </div>

      {meta.touched && meta.error && (
        <div className="text-red-500 text-xs mt-1">{meta.error}</div>
      )}
    </div>
  );
};

const editMentor = () => {
  const dispatch = useDispatch();
  const { showEditModal } = useSelector((state) => state.commonReducer);
  const { editData } = useSelector((state) => state.commonReducer);
  const [updateApi, { isLoading }] = useUpdateApiMutation();

  // Switch States
  const [isActive, setIsActive] = useState(editData?.is_active || false);
  const [isFeatured, setIsFeatured] = useState(editData?.is_featured || false);
  const [isForeigner, setIsForeigner] = useState(editData?.is_foreigner || false);
  const [isLifeCouch, setIsLifeCouch] = useState(editData?.is_life_couch || false);
  const [isHostStaff, setIsHostStaff] = useState(editData?.is_host_staff || false);
  const [isHostCertified, setIsHostCertified] = useState(editData?.is_host_certified || false);

  // Select options
  const genderOptions = [
    { label: "Male", value: "Male" },
    { label: "Female", value: "Female" },
    { label: "Others", value: "Others" },
  ];

  const bloodGroupOptions = [
    { label: "A+", value: "A+" },
    { label: "A-", value: "A-" },
    { label: "B+", value: "B+" },
    { label: "B-", value: "B-" },
    { label: "O+", value: "O+" },
    { label: "O-", value: "O-" },
    { label: "AB+", value: "AB+" },
    { label: "AB-", value: "AB-" }
  ];

  const maritalStatusOptions = [
    { label: "Single", value: "Single" },
    { label: "Married", value: "Married" },
    { label: "Divorced", value: "Divorced" },
    { label: "Widowed", value: "Widowed" }
  ];

  const religionOptions = [
    { label: "Islam", value: "Islam" },
    { label: "Hinduism", value: "Hinduism" },
    { label: "Christianity", value: "Christianity" },
    { label: "Buddhism", value: "Buddhism" },
    { label: "Others", value: "Others" }
  ];
  

  const onSubmit = async (values, { setSubmitting }) => {
    try {
      const formData = new FormData();

      // Basic information
      formData.append("name", values.name || "");
      formData.append("email", values.email || "");
      formData.append("contact_no", values.contact_no || "");
      formData.append("username", values.username || "");
      formData.append("mentor_code", values.mentor_code || "");

      // Handle image upload
      if (values.image && values.image instanceof File) {
        formData.append("image", values.image);
      }

      // Education & Professional details
      if (values.education) formData.append("education", values.education);
      if (values.institute) formData.append("institute", values.institute);
      if (values.profession) formData.append("profession", values.profession);
      if (values.bio) formData.append("bio", values.bio);

      // Personal details
      if (values.gender) formData.append("gender", values.gender);
      if (values.blood_group) formData.append("blood_group", values.blood_group);
      if (values.father_name) formData.append("father_name", values.father_name);
      if (values.mother_name) formData.append("mother_name", values.mother_name);
      if (values.religion) formData.append("religion", values.religion);
      if (values.marital_status) formData.append("marital_status", values.marital_status);
      if (values.date_of_birth) {
        const formattedDate = moment(values.date_of_birth).format('YYYY-MM-DD');
        formData.append("date_of_birth", formattedDate);
      }

      // Address details
      if (values.current_address) formData.append("current_address", values.current_address);
      if (values.permanent_address) formData.append("permanent_address", values.permanent_address);

      // ID documents
      if (values.nid_no) formData.append("nid_no", values.nid_no);
      if (values.birth_certificate_no) formData.append("birth_certificate_no", values.birth_certificate_no);
      if (values.passport_no) formData.append("passport_no", values.passport_no);

      // Additional contact details
      if (values.alternative_contact_no) formData.append("alternative_contact_no", values.alternative_contact_no);
      if (values.device_id) formData.append("device_id", values.device_id);
      if (values.referral_code) formData.append("referral_code", values.referral_code);
      if (values.referred_code) formData.append("referred_code", values.referred_code);

      // Status flags
      formData.append("is_active", values.is_active ? 1 : 0);
      formData.append("is_featured", values.is_featured ? 1 : 0);
      formData.append("is_foreigner", isForeigner ? 1 : 0);
      formData.append("is_life_couch", isLifeCouch ? 1 : 0);
      formData.append("is_host_staff", isHostStaff ? 1 : 0);
      formData.append("is_host_certified", isHostCertified ? 1 : 0);

      // Send the update request
      await updateApi({
        end_point: "admin/mentor-update/" + editData.id,
        body: formData,
      });

      dispatch(setEditShowModal(false));
    } catch (error) {
      console.error("Error updating mentor:", error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Modal
      activeModal={showEditModal}
      onClose={() => dispatch(setEditShowModal(false))}
      title="Update Teacher Profile"
      className="max-w-5xl"
      centered
    >
      <Formik
        validationSchema={updateValidationSchema}
        initialValues={editData}
        onSubmit={onSubmit}
      >
        {({ setFieldValue, values, isSubmitting }) => (
          <Form className="space-y-6">
            <div className="grid md:grid-cols-3 gap-6">
              {/* Left column - Image uploader and status toggles */}
              <div className="md:col-span-1">
                <ImageUploader
                  name="image"
                  label="Profile Photo"
                  initialImage={editData.image}
                />


                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Basic Information</h3>
                  <div className="space-y-4">
                    <InputField
                      label="Full Name"
                      name="name"
                      type="text"
                      placeholder="Enter teacher's full name"
                      required
                    />

                    <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                      <InputField
                        label="Email Address"
                        name="email"
                        type="email"
                        placeholder="Enter email address"
                      />

                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-1 gap-4">

                    <NumberInput
                        label="Contact Number"
                        name="contact_no"
                        type="text"
                        placeholder="Enter contact number"
                      />

                      <NumberInput
                        label="Alternative Contact"
                        name="alternative_contact_no"
                        type="text"
                        placeholder="Enter alternative contact"
                      />

                
                    </div>

                  </div>
                </div>


                <div className="mt-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Status Settings</h3>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <label className="block text-sm text-gray-700">Active Status</label>
                      
                      <Switch
                        activeClass="bg-success-500"
                        value={values.is_active}
                        name="is_active"
                        onChange={(e) => setFieldValue("is_active", e.target.checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <label className="block text-sm text-gray-700">Featured Teacher</label>
                      
                      <Switch
                        activeClass="bg-success-500"
                        value={values.is_featured}
                        name="is_featured"
                        onChange={(e) => setFieldValue("is_featured", e.target.checked)}
                      />
                    </div>

                  </div>
                </div>
              </div>

              {/* Right column - Form fields */}
              <div className="md:col-span-2 space-y-5">
                {/* Basic Information */}


                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <div className="space-y-4">
               

                    <Textarea
                      label="Bio"
                      name="bio"
                      placeholder="Enter a short bio"
                      row={3}
                      value={values.bio || ""}
                      onChange={(e) => setFieldValue("bio", e.target.value)}
                    />
                  </div>
                </div>

                {/* Education & Professional Details */}
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Education & Professional Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <InputField
                      label="Education"
                      name="education"
                      type="text"
                      placeholder="Highest education level"
                    />

                    <InputField
                      label="Institute"
                      name="institute"
                      type="text"
                      placeholder="Educational institute"
                    />

                    <InputField
                      label="Profession"
                      name="profession"
                      type="text"
                      placeholder="Current profession"
                    />
                  </div>
                </div>

                {/* Personal Details */}
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Personal Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Select
                      label="Gender"
                      name="gender"
                      placeholder="Select gender"
                      options={genderOptions}
                      onChange={(e) => setFieldValue("gender", e.target.value)}
                      defaultValue={values.gender}
                    />

                    <Select
                      label="Blood Group"
                      name="blood_group"
                      placeholder="Select blood group"
                      options={bloodGroupOptions}
                      onChange={(e) => setFieldValue("blood_group", e.target.value)}
                      defaultValue={values.blood_group}
                    />

                    <InputField
                      label="Father's Name"
                      name="father_name"
                      type="text"
                      placeholder="Enter father's name"
                    />

                    <InputField
                      label="Mother's Name"
                      name="mother_name"
                      type="text"
                      placeholder="Enter mother's name"
                    />

                    <Select
                      label="Religion"
                      name="religion"
                      placeholder="Select religion"
                      options={religionOptions}
                      onChange={(e) => setFieldValue("religion", e.target.value)}
                      defaultValue={values.religion}
                    />

                    <Select
                      label="Marital Status"
                      name="marital_status"
                      placeholder="Select marital status"
                      options={maritalStatusOptions}
                      onChange={(e) => setFieldValue("marital_status", e.target.value)}
                      defaultValue={values.marital_status}
                    />

                    <DatePicker
                      label="Date of Birth"
                      name="date_of_birth"
                      placeholder="YYYY-MM-DD"
                      format="YYYY-MM-DD"
                      defaultValue={values.date_of_birth}
                      onChange={(date) => setFieldValue("date_of_birth", date)}
                    />
                  </div>
                </div>

                {/* Address Information */}
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Address Information</h3>
                  <div className="space-y-4">
                    <Textarea
                      label="Current Address"
                      name="current_address"
                      placeholder="Enter current address"
                      row={2}
                      value={values.current_address || ""}
                      onChange={(e) => setFieldValue("current_address", e.target.value)}
                    />

                    <Textarea
                      label="Permanent Address"
                      name="permanent_address"
                      placeholder="Enter permanent address"
                      row={2}
                      value={values.permanent_address || ""}
                      onChange={(e) => setFieldValue("permanent_address", e.target.value)}
                    />
                  </div>
                </div>

                {/* ID Documents */}
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">ID Documents</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <InputField
                      label="NID / Social Security Number"
                      name="nid_no"
                      type="text"
                      placeholder="Enter NID / Social Security number"
                    />

                    <InputField
                      label="Birth Certificate No."
                      name="birth_certificate_no"
                      type="text"
                      placeholder="Enter birth certificate number"
                    />

                    <InputField
                      label="Passport Number"
                      name="passport_no"
                      type="text"
                      placeholder="Enter passport number"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <Button
                text="Cancel"
                className="btn-outline-dark"
                onClick={() => dispatch(setEditShowModal(false))}
                type="button"
              />
              <Button
                text="Save Changes"
                className="btn-primary"
                type="submit"
                isLoading={isSubmitting || isLoading}
                disabled={isSubmitting || isLoading}
              />
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default editMentor;

{
  /*

            <div className="grid md:grid-cols-2 gap-4 mt-3">
              <InputField
                label="Current Address"
                name="current_address"
                type="text"
                placeholder="Enter Current Address"
              />
              <InputField
                label="Permanent Address"
                name="permanent_address"
                type="text"
                placeholder="Enter Permanent Address"
              />
            </div>


  <InputField label="Username" name="username" type="text" placeholder="Enter Username" required/>
              <NumberInput label="Alternative Contact Number" name="alternative_contact_no" type="text" placeholder="Enter Alternative Contact Number"/>
              <InputField label="Education" name="education" type="text" placeholder="Enter Education"/>
              <InputField label="Institute" name="institute" type="text" placeholder="Enter Institute"/>
              <InputField label="Organization Slug" name="organization_slug" type="text" placeholder="Enter Organization Slug" required/>
              <InputField label="Referral Code" name="referral_code" type="text" placeholder="Enter Referral Code"/>
              <InputField label="Referred Code" name="referred_code" type="text" placeholder="Enter Referred Code"/>
              <InputField label="Device ID" name="device_id" type="text" placeholder="Enter Device ID"/><>
                <Select
                defaultValue={editData.gender}
                label="Gender"
                placeholder="Select Gender"
                options={[
                        { label: "Male", value: "Male" },
                        { label: "Female", value: "Female" },
                        { label: "Others", value: "Others" },
                        ]}
                name="gender"
                onChange={(e) => {
                  setFieldValue("gender", e.target.value);

                  }}
                />

              </>
              <>
                <Select
                defaultValue={editData.blood_group}
                label="Blood Group"
                placeholder="Select Blood Group"
                options={[
                        { label: "A+", value: "A+" },
                        { label: "A-", value: "A-" },
                        { label: "B+", value: "B+" },
                        { label: "B-", value: "B-" },
                        { label: "O+", value: "O+" },
                        { label: "O-", value: "O-" },
                        { label: "AB+", value: "AB+" },
                        { label: "AB-", value: "AB-" }
                        ]}
                name="blood_group"
                onChange={(e) => {
                  setFieldValue("blood_group", e.target.value);

                  }}
                />
              </>
              <InputField label="Father Name" name="father_name" type="text" placeholder="Enter Father Name"/>
              <InputField label="Mother Name" name="mother_name" type="text" placeholder="Enter Mother Name"/>
              <InputField label="Religion" name="religion" type="text" placeholder="Enter Religion"/>
              <Select
                defaultValue={editData.marital_status}

                // defaultValue={maritalStatusList.find((item) => item.value === editData.marital_status)}
                label="Marital Status"
                placeholder="Select Marital Status"
                options={maritalStatusList}
                name="marital_status"
                onChange={(e) => {
                  setFieldValue("marital_status", e.target.value);

                  }}
                />
              <DatePicker
              label="Date Of Birth"
              placeholder="YYYY-MM-DD"
              format="YYYY-MM-DD"
              defaultValue={editData.date_of_birth}
              name="date_of_birth"
                onChange={(e) => {
                  setFieldValue("date_of_birth", e);
                  }}
              />
              <InputField label="Profession" name="profession" type="text" placeholder="Enter Profession"/>
              <InputField label="NID Number" name="nid_no" type="text" placeholder="Enter NID Number"/>
              <InputField label="Birth Certificate Number" name="birth_certificate_no" type="text" placeholder="Enter Birth Certificate Number"/>
              <InputField label="Passport Number" name="passport_no" type="text" placeholder="Enter Passport Number"/> */
}

{
  /* <div className="grid md:grid-cols-1 mt-3">
<>
  <label className="block text-[#1D1D1F] text-base font-medium">
    {" "}
    Bio{" "}
  </label>
  <Textarea
    placeholder="Enter Bio"
    name="bio"
    defaultValue={editData.bio}
    onChange={(e) => {
      setFieldValue("bio", e.target.value);
    }}
  />
</>
</div> */
}

// <Switch
//   label="Foreigner"
//   activeClass="bg-success-500"
//   value={isForeigner}
//   name="is_foreigner"
//   onChange={() => setIsForeigner(!isForeigner)}
// />
// <Switch
//   label="Life Couch"
//   activeClass="bg-success-500"
//   value={isLifeCouch}
//   name="is_life_couch"
//   onChange={() => setIsLifeCouch(!isLifeCouch)}
// />
// <Switch
//   label="Host Staff"
//   activeClass="bg-success-500"
//   value={isHostStaff}
//   name="is_host_staff"
//   onChange={() => setIsHostStaff(!isHostStaff)}
// />
// <Switch
//   label="Host Certified"
//   activeClass="bg-success-500"
//   value={isHostCertified}
//   name="is_host_certified"
//   onChange={() => setIsHostCertified(!isHostCertified)}
// />

// <Switch
//   label="Featured"
//   activeClass="bg-success-500"
//   value={isFeatured}
//   name="is_featured"
//   onChange={() => setIsFeatured(!isFeatured)}
// />
