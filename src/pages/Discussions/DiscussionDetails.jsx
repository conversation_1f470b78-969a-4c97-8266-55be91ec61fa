import React, { useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useGetApiQuery, usePostApiMutation, useUpdateApiMutation } from "@/store/api/master/commonSlice";
import Card from "@/components/ui/Card";
import Badge from "@/components/ui/Badge";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";
import Loading from "@/components/Loading";
import Tooltip from "@/components/ui/Tooltip";
import EditorData from "../components/EditorData";

const DiscussionDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [postApi] = usePostApiMutation();
  const [putApi] = useUpdateApiMutation();
  const { data: discussion, isLoading, isError, refetch } = useGetApiQuery(`admin/discussions/${id}`);
  const [activeTab, setActiveTab] = useState("discussion");
  const [expandedComments, setExpandedComments] = useState([]);

  if (isLoading) {
    return <Loading />;
  }

  if (isError || !discussion) {
    return (
      <Card>
        <div className="p-6 text-center">
          <h4 className="text-xl font-medium text-danger-500 mb-2">Error Loading Discussion</h4>
          <p className="text-slate-500 dark:text-slate-300 mb-5">
            There was an error loading the discussion details.
          </p>
          <Button
            text="Go Back"
            className="btn-dark"
            onClick={() => navigate("/discussions")}
          />
        </div>
      </Card>
    );
  }

  const toggleCommentExpand = (commentId) => {
    if (expandedComments.includes(commentId)) {
      setExpandedComments(expandedComments.filter(id => id !== commentId));
    } else {
      setExpandedComments([...expandedComments, commentId]);
    }
  };

  const handleStatusChange = async (status) => {
    try {
      let formData = new FormData();
      await putApi({
        end_point: `admin/discussions/${id}/${status}`,
        body: formData
      });
      refetch();
    } catch (error) {
      console.error("Error updating status:", error);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  return (
    <div className="space-y-5">
      {/* Back button */}
      <div className="flex items-center mb-5">
        <Button
          icon="heroicons-outline:arrow-left"
          text="Back to Discussions"
          className="btn-outline-dark"
          onClick={() => navigate("/discussions")}
        />
      </div>

      {/* Discussion header */}
      <Card>
        <div className="p-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h2 className="text-2xl font-bold text-slate-900 dark:text-white">
                {discussion.title}
              </h2>
              <div className="flex items-center mt-2 text-sm text-slate-500 dark:text-slate-400">
                <span className="mr-2">Posted by: {discussion.user?.name}</span>
                <span className="mr-2">•</span>
                <span>Course: {discussion.course?.title}</span>
                <span className="mr-2">•</span>
                <span>{formatDate(discussion.created_at)}</span>
              </div>
            </div>
            <div className="flex space-x-2">
              <Tooltip content={discussion.is_pinned ? "Unpin Discussion" : "Pin Discussion"} placement="top">
                <Button
                  icon={discussion.is_pinned ? "lucide:pin" : "lucide:pin-off"}
                  className={discussion.is_pinned ? "btn-primary" : "btn-outline-primary"}
                  onClick={() => handleStatusChange("toggle-pin")}
                />

              </Tooltip>
              {/* <Tooltip content={discussion.is_active ? "Deactivate Discussion" : "Activate Discussion"} placement="top">
                <Button
                  icon={discussion.is_active ? "heroicons-solid:check-circle" : "heroicons-outline:x-circle"}
                  className={discussion.is_active ? "btn-success" : "btn-outline-danger"}
                  onClick={() => handleStatusChange("is_active", !discussion.is_active)}
                />
              </Tooltip> */}
              <Tooltip content={discussion.is_approved ? "Unapprove Discussion" : "Approve Discussion"} placement="top">
                <Button
                  icon={discussion.is_approved ? "heroicons-solid:badge-check" : "heroicons-outline:badge-check"}
                  className={discussion.is_approved ? "btn-info" : "btn-outline-info"}
                  onClick={() => handleStatusChange("approve")}
                />
              </Tooltip>
            </div>
          </div>

          <div className="flex space-x-2 mb-4">
            {discussion.is_active && (
              <Badge className="bg-success-500 text-white">Active</Badge>
            )}
            {discussion.is_pinned && (
              <Badge className="bg-info-500 text-white">Pinned</Badge>
            )}
            {discussion.is_approved && (
              <Badge className="bg-primary-500 text-white">Approved</Badge>
            )}
            {!discussion.is_approved && (
              <Badge className="bg-warning-500 text-white">Pending</Badge>
            )}
          </div>

          <div className="border-t border-slate-200 dark:border-slate-700 pt-4">
            <div className="prose dark:prose-invert max-w-none">
              < EditorData htmlData={discussion.content} />
            </div>
          </div>

          <div className="flex items-center mt-4 text-sm text-slate-500 dark:text-slate-400">
            <div className="flex items-center mr-4">
              <Icon icon="heroicons-outline:thumb-up" className="text-lg mr-1" />
              <span>{discussion.likes_count || 0} Likes</span>
            </div>
            <div className="flex items-center">
              <Icon icon="heroicons-outline:chat" className="text-lg mr-1" />
              <span>{discussion.comments?.length || 0} Comments</span>
            </div>
          </div>
        </div>
      </Card>

      {/* Tabs */}
      <div className="flex border-b border-slate-200 dark:border-slate-700">
        <button
          className={`px-4 py-2 font-medium text-sm ${
            activeTab === "discussion"
              ? "text-primary-500 border-b-2 border-primary-500"
              : "text-slate-500 dark:text-slate-400"
          }`}
          onClick={() => setActiveTab("discussion")}
        >
          Comments
        </button>
        {discussion.reports && discussion.reports.length > 0 && (
          <button
            className={`px-4 py-2 font-medium text-sm ${
              activeTab === "reports"
                ? "text-primary-500 border-b-2 border-primary-500"
                : "text-slate-500 dark:text-slate-400"
            }`}
            onClick={() => setActiveTab("reports")}
          >
            Reports <Badge className="bg-danger-500 text-white ml-1">{discussion.reports.length}</Badge>
          </button>
        )}
      </div>

      {/* Comments section */}
      {activeTab === "discussion" && (
        <div className="space-y-4">
          {discussion.comments && discussion.comments.length > 0 ? (
            discussion.comments.map((comment) => (
              <Card key={comment.id} className="border border-slate-200 dark:border-slate-700">
                <div className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center">
                      <div className="font-medium text-slate-900 dark:text-white">
                        {comment.user?.name}
                      </div>
                      <span className="mx-2 text-slate-400">•</span>
                      <div className="text-sm text-slate-500 dark:text-slate-400">
                        {formatDate(comment.created_at)}
                      </div>
                    </div>
                    <div className="flex items-center">
                      <span className="text-sm text-slate-500 dark:text-slate-400 mr-2">
                        {comment.likes_count || 0} Likes
                      </span>
                    </div>
                  </div>
                  <div className="text-slate-600 dark:text-slate-300 mb-2">
                    {comment.content}
                  </div>

                  {/* Replies */}
                  {comment.replies && comment.replies.length > 0 && (
                    <div className="mt-3">
                      <button
                        className="text-sm text-primary-500 flex items-center"
                        onClick={() => toggleCommentExpand(comment.id)}
                      >
                        <Icon
                          icon={
                            expandedComments.includes(comment.id)
                              ? "heroicons-outline:chevron-down"
                              : "heroicons-outline:chevron-right"
                          }
                          className="text-lg mr-1"
                        />
                        {comment.replies.length} {comment.replies.length === 1 ? "Reply" : "Replies"}
                      </button>

                      {expandedComments.includes(comment.id) && (
                        <div className="ml-6 mt-2 space-y-3 border-l-2 border-slate-200 dark:border-slate-700 pl-4">
                          {comment.replies.map((reply) => (
                            <div key={reply.id} className="bg-slate-50 dark:bg-slate-800 p-3 rounded">
                              <div className="flex justify-between items-start mb-1">
                                <div className="flex items-center">
                                  <div className="font-medium text-slate-900 dark:text-white">
                                    {reply.user?.name}
                                  </div>
                                  <span className="mx-2 text-slate-400">•</span>
                                  <div className="text-sm text-slate-500 dark:text-slate-400">
                                    {formatDate(reply.created_at)}
                                  </div>
                                </div>
                                <div className="flex items-center">
                                  <span className="text-sm text-slate-500 dark:text-slate-400">
                                    {reply.likes_count || 0} Likes
                                  </span>
                                </div>
                              </div>
                              <div className="text-slate-600 dark:text-slate-300">
                                {reply.content}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </Card>
            ))
          ) : (
            <Card>
              <div className="p-6 text-center">
                <p className="text-slate-500 dark:text-slate-400">No comments yet.</p>
              </div>
            </Card>
          )}
        </div>
      )}

      {/* Reports section */}
      {activeTab === "reports" && (
        <div className="space-y-4">
          {discussion.reports && discussion.reports.length > 0 ? (
            discussion.reports.map((report) => (
              <Card key={report.id} className="border border-slate-200 dark:border-slate-700">
                <div className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center">
                      <div className="font-medium text-slate-900 dark:text-white">
                        {report.user?.name}
                      </div>
                      <span className="mx-2 text-slate-400">•</span>
                      <div className="text-sm text-slate-500 dark:text-slate-400">
                        {formatDate(report.created_at)}
                      </div>
                    </div>
                    <Badge
                      className={
                        report.status === "pending"
                          ? "bg-warning-500 text-white"
                          : report.status === "resolved"
                          ? "bg-success-500 text-white"
                          : "bg-danger-500 text-white"
                      }
                    >
                      {report.status}
                    </Badge>
                  </div>
                  <div className="text-slate-600 dark:text-slate-300">
                    <p className="font-medium mb-1">Reason:</p>
                    <p>{report.reason}</p>
                  </div>
                  {report.comment_id && (
                    <div className="mt-2 p-3 bg-slate-50 dark:bg-slate-800 rounded">
                      <p className="text-sm text-slate-500 dark:text-slate-400 mb-1">
                        Reported Comment:
                      </p>
                      <p className="text-slate-600 dark:text-slate-300">
                        {discussion.comments?.find(c => c.id === report.comment_id)?.content || 
                         "Comment not found"}
                      </p>
                    </div>
                  )}
                  <div className="mt-3 flex space-x-2">
                    <Button
                      text="Mark as Resolved"
                      className="btn-success btn-sm"
                      onClick={() => {
                        // Handle resolving report
                      }}
                      disabled={report.status !== "pending"}
                    />
                    <Button
                      text="Reject Report"
                      className="btn-danger btn-sm"
                      onClick={() => {
                        // Handle rejecting report
                      }}
                      disabled={report.status !== "pending"}
                    />
                  </div>
                </div>
              </Card>
            ))
          ) : (
            <Card>
              <div className="p-6 text-center">
                <p className="text-slate-500 dark:text-slate-400">No reports for this discussion.</p>
              </div>
            </Card>
          )}
        </div>
      )}
    </div>
  );
};

export default DiscussionDetails;
