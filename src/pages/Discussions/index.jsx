import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Badge from "@/components/ui/Badge";
import { useNavigate, Link } from "react-router-dom";
import Card from "@/components/ui/Card";
import Select from "@/components/ui/Select";
import Textinput from "@/components/ui/Textinput";
import Button from "@/components/ui/Button";


const Filter = ({ setApiParam }) => {
  const {
    data: courseList,
  } = useGetApiQuery(`admin/course-list-for-filter`);
  return (
    <div className="flex gap-2">
      <Select
        className="w-72"
        defaultValue=""
        placeholder="Select Course"
        options={courseList?.map((item) => {
          return { label: item.title, value: item.id };
        })}
        name="class_level"
        onChange={(e) => {
          setApiParam("?course_id=" + e.target.value);
        }}
      />
    </div>
  );
};
const DiscussionList = () => {
  const navigate = useNavigate();
  const [apiParam, setApiParam] = useState("");
  const [filterParams, setFilterParams] = useState({
    course_id: "",
    user_id: "",
    status: "",
    search: "",
  });

  const { data, isLoading, isFetching } = useGetApiQuery(`admin/discussions${apiParam}`);

  const handleViewDetails = (id) => {
    navigate(`/discussions/${id}`);
  };

  const changePage = (val) => {
    setApiParam(val);
  };

  const columns = [
    {
      label: "ID",
      field: "id",
    },
    {
      label: "Title",
      field: "title",
    },
    {
      label: "Course",
      field: "course",
    },
    {
      label: "User",
      field: "user",
    },
    {
      label: "Comments",
      field: "comments_count",
    },
    {
      label: "Status",
      field: "status",
    },
    {
      label: "Created At",
      field: "created_at",
    },
    {
      label: "Action",
      field: "",
    },
  ];

  const tableData = data?.data?.map((item) => {
    return {
      id: item.id,
      title:  (
        <Link
          state={{ discussion: item }}
          className="text-blue-600 hover:underline"
          to={`/discussions/${item?.id}`}
        >
          {item.title}
        </Link>
      ),
      // title: item.title,
      course: item.course?.title || "N/A",
      user: item.user?.name || "N/A",
      comments_count: item.comments_count,
      status: (
        <div className="flex space-x-2">
          {item.is_active && (
            <Badge className="bg-success-500 text-white">Active</Badge>
          )}
          {item.is_pinned && (
            <Badge className="bg-info-500 text-white">Pinned</Badge>
          )}
          {item.is_approved && (
            <Badge className="bg-primary-500 text-white">Approved</Badge>
          )}
          {!item.is_approved && (
            <Badge className="bg-warning-500 text-white">Pending</Badge>
          )}
        </div>
      ),
      created_at: new Intl.DateTimeFormat(undefined, {
        year: "numeric",
        month: "short",
        day: "2-digit",
      }).format(new Date(item.created_at)),
    };
  });

  const actions = [
    {
      name: "view",
      icon: "heroicons-outline:eye",
      onClick: (index) => {
        handleViewDetails(data?.data[index].id);
      },
    },
  ];

  const handleFilterSubmit = (e) => {
    e.preventDefault();
    let queryParams = "?";
    
    if (filterParams.course_id) {
      queryParams += `course_id=${filterParams.course_id}&`;
    }
    
    if (filterParams.user_id) {
      queryParams += `user_id=${filterParams.user_id}&`;
    }
    
    if (filterParams.status) {
      queryParams += `status=${filterParams.status}&`;
    }
    
    if (filterParams.search) {
      queryParams += `search=${filterParams.search}&`;
    }
    
    setApiParam(queryParams);
  };

  const handleFilterReset = () => {
    setFilterParams({
      course_id: "",
      user_id: "",
      status: "",
      search: "",
    });
    setApiParam("");
  };

 
  return (
    <div>
      <BasicTablePage
        tableHeaderExtra={<Filter setApiParam={setApiParam} />}
        loading={isLoading || isFetching}
        title="Discussion List"
        actions={actions}
        columns={columns}
        data={tableData}
        filter={<Filter />}
        changePage={changePage}
        currentPage={data?.current_page}
        totalPages={Math.ceil(data?.total / data?.per_page)}
      />
    </div>
  );
};

export default DiscussionList;
