import React from "react";
import Modal from "../../../../components/ui/Modal";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { Icon } from "@iconify/react/dist/iconify.js";
import { Link } from "react-router-dom";
import Loading from "../../../../components/Loading";
import { BASE_URL } from "@/config";
import { useGetApiQuery } from "@/store/api/master/commonSlice";

const ClassDetailsModal = ({ showModal, setShowModal, setEditData }) => {
  const dispatch = useDispatch();
  const { editData } = useSelector((state) => state.commonReducer);

  const { data, isLoading, isError } = useGetApiQuery(`mentor/live-class-details?id=${editData?.id}`);

  const classDetails = data?.data;

  return (
    <Modal
      activeModal={showModal}
      onClose={() => dispatch(setShowModal(false), dispatch(setEditData(null)))}
      title={classDetails?.title}
      className="max-w-3xl"
    >
      {isLoading && (
        <h2 className="text-lg font-semibold h-28 flex items-center justify-center">
          Loading...
        </h2>
      )}

      {!isLoading && (
        <div className="pb-2 space-y-4">
          <div>
            <h2 className="text-lg font-semibold mb-2">
              {!classDetails.has_started && !classDetails.has_completed
                ? "Class will Start"
                : classDetails.has_completed && classDetails.has_started
                ? "Class Completed"
                : !classDetails.has_completed && classDetails.has_started
                ? "Ongoing class"
                : "Class Info"
            }
            </h2>
            <div className="flex gap-4">
              <p className="flex items-center gap-2 font-semibold">
                <Icon icon="uiw:date" />
                {classDetails.schedule_datetime.slice(0, 10)}
              </p>
              <p className="flex items-center gap-2 font-semibold">
                <Icon icon="mdi:clock-outline" />
                {classDetails.start_time}
              </p>
            </div>
          </div>

          {classDetails?.class_url && (
            <div>
              <h2 className="text-lg font-semibold mb-2">Class Link</h2>

              <p className="text-sky-600 bg-blue-100 p-3 rounded-lg shadow w-full">
                {classDetails?.class_url}
              </p>
            </div>
          )}

          {classDetails?.students?.length > 0 && (
            <div className="">
              <h2 className="text-lg font-semibold mb-2">Assigned Students</h2>
              <div className="max-h-40 overflow-y-auto bg-blue-50 rounded-lg shadow-lg p-3">
                {classDetails.students.map((item, idx) => (
                  <span key={idx} className="flex items-center gap-2 mb-3">
                    <img
                      src={BASE_URL + item?.image}
                      className="h-8 w-8 rounded-full"
                      alt=""
                    />
                    <p>{item.name}</p>
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </Modal>
  );
};

export default ClassDetailsModal;
