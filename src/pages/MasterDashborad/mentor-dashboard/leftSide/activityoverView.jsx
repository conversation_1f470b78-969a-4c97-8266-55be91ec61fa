import React from "react";
import GraphImage from "@/assets/MentorDashboard/timeSpending.svg";
import ReactApexChart from "react-apexcharts";

// Graph set in image

const activityoverView = ({info}) => {
  const data = [44, 55, 41, 67, 22, 43];
  const categories = [1, 2, 3, 4, 5, 6, 7];

  const chartOptions = {
    chart: {
      type: "bar",
      height: 350,
      toolbar: {
        show: false,
      },
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "50%",
        endingShape: "rounded",
      },
    },
    dataLabels: {
      enabled: false,
    },
    stroke: {
      show: true,
      width: 2,
      colors: ["transparent"],
    },
    xaxis: {
      categories: categories,
    },
    yaxis: {
      title: {
        text: "Values",
      },
    },
    fill: {
      opacity: 1,
    },
    tooltip: {
      y: {
        formatter: (val) => `${val} units`,
      },
    },
    title: {
      text: "Activity Overview",
      align: "center",
      fontSize: "30px",
    },
    colors: ['#EB8317', '#10375C'],
  };

  const dataSeries = [
    {
      name: 'Live Class',
      data: [44, 55, 41, 67, 22, 43],
    },
    {
      name: 'Assignment',
      data: [53, 32, 33, 52, 13, 44],
    },
  ];

  return (
    <div className="w-full h-full">
      {/* <img src={GraphImage} alt="" /> */}
      <div className="column-chart p-5">
        <ReactApexChart
          options={chartOptions}
          series={dataSeries}
          type="bar"
          height={350}
        />
      </div>
    </div>
  );
};

export default activityoverView;
