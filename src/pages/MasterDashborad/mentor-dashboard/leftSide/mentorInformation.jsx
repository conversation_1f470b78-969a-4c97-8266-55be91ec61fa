import React from "react";
import MentorIcon from "@/assets/MentorDashboard/MentorProfile.svg";
import ProfessionIcon from "@/assets/MentorDashboard/suitcase.svg";
import InstituteIcon from "@/assets/MentorDashboard/public.svg";

const mentorInformation = () => {
  return (
    <div className="p-2 flex flex-wrap justify-center items-center space-x-4 gap-6">
      {/* Mentor Profile Image */}
      <img src={MentorIcon} alt="Teacher" className="w-16 h-16 rounded-full" />
      {/* Mentor Information */}
      <div>
        <span className="text-cello-900 text-lg font-semibold">
          Teacher Name
        </span>
        {/* Profession Name with Icon */}
        <div className="flex items-center text-fuscousGray-700 mt-2 space-x-2 w-full h-6">
          <img
            src={ProfessionIcon}
            alt="Teacher Profile"
            className="w-5 h-5 my-1"
          />
          <p className="text-base font-normal">Profession Name</p>
        </div>
        {/* Institute Name with Icon*/}
        <div className="flex items-center text-fuscousGray-700 mt-1 space-x-2 w-full h-6">
          <img src={InstituteIcon} alt="Teacher" className="w-5 h-5 my-1" />
          <p className="text-base font-normal">Institute Name</p>
        </div>
      </div>
    </div>
  );
};

export default mentorInformation;
