import React, { useState } from "react";
import Card from "@/components/ui/Card";
import HomeBredCurbs from "./HomeBredCurbs";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Loading from "@/components/Loading";
import { Link } from "react-router-dom";

const MasterDashboard = () => {
 
      const { data, isLoading, isFetching, error } = useGetApiQuery(`admin/super-admin-dashboard`);
      console.log(data);
  
      if (isLoading || isFetching) {
        return <Loading />;
      }
    
      if (error) {
        return (
          <div>
            <h1>Error loading data</h1>
            <p>{error?.data?.message || "Something went wrong."}</p>
          </div>
        );
      }
      return (


        <>
        <Card className="mx-4 shadow-lg border border-slate-200 rounded-lg">
        <div className="grid grid-cols-4 gap-5 mb-5">
    {/* Payment Collection Block */}
        <div className={`py-[18px] px-4 rounded-[6px] bg-[#deffd3] dark:bg-slate-900`} >
          <div className="flex items-center space-x-6 rtl:space-x-reverse">
            <div className="flex-none">
              
            </div>
            <div className="flex-1">
              <div className="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                Total Organization
              </div>
              <div className="text-slate-900 dark:text-white text-lg font-medium">
                {data?.organizations_count} 
              </div>
            </div>
          </div>
        </div>
    {/* Students Block */}
        <div className={`py-[18px] px-4 rounded-[6px] bg-[#E5F9FF] dark:bg-slate-900`} >
          <div className="flex items-center space-x-6 rtl:space-x-reverse">
            <div className="flex-none">
              
            </div>
            <div className="flex-1">
              <div className="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                Total Students
              </div>
              <div className="text-slate-900 dark:text-white text-lg font-medium">
                {data?.students_count}
              </div>
            </div>
          </div>
        </div>

    {/* Mentors Block */}
        <div className={`py-[18px] px-4 rounded-[6px] bg-[#FFEDE5] dark:bg-slate-900`} >
          <div className="flex items-center space-x-6 rtl:space-x-reverse">
            <div className="flex-none">
              
            </div>
            <div className="flex-1">
              <div className="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                Teacher Number
              </div>
              <div className="text-slate-900 dark:text-white text-lg font-medium">
                {data?.mentors_count}
              </div>
            </div>
          </div>
        </div>

    {/* Course Block */}
        <div className={`py-[18px] px-4 rounded-[6px] bg-[#EAE5FF] dark:bg-slate-900`} >
          <div className="flex items-center space-x-6 rtl:space-x-reverse">
            <div className="flex-none">
              
            </div>
            <div className="flex-1">
              <div className="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                Total Available Courses
              </div>
              <div className="text-slate-900 dark:text-white text-lg font-medium">
                {data?.courses_count}
              </div>
            </div>
          </div>
        </div>
</div>
</Card>
  
<Card className="m-4  shadow-lg border border-slate-200 rounded-lg">
        <div className="">
          <div className="flex justify-between items-center mb-4">
            <h1 className="text-xl font-semibold">Organizations Overview</h1>
            <Link
              to="/organizations"
              className="text-blue-600 hover:underline"
            >
              See All
            </Link>
          </div>
    
          <div className="overflow-x-auto rounded shadow">
            <table className="min-w-full bg-white border">
              <thead className="bg-gray-100 text-left">
                <tr>
                  <th className="py-2 px-4 border-b">Organization Name</th>
                  <th className="py-2 px-4 border-b">Course Number</th>
                  <th className="py-2 px-4 border-b">Student Number</th>
                  <th className="py-2 px-4 border-b">Teacher Number</th>
                  <th className="py-2 px-4 border-b">Others</th>
                </tr>
              </thead>
              <tbody>
                {data?.organizations.map((org) => (
                  <tr key={org.id} className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b">{org.name}</td>
                    <td className="py-2 px-4 border-b">{org.courses_count}</td>
                    <td className="py-2 px-4 border-b">{org.students_count}</td>
                    <td className="py-2 px-4 border-b">{org.mentors_count}</td>
                    <td className="py-2 px-4 border-b">
                      <span className="text-sm text-gray-500">
                        Videos: {org.videos_count}, Quizzes: {org.quizzes_count}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
        </Card>
        </>
      );
    };
    
export default MasterDashboard;
