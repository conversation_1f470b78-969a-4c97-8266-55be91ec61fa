import React from 'react';
import ReactApexChart from 'react-apexcharts';

const BarChart = () => {
  // Data for each category, now each category has a single value
  const categoryData = {
    'Live Class': 44, // Single value for Live Class
    'Video': 55,      // Single value for Video
    'Exam': 41,       // Single value for Exam
    'Written Exam': 67, // Single value for Written Exam
  };

  // Chart options
  const chartOptions = {
    chart: {
      type: 'bar',
      height: 350,
      toolbar: {
        show: false,  
      },
    },
    plotOptions: {
      bar: {
        horizontal: false,
        endingShape: "rounded",
        borderRadius: 3
      },
    },
    series: [
      {
        name: 'Categories',
        data: Object.values(categoryData), // Using the values for each category
      },
    ],
    colors: ['#3b82f6', '#ef4444', '#fbbf24', '#22d3ee'], // Custom colors for each category
    xaxis: {
      categories: Object.keys(categoryData), // Category names
    },
    yaxis: {
      title: {
        text: 'Count', // Title for the Y-axis
      },
    },
    grid: {
      borderColor: '#e2e8f0', // Tailwind gray-300 for grid lines
    },
  };

  return (
    <div className="lg:p-4 p-2 bg-white rounded dark:bg-slate-800">
      <ReactApexChart
        options={chartOptions}
        series={chartOptions.series}
        type="bar"
        height={350}
      />
    </div>
  );
};

export default BarChart;
