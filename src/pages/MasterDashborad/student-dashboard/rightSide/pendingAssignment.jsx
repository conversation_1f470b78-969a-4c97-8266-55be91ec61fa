import React from "react";
import ClassSchedulesIcon from "@/assets/StudentDashboard/ClassSchedulesIcon.svg";
import { Icon } from "@iconify/react/dist/iconify.js";

const pendingAssignment = ({assignments}) => {

  // Sample Data for live classes
  const Classes = [
    {
      title: "Product Management",
      date: "20/11/2024",
      time: "(10.30 P.M)",
      status: "Join Now",
      nextClass: "N Class 9th Oct,24",
    },
    {
      title: "Product Management",
      date: "20/11/2024",
      time: "(10.30 P.M)",
      status: "Class 9th Oct,24",
    },
  ];

  return (
    <div className="p-4 rounded-lg bg-white">
      {/* Header Section */}
      <div className="flex justify-between items-center mb-4 shadow-md border py-2 px-3 rounded-lg">
        <div className="flex items-center gap-2">
          <img
            src={ClassSchedulesIcon}
            alt="Class Schedules Icon"
            className="w-8 h-8"
          />
          <h3 className="text-lg font-semibold text-blue-900">
            Pending Assignment
          </h3>
        </div>
        <a href="#" className="text-blue-600 font-medium">
          See All
        </a>
      </div>

      {/* Class Cards */}
      <div className="space-y-4">
        {assignments?.map((data, index) => (
          <div
            key={index}
            className="flex flex-col bg-white rounded-lg p-4 border border-gray-200 shadow-md"
          >
            {/* Class Details */}
            <div className="flex justify-between items-center divide-x mb-2 gap-2">
              <div className="flex flex-col gap-1">
                <p className="text-sm text-gray-700 flex items-center gap-1"><Icon icon="uiw:date"  className="text-sky-600"/> {data.deadline.slice(0, 10)}</p>
                <p className="text-sm text-gray-700 flex items-center gap-1"><Icon icon="mingcute:time-line" className="text-sky-600" /> 12:00</p>
              </div>

              <div className="items-center gap-2 ps-1">
                <span className="text-blue-700 font-semibold text-sm">
                  {data.title}
                </span>
                {/* Action Button or Next Class */}
                {data.status === "ongoing" ? (
                  <button className="mt-2 self-start bg-green-600 hover:bg-green-500 text-white font-medium text-xs py-2 px-4 rounded-lg flex items-center">
                    {data.status} <span className="ml-2">→</span>
                  </button>
                ) : (
                  <span className="text-sm text-gray-600 mt-2">
                    <br />
                    {data.status}
                  </span>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default pendingAssignment;
