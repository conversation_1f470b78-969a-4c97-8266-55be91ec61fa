import React from 'react';
import EditorData from './EditorData';

const EditorDataTest = () => {
  // Sample HTML content with various CKEditor elements including bullet points
  const sampleHtmlData = `
    <h1>Test Heading 1</h1>
    <h2>Test Heading 2</h2>
    <h3>Test Heading 3</h3>
    
    <p>This is a regular paragraph with <strong>bold text</strong> and <em>italic text</em>.</p>
    
    <h3>Bullet Points Test:</h3>
    <ul>
      <li>First bullet point</li>
      <li>Second bullet point with <strong>bold text</strong></li>
      <li>Third bullet point with <em>italic text</em></li>
      <li>Fourth bullet point with a <a href="#">link</a></li>
      <li>Nested list:
        <ul>
          <li>Nested item 1</li>
          <li>Nested item 2</li>
          <li>Deeply nested:
            <ul>
              <li>Deep nested item</li>
            </ul>
          </li>
        </ul>
      </li>
    </ul>
    
    <h3>Numbered List Test:</h3>
    <ol>
      <li>First numbered item</li>
      <li>Second numbered item</li>
      <li>Third numbered item with nested list:
        <ol>
          <li>Nested numbered item 1</li>
          <li>Nested numbered item 2</li>
        </ol>
      </li>
    </ol>
    
    <h3>Table Test:</h3>
    <table>
      <thead>
        <tr>
          <th>Header 1</th>
          <th>Header 2</th>
          <th>Header 3</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Cell 1</td>
          <td>Cell 2</td>
          <td>Cell 3</td>
        </tr>
        <tr>
          <td>Cell 4</td>
          <td>Cell 5</td>
          <td>Cell 6</td>
        </tr>
      </tbody>
    </table>
    
    <h3>Blockquote Test:</h3>
    <blockquote>
      This is a blockquote to test styling. It should have proper indentation and styling.
    </blockquote>
    
    <p>Another paragraph after the blockquote to test spacing.</p>
  `;

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">EditorData Component Test</h1>
      <div className="border border-gray-300 rounded-lg p-4 bg-white">
        <EditorData htmlData={sampleHtmlData} />
      </div>
      
      <div className="mt-6 p-4 bg-gray-100 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">What to check:</h2>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>Bullet points should display with proper disc markers</li>
          <li>Numbered lists should display with proper numbers</li>
          <li>Nested lists should have proper indentation</li>
          <li>Headings should have proper sizing and weight</li>
          <li>Bold and italic text should display correctly</li>
          <li>Tables should have borders and proper spacing</li>
          <li>Blockquotes should have left border and indentation</li>
          <li>Links should be styled and clickable</li>
        </ul>
      </div>
    </div>
  );
};

export default EditorDataTest;
