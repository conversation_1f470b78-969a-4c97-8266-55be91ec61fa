import React from 'react';

import "ckeditor5/ckeditor5.css";
import "@/assets/scss/custom-ckeditor.css";
import "@/assets/scss/image-styles.css";
import "@/assets/scss/editor-fix.css";
import "@/assets/scss/editor-data.css";


const EditorData = ({htmlData}) => {

  // Add inline styles to ensure list elements display properly with highest specificity
  const inlineStyles = `
    <style>
      /* Override Tailwind CSS resets and ensure CKEditor content displays properly */
      .ck-content ul:not(.custom-list), .editor-data-content ul:not(.custom-list) {
        list-style-type: disc !important;
        padding-left: 40px !important;
        margin: 1em 0 !important;
        list-style-position: outside !important;
      }
      .ck-content ol:not(.custom-list), .editor-data-content ol:not(.custom-list) {
        list-style-type: decimal !important;
        padding-left: 40px !important;
        margin: 1em 0 !important;
        list-style-position: outside !important;
      }
      .ck-content li:not(.custom-list li), .editor-data-content li:not(.custom-list li) {
        display: list-item !important;
        margin: 0.5em 0 !important;
        list-style: inherit !important;
      }
      .ck-content p, .editor-data-content p { margin: 1em 0 !important; line-height: 1.6 !important; }
      .ck-content strong, .editor-data-content strong { font-weight: bold !important; }
      .ck-content em, .editor-data-content em { font-style: italic !important; }
      .ck-content h1, .editor-data-content h1 { font-size: 2em !important; font-weight: bold !important; margin: 0.67em 0 !important; }
      .ck-content h2, .editor-data-content h2 { font-size: 1.5em !important; font-weight: bold !important; margin: 0.83em 0 !important; }
      .ck-content h3, .editor-data-content h3 { font-size: 1.17em !important; font-weight: bold !important; margin: 1em 0 !important; }
      .ck-content table, .editor-data-content table { border-collapse: collapse !important; width: 100% !important; margin: 1em 0 !important; }
      .ck-content table td, .ck-content table th, .editor-data-content table td, .editor-data-content table th { border: 1px solid #ccc !important; padding: 8px !important; }
    </style>
  `;

return (
  <>
    <div dangerouslySetInnerHTML={{ __html: inlineStyles }} />
    <div
      className="ck-content editor-data-content overflow-auto"
      style={{
        height: '100%',
        maxHeight: 'calc(100vh - 200px)',
        width: '100%',
        // Ensure proper list styling
        paddingLeft: '0',
        paddingRight: '0',
      }}
      dangerouslySetInnerHTML={{
        __html: htmlData, // Sanitize the data before rendering
      }}
    />
  </>
);

}
export default EditorData;