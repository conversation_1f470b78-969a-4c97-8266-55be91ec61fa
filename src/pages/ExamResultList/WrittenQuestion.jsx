import React, { useState } from "react";
import Icon from "@/components/ui/Icon";
import Modal from "@/components/ui/Modal";

const WrittenQuestion = ({ question, answers, result }) => {
    const ASSET_URL = import.meta.env.VITE_ASSET_HOST_URL;
    const [zoomImage, setZoomImage] = useState(null); // For image popup
    const [showImageModal, setShowImageModal] = useState(false);

    return (
        <div className="p-4 bg-white rounded-lg border border-gray-200 shadow-sm">
            {/* Question Section */}
            <div className="mb-6">
                <div className="flex items-center mb-3">
                    <div className="w-1.5 h-6 bg-indigo-600 rounded mr-2"></div>
                    <h3 className="text-base font-bold text-gray-800">Written Question</h3>
                    <span className="ml-auto bg-indigo-100 text-indigo-800 text-xs px-2 py-0.5 rounded">
                        {question.marks} marks
                    </span>
                </div>

                <div className="space-y-3">
                    <div className="p-3 bg-gray-50 rounded border">
                        <p className="text-gray-800 text-sm">{question.description}</p>
                        {question.instruction && (
                            <div className="mt-2 pt-2 border-t border-gray-200">
                                <p className="text-xs text-gray-600">
                                    <Icon icon="heroicons-outline:information-circle" className="inline mr-1" />
                                    <strong>Instructions:</strong> {question.instruction}
                                </p>
                            </div>
                        )}
                    </div>

                    {question.question_attachment && (
                        <div className="space-y-2">
                            <p className="text-xs font-medium text-gray-600">Question Attachment:</p>
                            <img
                                src={`${ASSET_URL}/${question.question_attachment}`}
                                alt="Question Attachment"
                                className="max-w-full h-auto rounded border border-gray-200 cursor-pointer hover:shadow-md transition-shadow"
                                onClick={() => {
                                    setZoomImage(`${ASSET_URL}/${question.question_attachment}`);
                                    setShowImageModal(true);
                                }}
                            />
                        </div>
                    )}
                </div>
            </div>

            {/* Answer Section */}
            {answers && answers.length > 0 && (
                <div className="mb-6">
                    <h4 className="text-sm font-bold text-gray-800 mb-3 flex items-center">
                        <Icon icon="heroicons-outline:document-text" className="mr-2" />
                        Student Answers
                    </h4>
                    <div className="space-y-3">
                        {answers.map((answer, index) => (
                            <div key={answer.id} className="p-3 bg-blue-50 rounded border border-blue-100">
                                <div className="flex items-center justify-between mb-2">
                                    <span className="text-xs font-medium text-blue-700">
                                        Answer {index + 1}
                                    </span>
                                </div>
                                {answer.attachment_url && (
                                    <img
                                        src={`${ASSET_URL}/${answer.attachment_url}`}
                                        alt={`Answer ${index + 1}`}
                                        className="max-w-full h-auto rounded border border-gray-200 cursor-pointer hover:shadow-md transition-shadow"
                                        onClick={() => {
                                            setZoomImage(`${ASSET_URL}/${answer.attachment_url}`);
                                            setShowImageModal(true);
                                        }}
                                    />
                                )}
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Result Section */}
            {result && (
                <div className="pt-3 border-t border-gray-200">
                    <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-600">
                            <Icon icon="heroicons-outline:academic-cap" className="inline mr-1" />
                            Obtained Mark:
                        </span>
                        <span className={`px-3 py-1 rounded text-sm font-medium ${
                            result.mark > 0 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                            {result.mark || 0} / {question.marks}
                        </span>
                    </div>
                </div>
            )}

            {/* Image Zoom Modal */}
            <Modal
                activeModal={showImageModal}
                onClose={() => {
                    setShowImageModal(false);
                    setZoomImage(null);
                }}
                title="Image Preview"
                className="max-w-4xl"
                centered={true}
                themeClass="bg-slate-900 dark:bg-slate-800"
            >
                {zoomImage && (
                    <div className="flex justify-center items-center">
                        <img
                            src={zoomImage}
                            alt="Zoomed Image"
                            className="max-w-full max-h-[70vh] object-contain rounded"
                        />
                    </div>
                )}
            </Modal>
        </div>
    );
};

export default WrittenQuestion;
