import React, { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import Icon from "@/components/ui/Icon";
import Modal from "@/components/ui/Modal";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import WrittenQuestion from './WrittenQuestion';

const StatCard = ({ detail }) => {
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-3 shadow-xs hover:shadow-sm transition-all">
      <div className="flex items-center space-x-2">
        <div className="p-1.5 rounded-lg bg-blue-50 text-blue-600">
          <Icon icon={detail.icon} className="text-lg" />
        </div>
        <div className="min-w-0">
          <p className="text-xs font-medium text-gray-500 truncate">{detail.label}</p>
          <p className="text-sm font-semibold text-gray-800 truncate">{detail.value}</p>
        </div>
      </div>
    </div>
  );
};

const QuestionCard = ({ children, isCorrect, unanswered }) => {
  const bgColor = unanswered ? 'bg-gray-50' : isCorrect ? 'bg-green-50' : 'bg-red-50';
  const borderColor = unanswered ? 'border-gray-200' : isCorrect ? 'border-green-200' : 'border-red-200';
  
  return (
    <div className={`${bgColor} ${borderColor} border rounded-md p-3 mb-3 text-sm`}>
      {children}
    </div>
  );
};

const AnswerBadge = ({ isCorrect, positiveMark, negativeMark, unanswered }) => {
  if (unanswered) {
    return (
      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
        ---
      </span>
    );
  }
  
  return (
    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
      isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
    }`}>
      {isCorrect 
        ? `+${positiveMark}` 
        : `-${negativeMark}`}
    </span>
  );
};

const OptionItem = ({ option, image, isSelected, isCorrect, hasImage }) => {
  const textColor = isSelected 
    ? (isCorrect ? 'text-green-700' : 'text-red-700') 
    : 'text-gray-700';
  
  return (
    <div className={`flex items-start p-2 rounded border text-sm ${
      isSelected ? (isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50') : 'border-gray-200'
    }`}>
      <div className={`flex items-center h-4 mt-0.5 ${
        isSelected ? (isCorrect ? 'text-green-600' : 'text-red-600') : 'text-gray-400'
      }`}>
        <input 
          type="checkbox" 
          checked={isSelected} 
          readOnly 
          className="w-3.5 h-3.5 rounded"
        />
      </div>
      <div className="ml-2">
        {hasImage ? (
          <div className="flex items-center gap-2">
            <img 
              src={image} 
              className="max-w-[80px] rounded border border-gray-200" 
              alt="Option" 
            />
            <span className={textColor}>{option}</span>
          </div>
        ) : (
          <span className={textColor}>{option}</span>
        )}
      </div>
    </div>
  );
};

const OnlineExamDetails = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const data = location.state?.exam;
  const [selectedBatch, setSelectedBatch] = useState("");
  const [zoomImage, setZoomImage] = useState(null);
  const [showImageModal, setShowImageModal] = useState(false);
  const { id } = useParams();
  const ASSET_URL = import.meta.env.VITE_ASSET_HOST_URL;

  const {
    data: exam,
    isLoading: isExamLoading,
    refetch,
  } = useGetApiQuery(
    selectedBatch
      ? `admin/offline-exams/${id}?batch_id=${selectedBatch}`
      : `admin/student-quiz-result-details-by-id/${id}`
  );

  const questions = exam?.questions;
  const mcqQuestions = exam?.mcq_questions || [];
  const trueFalseQuestions = exam?.true_false_questions || [];
  const fillInBlankQuestions = exam?.fill_in_blank_questions || [];
  const matchingQuestions = exam?.matching_questions || [];

  // Helper function to handle image clicks
  const handleImageClick = (imageUrl) => {
    setZoomImage(imageUrl);
    setShowImageModal(true);
  };

  useEffect(() => {
    if (questions && !selectedBatch) {
      setSelectedBatch(questions[0]?.batch_id);
    }
  }, [questions]);

  const details = [
    {
      label: "Student",
      value: (
        <div className="flex items-center">
          {exam?.student_image && (
            <img 
              src={ASSET_URL + exam?.student_image} 
              className="w-6 h-6 rounded-full mr-1" 
              alt="Student" 
            />
          )}
          <span className="truncate">{exam?.student_name}</span>
        </div>
      ),
      icon: "uiw:user",
    },
    {
      label: "Exam Date",
      value: new Date(exam?.created_at).toLocaleDateString('en-GB', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
      }),
      icon: "uiw:date",
    },
    {
      label: "Duration",
      value: `${exam?.duration} mins`,
      icon: "mingcute:time-duration-line",
    },
    {
      label: "Marks",
      value: exam?.mark,
      icon: "ix:certificate-success",
    },
    {
      label: "Questions",
      value: exam?.number_of_question,
      icon: "mdi:format-list-bulleted",
    },
    {
      label: "Written Mark",
      value: exam?.written_result?.mark 
        ? `${exam?.written_result?.mark}/${exam?.written_question?.marks}` 
        : '--/' + exam?.written_question?.marks,
      icon: "material-symbols-light:edit-note-outline",
    },
    {
      label: "Total Mark",
      value: exam?.exam_mark,
      icon: "mdi:chart-bar",
    },
  ];

  if (!exam) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <p className="text-gray-500 text-sm">No exam details available</p>
          <button 
            onClick={() => navigate(-1)} 
            className="mt-3 px-3 py-1.5 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="px-3 ">
      {/* Header Section */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm mb-4">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
          <div className="mb-2 sm:mb-0">
            <h1 className="text-lg font-bold text-gray-800">{exam?.title} </h1>
            <p className="text-gray-600 text-sm">{exam?.course_title}</p>
          </div>
          <button 
            onClick={() => navigate(-1)} 
            className="px-3 py-1.5 bg-gray-100 text-gray-700 text-sm rounded hover:bg-gray-200 transition flex items-center"
          >
            <Icon icon="heroicons-outline:arrow-left" className="mr-1.5 text-base" />
            Back
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-7 gap-2 mb-4">
        {details.map((item, idx) => (
          <StatCard key={idx} detail={item} exam={exam} />
        ))}
      </div>

      {/* Questions Sections */}
      <div className="space-y-5">
        {/* Written Question */}
        {exam?.written_question && (
          <div>
            <div className="flex items-center mb-3">
              <div className="w-1.5 h-6 bg-indigo-600 rounded mr-2"></div>
              <h3 className="text-base font-bold text-gray-800">Written Question</h3>
              <span className="ml-auto bg-indigo-100 text-indigo-800 text-xs px-2 py-0.5 rounded">
                {exam?.written_question?.marks} marks
              </span>
            </div>

            <div className="grid lg:grid-cols-1 gap-6">
              <WrittenQuestion
                question={exam?.written_question}
                answers={exam?.written_answers}
                result={exam?.written_result}
              />
            </div>
          </div>
        )}
        {/* MCQ Questions */}
        {mcqQuestions?.length > 0 && (
          <div>
            <div className="flex items-center mb-3">
              <div className="w-1.5 h-6 bg-blue-600 rounded mr-2"></div>
              <h3 className="text-base font-bold text-gray-800">Multiple Choice</h3>
              <span className="ml-auto bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded">
                {mcqQuestions.length}
              </span>
            </div>
            
            <div className="grid lg:grid-cols-2 gap-6">
              {mcqQuestions.map((question, index) => {
                const unanswered = !question.answer1 && !question.answer2 && !question.answer3 && !question.answer4;
                const isCorrect = question.correct_answer1 == question.answer1 &&
                                 question.correct_answer2 == question.answer2 &&
                                 question.correct_answer3 == question.answer3 &&
                                 question.correct_answer4 == question.answer4;
                
                return (
                  <QuestionCard key={index} isCorrect={isCorrect} unanswered={unanswered}>
                    <div className="space-y-3">
                      <div className="flex justify-between items-start">
                        <div className="flex-1 pr-2">
                          <p className="font-medium text-gray-800">
                            <span className="text-blue-600">Q{index + 1}:</span> {question.question_text}
                          </p>
                          {question?.question_image && (
                            <img
                              src={ASSET_URL + question?.question_image}
                              className="mt-1 max-w-full h-auto rounded border border-gray-200 cursor-pointer hover:shadow-md transition-shadow"
                              alt="Question"
                              onClick={() => handleImageClick(ASSET_URL + question?.question_image)}
                            />
                          )}
                        </div>
                        <AnswerBadge 
                          isCorrect={isCorrect} 
                          unanswered={unanswered}
                          positiveMark={exam?.positive_mark}
                          negativeMark={exam?.negative_mark}
                        />
                      </div>
                      
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                        {[1, 2, 3, 4].map((num) => (
                          question[`option${num}`] && (
                            <OptionItem
                              key={num}
                              option={question[`option${num}`]}
                              image={question[`option${num}_image`] ? ASSET_URL + question[`option${num}_image`] : null}
                              isSelected={question[`answer${num}`]}
                              isCorrect={question[`correct_answer${num}`]}
                              hasImage={!!question[`option${num}_image`]}
                            />
                          )
                        ))}
                      </div>
                      
                      {unanswered ? (
                        <p className="text-red-500 font-medium text-xs">
                          <Icon icon="heroicons-outline:exclamation-circle" className="inline mr-1" />
                          Not answered
                        </p>
                      ) : (
                        <div className="space-y-1.5">
                          <div className="p-2 bg-green-50 rounded border border-green-100">
                            <p className="text-green-700 font-medium text-xs">
                              <Icon icon="heroicons-outline:check-circle" className="inline mr-1" />
                              Correct: {[1, 2, 3, 4]
                                .filter((i) => question[`correct_answer${i}`])
                                .map((i) => question[`option${i}`])
                                .join(', ')}
                            </p>
                          </div>
                          
                          {question.explanation_text && (
                            <div className="p-2 bg-blue-50 rounded border border-blue-100">
                              <p className="text-blue-800 font-medium text-xs">Explanation:</p>
                              <p className="text-gray-700 text-xs">{question.explanation_text}</p>
                              {question.explanation_image && (
                                <img
                                  src={ASSET_URL + question.explanation_image}
                                  className="mt-1 max-w-full h-auto rounded border border-gray-200 cursor-pointer hover:shadow-md transition-shadow"
                                  alt="Explanation"
                                  onClick={() => handleImageClick(ASSET_URL + question.explanation_image)}
                                />
                              )}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </QuestionCard>
                );
              })}
            </div>
          </div>
        )}

        {/* True/False Questions */}
        {trueFalseQuestions?.length > 0 && (
          <div>
            <div className="flex items-center mb-3">
              <div className="w-1.5 h-6 bg-green-600 rounded mr-2"></div>
              <h3 className="text-base font-bold text-gray-800">True/False</h3>
              <span className="ml-auto bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">
                {trueFalseQuestions.length}
              </span>
            </div>
            
            <div className="grid lg:grid-cols-2 gap-6">
              {trueFalseQuestions.map((question, index) => (
                <QuestionCard 
                  key={index} 
                  isCorrect={question.is_correct} 
                  unanswered={question.user_answer === null}
                >
                  <div className="space-y-3">
                    <div className="flex justify-between items-start">
                      <div className="flex-1 pr-2">
                        <p className="font-medium text-gray-800">
                          <span className="text-green-600">Q{index + 1}:</span> {question.question_text}
                        </p>
                      </div>
                      <AnswerBadge 
                        isCorrect={question.is_correct} 
                        unanswered={question.user_answer === null}
                        positiveMark={exam?.positive_mark}
                        negativeMark={exam?.negative_mark}
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-2">
                      <div className={`p-2 rounded border ${
                        question.user_answer === true 
                          ? (question.is_correct ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50')
                          : 'border-gray-200'
                      }`}>
                        <label className="flex items-center space-x-2">
                          <input 
                            type="radio" 
                            checked={question.user_answer === true}
                            readOnly
                            className="w-3.5 h-3.5 text-blue-600"
                          />
                          <span className={`text-sm ${
                            question.user_answer === true 
                              ? (question.is_correct ? 'text-green-700' : 'text-red-700')
                              : 'text-gray-700'
                          }`}>True</span>
                        </label>
                      </div>
                      
                      <div className={`p-2 rounded border ${
                        question.user_answer === false 
                          ? (question.is_correct ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50')
                          : 'border-gray-200'
                      }`}>
                        <label className="flex items-center space-x-2">
                          <input 
                            type="radio" 
                            checked={question.user_answer === false}
                            readOnly
                            className="w-3.5 h-3.5 text-blue-600"
                          />
                          <span className={`text-sm ${
                            question.user_answer === false 
                              ? (question.is_correct ? 'text-green-700' : 'text-red-700')
                              : 'text-gray-700'
                          }`}>False</span>
                        </label>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-2">
                      <div className="p-2 bg-blue-50 rounded border border-blue-100">
                        <p className="text-blue-700 font-medium text-xs">
                          <Icon icon="heroicons-outline:light-bulb" className="inline mr-1" />
                          Your answer: {question.user_answer === null 
                            ? '---' 
                            : question.user_answer ? 'True' : 'False'}
                        </p>
                      </div>
                      
                      <div className="p-2 bg-green-50 rounded border border-green-100">
                        <p className="text-green-700 font-medium text-xs">
                          <Icon icon="heroicons-outline:check-circle" className="inline mr-1" />
                          Correct: {question.correct_answer ? 'True' : 'False'}
                        </p>
                      </div>
                    </div>
                    
                    {question.explanation_text && (
                      <div className="p-2 bg-gray-50 rounded border border-gray-200">
                        <p className="text-gray-800 font-medium text-xs">Explanation:</p>
                        <p className="text-gray-600 text-xs">{question.explanation_text}</p>
                      </div>
                    )}
                  </div>
                </QuestionCard>
              ))}
            </div>
          </div>
        )}

        {/* Fill in the Blank Questions */}
        {fillInBlankQuestions?.length > 0 && (
          <div>
            <div className="flex items-center mb-3">
              <div className="w-1.5 h-6 bg-purple-600 rounded mr-2"></div>
              <h3 className="text-base font-bold text-gray-800">Fill in Blanks</h3>
              <span className="ml-auto bg-purple-100 text-purple-800 text-xs px-2 py-0.5 rounded">
                {fillInBlankQuestions.length}
              </span>
            </div>

            <div className="grid lg:grid-cols-2 gap-6">
              {fillInBlankQuestions.map((question, index) => (
                  <QuestionCard 
                    key={index} 
                    isCorrect={question.is_correct} 
                    unanswered={!question.answers.some(a => a.user_answer)}
                  >
                    <div className="space-y-3">
                      <div className="flex justify-between items-start">
                        <div className="flex-1 pr-2">
                          <p className="text-md text-gray-800">
                            <span className="text-purple-600">Q{index + 1}:</span> {question.question_text}
                          </p>
                        </div>
                        <AnswerBadge 
                          isCorrect={question.is_correct} 
                          unanswered={!question.answers.some(a => a.user_answer)}
                          positiveMark={exam?.positive_mark}
                          negativeMark={exam?.negative_mark}
                        />
                      </div>
                      
                      <div className="space-y-2">
                        {question.answers.map((answer, ansIndex) => {
                          const isCorrect = answer.user_answer === answer.correct_answer;
                          const unanswered = !answer.user_answer;
                          
                          return (
                            <div key={ansIndex} className="grid grid-cols-3 gap-2">
                              <div className="p-2 bg-white rounded border border-gray-200">
                                <p className="text-xs font-medium text-gray-500">Blank {ansIndex + 1}</p>
                                <p className="text-sm">{answer.blank_text}</p>
                              </div>
                              
                              <div className={`p-2 rounded border ${
                                unanswered 
                                  ? 'border-gray-200 bg-gray-50' 
                                  : isCorrect 
                                    ? 'border-green-200 bg-green-50' 
                                    : 'border-red-200 bg-red-50'
                              }`}>
                                <p className="text-xs font-medium text-gray-500">Your answer</p>
                                <p className={`text-sm ${
                                  unanswered ? 'text-gray-500' : isCorrect ? 'text-green-700' : 'text-red-700'
                                }`}>
                                  {unanswered ? '---' : answer.user_answer}
                                </p>
                              </div>
                              
                              <div className="p-2 bg-green-50 rounded border border-green-200">
                                <p className="text-xs font-medium text-gray-500">Correct</p>
                                <p className="text-sm text-green-700">{answer.correct_answer}</p>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                      
                      {question.explanation_text && (
                        <div className="p-2 bg-gray-50 rounded border border-gray-200">
                          <p className="text-gray-800 font-medium text-xs">Explanation:</p>
                          <p className="text-gray-600 text-xs">{question.explanation_text}</p>
                        </div>
                      )}
                    </div>
                  </QuestionCard>
                ))}
            </div>
          </div>
        )}

        {/* Matching Questions */}
        {matchingQuestions?.length > 0 && (
          <div>
            <div className="flex items-center mb-3">
              <div className="w-1.5 h-6 bg-orange-600 rounded mr-2"></div>
              <h3 className="text-base font-bold text-gray-800">Matching</h3>
              <span className="ml-auto bg-orange-100 text-orange-800 text-xs px-2 py-0.5 rounded">
                {matchingQuestions.length}
              </span>
            </div>

            <div className="grid lg:grid-cols-2 gap-6">
              {matchingQuestions.map((question, index) => (
                  <QuestionCard 
                    key={index} 
                    isCorrect={question.is_correct} 
                    unanswered={!question.matches.some(m => m.selected_right_item)}
                  >
                    <div className="space-y-3">
                      <div className="flex justify-between items-start">
                        <div className="flex-1 pr-2">
                          <p className="font-medium text-gray-800">
                            <span className="text-orange-600">Q{index + 1}:</span> {question.question_text}
                          </p>
                        </div>
                        <AnswerBadge 
                          isCorrect={question.is_correct} 
                          unanswered={!question.matches.some(m => m.selected_right_item)}
                          positiveMark={exam?.positive_mark}
                          negativeMark={exam?.negative_mark}
                        />
                      </div>
                      
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200 text-sm">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Left</th>
                              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Your Match</th>
                              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Correct</th>
                              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"></th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {question.matches.map((match, matchIndex) => {
                              const isCorrect = match.selected_right_item === match.correct_right_item;
                              const unanswered = !match.selected_right_item;
                              
                              return (
                                <tr key={matchIndex}>
                                  <td className="px-3 py-2 whitespace-nowrap">
                                    {match.left_item}
                                  </td>
                                  <td className={`px-3 py-2 whitespace-nowrap ${
                                    unanswered ? 'text-gray-500' : isCorrect ? 'text-green-600' : 'text-red-600'
                                  }`}>
                                    {unanswered ? '---' : match.selected_right_item}
                                  </td>
                                  <td className="px-3 py-2 whitespace-nowrap text-green-600">
                                    {match.correct_right_item}
                                  </td>
                                  <td className="px-3 py-2 whitespace-nowrap">
                                    {unanswered ? (
                                      <span className="px-1.5 py-0.5 text-xs rounded-full bg-gray-100 text-gray-800">
                                        ---
                                      </span>
                                    ) : isCorrect ? (
                                      <span className="px-1.5 py-0.5 text-xs rounded-full bg-green-100 text-green-800">
                                        ✓
                                      </span>
                                    ) : (
                                      <span className="px-1.5 py-0.5 text-xs rounded-full bg-red-100 text-red-800">
                                        ✗
                                      </span>
                                    )}
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                      
                      {question.explanation_text && (
                        <div className="p-2 bg-gray-50 rounded border border-gray-200">
                          <p className="text-gray-800 font-medium text-xs">Explanation:</p>
                          <p className="text-gray-600 text-xs">{question.explanation_text}</p>
                        </div>
                      )}
                    </div>
                  </QuestionCard>
                ))}
            </div>
          </div>
        )}
      </div>

      {/* Image Zoom Modal */}
      <Modal
        activeModal={showImageModal}
        onClose={() => {
          setShowImageModal(false);
          setZoomImage(null);
        }}
        title="Image Preview"
        className="max-w-4xl"
        centered={true}
        themeClass="bg-slate-900 dark:bg-slate-800"
      >
        {zoomImage && (
          <div className="flex justify-center items-center">
            <img
              src={zoomImage}
              alt="Zoomed Image"
              className="max-w-full max-h-[70vh] object-contain rounded"
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default OnlineExamDetails;