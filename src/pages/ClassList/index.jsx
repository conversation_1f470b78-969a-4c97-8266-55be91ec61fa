import React, { useState, useMemo, useEffect } from "react";
import { Calendar, momentLocalizer } from "react-big-calendar";
import moment from "moment";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import CreateClass from "./createClass";
import EditClass from "./editClass";
import Delete from "./Delete";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import Icon from "@/components/ui/Icon";
import Select from "react-select";

const localizer = momentLocalizer(moment);

const ClassList = () => {
  const dispatch = useDispatch();
  const { showModal } = useSelector((state) => state.commonReducer);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [classId, setClassId] = useState(null);
  const [contextMenu, setContextMenu] = useState(null);
  const [eventMenu, setEventMenu] = useState(null);
  const [deleteItem, setDeleteItem] = useState(null);
  const [queryParam, setQueryParam] = useState("");
  const [selectedCourseId, setSelectedCourseId] = useState("");
  const [currentDateRange, setCurrentDateRange] = useState({
    startDate: moment().startOf("month").format("YYYY-MM-DD"),
    endDate: moment().endOf("month").format("YYYY-MM-DD")
  });

  // Helper function to build query parameters
  const buildQueryParams = (startDate, endDate, courseId = selectedCourseId) => {
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    if (courseId) params.append('course_id', courseId);
    return params.toString() ? `?${params.toString()}` : '';
  };

  // Set initial query parameters for current month
  useEffect(() => {
    // Use current date range if available, otherwise use current month
    const initialQueryParam = buildQueryParams(currentDateRange.startDate, currentDateRange.endDate);
    setQueryParam(initialQueryParam);
  }, [selectedCourseId, currentDateRange.startDate, currentDateRange.endDate]); // Re-run when course selection changes

  // Fetch data using RTK Query
  const { data: res, isLoading, isError, error } = useGetApiQuery("admin/class-schedules" + queryParam);

  // Fetch courses for the filter dropdown
  const { data: coursesData } = useGetApiQuery("admin/course-list-for-filter");

  const events = useMemo(() => {
    return res?.map((schedule) => ({
      id: schedule.id,
      title: `${schedule.title} - ${schedule.mentor_name}`,
      start: new Date(schedule.schedule_datetime),
      end: new Date(new Date(schedule.schedule_datetime).getTime() + schedule.duration * 60000),
      resource: schedule,
    }));
  }, [res]);

  const handleSlotSelect = (slotInfo) => {
    setSelectedDate(slotInfo.start);
    dispatch(setShowModal(true));
  };

  const handleRangeChange = (range, view) => {
    let start, end;

    if (view === "month" && Array.isArray(range)) {
      start = moment(range[0]).startOf("month").format("YYYY-MM-DD");
      end = moment(range[range.length - 1]).endOf("month").format("YYYY-MM-DD");
    } else if (view === "week" && Array.isArray(range)) {
      start = moment(range[0]).format("YYYY-MM-DD");
      end = moment(range[range.length - 1]).format("YYYY-MM-DD");
    } else if (view === "day" && range instanceof Date) {
      start = moment(range).format("YYYY-MM-DD");
      end = moment(range).format("YYYY-MM-DD");
    } else {
      // Fallback for other cases
      start = moment(range.start || range[0]).startOf("month").format("YYYY-MM-DD");
      end = moment(range.end || range[range.length - 1]).endOf("month").format("YYYY-MM-DD");
    }

    console.log("Date range:", start, "to", end);

    // Update current date range state
    setCurrentDateRange({ startDate: start, endDate: end });

    // Update query parameters
    const newQueryParam = buildQueryParams(start, end);
    setQueryParam(newQueryParam);
  };
  // Handle course filter change
  const handleCourseChange = (selectedOption) => {
    setSelectedCourseId(selectedOption ? selectedOption.value : "");
  };

  // Prepare course options for the dropdown
  const courseOptions = useMemo(() => {
    if (!coursesData) return [];
    return [
      { value: "", label: "All Courses" },
      ...coursesData.map(course => ({
        value: course.id.toString(),
        label: course.title || course.name
      }))
    ];
  }, [coursesData]);

  const handleRightClick = (slotInfo, e) => {
    e.preventDefault();
    setSelectedDate(slotInfo.start);
    setContextMenu({
      mouseX: e.clientX + 2,
      mouseY: e.clientY + 2,
    });
  };

  const closeContextMenu = () => setContextMenu(null);

  const handleCreateClass = () => {
    dispatch(setShowModal(true));
    closeContextMenu();
  };

  const handleEventSelect = (event, e) => {
    e.preventDefault();
  
    // Calculate the dropdown position relative to the document
    setEventMenu({
      mouseX: e.clientX + window.scrollX + 2, // Adjust for horizontal scroll
      mouseY: e.clientY + window.scrollY + 2, // Adjust for vertical scroll
      eventId: event.id,
    });
  };
  

  const closeEventMenu = () => setEventMenu(null);

  const handleEdit = () => {
    setClassId(eventMenu.eventId);
    setShowEditModal(true);
    closeEventMenu();
  };

  const handleDelete = () => {
    let item = res.find((item) => item.id === eventMenu.eventId);
    setDeleteItem(item);
    setShowDeleteModal(true);
    closeEventMenu();
  };

  useEffect(() => {
    if (!showEditModal) setClassId(null);
  }, [showEditModal]);

  return (
    <div style={{ margin: "20px" }}>
      <div className="flex justify-between items-center mb-4">
        <div>
          <h2 className="text-2xl font-semibold mb-1">Class Routine</h2>
          <p className="text-sm text-red-400">Click on an empty date slot to create a new class.</p>
        </div>

        {/* Course Filter Dropdown */}
        <div className="w-64">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
            Filter by Course
          </label>
          <Select
            options={courseOptions}
            value={courseOptions.find(option => option.value === selectedCourseId)}
            onChange={handleCourseChange}
            placeholder="Search and select a course..."
            isSearchable={true}
            isClearable={true}
            className="react-select"
            classNamePrefix="select"
            styles={{
              control: (base) => ({
                ...base,
                minHeight: '38px',
                borderColor: '#d1d5db',
                '&:hover': {
                  borderColor: '#9ca3af'
                }
              }),
              menu: (base) => ({
                ...base,
                zIndex: 9999
              })
            }}
          />
        </div>
      </div>

      {isLoading && <div>Loading...</div>}
      {isError && <div>Error: {error.message}</div>}
      {!isLoading && !isError && (
        <div style={{ height: "800px" }} onClick={closeContextMenu}>
          <Calendar
            localizer={localizer}
            events={events}
            startAccessor="start"
            endAccessor="end"
            style={{ height: 800 }}
            selectable
            onSelectSlot={handleSlotSelect}
            onSelectEvent={(event, e) => handleEventSelect(event, e)}
            onRangeChange={handleRangeChange}
            views={["month", "week", "day"]}
            defaultView="month"
            defaultDate={new Date()}
            dayLayoutAlgorithm="no-overlap"
          />
        </div>
      )}

      {/* Custom Context Menu */}
      {contextMenu && (
        <div
          style={{
            position: "absolute",
            top: `${contextMenu.mouseY}px`,
            left: `${contextMenu.mouseX}px`,
            backgroundColor: "white",
            border: "1px solid #ddd",
            boxShadow: "0 2px 8px rgba(0,0,0,0.2)",
            borderRadius: "4px",
            zIndex: 1000,
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <button
            style={{
              display: "block",
              padding: "8px 12px",
              background: "transparent",
              border: "none",
              width: "100%",
              textAlign: "left",
              cursor: "pointer",
            }}
            onClick={handleCreateClass}
          >
            Create Class
          </button>
        </div>
      )}

      {/* Event Dropdown */}
      {eventMenu && (
        <div
          style={{
            position: "absolute",
            top: `${eventMenu.mouseY}px`,
            left: `${eventMenu.mouseX}px`,
            backgroundColor: "white",
            border: "1px solid #ddd",
            boxShadow: "0 2px 8px rgba(0,0,0,0.2)",
            borderRadius: "4px",
            zIndex: 1000,
            minWidth: "150px",
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <button
            style={{
              display: "flex",
              alignItems: "center",
              padding: "8px 12px",
              background: "transparent",
              border: "none",
              width: "100%",
              textAlign: "left",
              cursor: "pointer",
            }}
            onClick={handleEdit}
          >
            <Icon icon="heroicons-outline:pencil-square" className="mr-2" />
            Edit
          </button>
          <button 
            style={{
              display: "flex",
              alignItems: "center",
              padding: "8px 12px",
              background: "transparent",
              border: "none",
              width: "100%",
              textAlign: "left",
              cursor: "pointer",
              color: "red",
            }}
            onClick={handleDelete}
          >
            <Icon icon="heroicons-outline:trash" className="mr-2" />
            Delete
          </button>
        </div>
      )}

      {/* Modals */}
      {showModal && <CreateClass selectedDate={selectedDate} />}
      {showEditModal && <EditClass showModal={showEditModal} setShowModal={setShowEditModal} id={classId} />}
      {showDeleteModal && <Delete showDeleteModal={showDeleteModal} setShowDeleteModal={setShowDeleteModal} data={deleteItem} />}
    </div>
  );
};

export default ClassList;
