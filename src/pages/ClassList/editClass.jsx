import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import FileInput from "@/components/ui/Fileinput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { Formik, Form, ErrorMessage } from "formik";
import { validationSchema } from "./formClass";
import { useDispatch, useSelector } from "react-redux";
import Select from '@/components/ui/Select';
import { useGetApiQuery, useUpdateApiMutation } from "@/store/api/master/commonSlice";
import DateTimePicker from "@/components/ui/DateTimePicker";

const EditClass = ({ showModal, setShowModal, id }) => {

  const dispatch = useDispatch();
  const [courseId, setCourseId] = useState(null);
  const [isPhysical, setIsPhysical] = useState(true);
  const [mentorList, setMentorList] = useState([]);

  const { data: courseList, isLoading: courseLoading } = useGetApiQuery(
    `admin/course-list-for-filter`
  );

  const { data: classDetail, isLoading: classLoading } = useGetApiQuery(
    `admin/class-schedules/${id}`
  );

  const { data: batches, isLoading: batchLoading } = useGetApiQuery(
    `admin/batches?pagination=false&is_active=1&mentor=1&course_id=${classDetail?.course_id || courseId}`
  );

  React.useEffect(() => {
    if (classDetail && batches) {
      const selectedBatch = batches.find((batch) => batch.id === classDetail.batch_id);
      console.log(selectedBatch);
      if (selectedBatch) {
        setMentorList(selectedBatch.mentors);
      }
    }
  }, [classDetail, batches]);

  const initialValues = {
    id: classDetail?.id,
    title: classDetail?.title,
    schedule_datetime: classDetail?.schedule_datetime,
    duration: (new Date(classDetail?.end_time) - new Date(classDetail?.start_time)) / 60000,
    batch_id: classDetail?.batch_id,
    course_id: classDetail?.course_id,
    mentor_id: classDetail?.mentor_id,
    class_url: classDetail?.class_url ? classDetail?.class_url : "",
    is_physical: classDetail?.is_physical ? true : false
  }

  const [updateApi, { isLoading: isPostLoading }] = useUpdateApiMutation();



  const onSubmit = async (values, { resetForm }) => {

    console.log(values);

    const date = new Date(values.schedule_datetime);
    date.setHours(date.getHours() + 6);
    values.schedule_datetime = date.toISOString().slice(0, 19).replace('T', ' ');

    const formData = new FormData();
    Object.keys(values).forEach((key) => {
      if (key === "is_physical") {
        formData.append(key, values[key] ? 1 : 0);
      } else {
        formData.append(key, values[key]);
      }
    });
    
    const response = await updateApi({
      end_point: `admin/update-live-class-schedule`,
      body: formData,
    });
    
    if (response?.error) {
      setErrors(response.error?.data.errors);
    } else {
      resetForm();
      dispatch(setShowModal(false));
    }
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={() => dispatch(setShowModal(false))}
      title="Update Class"
      className="max-w-4xl p-6"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setShowModal(false))}
        />
      }
    >

      { classLoading || batchLoading || courseLoading ? <div>Loading...</div>  : 
      <Formik
        validationSchema={validationSchema}
        initialValues={initialValues}
        onSubmit={onSubmit}
      >
        {({ values, setFieldValue, errors }) => (
          <Form className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <InputField
                label="Class Title"
                name="title"
                type="text"
                placeholder="Live Class Name"
                required
              />
              { courseLoading ? <div>Loading...</div> : 
              <div>
                <label className="block text-gray-600 text-sm font-medium mb-2">
                  Course<span className="text-red-500"> *</span>
                </label>
                <Select
                  placeholder="Select Course"
                  options={
                    courseList?.map((course) => ({
                      value: course.id,
                      label: course.title,
                    }))
                  }
                  name="course_id"
                  onChange={(e) => {
                    setCourseId(e.value);
                    setFieldValue("course_id", e.value);
                  }}
                />
                <ErrorMessage
                  name="course_id"
                  component="div"
                  className="text-red-500 text-xs mt-1"
                />
              </div>
              }
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              {batchLoading ? <div>Loading...</div> :
              <div>
                <label className="block text-gray-600 text-sm font-medium mb-2">
                  Batch<span className="text-red-500"> *</span>
                </label>
                <Select
                  defaultValue={classDetail?.batch_id}
                  placeholder="Select Batch"
                  options={
                    batches?.map((batch) => ({
                      value: batch.id,
                      label: batch.name,
                    }))
                  }
                  name="batch_id"
                  onChange={(selected) => {
                    const batchId = selected.target.value;
                    setFieldValue("batch_id", batchId);
                    const selectedBatch = batches?.find((batch) => batch.id == batchId);
                    setMentorList(selectedBatch?.mentors || []);
                  }}
                />
                <ErrorMessage
                  name="batch_id"
                  component="div"
                  className="text-red-500 text-xs mt-1"
                />
              </div>
              }

              <div>
                <label className="block text-gray-600 text-sm font-medium mb-2">
                  Select Mentor<span className="text-red-500"> *</span>
                </label>
                <Select
                  placeholder="Select Mentor"
                  options={
                    mentorList?.map((mentor) => ({
                      value: mentor.mentor_id,
                      label: mentor.name,
                    }))}
                  name="mentor_id"
                  onChange={(e) => setFieldValue("mentor_id", e.target.value)}
                />
                <ErrorMessage
                  name="mentor_id"
                  component="div"
                  className="text-red-500 text-xs mt-1"
                />
              </div>

              <DateTimePicker
                time={1}
                label="Class Date & Time"
                placeholder="Select Date & Time"
                name="schedule_datetime"
                required
              />


              <InputField
                label="Duration"
                name="duration"
                type="text"
                placeholder="Class Duration (e.g., 1hr 30min)"
                required
              />
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div >
                <Switch
                  label={`Class Mode: ${values.is_physical ? "Physical" : "Live Class"}`}
                  activeClass="bg-success-500"
                  name="is_physical"
                  value={values.is_physical}
                  onChange={() => setFieldValue("is_physical", !values.is_physical)}
                />
              </div>
            {!values.is_physical && (
              <InputField
                name="class_url"
                type="text"
                placeholder="Enter Class URL"
              />
            )}

            </div>

            <div className="flex justify-end">
              <Button
                isLoading={isPostLoading}
                type="submit"
                className="btn btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
      }
    </Modal>
  );
};

export default EditClass;

