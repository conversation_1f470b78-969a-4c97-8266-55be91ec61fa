import React from 'react';
import { Link } from 'react-router-dom';
const RegistrationSuccessful = () => {

    return (
        <div className="max-w-7xl mx-auto">
            <div className="flex flex-col items-center justify-center min-h-[390px] bg-white p-6">
                <div className="flex items-center justify-center w-12 h-12 mb-4 overflow-hidden bg-gray-100 rounded-full">
                    <svg className="w-full text-green-500" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                </div>
                <h3 className="mb-2 text-2xl font-medium text-center text-gray-900">Registration successful</h3>
                <p className="text-md mb-6 text-center text-green-500">You've successfully registered. Please check your email.</p>
               
                {/* <div className="mt-6">
                    <Link to="/login" className="inline-flex items-center justify-center w-full h-12 px-6 font-medium tracking-wide text-gray-900 transition duration-200 rounded shadow-md bg-white hover:bg-gray-100 focus:shadow-outline focus:outline-none">
                        Go to Admin Panel
                    </Link>
                </div> */}
            </div>
        </div>
    )
}

export default RegistrationSuccessful;