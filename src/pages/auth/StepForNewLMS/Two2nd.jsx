import React from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import { useDispatch } from "react-redux";
import { setLogo, setBanner } from "@/pages/auth/StepForNewLMS/store";
import { Icon } from "@iconify/react";
import { motion } from "framer-motion";

const Two = ({ setPercent, createData, dataToSubmit }) => {
  const dispatch = useDispatch();

  const initialValues = {
    headline: dataToSubmit?.headline || "",
    sub_headline: dataToSubmit?.sub_headline || "",
    logo: dataToSubmit?.logo || "",
    banner: dataToSubmit?.banner || "",
  };

  const validationSchema = Yup.object().shape({
    headline: Yup.string().required("Headline is required")
    .max(255, "Sub Headline must be less than or equal to 255 characters"),
    sub_headline: Yup.string()
      .required("Sub Headline is required")
      .max(255, "Sub Headline must be less than or equal to 255 characters"),
    logo: Yup.mixed()
      .required("Upload Logo")
      .test("is-image", "File must be an image", (value) => {
        if (!value) return true;
        return value?.type.startsWith("image/");
      }),
    banner: Yup.mixed()
      .required("Banner is required")
      .test("is-image", "File must be an image", (value) => {
        if (!value) return true;
        return value?.type.startsWith("image/");
      }),
  });

  const onSubmit = (values) => {
    createData(values);
    setPercent(75);
  };

  const fadeIn = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 },
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 p-4 sm:p-6 lg:p-8 relative overflow-hidden">
      {/* Background Shapes - Responsive */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-48 sm:w-72 lg:w-96 h-48 sm:h-72 lg:h-96 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute top-0 right-0 w-48 sm:w-72 lg:w-96 h-48 sm:h-72 lg:h-96 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
        <div className="absolute -bottom-8 left-20 w-48 sm:w-72 lg:w-96 h-48 sm:h-72 lg:h-96 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
        <div className="absolute -top-1/4 left-1/3 w-48 sm:w-72 lg:w-96 h-48 sm:h-72 lg:h-96 border-2 border-blue-100 rounded-full opacity-20 rotate-45"></div>
        <div className="absolute top-1/2 right-1/4 w-32 sm:w-48 lg:w-64 h-32 sm:h-48 lg:h-64 border-2 border-purple-100 rounded-full opacity-20"></div>
      </div>

      <motion.div
        className="max-w-xl sm:max-w-2xl lg:max-w-4xl mx-auto bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden relative z-10"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="relative p-4 sm:p-6 lg:p-8">
          {/* Header Section */}
          <motion.div className="text-center mb-6 sm:mb-8 lg:mb-12" {...fadeIn}>
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Customize Your LMS
            </h2>
            <p className="mt-2 sm:mt-3 text-sm sm:text-base text-gray-600">
              Create an engaging learning experience with your brand identity
            </p>
          </motion.div>

          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={onSubmit}
          >
            {({ setFieldValue, values, errors, touched, isValid }) => (
              <Form className="space-y-6 sm:space-y-8">
                {/* Logo Upload Section */}
                <motion.div className="flex flex-col items-center" {...fadeIn}>
                  <div className="relative">
                    <label
                      htmlFor="logo"
                      className={`group relative w-24 sm:w-28 lg:w-32 h-24 sm:h-28 lg:h-32 border-3 border-dashed transition-all duration-300 flex items-center justify-center cursor-pointer overflow-hidden rounded-lg
                        ${
                          values.logo
                            ? "border-green-200 bg-green-50"
                            : touched.logo && errors.logo
                            ? "border-red-200 bg-red-50"
                            : "border-blue-200 bg-blue-50"
                        } 
                        hover:border-blue-400`}
                    >
                      {values.logo ? (
                        <>
                          <img
                            src={URL.createObjectURL(values.logo)}
                            alt="Logo Preview"
                            className="w-full h-20 object-contained rounded-md"
                          />
                          <button
                            type="button"
                            onClick={(e) => {
                              e.preventDefault();
                              setFieldValue("logo", "");
                            }}
                            className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                          >
                            <Icon icon="mdi:close" className="w-3 sm:w-4" />
                          </button>
                        </>
                      ) : (
                        <div className="text-center">
                          <Icon
                            icon="solar:gallery-add-bold-duotone"
                            className="w-8 sm:w-10 lg:w-12 h-8 sm:h-10 lg:h-12 text-blue-400 mx-auto group-hover:text-blue-600 transition-colors"
                          />
                          <p className="text-xs sm:text-sm text-blue-500 mt-2">
                            Add a Logo<span className="text-red-600">*</span> 
                          </p>
                        </div>
                      )}
                    </label>
                    <input
                      type="file"
                      id="logo"
                      name="logo"
                      accept="image/*"
                      className="hidden"
                      onChange={(e) => setFieldValue("logo", e.target.files[0])}
                    />
                    <ErrorMessage
                      name="logo"
                      component="div"
                      className="text-red-500 text-xs sm:text-sm mt-2 text-center"
                    />
                  </div>
                </motion.div>

                {/* Form Fields */}
                <div className="space-y-4 sm:space-y-6">
                  <motion.div {...fadeIn}>
                    <div className="relative">
                      <label
                        htmlFor="headline"
                        className="block text-sm sm:text-base text-gray-700 mb-1"
                      >
                        Headline<span className="text-red-600">*</span>
                      </label>
                      <Field
                        name="headline"
                        type="text"
                        className="w-full px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base rounded-lg border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-300 text-gray-800"
                        placeholder="Enter headline"
                      />
                      <ErrorMessage
                        name="headline"
                        component="div"
                        className="text-red-500 text-xs sm:text-sm mt-1"
                      />
                    </div>
                  </motion.div>

                  <motion.div {...fadeIn}>
                    <div className="relative">
                      <label
                        htmlFor="sub_headline"
                        className="block text-sm sm:text-base text-gray-700 mb-1"
                      >
                        Sub Headline<span className="text-red-600">*</span>
                      </label>
                      <Field
                        as="textarea"
                        name="sub_headline"
                        className="w-full px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base rounded-lg border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-300 text-gray-800 min-h-[80px] sm:min-h-[120px]"
                        placeholder="Enter sub-headline"
                      />
                      <ErrorMessage
                        name="sub_headline"
                        component="div"
                        className="text-red-500 text-xs sm:text-sm mt-1"
                      />
                    </div>
                  </motion.div>

                  {/* Banner Upload */}
                  <motion.div {...fadeIn}>
                    <div className="relative">
                      <label
                        htmlFor="banner"
                        className={`group relative block w-full h-32 sm:h-40 lg:h-64 rounded-xl border-3 border-dashed transition-all duration-300 cursor-pointer overflow-hidden
                          ${
                            values.banner
                              ? "border-green-200 bg-green-50"
                              : touched.banner && errors.banner
                              ? "border-red-200 bg-red-50"
                              : "border-blue-200 bg-blue-50"
                          }
                          hover:border-blue-400`}
                      >
                        {values.banner ? (
                          <>
                            <img
                              src={URL.createObjectURL(values.banner)}
                              alt="Banner Preview"
                              className="w-full h-full object-contained"
                            />
                            <button
                              type="button"
                              onClick={(e) => {
                                e.preventDefault();
                                setFieldValue("banner", "");
                                dispatch(setBanner(null));
                              }}
                              className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1.5 hover:bg-red-600 transition-colors"
                            >
                              <Icon icon="mdi:close" className="w-4 sm:w-5" />
                            </button>
                          </>
                        ) : (
                          <div className="flex flex-col items-center justify-center h-full">
                            <Icon
                              icon="solar:gallery-wide-bold-duotone"
                              className="w-12 sm:w-14 lg:w-16 h-12 sm:h-14 lg:h-16 text-blue-400 group-hover:text-blue-600 transition-colors"
                            />
                            <p className="text-sm sm:text-base text-blue-500 mt-2 sm:mt-4">
                              Upload Banner Image
                              <span className="text-red-600">*</span>
                            </p>
                          </div>
                        )}
                      </label>
                      <input
                        type="file"
                        id="banner"
                        name="banner"
                        accept="image/*"
                        className="hidden"
                        onChange={(e) => {
                          const file = e.target.files[0];
                          setFieldValue("banner", file);
                          dispatch(setBanner(file));
                        }}
                      />
                      <ErrorMessage
                        name="banner"
                        component="div"
                        className="text-red-500 text-xs sm:text-sm mt-2"
                      />
                    </div>
                  </motion.div>
                </div>

                {/* Navigation Buttons */}
                <motion.div
                  className="flex justify-between pt-6 sm:pt-8"
                  {...fadeIn}
                >
                  <button
                    type="button"
                    onClick={() => setPercent(25)}
                    className="flex items-center px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors duration-300"
                  >
                    <Icon
                      icon="solar:arrow-left-linear"
                      className="mr-2 w-4 sm:w-5 h-4 sm:h-5"
                    />
                    Back
                  </button>
                  <button
                    type="submit"
                    className="flex items-center px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base rounded-lg transition-all duration-300 bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:shadow-lg hover:scale-105"
                  >
                    Next
                    <Icon
                      icon="solar:arrow-right-linear"
                      className="ml-2 w-4 sm:w-5 h-4 sm:h-5"
                    />
                  </button>
                </motion.div>
              </Form>
            )}
          </Formik>
        </div>
      </motion.div>
    </div>
  );
};

export default Two;
