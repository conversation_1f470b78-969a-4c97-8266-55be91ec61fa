import React, { useEffect, useState } from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import { Icon } from "@iconify/react";
// import api from '@/server/api';
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { BASE_URL } from "@/config";
import Button from "@/components/ui/Button";
import { toast, ToastContainer } from "react-toastify";

const Three = ({ template, dataToSubmit, setPercent, createData }) => {
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [postApi] = usePostApiMutation();

  const initialValues = {
    user_name: dataToSubmit?.user_name || "",
    user_username: dataToSubmit?.user_username || "",
    user_email: dataToSubmit?.user_email || "",
    contact_no: dataToSubmit?.contact_no || "",
    user_password: "",
    password_confirmation: "",
  };
  console.log(dataToSubmit);

  const validationSchema = Yup.object().shape({
    user_name: Yup.string().required("Please enter your name"),
    contact_no: Yup.string()
      .required("Please enter your phone number")
      .test("is-numeric", "Phone number should only have number", (value) => {
        return /^\d+$/.test(value); // Ensures the phone number consists only of digits
      })
      .matches(
        /^[0-9]{11}$/, // Ensures exactly 11 digits are allowed
        "Phone number must be 11 digits bangladeshi number"
      ),
    user_username: Yup.string().required("Please enter your username"),
    user_email: Yup.string()
      .email("Invalid email format")
      .required("Please enter your email"),
    user_password: Yup.string().required("Please enter your password"),
    password_confirmation: Yup.string()
      .oneOf([Yup.ref("user_password"), null], "Passwords must match")
      .required("Please confirm your password"),
  });

  const onSubmit = async (values, { setSubmitting, setErrors }) => {
    setLoading(true);
    createData(values);
    const formData = new FormData();
    const mergedData = { ...dataToSubmit, ...values };
    Object.keys(mergedData).forEach((key) =>
      formData.append(key, mergedData[key])
    );

    try {
      const response = await postApi({
        end_point: "website/organization-register",
        body: formData,
      });
      if (response.error) {
        setErrors(response?.error?.data.errors);
        if (response?.error?.data?.errors) {
          Object.values(response.error.data.errors)
            .flat()
            .forEach((msg) => toast.warn(msg));
        }
      }
      if (response.data) {
        setPercent(100);
      }
    } catch (error) {
      console.error("Registration error:", error);
    } finally {
      setLoading(false);
      setSubmitting(false);
    }
  };

  // console.log(createData);

  return (
    <div className="relative w-full h-full p-4">
      {/* Background Shapes */}
      <div className="absolute top-0 left-0 w-full h-full z-0">
        <div className="absolute top-1/4 left-0 w-[600px] h-1 bg-gradient-to-r from-blue-500 to-transparent transform rotate-45 origin-left opacity-30"></div>
        <div className="absolute bottom-1/4 right-0 w-full h-1 bg-gradient-to-l from-green-500 to-transparent transform rotate-45 origin-right opacity-30"></div>
        <div className="absolute top-1/2 left-1/4 w-40 h-40 bg-gradient-to-b from-purple-500 to-transparent transform rotate-45 origin-bottom-left opacity-20"></div>
        <div className="absolute top-3/4 left-3/4 w-48 h-48 bg-gradient-to-tl from-yellow-400 to-transparent transform rotate-45 origin-bottom-right opacity-25"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 w-full max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-2">
            Create Your Account
          </h1>
          <p className="text-gray-500">
            To log in and explore the LMS, please create an account by providing
            the required details.
          </p>
        </div>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={onSubmit}
        >
          {({ isSubmitting, values, setFieldValue }) => (
            <Form className="space-y-6">
              <div className="relative w-full mx-auto">
                <Field
                  id="user_name"
                  name="user_name"
                  type="text"
                  value={values.user_name}
                  className={`peer w-full text-sm text-gray-800 bg-transparent border-b border-gray-300 
                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                      ${
                        values.user_name
                          ? "outline-none ring-2 ring-blue-500 border-blue-500"
                          : ""
                      }
                      px-4 py-3.5 rounded-md shadow-sm focus:text-blue-500`}
                  placeholder=""
                  onChange={(e) => {
                    setFieldValue("user_name", e.target.value),
                      createData({ user_name: e.target.value });
                  }}
                />
                <label
                  htmlFor="user_name"
                  className="px-2 bg-white absolute text-sm text-gray-500 left-4 top-0 transform -translate-y-1/2 transition-all duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-gray-500 peer-focus:top-0 peer-focus:text-blue-500 peer-focus:text-xs"
                >
                  Your Name
                </label>
                <ErrorMessage name="user_name">
                  {(msg) => <div className="text-red-500 text-sm">{msg}</div>}
                </ErrorMessage>
                <Icon
                  icon="akar-icons:user"
                  className="w-4 h-4 absolute right-4 top-1/2 transform -translate-y-1/2"
                />
              </div>

              {/* Username */}

              <div className="relative w-full mx-auto">
                <Field
                  id="user_username"
                  name="user_username"
                  type="text"
                  value={values.user_username}
                  className={`peer w-full text-sm text-gray-800 bg-transparent border-b border-gray-300 
                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                      ${
                        values.user_username
                          ? "outline-none ring-2 ring-blue-500 border-blue-500"
                          : ""
                      }
                      px-4 py-3.5 rounded-md shadow-sm focus:text-blue-500`}
                  placeholder=""
                  onChange={(e) => {
                    setFieldValue("user_username", e.target.value),
                      createData({ user_username: e.target.value });
                  }}
                />
                <label
                  htmlFor="user_username"
                  className="px-2 bg-white absolute text-sm text-gray-500 left-4 top-0 transform -translate-y-1/2 transition-all duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-gray-500 peer-focus:top-0 peer-focus:text-blue-500 peer-focus:text-xs"
                >
                  Username
                </label>
                <ErrorMessage name="user_username">
                  {(msg) => <div className="text-red-500 text-sm">{msg}</div>}
                </ErrorMessage>
                <Icon
                  icon="akar-icons:user"
                  className="w-4 h-4 absolute right-4 top-1/2 transform -translate-y-1/2"
                />
              </div>

              <div className="relative w-full mx-auto">
                <Field
                  id="user_email"
                  name="user_email"
                  type="email"
                  value={values.user_email}
                  className={`peer w-full text-sm text-gray-800 bg-transparent border-b border-gray-300 
                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                      ${
                        values.user_email
                          ? "outline-none ring-2 ring-blue-500 border-blue-500"
                          : ""
                      }
                      px-4 py-3.5 rounded-md shadow-sm focus:text-blue-500`}
                  placeholder=""
                  onChange={(e) => {
                    setFieldValue("user_email", e.target.value),
                      createData({ user_email: e.target.value });
                  }}
                />
                <label
                  htmlFor="user_email"
                  className="px-2 bg-white absolute text-sm text-gray-500 left-4 top-0 transform -translate-y-1/2 transition-all duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-gray-500 peer-focus:top-0 peer-focus:text-blue-500 peer-focus:text-xs"
                >
                  Email
                </label>
                <ErrorMessage name="user_email">
                  {(msg) => <div className="text-red-500 text-sm">{msg}</div>}
                </ErrorMessage>
                <Icon
                  icon="akar-icons:user"
                  className="w-4 h-4 absolute right-4 top-1/2 transform -translate-y-1/2"
                />
              </div>

              <div className="relative w-full mx-auto">
                <Field
                  id="contact_no"
                  name="contact_no"
                  type="text"
                  value={values.contact_no}
                  className={`peer w-full text-sm text-gray-800 bg-transparent border-b border-gray-300 
                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                      ${
                        values.contact_no
                          ? "outline-none ring-2 ring-blue-500 border-blue-500"
                          : ""
                      }
                      px-4 py-3.5 rounded-md shadow-sm focus:text-blue-500`}
                  placeholder=""
                  onChange={(e) => {
                    setFieldValue("contact_no", e.target.value),
                      createData({ contact_no: e.target.value });
                  }}
                />
                <label
                  htmlFor="contact_no"
                  className="px-2 bg-white absolute text-sm text-gray-500 left-4 top-0 transform -translate-y-1/2 transition-all duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-gray-500 peer-focus:top-0 peer-focus:text-blue-500 peer-focus:text-xs"
                >
                  Phone no
                </label>
                <ErrorMessage name="contact_no">
                  {(msg) => <div className="text-red-500 text-sm">{msg}</div>}
                </ErrorMessage>
                <Icon
                  icon="akar-icons:user"
                  className="w-4 h-4 absolute right-4 top-1/2 transform -translate-y-1/2"
                />
              </div>

              {/* Password */}
              <div>
                <div className="relative w-full mx-auto">
                  <Field
                    id="user_password"
                    name="user_password"
                    type={showPassword ? "text" : "password"}
                    value={values.user_password}
                    className={`peer w-full text-sm text-gray-800 bg-transparent border-b border-gray-300 
                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                      ${
                        values.user_password
                          ? "outline-none ring-2 ring-blue-500 border-blue-500"
                          : ""
                      }
                      px-4 py-3.5 rounded-md shadow-sm focus:text-blue-500`}
                    placeholder=""
                    onChange={(e) => {
                      setFieldValue("user_password", e.target.value);
                    }}
                  />
                  <label
                    htmlFor="user_password"
                    className="px-2 bg-white absolute text-sm text-gray-500 left-4 top-0 transform -translate-y-1/2 transition-all duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-gray-500 peer-focus:top-0 peer-focus:text-blue-500 peer-focus:text-xs"
                  >
                    Password
                  </label>
                  <button
                    type="button"
                    className="absolute right-4 top-1/2 transform -translate-y-1/2"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    <Icon
                      icon={
                        showPassword
                          ? "akar-icons:eye-closed"
                          : "akar-icons:eye"
                      }
                      className="h-full w-full"
                    />
                  </button>
                </div>
                <ErrorMessage name="user_password">
                  {(msg) => <div className="text-red-500 text-sm">{msg}</div>}
                </ErrorMessage>
              </div>

              {/* Confirm Password */}

              <div>
                <div className="relative w-full mx-auto">
                  <Field
                    id="password_confirmation"
                    name="password_confirmation"
                    type={showConfirmPassword ? "text" : "password"}
                    className={`peer w-full text-sm text-gray-800 bg-transparent border-b border-gray-300 
                    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      values.password_confirmation
                        ? "outline-none ring-2 ring-blue-500 border-blue-500"
                        : ""
                    } px-4 py-3.5 rounded-md shadow-sm focus:text-blue-500`}
                    placeholder=""
                    onChange={(e) => {
                      setFieldValue("password_confirmation", e.target.value);
                    }}
                  />
                  <label
                    htmlFor="password_confirmation"
                    className="px-2 bg-white absolute text-sm text-gray-500 left-4 top-0 transform -translate-y-1/2 transition-all duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-gray-500 peer-focus:top-0 peer-focus:text-blue-500 peer-focus:text-xs"
                  >
                    Confirm Password
                  </label>
                  <button
                    type="button"
                    className="absolute right-4 top-1/2 transform -translate-y-1/2"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    <Icon
                      icon={
                        showConfirmPassword
                          ? "akar-icons:eye-closed"
                          : "akar-icons:eye"
                      }
                      className="w-4 h-4"
                    />
                  </button>
                </div>
                <ErrorMessage name="password_confirmation">
                  {(msg) => <div className="text-red-500 text-sm">{msg}</div>}
                </ErrorMessage>
              </div>

              {/* Buttons */}
              <div className="flex justify-between items-center mt-6">
                <button
                  type="button"
                  className="flex items-center px-4 py-2 text-gray-700 rounded-lg bg-gray-200 hover:bg-gray-300"
                  onClick={() => setPercent(50)}
                >
                  <Icon icon="akar-icons:chevron-left" className="mr-2" />
                  Back
                </button>
                <Button
                  type="submit"
                  isLoading={loading}
                  className="flex items-center px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg"
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
      <ToastContainer />
    </div>
  );
};

export default Three;
