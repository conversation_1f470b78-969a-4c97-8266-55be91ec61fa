// touch src/pages/auth/StepForNewLMS/components/Two.jsx
import React, { useState, useEffect } from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import { useDispatch } from "react-redux";
import { setLogo, setBanner } from "@/pages/auth/StepForNewLMS/store";
import { Icon } from "@iconify/react";
import { motion } from "framer-motion";
import Tooltip from "@/components/ui/Tooltip";
import heroShape from "@/assets/images/all-img/heroShape.png";
import heroImg2 from "@/assets/images/all-img/heroImg2.png";
import heroBgImg from "@/assets/images/all-img/heroBg1.png";
import { useTranslation } from "react-i18next";

export default function TemplateTwo({ setPercent, createData, dataToSubmit }) {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [activeValidation, setActiveValidation] = useState(true);

  const initialValues = {
    headline: dataToSubmit?.headline || t("registration.tagline"),
    sub_headline:
      dataToSubmit?.sub_headline ||
      t("registration.short_description"),
    logo: dataToSubmit?.logo || "",
    banner: dataToSubmit?.banner || "",
    hotline_number: dataToSubmit?.hotline_number || t("registration.default_number"),
    promotional_video: dataToSubmit?.promotional_video || "",
  };

  const validationSchema = activeValidation
    ? Yup.object().shape({
      headline: Yup.string()
        .required("Headline is required")
        .max(
          255,
          "Sub Headline must be less than or equal to 255 characters"
        ),
      sub_headline: Yup.string()
        .required("Sub Headline is required")
        .max(
          255,
          "Sub Headline must be less than or equal to 255 characters"
        ),
      logo: Yup.mixed()
        .test("is-image", "File must be an image", (value) => {
          if (!value) return true;
          return value?.type.startsWith("image/");
        }),
      banner: Yup.mixed()
        .test("is-image", "File must be an image", (value) => {
          if (!value) return true;
          return value?.type.startsWith("image/");
        }),
    })
    : Yup.object().shape({});

  const onSubmit = (values) => {
    createData(values);
    setPercent(75);
  };

  const fadeIn = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 },
  };

  const [videoUrl, setVideoUrl] = useState("");
  const [tempUrl, setTempUrl] = useState("");

  const getEmbedUrl = (url) => {
    if (!url) return "";
    const youtubeMatch = url.match(
      /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})/
    );
    const shortYoutubeMatch = url.match(
      /(?:https?:\/\/)?(?:www\.)?youtu\.be\/([a-zA-Z0-9_-]{11})/
    );

    let videoId = "";
    if (youtubeMatch && youtubeMatch[1]) {
      videoId = youtubeMatch[1];
    } else if (shortYoutubeMatch && shortYoutubeMatch[1]) {
      videoId = shortYoutubeMatch[1];
    }

    if (videoId) {
      return `https://www.youtube.com/embed/${videoId}?autoplay=1&mute=1&loop=1&controls=0&playlist=${videoId}`;
    }

    return url;
  };

  return (
    <div className="min-h-screen">
      <motion.div
        className="max-w-xl sm:max-w-2xl lg:max-w-7xl mx-auto bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden relative z-10"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="relative ">
          {/* Header Section */}
          <motion.div className="text-center mb-6 sm:mb-8 lg:mb-12" {...fadeIn}>
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              {t("registration.customize_lms")}
            </h2>
            <p className="mt-2 sm:mt-3 text-sm sm:text-base text-gray-600">
              {t("registration.create_engaging")}
            </p>
          </motion.div>
        </div>

        <div>
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={onSubmit}
          >
            {({ setFieldValue, values, errors, touched, isValid }) => {
              useEffect(() => {
                if (values.promotional_video) {
                  setTempUrl(values.promotional_video);
                  setVideoUrl(getEmbedUrl(values.promotional_video));
                }
              }, []);

              const handleSave = () => {
                const embedUrl = getEmbedUrl(tempUrl);
                setVideoUrl(embedUrl);
                setFieldValue("promotional_video", tempUrl);
              };

              const handleCancel = () => {
                setTempUrl(values.promotional_video || "");
              };

              return (
                <Form className="">
                  <motion.div
                    className="flex flex-col items-center"
                    {...fadeIn}
                  >
                    <header className="w-full  bg-[#13497C] text-white shadow-sm px-4 sm:px-6 py-3 flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        {values.logo ? (
                          <div className="relative">
                            <img
                              src={URL.createObjectURL(values.logo)}
                              alt="Logo Preview"
                              className="w-20 h-10 object-contain rounded-md"
                            />
                            <button
                              type="button"
                              onClick={(e) => {
                                e.preventDefault();
                                setFieldValue("logo", "");
                              }}
                              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                            >
                              <Icon icon="mdi:close" className="w-3 h-3" />
                            </button>
                          </div>
                        ) : (
                          <div>
                            <label
                              htmlFor="logo"
                              className="inline-flex items-center gap-2 px-3 py-2 rounded-md cursor-pointer bg-blue-500 text-white hover:bg-blue-600 transition-colors"
                            >
                              {t("registration.logo")}
                              <Icon icon="mdi:upload" className="w-4 h-4" />
                            </label>
                            <input
                              type="file"
                              id="logo"
                              name="logo"
                              accept="image/*"
                              className="hidden"
                              onChange={(e) =>
                                setFieldValue("logo", e.target.files[0])
                              }
                            />
                            <ErrorMessage
                              name="logo"
                              component="div"
                              className="text-red-500 text-xs mt-1"
                            />
                            {!values.logo && !(errors.logo && touched.logo) && (
                              <div className="mt-1 text-gray-500 text-xs bg-gray-50 border border-gray-200 px-3 py-2 rounded-md">
                                Don’t have a logo? <span className="font-medium text-gray-700">No problem</span>, you can upload it later after registration.
                              </div>
                            )}

                          </div>
                        )}
                      </div>

                      <Tooltip content="Please set category after initialize your LMS">
                        <nav className="flex-1 flex items-center justify-center gap-4 ">
                          <a
                            href="#"
                            className=" hover:text-gray-400"
                          >
                            {t("registration.category_1")}
                          </a>
                          <a
                            href="#"
                            className=" hover:text-gray-400"
                          >
                            {t("registration.category_2")}
                          </a>
                          <a
                            href="#"
                            className=" hover:text-gray-400"
                          >
                            {t("registration.category_3")}
                          </a>
                        </nav>
                      </Tooltip>

                      <div className="flex items-center gap-4">
                        <Field
                          type="text"
                          name="hotline_number"
                          className="text-gray-700 border border-transparent hover:border-blue-500
                          focus:border-blue-500 focus:ring-1 focus:ring-blue-200
                          rounded-md px-2 py-1 transition-all placeholder-gray-400"
                          placeholder="Hotline number"
                          onInput={(e) => {
                            e.target.value = e.target.value.replace(
                              /[^0-9]/g,
                              ""
                            );
                          }}
                        />
                        <button className="p-2 hover:text-blue-500">
                          <Icon icon="mdi:bell" className="w-5 h-5" />
                        </button>
                        <button
                          disabled={true}
                          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors cursor-not-allowed opacity-90"
                        >
                          {t("registration.login")}
                        </button>
                      </div>
                    </header>
                  </motion.div>

                  <div className="py-10  relative overflow-hidden">
                    {/* Background Images */}
                    <img
                      src={heroBgImg}
                      className="max-sm:opacity-25 absolute left-0 top-0 z-0 h-full object-cover"
                      alt=""
                    />
                    {/* <img
                        src={heroMoveIcon}
                        style={{ transform: transformStyle }}
                        className="max-sm:opacity-50 absolute right-0 bottom-0 z-0 transition-transform duration-75"
                        alt="Hero Icon"
                        /> */}

                    {/* Content Container */}
                    <div className="flex items-center">
                      <div className="p-5 mx-auto flex flex-col lg:flex-row items-center gap-5 lg:gap-10 z-10 py-10">
                        {/* Left Column */}
                        <motion.div
                          {...fadeIn}
                          className="flex flex-col h-full w-full gap-4"
                        >
                          <div className="relative">
                            <Field
                              name="headline"
                              as="textarea"
                              rows="2"
                              className="w-full bg-transparent text-3xl font-light text-blue-500 placeholder:text-gray-400 border border-transparent hover:border-blue-500 focus:border-blue-500 focus:ring-1 focus:ring-blue-200 transition-all duration-300 rounded-md resize-none overflow-hidden min-h-[50px]"
                              placeholder={t("registration.tagline_placeholder")}
                              onInput={(e) => {
                                e.target.style.height = "auto";
                                e.target.style.height = `${e.target.scrollHeight}px`;
                              }}
                            />
                            <ErrorMessage
                              name="headline"
                              component="div"
                              className="text-red-500 text-xs sm:text-sm mt-1"
                            />
                          </div>

                          <div className="relative">
                            <Field
                              name="sub_headline"
                              as="textarea"
                              rows="2"
                              className="w-full bg-transparent text-base text-blue-500 placeholder:text-gray-400 border border-transparent hover:border-blue-500 focus:border-blue-500 focus:ring-1 focus:ring-blue-200 transition-all duration-300 rounded-md resize-none overflow-hidden min-h-[80px]"
                              placeholder={t("registration.short_description")}
                              onInput={(e) => {
                                e.target.style.height = "auto";
                                e.target.style.height = `${e.target.scrollHeight}px`;
                              }}
                            />
                            <ErrorMessage
                              name="sub_headline"
                              component="div"
                              className="text-red-500 text-xs sm:text-sm mt-1"
                            />
                          </div>
                        </motion.div>

                        <div className="">
                          {/* <img
                              className="xl:max-w-xl md:max-w-xl relative z-10 "
                              src={heroImg2}
                              alt=""
                            /> */}
                          <div className="xl:max-w-xl md:max-w-xl relative z-10 ">
                            <label
                              htmlFor="banner"
                              className={`group relative block border-3 border-dashed transition-all duration-300 cursor-pointer overflow-hidden
                                                       ${values.banner
                                  ? "border-green-200 bg-green-50"
                                  : touched.banner && errors.banner
                                    ? "border-red-200 bg-red-50"
                                    : "border-blue-200 bg-blue-50"
                                }
                                                       hover:border-blue-400`}
                            >
                              {values.banner ? (
                                <>
                                  <img
                                    src={URL.createObjectURL(values.banner)}
                                    alt="Banner Preview"
                                    className="w-full h-full object-cover"
                                  />
                                  <button
                                    type="button"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      setFieldValue("banner", "");
                                      dispatch(setBanner(null));
                                    }}
                                    className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1.5 hover:bg-red-600 transition-colors"
                                  >
                                    <Icon
                                      icon="mdi:close"
                                      className="w-4 sm:w-5"
                                    />
                                  </button>
                                </>
                              ) : (
                                <div className="flex flex-col items-center justify-center h-full">
                                  <Icon
                                    icon="solar:gallery-wide-bold-duotone"
                                    className="w-12 sm:w-14 lg:w-16 h-12 sm:h-14 lg:h-16 text-blue-400 group-hover:text-blue-600 transition-colors"
                                  />
                                  <p className="text-sm sm:text-base text-blue-500 mt-2 sm:mt-4">
                                    {t("registration.upload_banner")}
                                  </p>
                                </div>
                              )}
                            </label>
                            <input
                              type="file"
                              id="banner"
                              name="banner"
                              accept="image/*"
                              className="hidden"
                              onChange={(e) => {
                                const file = e.target.files[0];
                                setFieldValue("banner", file);
                                dispatch(setBanner(file));
                              }}
                            />
                            <ErrorMessage
                              name="banner"
                              component="div"
                              className="text-red-500 text-xs sm:text-sm mt-2"
                            />
                            {!values.banner && !(errors.banner && touched.banner) && (
                              <div className="mt-2 text-gray-500 text-xs bg-gray-50 border border-gray-200 px-3 py-2 rounded-md">
                                Don’t have a banner? <span className="font-medium text-gray-700">No problem</span>, you can upload it later after registration.
                              </div>
                            )}
                          </div>
                          <img
                            className="absolute -right-20 bottom-0 z-0"
                            src={heroShape}
                            alt=""
                          />
                        </div>
                      </div>
                    </div>

                  </div>

                  <motion.div
                    className="flex justify-between p-4 sm:pt-8"
                    {...fadeIn}
                  >
                    <button
                      type="button"
                      onClick={() => setPercent(25)}
                      className="flex items-center px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors duration-300"
                    >
                      <Icon
                        icon="solar:arrow-left-linear"
                        className="mr-2 w-4 sm:w-5 h-4 sm:h-5"
                      />
                      {t("registration.back")}
                    </button>
                    <button
                      type="submit"
                      className="flex items-center px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base rounded-lg transition-all duration-300 bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:shadow-lg hover:scale-105"
                    >
                      {t("registration.next")}
                      <Icon
                        icon="solar:arrow-right-linear"
                        className="ml-2 w-4 sm:w-5 h-4 sm:h-5"
                      />
                    </button>
                  </motion.div>
                </Form>
              );
            }}
          </Formik>

          <div className="h-16"></div>
        </div>
      </motion.div>
    </div>
  );
}
