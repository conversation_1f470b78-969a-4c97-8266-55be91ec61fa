// touch src/pages/auth/StepForNewLMS/components/Two.jsx
import React, { useState, useEffect } from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import { useDispatch } from "react-redux";
import { setLogo, setBanner } from "@/pages/auth/StepForNewLMS/store";
import { Icon } from "@iconify/react";
import { motion } from "framer-motion";
import worldGif from "@/assets/images/all-img/world.gif";
import HeroBg from "@/assets/images/all-img/websiteSettings.png";
import Tooltip from "@/components/ui/Tooltip";
import TemplateTwo from "./TemplateViews/TemplateTwo";
import { useTranslation } from 'react-i18next';

export default function Two({ setPercent, createData, dataToSubmit }) {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [activeValidation, setActiveValidation] = useState(true);

  const initialValues = {
    headline: dataToSubmit?.headline || t("registration.tagline_placeholder"),
    sub_headline:
      dataToSubmit?.sub_headline ||
      "Please write a short description which will be displayed at home page ",
    logo: dataToSubmit?.logo || "",
    banner: dataToSubmit?.banner || "",
    hotline_number: dataToSubmit?.hotline_number || t("registration.default_number"),
    promotional_video: dataToSubmit?.promotional_video || "",
  };

  const validationSchema = activeValidation
    ? Yup.object().shape({
      headline: Yup.string()
        .required("Headline is required")
        .max(
          255,
          "Sub Headline must be less than or equal to 255 characters"
        ),
      sub_headline: Yup.string()
        .required("Sub Headline is required")
        .max(
          255,
          "Sub Headline must be less than or equal to 255 characters"
        ),
      logo: Yup.mixed()
        .test("is-image", "File must be an image", (value) => {
          if (!value) return true;
          return value?.type.startsWith("image/");
        }),
      banner: Yup.mixed()
        .test("is-image", "File must be an image", (value) => {
          if (!value) return true;
          return value?.type.startsWith("image/");
        }),
    })
    : Yup.object().shape({});

  const onSubmit = (values) => {
    createData(values);
    setPercent(75);
  };

  const fadeIn = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 },
  };

  const [videoUrl, setVideoUrl] = useState("");
  const [tempUrl, setTempUrl] = useState("");

  const getEmbedUrl = (url) => {
    if (!url) return "";
    const youtubeMatch = url.match(
      /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})/
    );
    const shortYoutubeMatch = url.match(
      /(?:https?:\/\/)?(?:www\.)?youtu\.be\/([a-zA-Z0-9_-]{11})/
    );

    let videoId = "";
    if (youtubeMatch && youtubeMatch[1]) {
      videoId = youtubeMatch[1];
    } else if (shortYoutubeMatch && shortYoutubeMatch[1]) {
      videoId = shortYoutubeMatch[1];
    }

    if (videoId) {
      return `https://www.youtube.com/embed/${videoId}?autoplay=1&mute=1&loop=1&controls=0&playlist=${videoId}`;
    }

    return url;
  };

  console.log(dataToSubmit)

  return (
    <div className="min-h-screen">
      {dataToSubmit?.template_id == 1 ? <motion.div
        className="max-w-xl sm:max-w-2xl lg:max-w-7xl mx-auto bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden relative z-10"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="relative ">
          {/* Header Section */}
          <motion.div className="text-center mb-6 sm:mb-8 lg:mb-12" {...fadeIn}>
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              {t("registration.customize_lms")}
            </h2>
            <p className="mt-2 sm:mt-3 text-sm sm:text-base text-gray-600">
              {t("registration.create_engaging")}
            </p>
          </motion.div>

          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={onSubmit}
          >
            {({ setFieldValue, values, errors, touched, isValid }) => {
              useEffect(() => {
                if (values.promotional_video) {
                  setTempUrl(values.promotional_video);
                  setVideoUrl(getEmbedUrl(values.promotional_video));
                }
              }, []);

              const handleSave = () => {
                const embedUrl = getEmbedUrl(tempUrl);
                setVideoUrl(embedUrl);
                setFieldValue("promotional_video", tempUrl);
              };

              const handleCancel = () => {
                setTempUrl(values.promotional_video || "");
              };

              return (
                <Form className="">
                  {/* Header with Logo, Hotline, etc. */}
                  <motion.div
                    className="flex flex-col items-center"
                    {...fadeIn}
                  >
                    <header className="w-full bg-white shadow-sm px-4 sm:px-6 py-3 flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        {values.logo ? (
                          <div className="relative">
                            <img
                              src={URL.createObjectURL(values.logo)}
                              alt="Logo Preview"
                              className="w-20 h-10 object-cover rounded-md"
                            />
                            <button
                              type="button"
                              onClick={(e) => {
                                e.preventDefault();
                                setFieldValue("logo", "");
                              }}
                              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                            >
                              <Icon icon="mdi:close" className="w-3 h-3" />
                            </button>
                          </div>
                        ) : (
                          <div>
                            <label
                              htmlFor="logo"
                              className="inline-flex items-center gap-2 px-3 py-2 rounded-md cursor-pointer bg-blue-500 text-white hover:bg-blue-600 transition-colors"
                            >
                              {t("registration.logo")}
                              <Icon icon="mdi:upload" className="w-4 h-4" />
                            </label>
                            <input
                              type="file"
                              id="logo"
                              name="logo"
                              accept="image/*"
                              className="hidden"
                              onChange={(e) =>
                                setFieldValue("logo", e.target.files[0])
                              }
                            />
                            <ErrorMessage
                              name="logo"
                              component="div"
                              className="text-red-500 text-xs mt-1"
                            />
                            {!values.logo && !(errors.logo && touched.logo) && (
                              <div className="mt-1 text-gray-500 text-xs bg-gray-50 border border-gray-200 px-3 py-2 rounded-md">
                                Don’t have a logo? <span className="font-medium text-gray-700">No problem</span>, you can upload it later after registration.
                              </div>
                            )}

                          </div>
                        )}
                      </div>

                      <Tooltip content="Please set category after initialize your LMS">
                        <nav className="flex-1 flex items-center justify-center gap-4 text-gray-700">
                          <a
                            href="#"
                            className="text-gray-700 hover:text-gray-900"
                          >
                            {t("registration.category_1")}
                          </a>
                          <a
                            href="#"
                            className="text-gray-700 hover:text-gray-900"
                          >
                            {t("registration.category_2")}
                          </a>
                          <a
                            href="#"
                            className="text-gray-700 hover:text-gray-900"
                          >
                            {t("registration.category_3")}
                          </a>
                        </nav>
                      </Tooltip>

                      <div className="flex items-center gap-4">
                        <Field
                          type="text"
                          name="hotline_number"
                          className="text-gray-700 border border-transparent hover:border-blue-500
                          focus:border-blue-500 focus:ring-1 focus:ring-blue-200
                          rounded-md px-2 py-1 transition-all placeholder-gray-400"
                          placeholder="Hotline number"
                          onInput={(e) => {
                            e.target.value = e.target.value.replace(
                              /[^0-9]/g,
                              ""
                            );
                          }}
                        />
                        <button className="p-2 text-gray-600 hover:text-blue-500">
                          <Icon icon="mdi:bell" className="w-5 h-5" />
                        </button>
                        <button
                          disabled={true}
                          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors cursor-not-allowed opacity-50"
                        >
                          {t("registration.login")}
                        </button>
                      </div>
                    </header>
                  </motion.div>

                  {/* Main Content with Background Image */}
                  <div
                    className="grid grid-cols-2 gap-4 pt-6 px-4 items-center bg-cover bg-center bg-no-repeat"
                    style={{ backgroundImage: `url(${HeroBg})` }}
                  >
                    <motion.div {...fadeIn} className="flex flex-col gap-4">
                      <div className="relative">
                        <Field
                          name="headline"
                          as="textarea"
                          rows="2"
                          className="w-full text-3xl font-light text-blue-500 placeholder:text-gray-400 border border-transparent hover:border-blue-500 focus:border-blue-500 focus:ring-1 focus:ring-blue-200 transition-all duration-300 rounded-md resize-none overflow-hidden min-h-[50px]"
                          placeholder="Tagline of Organization"
                          onInput={(e) => {
                            e.target.style.height = "auto";
                            e.target.style.height = `${e.target.scrollHeight}px`;
                          }}
                        />
                        <ErrorMessage
                          name="headline"
                          component="div"
                          className="text-red-500 text-xs sm:text-sm mt-1"
                        />
                      </div>

                      <div className="relative">
                        <Field
                          name="sub_headline"
                          as="textarea"
                          rows="2"
                          className="w-full text-base text-blue-500 placeholder:text-gray-400 border border-transparent hover:border-blue-500 focus:border-blue-500 focus:ring-1 focus:ring-blue-200 transition-all duration-300 rounded-md resize-none overflow-hidden min-h-[80px]"
                          placeholder="Please write a short description which will be displayed at home page"
                          onInput={(e) => {
                            e.target.style.height = "auto";
                            e.target.style.height = `${e.target.scrollHeight}px`;
                          }}
                        />
                        <ErrorMessage
                          name="sub_headline"
                          component="div"
                          className="text-red-500 text-xs sm:text-sm mt-1"
                        />
                      </div>
                    </motion.div>

                    <div className="flex-1 grid grid-cols-12 gap-5">
                      <div className="col-span-6 flex flex-col">
                        <div className="flex-grow-[1]"></div>
                        <div className="mb-5 w-full flex-grow-[3] flex-shrink-0 basis-auto">
                          <Tooltip content="Set a few Mentors as Featured after you have added them">
                            <div className="rounded-lg w-68 h-72 relative bg-blue-500">
                              <h3 className="text-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                                Featured Mentors
                              </h3>
                            </div>
                          </Tooltip>
                        </div>
                        <div className="relative">
                          <label
                            htmlFor="banner"
                            className={`group relative block w-full h-32 sm:h-40 lg:h-48 rounded-xl border-3 border-dashed transition-all duration-300 cursor-pointer overflow-hidden
                              ${values.banner
                                ? "border-green-200 bg-green-50"
                                : touched.banner && errors.banner
                                  ? "border-red-200 bg-red-50"
                                  : "border-blue-200 bg-blue-50"
                              }
                              hover:border-blue-400`}
                          >
                            {values.banner ? (
                              <>
                                <img
                                  src={URL.createObjectURL(values.banner)}
                                  alt="Banner Preview"
                                  className="w-full h-full object-cover"
                                />
                                <button
                                  type="button"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    setFieldValue("banner", "");
                                    dispatch(setBanner(null));
                                  }}
                                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1.5 hover:bg-red-600 transition-colors"
                                >
                                  <Icon
                                    icon="mdi:close"
                                    className="w-4 sm:w-5"
                                  />
                                </button>
                              </>
                            ) : (
                              <div className="flex flex-col items-center justify-center h-full">
                                <Icon
                                  icon="solar:gallery-wide-bold-duotone"
                                  className="w-12 sm:w-14 lg:w-16 h-12 sm:h-14 lg:h-16 text-blue-400 group-hover:text-blue-600 transition-colors"
                                />
                                <p className="text-sm sm:text-base text-blue-500 mt-2 sm:mt-4">
                                  Upload Banner Image
                                  {/* <span className="text-red-600">*</span> */}
                                </p>
                              </div>
                            )}
                          </label>
                          <input
                            type="file"
                            id="banner"
                            name="banner"
                            accept="image/*"
                            className="hidden"
                            onChange={(e) => {
                              const file = e.target.files[0];
                              setFieldValue("banner", file);
                              dispatch(setBanner(file));
                            }}
                          />
                          <ErrorMessage
                            name="banner"
                            component="div"
                            className="text-red-500 text-xs sm:text-sm mt-2"
                          />
                          {!values.banner && !(errors.banner && touched.banner) && (
                            <div className="mt-1 text-gray-500 text-xs bg-gray-50 border border-gray-200 px-3 py-2 rounded-md">
                              Don’t have a banner? <span className="font-medium text-gray-700">No problem</span>, you can upload it later after registration.
                            </div>
                          )}

                        </div>
                      </div>

                      <div className="col-span-6 flex flex-col h-full">
                        <div className="flex-grow-[2]"></div>
                        <div className="flex-grow-[3] flex-shrink-0 basis-auto w-full relative flex items-center justify-center border-2 border-sky-50 rounded-lg shadow-lg hover:border-red-600 group cursor-pointer mb-5">
                          {values.promotional_video ? (
                            <iframe
                              title="video-frame"
                              src={values.promotional_video}
                              className="w-full h-full rounded-lg overflow-hidden"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center rounded-lg overflow-hidden text-gray-500">
                              Here add the video url
                            </div>
                          )}

                          <div className="absolute inset-0 hidden group-hover:flex items-center justify-center bg-white/90 p-4">
                            <div>
                              <input
                                type="text"
                                name="promotional_video"
                                onChange={(e) =>
                                  setFieldValue(
                                    "promotional_video",
                                    e.target.value
                                  )
                                }
                                className="border rounded-md px-2 py-1 text-sm focus:outline-none"
                                placeholder="Enter video URL"
                              />
                              {/* <div className="flex flex-col sm:flex-row gap-2 items-center mt-2">
                                <button
                                  type="button"
                                  onClick={handleSave}
                                  className="bg-blue-600 text-white px-3 text-sm py-1 rounded-md hover:bg-blue-700"
                                >
                                  Save
                                </button>
                                <button
                                  type="button"
                                  onClick={handleCancel}
                                  className="bg-gray-300 text-gray-700 px-3 py-1 rounded-md hover:bg-gray-400 text-sm"
                                >
                                  Cancel
                                </button>
                              </div> */}
                            </div>
                          </div>
                        </div>
                        <div className="flex-grow-[0.1] flex-shrink-0 basis-auto w-full rounded-lg relative border shadow-lg">
                          <img
                            src={worldGif}
                            alt="Rotating Globe"
                            className="w-full object-cover rounded-lg h-full z-0"
                          />
                          <div className="max-sm:text-xs text-lg font-semibold text-gray-500 rounded-r-lg h-full absolute top-0 right-0 flex items-center justify-center">
                            <div className="absolute inset-0 bg-gray-200 opacity-75 rounded-r-lg"></div>
                            <span className="relative z-10 text-center px-3">
                              <span className="text-2xl sm:text-4xl text-sky-700">
                                00
                              </span>
                              <br />
                              Worldwide User
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Navigation Buttons */}
                  <motion.div
                    className="flex justify-between p-4 sm:pt-8"
                    {...fadeIn}
                  >
                    <button
                      type="button"
                      onClick={() => setPercent(25)}
                      className="flex items-center px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors duration-300"
                    >
                      <Icon
                        icon="solar:arrow-left-linear"
                        className="mr-2 w-4 sm:w-5 h-4 sm:h-5"
                      />
                      {t("registration.back")}
                    </button>
                    <button
                      type="submit"
                      className="flex items-center px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base rounded-lg transition-all duration-300 bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:shadow-lg hover:scale-105"
                    >
                      {t("registration.next")}
                      <Icon
                        icon="solar:arrow-right-linear"
                        className="ml-2 w-4 sm:w-5 h-4 sm:h-5"
                      />
                    </button>
                  </motion.div>
                </Form>
              );
            }}
          </Formik>
        </div>
      </motion.div> : <TemplateTwo createData={createData} dataToSubmit={dataToSubmit} setPercent={setPercent} />}
    </div>
  );
}
