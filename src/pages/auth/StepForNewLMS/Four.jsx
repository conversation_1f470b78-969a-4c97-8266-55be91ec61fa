import React from 'react';

const Four = () => {
  
  return (
    <div className="max-w-7xl mx-auto h-screen">
    <div className="flex flex-col items-center justify-center min-h-screen bg-white p-6">
        <div className="flex items-center justify-center w-12 h-12 mb-4 overflow-hidden bg-gray-100 rounded-full">
            <svg className="w-full h-full text-green-500" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
            </svg>
        </div>
        <h3 className="mb-2 text-2xl font-medium text-center text-gray-900">Registration successful</h3>
        <p className="text-sm mb-6 text-center text-gray-500">You've successfully registered. You can now login.</p>
        <div className="flex w-full justify-center space-x-3">
            <a href="http://localhost:3000" className="inline-flex items-center justify-center h-12 px-6 font-medium tracking-wide text-white transition duration-200 rounded shadow-md bg-blue-600 hover:bg-blue-500 focus:shadow-outline focus:outline-none">
                Go to login
            </a>
        </div>
        <div className="relative self-center mt-6">
            <div className="absolute inset-0 flex items-center">
                <span className="w-full h-1 bg-gray-200" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-white px-2 text-gray-500">Or</span>
            </div>
        </div>
        <div className="mt-6">
            <a href="/" className="inline-flex items-center justify-center w-full h-12 px-6 font-medium tracking-wide text-gray-900 transition duration-200 rounded shadow-md bg-white hover:bg-gray-100 focus:shadow-outline focus:outline-none">
                Go to homepage sd
            </a>
        </div>
    </div>
</div>
  );
};

export default Four;
