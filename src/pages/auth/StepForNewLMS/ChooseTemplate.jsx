import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import Modal from "@/components/ui/Modal";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import { useDispatch } from "react-redux";
import { useTranslation } from 'react-i18next';

const ChooseTemplate = ({ template, setPercent, setTemplate, createData }) => {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [isNotSelected, setIsNotSelected] = useState(false);
  const [templateIndex, setTemplateIndex] = useState(null);
  const [previewTemplate, setPreviewTemplate] = useState(null);
  const [imagePopup, setImagePopup] = useState(null); // State to store clicked image

  const ASSET_URL = import.meta.env.VITE_ASSET_HOST_URL;
  const { data: res, isLoading } = useGetApiQuery("website/templates");
  const templates = res || [];

  useEffect(() => {
    if (template) {
      const index = templates.findIndex(t => t.id === template.id);
      setTemplateIndex(index);
    }
  }, [template, templates]);

  const selectTemplate = (value, index) => {
    setTemplate(value);
    setTemplateIndex(index);
    setIsNotSelected(false);
  };

  const goNextPage = () => {
    if (templateIndex !== null) {
      createData({ template_id: templates[templateIndex].id });
      setPercent(25);
    } else {
      setIsNotSelected(true);
    }
  };

  if (isLoading) return <p className="text-center text-lg">Loading...</p>;

  return (
    <div className={`w-full p-6 space-y-6 bg-white rounded-lg shadow-lg ${isNotSelected && "border border-red-500"}`}>
      <h1 className="text-3xl font-bold text-gray-800">{t("registration.choose_a_template")}</h1>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {templates.map((temp, index) => (
          <div key={temp.id} className="border rounded-lg shadow-md overflow-hidden hover:shadow-lg transition">
            <div className="relative">
              {templateIndex === index && (
                <div className="absolute top-2 right-2 bg-green-500 rounded-full w-8 h-8 flex items-center justify-center text-white">
                  <Icon icon="fa-solid:check" />
                </div>
              )}
              <img src={`${ASSET_URL}${temp.theme_image}`} alt={temp.title} className="w-full h-48 object-cover" />
            </div>
            <div className="p-4 text-center">
              <h5 className="text-lg font-semibold text-gray-800">
                {temp.title === "Template One" ? t("registration.template_one") :
                 temp.title === "Template Two" ? t("registration.template_two") : temp.title}
              </h5>
              <p className="text-gray-600">
                {temp.short_description === "This is Short Description of Template One" ? t("registration.template_one_desc") :
                 temp.short_description === "This is Short Description of Template Two" ? t("registration.template_two_desc") : temp.short_description}
              </p>
            </div>
            <div className="flex border-t divide-x">
              <button
                onClick={() => selectTemplate(temp, index)}
                className={`w-1/2 py-2 text-blue-500 hover:bg-blue-500 hover:text-white transition ${templateIndex === index && "bg-blue-500 text-white"}`}
              >
                {templateIndex === index ? t("registration.selected") : t("registration.select")}
              </button>
              <button
                onClick={() => { setIsOpen(true); setPreviewTemplate(temp); }}
                className="w-1/2 py-2 text-blue-500 hover:bg-blue-500 hover:text-white transition"
              >
                {t("registration.preview")}
              </button>
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-end">
        <button onClick={goNextPage} className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center">
          {t("registration.next")} <Icon icon="akar-icons:chevron-right" className="ml-2" />
        </button>
      </div>

      {isOpen && (
        <Modal activeModal={isOpen} onClose={() => setIsOpen(false)} className='max-w-5xl' title={
          previewTemplate?.title === "Template One" ? t("registration.template_one") :
          previewTemplate?.title === "Template Two" ? t("registration.template_two") : previewTemplate?.title
        }>
          <div className="p-4">
            <div className="grid grid-cols-3 gap-4">
              {previewTemplate?.items.length === 0 ? (
                <p className='text-center text-lg text-gray-600'>No items added</p>
              ) : (
                previewTemplate.items.map((item, index) => (
                  <div key={index} className="flex flex-col items-center space-y-2">
                    <img src={`${ASSET_URL}${item.image}`} className="h-32 object-cover" alt={item.title} />
                    <p className="text-center text-lg font-semibold text-gray-800">{item.title}</p>
                  </div>
                ))
              )}
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default ChooseTemplate;
