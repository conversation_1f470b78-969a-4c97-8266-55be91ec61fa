import React, { useState, useEffect } from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import { useDispatch } from "react-redux";
import { setShortName } from "@/pages/auth/StepForNewLMS/store";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { Icon } from "@iconify/react";
import Badge from "@/components/ui/Badge";
import { useTranslation } from 'react-i18next';

const One = ({ template, dataToSubmit, setPercent, createData }) => {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [errorMessage, setErrorMessage] = useState("");
  const [suggestedDomain, setSuggestedDomain] = useState([]);
  const [isAvailable, setIsAvailable] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [lmsTitle, setLmsTitle] = useState(dataToSubmit?.name || "");
  const [subDomain, setSubDomain] = useState(dataToSubmit?.short_name || "");
  const [typingTimeout, setTypingTimeout] = useState(null);

  const [postApi] = usePostApiMutation();

  useEffect(() => {
    if (dataToSubmit?.short_name) {
      checkSubDomain(dataToSubmit.short_name);
    }
  }, []);

  useEffect(() => {
    setLmsTitle(dataToSubmit?.name || "");
    setSubDomain(dataToSubmit?.short_name || "");
  }, [dataToSubmit]);

  const initialValues = {
    name: dataToSubmit ? dataToSubmit.name : "",
    short_name: dataToSubmit ? dataToSubmit.short_name : "",
    host_url: dataToSubmit ? dataToSubmit.host_url : "",
  };

  const validationSchema = Yup.object().shape({
    name: Yup.string().required("LMS Title is required"),
    short_name: Yup.string()
      .min(4, "Please enter at least 4 characters")
      .required("Subdomain is required"),
  });

  const checkSubDomain = async (subDomain) => {

    setIsChecked(false);
    try {
      if (subDomain.length > 3) {
        const response = await postApi({
          end_point: "website/short-name",
          body: { short_name: subDomain },
          notoast: true,
        });

        if (dataToSubmit?.short_name === subDomain) {
          setIsAvailable(true);
          setIsChecked(true);
          dispatch(setShortName(subDomain));
          return;
        }
        // console.log(response);
        if (response.error) {
          console.log(response.error.data.message);
          setErrorMessage(response.error.data.message);
          setIsAvailable(false);
        } else {
          setIsAvailable(true);
        }
        setIsChecked(true);
        dispatch(setShortName(subDomain));
      }
    } catch (error) {
      setIsChecked(true);
      setIsAvailable(false);
    }
  };

  const handleInputChange = (e) => {
    const value = e.target.value;
    setSubDomain(value);
    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }

    setTypingTimeout(
      setTimeout(() => {
        checkSubDomain(value);
      }, 1000)
    );
  };

  const suggestSubdomain = async () => {
    try {
      const response = await postApi({
        end_point: "website/suggest-sub-domain",
        body: {title: lmsTitle},
        notoast: true,
      });

     console.log(response?.data?.data);
     setSuggestedDomain(response?.data?.data);
    } catch (error) {
      console.error("Error:", error);
    }
  }
  const onSubmit = async (values, { setSubmitting }) => {
    createData(values);
    setPercent(50);
  };

  return (
    <div className=" w-full px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12 relative overflow-hidden">

      {/* Main content */}
      <div className="relative z-10 max-w-7xl mx-auto">
        <div className="flex flex-col space-y-4 sm:space-y-6 mb-8 sm:mb-12">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-800 mb-2 sm:mb-3">
              {lmsTitle || t("registration.title_of_lms")}
            </h1>
            <p className="text-sm sm:text-base text-gray-500 mb-1">
              {t("registration.setup_lms")}
            </p>
            <p className="text-sm sm:text-base text-gray-500">
              {template?.title === "Template One" ? t("registration.chosen_template_one") :
               `${t("registration.chosen_template")} ${template?.title}`}
            </p>
          </div>
        </div>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={onSubmit}
          enableReinitialize
        >
          {({ values, setFieldValue, errors, touched }) => (
            <Form className="flex flex-col items-center">
              <div className="w-full mx-auto space-y-8 sm:space-y-10">
                {/* LMS Title */}
                <div className="relative w-full">
                  <Field
                    id="name"
                    name="name"
                    type="text"
                    value={lmsTitle}
                    className={`peer w-full text-sm sm:text-base text-gray-800 bg-transparent border-b-2 border-gray-300
                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                      ${values.name ? "outline-none ring-2 ring-blue-500 border-blue-500" : ""}
                      px-4 py-3 sm:py-4 rounded-lg shadow-sm focus:text-blue-500 transition-all duration-200`}
                    placeholder=" "
                    onChange={(e) => {
                      setFieldValue("name", e.target.value);
                      setLmsTitle(e.target.value);
                    }}
                    onBlur={suggestSubdomain}
                  />
                  <label
                    htmlFor="name"
                    className="px-2 bg-white absolute text-sm sm:text-base text-gray-500 left-4 top-0 transform -translate-y-1/2 transition-all duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-gray-500 peer-focus:top-0 peer-focus:text-blue-500 peer-focus:text-xs sm:peer-focus:text-sm"
                  >
                    {t("registration.lms_title")}
                  </label>
                  <ErrorMessage name="name">
                    {(msg) => <div className="text-red-500 text-xs sm:text-sm mt-1">{msg}</div>}
                  </ErrorMessage>
                </div>

                {/* Short Name */}
                <div className="relative w-full mt-8 sm:mt-10">
                  <div className="flex gap-2">

                  <div className="text-sm sm:text-base text-gray-500 pt-4">
                      https://
                    </div>
                    <div>
                      <Field
                        id="short_name"
                        name="short_name"
                        type="text"
                        value={subDomain}
                        className={`peer w-full text-sm sm:text-base text-gray-800 bg-transparent border-b-2 border-gray-300
                          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                          ${values.short_name ? "outline-none ring-2 ring-blue-500 border-blue-500" : ""}
                          px-4 py-3 sm:py-4 rounded-lg shadow-sm focus:text-blue-500 transition-all duration-200`}
                        placeholder=" "
                        onChange={(e) => {
                          setFieldValue("short_name", e.target.value);
                          handleInputChange(e);
                        }}
                        onTouchStart={(e) => setFieldValue("short_name", e.target.value, true)}
                      />
                      <label
                        htmlFor="short_name"
                        className="px-2 bg-white absolute text-sm sm:text-base text-gray-500 left-20 top-0 transform -translate-y-1/2 transition-all duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-gray-500 peer-focus:top-0 peer-focus:text-blue-500 peer-focus:text-xs sm:peer-focus:text-sm"
                      >
                        {t("registration.subdomain")}

                      </label>
                    </div>
                    <div className="text-sm sm:text-base text-gray-500 pt-4">
                      .edupackbd.com
                    </div>
                  </div>

                </div>

                <div>

                {/* <ErrorMessage name="short_name">
                      {(msg) => <div className="text-red-500 text-xs sm:text-sm mt-1">{msg}</div>}
                </ErrorMessage> */}
                {subDomain.length > 3 && isChecked && (
                    <div className="mt-2 text-xs sm:text-sm break-words">
                      {isAvailable ? (
                        <p className="text-green-600">
                          {t("registration.subdomain_available")}{" "}
                          <span className="font-bold block sm:inline mt-1 sm:mt-0">
                            https://{subDomain}.edupackbd.com
                          </span>
                        </p>
                      ) : (
                        <p className="text-red-600">
                          { errorMessage || 'Sorry! Subdomain is not available'}
                        </p>
                      )}
                    </div>
                  )}

                  {suggestedDomain?.length > 0 && (
                    <div className="mt-2 text-xs sm:text-sm">

                      <div className="flex gap-4 mt-1">
                        {suggestedDomain.map((domain, index) => (
                          <span
                            key={index}
                            className="p-1 rounded-md cursor-pointer bg-blue-500 hover:bg-blue-600 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 text-transform-none"
                            onClick={() => {
                              setSubDomain(domain);
                              checkSubDomain(domain);
                              setFieldValue("short_name", domain);
                            }}
                          >
                            https://{domain}.edupackbd.com
                          </span>
                        ))}
                      </div>

                    </div>
                  )}

                </div>

                {/* Submit Button */}
                <div className="w-full flex justify-between mt-8 sm:mt-10">

                  <button
                    type="button"
                    onClick={() => setPercent(0)}
                    className="flex items-center px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors duration-300"
                  >
                    <Icon
                      icon="solar:arrow-left-linear"
                      className="mr-2 w-4 sm:w-5 h-4 sm:h-5"
                    />
                    {t("registration.back")}
                  </button>
                  <button
                    type="submit"
                    className={`flex items-center px-6 sm:px-8 py-3 sm:py-4 text-sm sm:text-base font-medium rounded-lg transition-all duration-200 ${
                      isAvailable && lmsTitle.trim() && subDomain.trim()
                        ? "bg-blue-500 hover:bg-blue-600 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                        : "bg-blue-200 text-white cursor-not-allowed"
                    }`}
                    disabled={!(isAvailable && lmsTitle.trim() && subDomain.trim())}
                  >
                    {t("registration.next")}
                    <Icon icon="akar-icons:chevron-right" className="ml-2" />
                  </button>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default One;
