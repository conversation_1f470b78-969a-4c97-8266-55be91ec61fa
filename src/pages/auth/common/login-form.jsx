import React, { useState } from "react";
import { Formik, Form, Field } from "formik";
import * as yup from "yup";
import InputField from "@/components/ui/InputField";
import Checkbox from "@/components/ui/Checkbox";
import Button from "@/components/ui/Button";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { useLoginMutation } from "@/store/api/auth/authApiSlice";
import { useDispatch } from "react-redux";
import { setUser, setOrganization } from "@/store/api/auth/authSlice";
import { Icon } from "@iconify/react";

// Validation schema
const schema = yup.object({
  username: yup.string().required("Username is required"),
  password: yup.string().required("Password is required"),
});

const LoginForm = ({ handleResponse, setError }) => {
  const navigate = useNavigate();
  const [checked, setChecked] = useState(true);
  const [showPassword, setShowPassword] = useState(false);

  const initialValues = {
    username: "",
    password: "",
    remember: false,
  };

  const [login, { isLoading, isError, error, isSuccess }] = useLoginMutation();

  const dispatch = useDispatch();
  const handleSubmit = async (values) => {
    values.checked = checked;
    try {
      const response = await login(values);

      if (response.error) {
        throw new Error(response.error.data.message);
      }


      if (response.data.data.error) {
        throw new Error(response.data.error);
      }

      if (response.data.message) {
        // setError(response.datamessage)
        handleResponse(response.data.message); // Pass the success message
        if(!response.data.data.id){
          throw new Error(response?.data.message);
        }
      }

      if (!response.data.data.token) {
        throw new Error("Invalid credentials");
      }

      let user = response.data.data;
      let organization = user.organization;
      // delete user.organization;
      dispatch(setUser(user));
      dispatch(setOrganization(organization));
      if (checked) {
        localStorage.setItem("user", JSON.stringify(user));
        localStorage.setItem("lms_token", JSON.stringify(response.data.token));
      }
      navigate("/dashboard");
    } catch (error) {
      toast.error(error?.message);
      // handleResponse(error.message || "An error occurred");
      // console.log(error);
    }
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={schema}
      onSubmit={handleSubmit}
    >
      {({ errors, touched, isSubmitting, setFieldValue }) => (
        <Form className="space-y-4">
          {/* Username Field */}
          <Field name="username">
            {({ field }) => (
              <InputField
                {...field}
                label="Username"
                type="text"
                // error={touched.username && errors.username ? errors.username : null}
                className="h-[48px]"
                placeholder="Enter Username"
              />
            )}
          </Field>

          {/* Password Field */}
          <div className="relative">
            <Field name="password">
              {({ field }) => (
                <InputField
                  key={showPassword ? "text" : "password"}
                  {...field}
                  label="Password"
                  type={showPassword ? "text" : "password"}
                  className="h-[48px] pr-10"
                  placeholder="Enter Password"
                />
              )}
            </Field>
            <button
              type="button"
              className="absolute right-3 top-[40px] text-gray-500 hover:text-gray-700"
              onClick={() => setShowPassword(!showPassword)}
            >
              <Icon icon={showPassword ? "akar-icons:eye-closed" : "akar-icons:eye"} className="w-5 h-5" />
            </button>
          </div>

          {/* Remember Me Checkbox */}
          {/* <div className="flex justify-between">
            <Checkbox
              value={checked}
              onChange={() => setChecked(!checked)}
              label="Keep me signed in"
            />
          </div> */}

          {/* Submit Button */}
          <Button
            type="submit"
            text="Sign in"
            className="btn btn-dark block w-full text-center"
            isLoading={isLoading}
          />
        </Form>
      )}
    </Formik>
  );
};

export default LoginForm;
