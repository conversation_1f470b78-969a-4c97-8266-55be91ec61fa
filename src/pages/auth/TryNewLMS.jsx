import React, { useState } from "react";
import Card from "@/components/ui/Card";
import ChooseTemplate from "./StepForNewLMS/ChooseTemplate";
import One from "./StepForNewLMS/One";
import Two from "./StepForNewLMS/Two";
import Three from "./StepForNewLMS/Three";
import RegistrationSuccessful from "./RegistrationSuccessful";
import { useLocation } from "react-router-dom";
import { useTranslation } from 'react-i18next';

const TryNewLMS = () => {
  const [percent, setPercent] = useState(0);
  const [template, setTemplate] = useState(null);
  const [dataToSubmit, setDataToSubmit] = useState({});
  const location = useLocation();
  const { t } = useTranslation();

  console.log(location?.state?.package_id);
  console.log(location?.state?.package_month);

  const items = [
    {
      id: 1,
      title: t("registration.choose_template"),
      description: "Choose a template to get started",
      onClick: () => setPercent(0),
    },
    {
      id: 2,
      title: t("registration.lms_details"),
      description: "Enter LMS details",
      onClick: () => setPercent(25),
    },
    {
      id: 3,
      title: t("registration.how_it_looks"),
      description: "Finish creating LMS",
      onClick: () => setPercent(50),
    },
    {
      id: 4,
      title: t("registration.user_information"),
      description: "Choose a template to get started",
      onClick: () => setPercent(75),
    },
  ];

  const createData = (value) => {
    console.log(value);

    // Create a new FormData instance
    const formData = new FormData();

    formData.append("template_id", template.id);

    for (const key in dataToSubmit) {
      if (dataToSubmit.hasOwnProperty(key)) {
        formData.append(key, dataToSubmit[key]);
      }
    }

    for (const key in value) {
      if (value.hasOwnProperty(key)) {
        formData.append(key, value[key]);
      }
    }

    // for (const pair of formData.entries()) {
    //   console.log(`${pair[0]}: ${pair[1]}`);
    // }

    // Set the data for submission
    const obj = {
      template_id: template.id,
      user_email: location?.state?.email ? location?.state?.email : "",
      package_id: location?.state?.package_id || "",
      package_month: location?.state?.package_month || "",
      is_trial: location?.state?.is_trial || 0,
      ...dataToSubmit,
      ...value,
    };
    setDataToSubmit(obj);

    // Debug after state update
    setTimeout(() => console.log(dataToSubmit), 1000);
  };

  // console.log(location.state.email)

  return (
    <div className="min-h-[93vh] flex flex-col">
      <div className="container mt-28 flex-grow mb-8">
        {percent === 100 ? (
          <RegistrationSuccessful />
        ) : (
          <>
            <div className="px-5 py-5 rounded-md bg-white dark:bg-slate-800 shadow-base">
              <div className="text-center">
                <h1 className="text-2xl mb-4">{t("registration.lms_registration")}</h1>
              </div>
              <div className="grid grid-cols-4 text-center gap-0 px-[-4px] mb-6">
                {items.map((i, index) => (
                  <div key={index}>
                    <div className="flex items-center justify-center mb-2">
                      <div
                        className={
                          i.id > 1
                            ? index <= percent / 25
                              ? "bg-primary-600 border border-2 border-primary-600 h-[10px] w-1/2 "
                              : "bg-gray-200 border border-2 border-gray-200 h-[10px] w-1/2 "
                            : `h-[10px] w-1/2`
                        }
                      ></div>

                      <button
                        type="button"
                        className={`inline-flex items-center justify-center rounded-full p-4 border border-transparent shadow-sm text-lg font-medium ${
                          index === percent / 25
                            ? "bg-primary-600 text-white"
                            : index < percent / 25
                            ? "bg-green-600 text-white"
                            : "bg-gray-200"
                        }`}
                        style={{ width: "56px", height: "56px" }}
                      >
                        {i.id}
                      </button>

                      <div
                        className={
                          i.id < 4
                            ? index < percent / 25
                              ? "bg-primary-600 border border-2 border-primary-600 h-[10px] w-1/2"
                              : "bg-gray-200 border border-2 border-gray-200 h-[10px] w-1/2"
                            : `h-[10px] w-1/2`
                        }
                      ></div>
                    </div>
                    <span> {i.title}</span>
                  </div>
                ))}
              </div>
              {percent === 0 && (
                <ChooseTemplate
                  template={template}
                  setPercent={setPercent}
                  setTemplate={setTemplate}
                  createData={createData}
                  dataToSubmit={dataToSubmit}
                />
              )}
              {percent === 25 && (
                <One
                  setPercent={setPercent}
                  template={template}
                  dataToSubmit={dataToSubmit}
                  createData={createData}
                />
              )}
              {percent === 50 && (
                <Two
                  setPercent={setPercent}
                  template={template}
                  createData={createData}
                  dataToSubmit={dataToSubmit}
                />
              )}
              {percent === 75 && (
                <Three
                  setPercent={setPercent}
                  template={template}
                  createData={createData}
                  dataToSubmit={dataToSubmit}
                  setDataToSubmit={setDataToSubmit}
                />
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default TryNewLMS;
