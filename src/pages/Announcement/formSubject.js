import React from "react";
import * as yup from "yup";

export const initialValues = { 
    title: '', 
    description:'',
    image:'',
    file:'',
    start_date:'',
    end_date:'',
};

export const validationSchema = yup.object({
    title: yup.string().required("Title is required").max(255, "Should not be more than 255 characters"),
    description: yup.string().nullable(),
    image: yup.mixed().nullable().test("fileType", "Invalid file format", value => {
        if (!value) return true; // Allow null value
        const supportedFormats = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/svg+xml"];
        return supportedFormats.includes(value.type);
    }),
    file: yup.mixed().nullable().test("fileType", "Invalid file format", value => {
        if (!value) return true; // Allow null value
        const supportedFormats = ["application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/pdf", "text/plain"];
        return supportedFormats.includes(value.type);
    }),
    start_date: yup.date().required('Start date is required'),
    end_date: yup.date().required('End date is required')
});


export const validationUpdateSchema = yup.object({
    title: yup.string().required("Title is required").max(255, "Should not be more than 255 characters"),
    description: yup.string().nullable(),
    image: yup.mixed().nullable(),
    file: yup.mixed().nullable(),
    start_date: yup.date().nullable(),
    end_date: yup.date().nullable()
});