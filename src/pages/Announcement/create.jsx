import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Textarea from "@/components/ui/Textarea";
import FileInput from "@/components/ui/Fileinput";
import Button from "@/components/ui/Button";
import { Formik, Form } from "formik";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import { initialValues, validationSchema } from "./formSubject";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";

const createSubject = () => {
  const [postApi, { isLoading }] = usePostApiMutation();
  const dispatch = useDispatch();
  const { showModal } = useSelector((state) => state.commonReducer);

  const onSubmit = async (values, { resetForm }) => {
    let formData = new FormData();
    formData.append("title", values.title);
    formData.append("description", values.description);
    if (values.image) formData.append("image", values.image);
    if (values.file) formData.append("file", values.file);

    formData.append(
      "start_date",
      new Date(values.start_date).toISOString().split("T")[0]
    );

    formData.append(
      "end_date",
      new Date(values.end_date).toISOString().split("T")[0]
    );

    await postApi({
      end_point: "admin/announcements",
      body: formData,
    });

    dispatch(setShowModal(false));
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={() => dispatch(setShowModal(false))}
      title="Create An Announcement"
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setShowModal(false))}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={initialValues}
        onSubmit={onSubmit}
      >
        {({
          touched,
          values,
          errors,
          handleBlur,
          handleSubmit,
          setFieldValue,
        }) => (
          console.log(errors),
          console.log(touched),
          <Form>
            <div className="grid md:grid-cols-1 gap-4 mb-4">
              <InputField
                label="Title"
                name="title"
                type="text"
                placeholder="Announcement Title"
                required
              />
             
            </div>
            <div className="grid md:grid-cols-1 gap-4 my-4">
              <Textarea
                label="Description"
                name="description"
                placeholder="Enter Description"
                rows={3}
                onChange={(e) => setFieldValue("description", e.target.value)}
              />
            </div>
            <div className="grid md:grid-cols-2 gap-4 my-4">
              <div>
                <DatePicker
                  label="Appeared From"
                  placeholder="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  name="start_date"
                  onChange={(e) => {
                    setFieldValue("start_date", e);
                  }}
                  required
                />
                {touched.start_date &&errors.start_date && (
                  <div className="text-red-500 text-xs mt-2">{errors.start_date}</div>
                )}
              </div>
              <div>
                <DatePicker
                  label="Appeared To"
                  placeholder="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  name="end_date"
                  onChange={(e) => {
                    setFieldValue("end_date", e);
                  }}
                  required
                /> 
                { touched.end_date && errors.end_date && (
                  <div className="text-red-500 text-xs mt-2">{errors.end_date}</div>
                )}
              </div>
            </div>
            <div className="grid md:grid-cols-2 gap-4 my-4">
              <div className="mb-4">
                <label className="block text-[#1D1D1F] text-base font-medium mb-2">
                  Image
                </label>
                <FileInput
                  name="image"
                  accept="image/*"
                  type="file"
                  placeholder="Image"
                  preview={true}
                  selectedFile={values.image}
                  onChange={(e) => {
                    setFieldValue("image", e.currentTarget.files[0]);
                  }}
                />
              </div>
              <div className="mb-4">
                <label className="block text-[#1D1D1F] text-base font-medium mb-2">
                  File
                </label>
                <FileInput
                  name="file"
                  accept=".pdf,.doc,.docx,.txt"
                  type="file"
                  placeholder="File"
                  preview={true}
                  selectedFile={values.file}
                  onChange={(e) => {
                    setFieldValue("file", e.currentTarget.files[0]);
                  }}
                />
              </div>
            </div>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default createSubject;

