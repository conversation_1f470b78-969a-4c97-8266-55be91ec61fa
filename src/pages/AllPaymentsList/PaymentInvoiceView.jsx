import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";
import { useGetPaymentDetailsQuery } from "@/store/api/master/allPaymentsSlice";
import Loading from "@/components/Loading";

const PaymentInvoiceView = ({ showModal, setShowModal, paymentId }) => {
  const { data, isLoading, error } = useGetPaymentDetailsQuery(paymentId, {
    skip: !paymentId
  });

  const payment = data;

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handlePrint = () => {
    window.print();
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      title="Payment Invoice"
      className="max-w-5xl"
      footer={
        <div className="flex justify-between w-full">
          <Button
            text="Print Invoice"
            icon="heroicons-outline:printer"
            btnClass="btn-success"
            onClick={handlePrint}
          />
          <Button
            text="Close"
            btnClass="btn-primary"
            onClick={() => setShowModal(false)}
          />
        </div>
      }
    >
      {isLoading ? (
        <Loading />
      ) : error ? (
        <div className="text-center text-danger-500 p-4">
          Error loading payment details. Please try again.
        </div>
      ) : payment ? (
        <div className="invoice-wrapper" id="printable-invoice">
          {/* Invoice Header */}
          <div className="flex justify-between items-center mb-6 border-b pb-4">
            <div>
              <h2 className="text-2xl font-bold">INVOICE</h2>
              <p className="text-gray-500 text-sm">#{payment.transaction_id || payment.id}</p>
            </div>
            <div className="text-right">
              <Badge
                label={payment.status}
                className={`${
                  payment.status === "Completed"
                    ? "bg-success-500 text-white"
                    : payment.status === "Pending"
                    ? "bg-warning-500 text-white"
                    : "bg-danger-500 text-white"
                }`}
              />
              <p className="mt-1 text-gray-500">Date: {formatDate(payment.created_at)}</p>
            </div>
          </div>

          {/* Invoice Body */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Customer Information */}
            <Card title="Customer Information" className="h-full">
              <div className="space-y-2">
                <div className="flex">
                  <span className="font-medium w-1/3">Name:</span>
                  <span>{payment.user?.name || "N/A"}</span>
                </div>
                <div className="flex">
                  <span className="font-medium w-1/3">Email:</span>
                  <span>{payment.user?.email || "N/A"}</span>
                </div>
                <div className="flex">
                  <span className="font-medium w-1/3">Contact:</span>
                  <span>{payment.user?.contact_no || "N/A"}</span>
                </div>
                <div className="flex">
                  <span className="font-medium w-1/3">Address:</span>
                  <span>{payment.user?.address || "N/A"}</span>
                </div>
              </div>
            </Card>

            {/* Payment Information */}
            <Card title="Payment Information" className="h-full">
              <div className="space-y-2">
                <div className="flex">
                  <span className="font-medium w-1/3">Method:</span>
                  <span>{payment.payment_method || "N/A"}</span>
                </div>
                <div className="flex">
                  <span className="font-medium w-1/3">Type:</span>
                  <span>{payment.payment_type || "N/A"}</span>
                </div>
                <div className="flex">
                  <span className="font-medium w-1/3">Transaction ID:</span>
                  <span>{payment.transaction_id || "N/A"}</span>
                </div>
                <div className="flex">
                  <span className="font-medium w-1/3">Currency:</span>
                  <span>{payment.currency || "N/A"}</span>
                </div>
              </div>
            </Card>
          </div>

          {/* Course Information */}
          {payment.course && (
            <Card title="Course Information" className="mb-6">
              <div className="flex flex-col md:flex-row md:items-center gap-4">
                {payment.course.thumbnail && (
                  <div className="w-24 h-24 flex-shrink-0">
                    <img 
                      src={import.meta.env.VITE_ASSET_HOST_URL + payment.course.thumbnail} 
                      alt={payment.course.title} 
                      className="w-full h-full object-cover rounded"
                    />
                  </div>
                )}
                <div className="flex-grow">
                  <h3 className="text-lg font-medium">{payment.course.title}</h3>
                  {payment.course.title_bn && (
                    <p className="text-gray-500">{payment.course.title_bn}</p>
                  )}
                  {payment.course.description && (
                    <p className="text-sm mt-2">{payment.course.description}</p>
                  )}
                </div>
              </div>
            </Card>
          )}

          {/* Payment Details */}
          <Card title="Payment Details" className="mb-6">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {payment.details && payment.details.length > 0 ? (
                    payment.details.map((detail, index) => (
                      <tr key={index}>
                        <td className="px-4 py-3 whitespace-nowrap">
                          {detail.course?.title || payment.course?.title || detail.pay_for || "Course Enrollment"}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          {payment.currency} {detail.unit_price || detail.payable_amount || payment.payable_amount}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          {detail.quantity || 1}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          {payment.currency} {detail.total || detail.paid_amount || payment.paid_amount}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td className="px-4 py-3 whitespace-nowrap">
                        {payment.course?.title || payment.item_type || "Course Enrollment"}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        {payment.currency} {payment.payable_amount}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">1</td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        {payment.currency} {payment.paid_amount}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </Card>

          {/* Payment Summary */}
          <div className="flex justify-end">
            <div className="w-full md:w-1/2 lg:w-1/3">
              <div className="border-t pt-4">
                <div className="flex justify-between py-1">
                  <span className="font-medium">Subtotal:</span>
                  <span>{payment.currency} {payment.payable_amount}</span>
                </div>
                <div className="flex justify-between py-1">
                  <span className="font-medium">Discount:</span>
                  <span>{payment.currency} {payment.discount_amount || 0}</span>
                </div>
                <div className="flex justify-between py-1 border-t border-b my-2">
                  <span className="font-bold">Total Paid:</span>
                  <span className="font-bold">{payment.currency} {payment.paid_amount}</span>
                </div>
                {payment.payable_amount > payment.paid_amount && (
                  <div className="flex justify-between py-1 text-danger-500">
                    <span className="font-medium">Due Amount:</span>
                    <span>{payment.currency} {payment.payable_amount - payment.paid_amount}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="mt-8 pt-4 border-t text-center text-gray-500 text-sm">
            <p>Thank you for your business!</p>
            {payment.createdBy && (
              <p className="mt-2">
                Payment processed by: {payment.createdBy.name} ({payment.createdBy.email})
              </p>
            )}
          </div>
        </div>
      ) : (
        <div className="text-center p-4">No payment data available</div>
      )}
    </Modal>
  );
};

export default PaymentInvoiceView;
