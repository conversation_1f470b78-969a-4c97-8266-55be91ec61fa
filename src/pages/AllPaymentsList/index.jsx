import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetAllPaymentsQuery } from "@/store/api/master/allPaymentsSlice";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import Filter from "./Filter";

const AllPaymentsList = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();



  
    const { labels } = useSelector((state) => state.languageReducer);

  const [apiParams, setApiParams] = useState({
    page: 1,
    itemsPerPage: 10
  });

  const { data, isLoading } = useGetAllPaymentsQuery(apiParams);

  console.log(data);
  const changePage = (val) => {
    setApiParams({
      ...apiParams,
      page: val
    });
  };

  const columns = [
    {
      label: `${ labels['student'] || 'Student'} ID`,
      field: "student_code",
    },
    {
      label: `${ labels['student'] || 'Student'}`,
      field: "student",
    },
    {
      label: `${ labels['course'] || 'Course'}`,
      field: "course",
    },
    {
      label: `${ labels['amount'] || 'Amount'}`,
      field: "amount",
    },
    {
      label: `${ labels['Payment Method'.toLocaleLowerCase()] || 'Payment Method'}`,
      field: "payment_method",
    },
    {
      label: "Status",
      field: "status",
    },
    {
      label: "Date",
      field: "date",
    },
    {
      label: "Action",
      field: "",
    },
  ];

  const tableData = data?.data?.map((item) => {
    return {
      transaction_id: item?.transaction_id,
      student_code: item?.student?.student_code,
      student: item.student?.name || "N/A",
      course: item.course?.title || "N/A",
      amount: `${item.currency} ${item.paid_amount}`,
      payment_method: item.payment_method,
      status: (
        <Badge
          label={item.status}
          className={`${
            item.status === "Completed"
              ? "bg-success-500 text-white"
              : item.status === "Pending"
              ? "bg-warning-500 text-white"
              : "bg-danger-500 text-white"
          }`}
        />
      ),
      date: new Date(item.created_at).toLocaleDateString(),
    };
  }) || [];

  const actions = [
    {
      name: "View Invoice",
      icon: "heroicons-outline:document-text",
      onClick: (val) => {
        // Get the payment ID from the tableData array
        const paymentId = tableData[val]?.transaction_id;
        if (paymentId) {
          navigate(`/invoice/${paymentId}`);
        }
      }
    },
  ];

  const handleFilterChange = (filterParams) => {
    setApiParams({
      ...apiParams,
      ...filterParams,
      page: 1 // Reset to first page when filter changes
    });
  };

  return (
    <div>
      <BasicTablePage
        title="All Payments"
        actions={actions}
        columns={columns}
        data={tableData}
        filter={<Filter onFilterChange={handleFilterChange} />}
        loading={isLoading}
        changePage={changePage}
        currentPage={data?.data?.current_page || 1}
        totalPages={data?.data?.last_page || 1}
      />
    </div>
  );
};

export default AllPaymentsList;
