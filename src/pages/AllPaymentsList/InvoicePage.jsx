import React from "react";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";
import { useGetPaymentDetailsQuery } from "@/store/api/master/allPaymentsSlice";
import Loading from "@/components/Loading";
import { useParams, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { ASSET_URL } from "@/config";
const InvoicePage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  
    const { organization } = useSelector((state) => state.auth);
    console.log(organization);

  const { data, isLoading, error } = useGetPaymentDetailsQuery(id, {
    skip: !id
  });

  const payment = data;

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handlePrint = () => {
    const printContents = document.getElementById('printable-invoice').innerHTML;
    const originalContents = document.body.innerHTML;
    
    document.body.innerHTML = `
      <html>
        <head>
          <title>Invoice #${payment.transaction_id || payment.id}</title>
          <style>
            @media print {
              body {
                font-family: Arial, sans-serif;
                line-height: 1.5;
                color: #000;
                background: #fff;
                padding: 20px;
              }
              .no-print {
                display: none !important;
              }
              .print-invoice {
                width: 100%;
                max-width: 800px;
                margin: 0 auto;
              }
              table {
                width: 100%;
                border-collapse: collapse;
              }
              th, td {
                padding: 8px;
                text-align: left;
                border-bottom: 1px solid #ddd;
              }
              .border-t {
                border-top: 1px solid #ddd;
              }
              .border-b {
                border-bottom: 1px solid #ddd;
              }
              .text-right {
                text-align: right;
              }
              .font-bold {
                font-weight: bold;
              }
              .bg-success-500 {
                background-color: #10b981;
                color: white;
                padding: 2px 8px;
                border-radius: 4px;
                display: inline-block;
              }
              .bg-warning-500 {
                background-color: #f59e0b;
                color: white;
                padding: 2px 8px;
                border-radius: 4px;
                display: inline-block;
              }
              .bg-danger-500 {
                background-color: #ef4444;
                color: white;
                padding: 2px 8px;
                border-radius: 4px;
                display: inline-block;
              }
              .text-danger-500 {
                color: #ef4444;
              }
              .text-gray-500 {
                color: #6b7280;
              }
              .p-6 {
                padding: 1.5rem;
              }
              .mb-6 {
                margin-bottom: 1.5rem;
              }
              .mt-8 {
                margin-top: 2rem;
              }
              .flex {
                display: flex;
              }
              .justify-between {
                justify-content: space-between;
              }
              .grid {
                display: grid;
              }
              .gap-6 {
                gap: 1.5rem;
              }
              .overflow-x-auto {
                overflow-x: auto;
              }
              .rounded {
                border-radius: 0.25rem;
              }
              .object-cover {
                object-fit: cover;
              }
              .w-full {
                width: 100%;
              }
              .h-full {
                height: 100%;
              }
            }
          </style>
        </head>
        <body>
          <div class="print-invoice">
            ${printContents}
          </div>
        </body>
      </html>
    `;
    
    window.print();
    document.body.innerHTML = originalContents;
    window.location.reload();
  };

  const handleBack = () => {
    navigate(-1);
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6 no-print">
        <h1 className="text-2xl font-bold">Payment Invoice</h1>
        <div className="flex space-x-4">
          <Button
            text="Back"
            icon="heroicons-outline:arrow-left"
            btnClass="btn-dark"
            onClick={handleBack}
          />
          <Button
            text="Print Invoice"
            icon="heroicons-outline:printer"
            btnClass="btn-success"
            onClick={handlePrint}
          />
        </div>
      </div>

      {isLoading ? (
        <Loading />
      ) : error ? (
        <div className="text-center text-danger-500 p-4">
          Error loading payment details. Please try again.
        </div>
      ) : payment ? (
        <Card className="p-6" id="INVOICE">
          <div className="invoice-wrapper" id="printable-invoice">
            {/* Invoice Header */}
            <div className="flex justify-between items-center mb-6 border-b pb-4">
              <div className="">
                <img src={ASSET_URL + organization?.logo} className="h-12 w-auto" />
                

              </div>
              <div>
                <h2 className="text-2xl font-bold">INVOICE</h2>
                <p className="text-gray-500 text-xs">#{payment.transaction_id || payment.id}</p>
              </div>
              <div className="text-right">
                <Badge
                  label={payment.status}
                  className={`${
                    payment.status === "Completed"
                      ? "bg-success-500 text-white"
                      : payment.status === "Pending"
                      ? "bg-warning-500 text-white"
                      : "bg-danger-500 text-white"
                  }`}
                />
                <p className="mt-1 text-gray-500">Date: {formatDate(payment.created_at)}</p>
              </div>
            </div>

            {/* Invoice Body - Student and Payment Info in a row */}
            <div className="flex md:flex-row gap-6 mb-6">
              {/* Student Information */}
              <div className="w-1/2">
                <Card title="Student Information" className="h-full">
                  <div className="space-y-2">
                    <div className="flex">
                      <span className="font-medium w-1/3">Name:</span>
                      <span>{payment.user?.name || "N/A"}</span>
                    </div>
                    <div className="flex">
                      <span className="font-medium w-1/3">Email:</span>
                      <span>{payment.user?.email || "N/A"}</span>
                    </div>
                    <div className="flex">
                      <span className="font-medium w-1/3">Contact:</span>
                      <span>{payment.user?.contact_no || "N/A"}</span>
                    </div>
                    <div className="flex">
                      <span className="font-medium w-1/3">Address:</span>
                      <span>{payment.student?.current_address || payment.student?.permanent_address || "N/A"}</span>
                    </div>
                  </div>
                </Card>
              </div>

              {/* Payment Information */}
              <div className="w-1/2">
                <Card title="Payment Information" className="h-full">
                  <div className="space-y-2">
                    <div className="flex">
                      <span className="font-medium w-1/2">Method:</span>
                      <span>{payment.payment_method || "N/A"}</span>
                    </div>
                    <div className="flex">
                      <span className="font-medium w-1/2">Type:</span>
                      <span>{payment.payment_type || "N/A"}</span>
                    </div>
                    <div className="flex">
                      <span className="font-medium w-1/2">Transaction ID:</span>
                      <span>{payment.transaction_id || "N/A"}</span>
                    </div>
                    <div className="flex">
                      <span className="font-medium w-1/2">Currency:</span>
                      <span>{payment.currency || "N/A"}</span>
                    </div>
                  </div>
                </Card>
              </div>
            </div>

            {/* Simplified Course Information */}
            {/* {payment.course && (
                <div className="flex items-center gap-4">
           
                  <div>
                    <h3 className="text-lg font-medium">
                      <b>Course Title:</b>
                      {payment.course.title}</h3>
                
                  </div>
                </div>
            )} */}

            {/* Payment Details */}
            <Card title="Payment Details" className="mb-6">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {payment.details && payment.details.length > 0 ? (
                      payment.details.map((detail, index) => (
                        <tr key={index}>
                          <td className="px-4 py-3 whitespace-nowrap">
                           <b> {detail.course?.title || payment.course?.title || detail.pay_for || "Course Enrollment"}</b>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap">
                            {payment.currency} {detail.unit_price || detail.payable_amount || payment.payable_amount}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap">
                            {detail.quantity || 1}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap">
                            {payment.currency} {detail.total || detail.paid_amount || payment.paid_amount}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td className="px-4 py-3 whitespace-nowrap">
                          {payment.course?.title || payment.item_type || "Course Enrollment"}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          {payment.currency} {payment.payable_amount}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">1</td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          {payment.currency} {payment.paid_amount}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </Card>

            {/* Payment Summary */}
            <div className="flex justify-between">

          {/* Footer with Organization Contact Info */}
            <div className="mt-4 px-4 flex justify-between">
              <div className="text-sm text-gray-600 w-2/3">
                <p> <b>Address: </b> {organization?.address}</p>
                <p className="mt-1"> <b>Email:</b> {organization?.email}</p>
                <p className="mt-1"><b>Hotline:</b> {organization?.hotline_number}</p>
                <p className="mt-1"><b>VAT#</b> CHE-214.329.683MWST</p>

              </div>
              <div className="text-center text-gray-500 text-sm w-1/2">
                {payment.createdBy && (
                  <p className="mt-2">
                    Payment processed by: {payment.createdBy.name} ({payment.createdBy.email})
                  </p>
                )}
              </div>
            </div>

              <div className="w-full md:w-1/3 lg:w-1/3">
                <div className="">
                  <div className="flex justify-between py-1">
                    <span className="font-medium">Subtotal:</span>
                    <span>{payment.currency} {payment.payable_amount}</span>
                  </div>
                  <div className="flex justify-between py-1">
                    <span className="font-medium">Discount:</span>
                    <span>{payment.currency} {payment.discount_amount || 0}</span>
                  </div>
                  <div className="flex justify-between pt-1 border-t mt-2">
                    <span className="font-bold">Total Paid:</span>
                    <span className="font-bold">{payment.currency} {payment.paid_amount}</span>
                    
                  </div>
                  <span className="text-xs">
                      *Note: 8.1% VAT Included
                    </span>
                  {payment.payable_amount > payment.paid_amount && (
                    <div className="flex justify-between py-1 text-danger-500">
                      <span className="font-medium">Due Amount:</span>
                      <span>{payment.currency} {payment.payable_amount - payment.paid_amount}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="mt-8 pt-4 border-t text-center text-gray-500 text-sm">
              <p>Thank you for your business!</p>
              {payment.createdBy && (
                <p className="mt-2">
                  Payment processed by: {payment.createdBy.name} ({payment.createdBy.email})
                </p>
              )}
            </div>
          </div>
        </Card>
      ) : (
        <div className="text-center p-4">No payment data available</div>
      )}
    </div>
  );
};

export default InvoicePage;