/* Print-specific styles for invoice */
@media print {
  /* Hide elements not needed for printing */
  button, 
  .no-print {
    display: none !important;
  }

  /* Ensure the page doesn't break inside important elements */
  .invoice-wrapper {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  /* Adjust layout for print */
  body {
    margin: 0;
    padding: 0;
    font-size: 12px;
    width: 100%;
    height: 100%;
  }

  /* Ensure the invoice fits on one page */
  .print-container {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
  }

  /* Reduce spacing */
  .card {
    margin-bottom: 10px !important;
    padding: 8px !important;
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }

  /* Adjust grid for print */
  .print-grid {
    display: flex !important;
    flex-wrap: wrap !important;
  }

  .print-col-half {
    width: 50% !important;
    flex: 0 0 50% !important;
    padding-right: 8px !important;
  }

  /* Reduce font sizes */
  h1 {
    font-size: 18px !important;
  }

  h2 {
    font-size: 16px !important;
  }

  h3 {
    font-size: 14px !important;
  }

  p, span, td, th {
    font-size: 11px !important;
  }

  /* Adjust table for print */
  table {
    width: 100% !important;
    font-size: 10px !important;
  }

  th, td {
    padding: 4px !important;
  }

  /* Reduce spacing between elements */
  .mb-6 {
    margin-bottom: 10px !important;
  }

  .p-6 {
    padding: 10px !important;
  }

  .gap-6 {
    gap: 10px !important;
  }

  .space-y-2 > * {
    margin-top: 4px !important;
    margin-bottom: 4px !important;
  }

  /* Ensure the footer stays at the bottom */
  .invoice-footer {
    margin-top: 10px !important;
    padding-top: 10px !important;
  }
}

/* Regular styles (non-print) */
.print-only {
  display: none;
}

@media print {
  .print-only {
    display: block;
  }
}
