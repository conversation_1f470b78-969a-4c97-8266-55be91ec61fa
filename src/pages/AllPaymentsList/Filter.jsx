import React, { useState } from "react";
import Card from "@/components/ui/Card";
import InputField from "@/components/ui/InputField";
import Button from "@/components/ui/Button";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import Select from "@/components/ui/Select";

const Filter = ({ onFilterChange }) => {
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [search, setSearch] = useState("");
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const handleSubmit = (e) => {
    e.preventDefault();
    
    const filterParams = {
      start_date: startDate ? new Date(startDate).toISOString().split('T')[0] : "",
      end_date: endDate ? new Date(endDate).toISOString().split('T')[0] : "",
      search: search,
      itemsPerPage: itemsPerPage
    };
    
    onFilterChange(filterParams);
  };

  const handleReset = () => {
    setStartDate("");
    setEndDate("");
    setSearch("");
    setItemsPerPage(10);
    
    onFilterChange({
      start_date: "",
      end_date: "",
      search: "",
      itemsPerPage: 10
    });
  };

  return (
    <Card className="mb-4">
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4">
          <div>
            <label className="form-label">Start Date</label>
            <DatePicker
              placeholder="Start Date"
              value={startDate}
              onChange={(date) => setStartDate(date)}
              className="w-full"
            />
          </div>
          
          <div>
            <label className="form-label">End Date</label>
            <DatePicker
              placeholder="End Date"
              value={endDate}
              onChange={(date) => setEndDate(date)}
              className="w-full"
            />
          </div>
          
          <div>
            <label className="form-label">Search</label>
            <input
              type="text"
              placeholder="Search by name, email, transaction ID..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="form-control py-2"
            />
          </div>
          
          <div>
            <label className="form-label">Items Per Page</label>
            <Select
              options={[
                { value: 5, label: "5" },
                { value: 10, label: "10" },
                { value: 25, label: "25" },
                { value: 50, label: "50" },
              ]}
              value={itemsPerPage}
              onChange={(value) => setItemsPerPage(value)}
              className="w-full"
            />
          </div>
        </div>
        
        <div className="flex justify-end space-x-4 p-4 border-t">
          <Button
            text="Reset"
            className="btn-outline-dark"
            onClick={handleReset}
          />
          <Button
            text="Filter"
            className="btn-primary"
            type="submit"
          />
        </div>
      </form>
    </Card>
  );
};

export default Filter;
