import React from 'react';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';

const DeleteConfirmationModal = ({ isOpen, onClose, onConfirm, ebookTitle, isLoading = false }) => {
  return (
    <Modal
      title="Confirm Deletion"
      activeModal={isOpen}
      onClose={onClose}
      className="max-w-4xl"
    >
      <div className="p-5">
        <div className="mb-6">
          <p className="text-gray-700">
            Are you sure you want to delete the ebook <span className="font-semibold">"{ebookTitle}"</span>?
          </p>
          <p className="text-gray-500 text-sm mt-2">
            This action cannot be undone.
          </p>
        </div>

        <div className="flex justify-end space-x-3">
          <Button
            text="Cancel"
            className="btn-outline-dark"
            onClick={onClose}
            type="button"
          />
          <Button
            text="Delete"
            className="btn-danger"
            onClick={onConfirm}
            type="button"
            isLoading={isLoading}
            disabled={isLoading}
          />
        </div>
      </div>
    </Modal>
  );
};

export default DeleteConfirmationModal;
