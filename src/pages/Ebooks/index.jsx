import React, { useState } from 'react';
import BasicTablePage from '@/components/partials/common-table/table-basic';
import { useDispatch } from 'react-redux';
import { setEditData, setEditShowModal } from '@/features/commonSlice';
import EbookForm from './EbookForm';
import EbookViewModal from './EbookViewModal';
import DeleteConfirmationModal from './DeleteConfirmationModal';
import { useGetApiQuery, useDeleteApiMutation } from '@/store/api/master/commonSlice';
import { formattedDate } from '@/utils/date';

const EbookList = () => {
    const dispatch = useDispatch();
    const [apiParam, setApiParam] = useState('');
    const { data, isLoading, error, refetch } = useGetApiQuery(`admin/ebooks${apiParam}`);
    const ebooks = data || [];
    const [viewModalOpen, setViewModalOpen] = useState(false);
    const [selectedEbook, setSelectedEbook] = useState(null);
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);
    const [ebookToDelete, setEbookToDelete] = useState(null);

    const [deleteApi, { isLoading: isDeleting }] = useDeleteApiMutation();
    const tableData = ebooks.map(ebook => ({
        ...ebook,
        created_at: formattedDate(ebook.created_at)
    }));

    const columns = [
        {
            label: 'Title',
            field: 'title'
        },
        {
            label: 'Price',
            field: 'price',
            formatter: (value) => `$${value}`
        },
        {
            label: 'Created At',
            field: 'created_at'
        },
        {
            label: 'Actions',
            field: ''
        }
    ];

    const actions = [
        {
            name: 'view',
            icon: 'heroicons-outline:eye',
            onClick: (index) => {
                setSelectedEbook(tableData[index]);
                setViewModalOpen(true);
            }
        },
        {
            name: 'edit',
            icon: 'heroicons:pencil-square',
            onClick: (index) => {
                dispatch(setEditData(tableData[index]));
                dispatch(setEditShowModal(true));
            }
        },
        {
            name: 'delete',
            icon: 'heroicons-outline:trash',
            onClick: (index) => {
                setEbookToDelete(tableData[index]);
                setDeleteModalOpen(true);
            }
        }
    ];

    const handleDeleteConfirm = async () => {
        if (ebookToDelete) {
            try {
                await deleteApi({
                    end_point: `admin/ebooks/${ebookToDelete.id}`,
                    body: {}
                });
                setDeleteModalOpen(false);
                refetch();
            } catch (error) {
                console.error('Error deleting ebook:', error);
            }
        }
    };

    const changePage = (page) => {
        console.log('Change page to:', page);
    };

    const handleSearch = (searchTerm) => {
        if (searchTerm && searchTerm.trim() !== '') {
            const searchQuery = searchTerm.replace('?search=', '').toLowerCase();
            if (searchQuery) {
                setApiParam(`?search=${searchQuery}`);
            }
        } else {
            setApiParam('');
        }
    };

    return (
        <div>
            {isLoading ? <p>Loading...</p> : error ? <p>Error loading data</p> :
                <>
                    <BasicTablePage
                        title="Ebook List"
                        createButton="Add New Ebook"
                        actions={actions}
                        columns={columns}
                        data={tableData}
                        changePage={changePage}
                        currentPage={1}
                        totalPages={data && data.length > 0 ? Math.ceil(data.length / 10) : 1}
                        loading={isLoading}
                        filter={true}
                        setFilter={handleSearch}
                        createPage={<EbookForm />}
                        editPage={<EbookForm />}
                    />
                    <EbookViewModal
                        ebook={selectedEbook}
                        isOpen={viewModalOpen}
                        onClose={() => setViewModalOpen(false)}
                    />
                    <DeleteConfirmationModal
                        isOpen={deleteModalOpen}
                        onClose={() => setDeleteModalOpen(false)}
                        onConfirm={handleDeleteConfirm}
                        ebookTitle={ebookToDelete?.title || ''}
                        isLoading={isDeleting}
                    />
                </>
            }
        </div>
    );
};

export default EbookList;
