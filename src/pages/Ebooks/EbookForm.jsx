import React, { useState } from 'react';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import { useDispatch, useSelector } from 'react-redux';
import { setShowModal, setEditShowModal } from '@/features/commonSlice';
import { Formik, Form } from 'formik';
import * as yup from 'yup';
import InputField from '@/components/ui/InputField';
import Textarea from '@/components/ui/Textarea';
import FileInput from '@/components/ui/FileInput';
import { usePostApiMutation, useUpdateApiMutation } from '@/store/api/master/commonSlice';

const getValidationSchema = (isEdit) => yup.object({
  title: yup.string()
    .required('Title is required')
    .max(100, 'Title should not be more than 100 characters'),

  description: yup.string()
    .max(500, 'Description should not be more than 500 characters'),
  price: yup.number()
    .typeError('Price must be a number')
    .required('Price is required')
    .min(0, 'Price cannot be negative'),
  pdf: yup.mixed()
    .test('fileType', 'Only PDF files are allowed', function (value) {
      if (!value) {
        // If editing, allow empty value
        if (isEdit) return true;
        // If creating new, require a value
        return this.createError({ message: 'PDF file is required' });
      }
      return value && (
        (value instanceof File && value.type === 'application/pdf') ||
        (typeof value === 'string')
      );
    }),
  image: yup.mixed()
    .test('fileType', 'Only image files are allowed', function (value) {
      if (!value) {
        if (isEdit) return true;
        return this.createError({ message: 'Cover image is required' });
      }
      return value && (
        (value instanceof File && value.type.startsWith('image/')) ||
        (typeof value === 'string')
      );
    })
});

const EbookForm = () => {
  const dispatch = useDispatch();
  const { showModal, showEditModal, editData } = useSelector((state) => state.commonReducer);
  const [isLoading, setIsLoading] = useState(false);
  const [postApi] = usePostApiMutation();
  const [updateApi] = useUpdateApiMutation();
  const isEditMode = showEditModal && editData;

  // Get the appropriate validation schema based on whether we're editing or creating
  const validationSchema = getValidationSchema(isEditMode);

  const onSubmit = async (values, { resetForm }) => {
    setIsLoading(true);
    try {
      const formData = new FormData();
      formData.append('title', values.title);
      formData.append('description', values.description);
      formData.append('price', values.price);

      if (values.image && values.image instanceof File) {
        formData.append('image', values.image);
      }

      if (values.pdf && values.pdf instanceof File) {
        formData.append('pdf', values.pdf);
      }

      if (isEditMode) {
        await updateApi({
          end_point: `admin/ebooks/${editData.id}`,
          body: formData
        });
      } else {
        await postApi({
          end_point: 'admin/ebooks',
          body: formData
        });
      }

      // Close modal and reset form
      handleCloseModal();
      resetForm();
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCloseModal = () => {
    if (showModal) {
      dispatch(setShowModal(false));
    }
    if (showEditModal) {
      dispatch(setEditShowModal(false));
    }
  };

  return (
    <Modal
      title={isEditMode ? 'Edit Ebook' : 'Add New Ebook'}
      activeModal={showModal || showEditModal}
      onClose={handleCloseModal}
      className={'max-w-5xl'}
    >
      <Formik
        initialValues={{
          title: isEditMode && editData ? editData.title || '' : '',
          description: isEditMode && editData ? editData.description || '' : '',
          price: isEditMode && editData ? editData.price || '' : '',
          image: isEditMode && editData ? editData.image_url || null : null,
          pdf: isEditMode && editData ? editData.pdf_url || null : null
        }}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
        enableReinitialize
      >
        {({ isSubmitting }) => (
          <Form className="space-y-4 p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <InputField
                  name="title"
                  label="Title"
                  placeholder="Enter title"
                  required
                />
              </div>
              <div>
                <InputField
                  name="price"
                  label="Price"
                  type="number"
                  placeholder="Enter price"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <FileInput
                  name="image"
                  label="Cover Image"
                  accept={{ 'image/*': ['.jpg', '.jpeg', '.png', '.gif'] }}
                  accepts="image/"
                  required={!isEditMode}
                />
              </div>
              <div>
                <FileInput
                  name="pdf"
                  label="PDF File"
                  accept={{ 'application/pdf': ['.pdf'] }}
                  accepts="application/pdf"
                  isPdfOnly={true}
                  required={!isEditMode}
                />
              </div>
            </div>

            <div>
              <Textarea
                name="description"
                label="Description"
                placeholder="Enter ebook description"
                rows={4}
              />
            </div>

            <div className="flex justify-end space-x-3 mt-4">
              <Button
                text="Cancel"
                className="btn-outline-dark"
                onClick={handleCloseModal}
                type="button"
              />
              <Button
                text={isEditMode ? 'Update' : 'Save'}
                className="btn-primary"
                type="submit"
                isLoading={isLoading || isSubmitting}
                disabled={isLoading || isSubmitting}
              />
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default EbookForm;
