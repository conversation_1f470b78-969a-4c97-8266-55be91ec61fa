import React, { useState } from "react";
import Card from "@/components/ui/Card";
import GroupChart1 from "./chart/group-chart-1";
import RevenueBarChart from "./chart/revenue-bar-chart";
import PendingPaymentsTable from "./Table/PendingPaymentsTable";
import HomeBredCurbs from "./HomeBredCurbs";
import Icon from "@/components/ui/Icon";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Loading from "@/components/Loading";
import { useParams, Link } from "react-router-dom";
import { useTranslation } from "react-i18next";

const Dashboard = () => {
  const [filterMap, setFilterMap] = useState("usa");
  const { id } = useParams();
  const { t } = useTranslation();
  const dashboard = useGetApiQuery(`admin/${id ? `super-admin-organization-dashboard?id=${id}` : "dashboard"}`)?.data;



  if (!dashboard) {
    return <Loading />;
  }
  return (
    <div>
      {id && (
              <div className="flex items-center mb-4">
              <Link to="/organizations" className="btn btn-secondary btn-sm flex items-center gap-1">
                <Icon icon="heroicons:chevron-left" className="w-5 h-5" />
                {t("dashboard.go_back")}
              </Link>
            </div>

      )}
      <HomeBredCurbs title={dashboard?.name } />
      <div className="grid grid-cols-1 gap-5 mb-5">

        <div className="">
          <Card bodyClass="p-4">
            <div className="grid md:grid-cols-4 col-span-1 gap-4">
              <GroupChart1 dashboard={dashboard}/>
            </div>
          </Card>
        </div>
      </div>
      <div className="grid grid-cols-12 gap-5">
        <div className="lg:col-span-8 col-span-12">
          <Card>
            <div className="legend-ring">
              <RevenueBarChart lastYearData={dashboard?.last_year_per_months} />
            </div>
          </Card>
        </div>
        <div className="lg:col-span-4 col-span-12">
            <PendingPaymentsTable pendingPayments={dashboard?.pending_payments} />
        </div>

      </div>
    </div>
  );
};

export default Dashboard;
