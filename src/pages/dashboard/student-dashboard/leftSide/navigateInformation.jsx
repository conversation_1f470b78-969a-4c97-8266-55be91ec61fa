import React from "react";
import SimplificationIcon1 from "@/assets/StudentDashboard/Simplification1.svg";
import SimplificationIcon2 from "@/assets/StudentDashboard/Simplification2.svg";
import SimplificationIcon3 from "@/assets/StudentDashboard/Simplification3.svg";
import SimplificationIcon4 from "@/assets/StudentDashboard/Simplification4.svg";
import SimplificationIcon5 from "@/assets/StudentDashboard/Simplification5.svg";
import { Link } from "react-router-dom";

const navigateInformation = () => {
  const menuData = [
    {
      title: "Subscription",
      icon: SimplificationIcon1,
      link: ''
    },
    {
      title: "Certificates",
      icon: SimplificationIcon2,
      link: '/my-certificates'
    },
    {
      title: "Accounts",
      icon: SimplificationIcon3,
      link: '/profile'
    },
    {
      title: "Payment History",
      icon: SimplificationIcon4,
      link: ''
    },
    {
      title: "Device Manager",
      icon: SimplificationIcon5,
      link: '/device-manager'
    },
  ];

  return (
    <div className="shadow-lg p-3">
      {/* Menu List */}
      <div className="space-y-4 p-4">
        {menuData.map((data, index) => (
          <Link
            key={index}
            to={data.link}
            className="flex items-center border border-gray-200 p-3 gap-4 shadow hover:shadow-lg transition-transform duration-200 rounded-md"
          >
            {/* Icon */}
            <img
              src={data.icon}
              alt={`${data.title} Icon`}
              width={28}
              height={28}
              className="rounded-md"
            />
            {/* Title */}
            <p className="text-sm font-medium text-[#4D4D4D]">{data.title}</p>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default navigateInformation;
