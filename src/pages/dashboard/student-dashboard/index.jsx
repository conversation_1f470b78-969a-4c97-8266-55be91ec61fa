import React, { useState } from "react";
import Card from "@/components/ui/Card";
import StudentInformation from "./leftSide/studentInformation";
import NavigateInformation from "./leftSide/navigateInformation";
import ActivityOverView from "./leftSide/activityOverView";
import TopCardInformation from "./center/topInformation";
import MyCoursesInformation from "./center/myCourseInformation";
import ClassSchedules from "./rightSide/ClassSchedules";
import PendingAssignment from "./rightSide/pendingAssignment";
import ResumeInformation from "./center/resume";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import { useParams } from "react-router-dom";

const StudentDashboard = () => {
  const [isLoading, setLoading] = useState(false);
  const {id} = useParams();
  const { data: studentDashboard, isError } = useGetApiQuery('student-dashboard');
  console.log(studentDashboard)

  // console.log("student", studentDashboard?.data);

  const pendingAssignment = studentDashboard?.data?.assignments;
  const courses = studentDashboard?.data?.my_course;
  const lastActivities = studentDashboard?.data?.latest_activities;
  const nextClasses = studentDashboard?.data?.next_class;

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error fetching data</div>;
  
  return (
    <>
      <div className="my-5"></div>
      <div className="grid grid-cols-12 gap-5 mb-5 mx-5">
        <div className="md:col-span-12 lg:col-span-3 col-span-12 space-y-4">
          <div className="shadow-lg border border-slate-200 rounded-lg p-4">
            <StudentInformation />
          </div>
          <div className="shadow-lg border border-slate-200 rounded-lg p-4 ">
            <ActivityOverView />
          </div>
          <div className="shadow-lg border border-slate-200 rounded-lg">
            <NavigateInformation />
          </div>
        </div>
        <div className="md:col-span-12 lg:col-span-6 col-span-12 space-y-4">
          <Card className="shadow-lg border border-slate-200 rounded-lg">
            <TopCardInformation />
          </Card>
          <div className="shadow-lg border border-slate-200 rounded-lg">
            <ResumeInformation lastActivity={lastActivities} />
          </div>
          <div className="shadow-lg border border-slate-200 rounded-lg">
            <MyCoursesInformation courses={courses} />
          </div>
        </div>
        <div className="md:col-span-12 lg:col-span-3 col-span-12 space-y-4">
          <div className="shadow-lg border border-slate-200 rounded-lg">
            <ClassSchedules classes={nextClasses} />
          </div>
          <div className="shadow-lg border border-slate-200 rounded-lg">
            <PendingAssignment assignments={pendingAssignment} />
          </div>
        </div>
      </div>
    </>
  );
};

export default StudentDashboard;
