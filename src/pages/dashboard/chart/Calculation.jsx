import React from "react";
import { colors } from "@/constant/data";
import Chart from "react-apexcharts";
import useDarkMode from "@/hooks/useDarkMode";

const Calculation = ({ dashboard, height = 335 }) => {
  const [isDark] = useDarkMode();

  const totalElements =
    dashboard?.videos_count +
    dashboard?.quizzes_count +
    dashboard?.scripts_count;

  const series = [
    dashboard?.videos_count,
    dashboard?.quizzes_count,
    dashboard?.scripts_count,
  ];

  const labels = ["Video", "Quiz", "Script"];

  const options = {
    labels: labels,
    dataLabels: {
      enabled: true,
      formatter: (val) => `${val.toFixed(1)}%`,
    },
    colors: [colors.success, colors.warning, "#A3A1FB"],
    legend: {
      position: "bottom",
      fontSize: "12px",
      fontFamily: "Inter",
      fontWeight: 400,
      labels: {
        colors: isDark ? "#CBD5E1" : "#475569",
      },
      markers: {
        width: 6,
        height: 6,
        offsetY: -1,
        offsetX: -5,
        radius: 12,
      },
      itemMargin: {
        horizontal: 10,
        vertical: 0,
      },
    },
    tooltip: {
      enabled: true,
      y: {
        formatter: (value, { seriesIndex }) => {
          const percentage = ((value / totalElements) * 100).toFixed(1);
          return `${percentage}% ${labels[seriesIndex]}`;
        },
      },
    },
    responsive: [
      {
        breakpoint: 480,
        options: {
          legend: {
            position: "bottom",
          },
        },
      },
    ],
  };

  return (
    <>
      <Chart options={options} series={series} type="pie" height={height} />
    </>
  );
};

export default Calculation;
