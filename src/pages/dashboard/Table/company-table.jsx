import React from "react";
import { useTable, usePagination } from "react-table";

const COLUMNS = [
  {
    Header: "Institute Name",
    accessor: "instituteName",
  },
  {
    Header: "Students Number",
    accessor: "studentsNumber",
  },
  {
    Header: "Teacher Number",
    accessor: "mentorNumber",
  },
  {
    Header: "Total Video",
    accessor: "totalVideo",
  },
  {
    Header: "Total Quiz",
    accessor: "totalQuiz",
  },
  {
    Header: "Total Learning Materials",
    accessor: "totalScripts",
  },
];

const DUMMY_DATA = [
  {
    instituteName: "Institute 1",
    studentsNumber: "100",
    mentorNumber: "15",
    totalVideo: "50",
    totalQuiz: "20",
    totalScripts: "30",
  },
  {
    instituteName: "Institute 2",
    studentsNumber: "80",
    mentorNumber: "10",
    totalVideo: "40",
    totalQuiz: "15",
    totalScripts: "25",
  },
  {
    instituteName: "Institute 3",
    studentsNumber: "60",
    mentorNumber: "8",
    totalVideo: "30",
    totalQuiz: "10",
    totalScripts: "20",
  },
  {
    instituteName: "Institute 1",
    studentsNumber: "100",
    mentorNumber: "15",
    totalVideo: "50",
    totalQuiz: "20",
    totalScripts: "30",
  },
  {
    instituteName: "Institute 2",
    studentsNumber: "80",
    mentorNumber: "10",
    totalVideo: "40",
    totalQuiz: "15",
    totalScripts: "25",
  },
  {
    instituteName: "Institute 3",
    studentsNumber: "60",
    mentorNumber: "8",
    totalVideo: "30",
    totalQuiz: "10",
    totalScripts: "20",
  },
];

const CompanyTable = () => {
  const columns = React.useMemo(() => COLUMNS, []);
  const data = React.useMemo(() => DUMMY_DATA, []);

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    page,
    prepareRow,
    gotoPage,
    setPageSize,
    state: { pageIndex, pageSize },
    canPreviousPage,
    canNextPage,
  } = useTable(
    {
      columns,
      data,
      initialState: { pageSize: 10 },
    },
    usePagination
  );

  return (
    <div>
      <div className="overflow-x-auto -mx-6">
        <div className="inline-block min-w-full align-middle">
          <div className="overflow-hidden ">
            <table
              className="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700"
              {...getTableProps}
            >
              <thead className=" bg-slate-200 dark:bg-slate-700">
                {headerGroups.map((headerGroup) => (
                  <tr {...headerGroup.getHeaderGroupProps()}>
                    {headerGroup.headers.map((column) => (
                      <th
                        {...column.getHeaderProps()}
                        scope="col"
                        className=" text-left table-th "
                      >
                        {column.render("Header")}
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody
                className="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700"
                {...getTableBodyProps}
              >
                {page.map((row) => {
                  prepareRow(row);
                  return (
                    <tr {...row.getRowProps()}>
                      {row.cells.map((cell) => {
                        return (
                          <td {...cell.getCellProps()} className="table-td">
                            {cell.render("Cell")}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div className="flex justify-between py-4">
        <button
          className="px-4 py-2 rounded-l-lg bg-slate-200 text-slate-700 hover:bg-slate-300"
          onClick={() => gotoPage(0)}
          disabled={!canPreviousPage}
        >
          {"<<"}
        </button>{" "}
        <button
          className="px-4 py-2 rounded-r-lg bg-slate-200 text-slate-700 hover:bg-slate-300"
          onClick={() => gotoPage(pageIndex - 1)}
          disabled={!canPreviousPage}
        >
          {"<"}
        </button>{" "}
        <span>
          Page{" "}
          <strong>
            {pageIndex + 1} of {Math.ceil(data.length / pageSize)}
          </strong>{" "}
        </span>{" "}
        <button
          className="px-4 py-2 rounded-l-lg bg-slate-200 text-slate-700 hover:bg-slate-300"
          onClick={() => gotoPage(pageIndex + 1)}
          disabled={!canNextPage}
        >
          {">"}
        </button>{" "}
        <button
          className="px-4 py-2 rounded-r-lg bg-slate-200 text-slate-700 hover:bg-slate-300"
          onClick={() =>
            gotoPage(Math.ceil(data.length / pageSize) - 1)
          }
          disabled={!canNextPage}
        >
          {">>"}
        </button>{" "}
        <select
          className="p-1"
          value={pageSize}
          onChange={(e) => {
            setPageSize(Number(e.target.value));
          }}
        >
          {[10, 20, 30, 40, 50].map((pageSize) => (
            <option key={pageSize} value={pageSize}>
              Show {pageSize}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default CompanyTable;

