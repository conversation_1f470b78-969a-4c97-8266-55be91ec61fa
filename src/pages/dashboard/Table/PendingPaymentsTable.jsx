import React from "react";
import { Link } from "react-router-dom";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
const PendingPaymentsTable = ({ pendingPayments }) => {
  const { id } = useParams();
  const { t } = useTranslation();
  return (
    <div className="p-4 bg-white shadow-lg rounded-2xl">
      <div className="flex justify-between items-center pb-4 border-b">
        <h2 className="text-xl font-semibold">{t("dashboard.pending_payments")}</h2>
        { !id &&
        <Link to="/pending-payment-list" className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
          {t("dashboard.see_more")}
        </Link>
        }
      </div>
      <div className="overflow-x-auto mt-4">
        <table className="w-full border-collapse text-left">
          <thead>
            <tr className="bg-gray-100 text-gray-700">
              <th className="p-2">User</th>
              <th className="p-2">Payment Method</th>
              <th className="p-2">Amount</th>
            </tr>
          </thead>
          <tbody>
            {pendingPayments.length > 0 ? (
              pendingPayments.map((payment) => (
                <tr key={payment.id} className="border-b hover:bg-gray-50">
                  <td className="p-2 flex items-center gap-2">
                    <img
                      src={import.meta.env.VITE_ASSET_HOST_URL + payment.user_image}
                      alt="User"
                      className="w-8 h-8 rounded-full"
                      onError={(e) => {
                        e.target.src = "/src/assets/images/all-img/user.png";
                      }}
                    />
                    {payment.user_name}
                  </td>
                  <td className="p-2">{payment.payment_method}</td>
                  <td className="p-2">&#x9f3; {payment.paid_amount.toFixed(2)}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="6" className="p-4 text-center text-gray-500">
                  No pending payments
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PendingPaymentsTable;
