import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";

const View = ({ showPurchaseModal, setShowPurchaseModal, data }) => {
  return (
    <Modal
      activeModal={showPurchaseModal}
      onClose={() => setShowPurchaseModal(false)}
      title="Purchase Details"
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowPurchaseModal(false)}
        />
      }
    >
      {data && (
        <Card>
          <div className="flex justify-between bg-white p-3 rounded-lg shadow border dark:border-gray-700">
            <div className="w-1/2 px-4">
              <div className="flex gap-4 mb-2">
                <div className="font-bold w-1/2">Course Name:</div>
                <div className="w-1/2">{data.course_name}</div>
              </div>
              <div className="flex gap-4 mb-2">
                <div className="font-bold w-1/2">Name:</div>
                <div className="w-1/2">{data.name}</div>
              </div>
              <div className="flex gap-4 mb-2">
                <div className="font-bold w-1/2">Email:</div>
                <div className="w-1/2">{data.email}</div>
              </div>
              <div className="flex gap-4 mb-2">
                <div className="font-bold w-1/2">Phone:</div>
                <div className="w-1/2">{data.contact_no}</div>
              </div>
            </div>
            <div className="w-1/2 px-4">
              <div className="flex gap-4 mb-2">
                <div className="font-bold w-1/2">Type:</div>
                <div className="w-1/2">{data.item_type}</div>
              </div>
              <div className="flex gap-4 mb-2">
                <div className="font-bold w-1/2">Item Price:</div>
                <div className="w-1/2">{data.item_price}</div>
              </div>
              <div className="flex gap-4 mb-2">
                <div className="font-bold w-1/2">Paid Amount:</div>
                <div className="w-1/2">{data.paid_amount}</div>
              </div>
              <div className="flex gap-4 mb-2">
                <div className="font-bold w-1/2">Discount:</div>
                <div className="w-1/2">{data.discount}</div>
              </div>
            </div>
          </div>
        </Card>
      )}
    </Modal>
  );
};

export default View;
