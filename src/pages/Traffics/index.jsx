import React, { useState } from "react";
import { Icon } from "@iconify/react";
import VisitorStatistics from "./VisitorStatistics";
import VisitorList from "./VisitorList";

const Traffics = () => {
  const [activeTab, setActiveTab] = useState("statistics");

  const tabs = [
    {
      id: "statistics",
      label: "Analytics Dashboard",
      icon: "heroicons:chart-bar-square",
      component: VisitorStatistics,
    },
    {
      id: "visitors",
      label: "Visitor Records",
      icon: "heroicons:table-cells",
      component: VisitorList,
    },
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
            Traffic Analytics
          </h1>
          <p className="text-slate-600 dark:text-slate-400 mt-1">
            Monitor and analyze visitor behavior across your promotional pages
          </p>
        </div>
        <div className="flex items-center space-x-2 text-slate-600 dark:text-slate-400">
          <Icon icon="heroicons:globe-alt" className="w-5 h-5" />
          <span className="text-sm">Real-time visitor tracking</span>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-slate-200 dark:border-slate-700">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? "border-primary-500 text-primary-600 dark:text-primary-400"
                  : "border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300"
              }`}
            >
              <Icon icon={tab.icon} className="w-5 h-5" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {ActiveComponent && <ActiveComponent />}
      </div>
    </div>
  );
};

export default Traffics;
