import React, { useState } from "react";
import Card from "@/components/ui/Card";
import { useGetVisitorListQuery } from "@/store/api/master/visitorTrackingSlice";
import { Icon } from "@iconify/react";
import Loading from "@/components/Loading";

const VisitorList = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(50);
  const [pageFilter, setPageFilter] = useState("");
  const [dateRange, setDateRange] = useState(7);

  const { data: visitorData, isLoading, error } = useGetVisitorListQuery({
    page: currentPage,
    per_page: perPage,
    page_name: pageFilter || undefined,
    date_range: dateRange,
  });

  console.log(visitorData);

  const visitors = visitorData?.visitors || [];
  const pagination = visitorData?.pagination || {};

  const pageOptions = [
    { value: "", label: "All Pages" },
    { value: "home", label: "Home" },
    { value: "pricing", label: "Pricing" },
    { value: "try-new-lms", label: "Try New LMS" },
  ];

  const perPageOptions = [25, 50, 100];

  const dateRangeOptions = [
    { value: 1, label: "Last 24 Hours" },
    { value: 7, label: "Last 7 Days" },
    { value: 30, label: "Last 30 Days" },
    { value: 90, label: "Last 90 Days" },
  ];

  const getBrowserIcon = (browserName) => {
    switch (browserName?.toLowerCase()) {
      case 'chrome': return 'logos:chrome';
      case 'firefox': return 'logos:firefox';
      case 'safari': return 'logos:safari';
      case 'edge': return 'logos:microsoft-edge';
      default: return 'heroicons:globe-alt';
    }
  };

  const getDeviceIcon = (deviceType) => {
    switch (deviceType?.toLowerCase()) {
      case 'desktop': return 'heroicons:computer-desktop';
      case 'mobile': return 'heroicons:device-phone-mobile';
      case 'tablet': return 'heroicons:device-tablet';
      default: return 'heroicons:computer-desktop';
    }
  };

  const getOSIcon = (os) => {
    switch (os?.toLowerCase()) {
      case 'windows': return 'logos:microsoft-windows';
      case 'macos': return 'logos:apple';
      case 'linux': return 'logos:linux-tux';
      case 'android': return 'logos:android';
      case 'ios': return 'logos:apple';
      default: return 'heroicons:computer-desktop';
    }
  };

  const formatLocalTime = (utcTime) => {
    if (!utcTime) return 'N/A';

    const date = new Date(utcTime);

    // Add 6 hours to the time
    const adjustedDate = new Date(date.getTime() + (6 * 60 * 60 * 1000));

    // Format with user's local timezone
    const localDate = adjustedDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
    });

    const localTime = adjustedDate.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true,
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
    });

    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    return {
      date: localDate,
      time: localTime,
      timezone: timezone.split('/')[1]?.replace('_', ' ') || timezone
    };
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const renderPagination = () => {
    const totalPages = pagination.last_page || 1;
    const current = pagination.current_page || 1;
    
    return (
      <div className="flex items-center justify-between mt-6">
        <div className="text-sm text-slate-600 dark:text-slate-400">
          Showing {((current - 1) * perPage) + 1} to {Math.min(current * perPage, pagination.total || 0)} of {pagination.total || 0} results
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handlePageChange(current - 1)}
            disabled={current <= 1}
            className="px-3 py-1 border border-slate-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-50"
          >
            Previous
          </button>
          
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            const page = i + Math.max(1, current - 2);
            if (page > totalPages) return null;
            
            return (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-3 py-1 border rounded-md ${
                  page === current
                    ? 'bg-primary-500 text-white border-primary-500'
                    : 'border-slate-300 hover:bg-slate-50'
                }`}
              >
                {page}
              </button>
            );
          })}
          
          <button
            onClick={() => handlePageChange(current + 1)}
            disabled={current >= totalPages}
            className="px-3 py-1 border border-slate-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-50"
          >
            Next
          </button>
        </div>
      </div>
    );
  };

  if (isLoading) return <Loading />;
  if (error) return <div className="text-red-500">Error loading visitor list</div>;

  return (
    <div className="space-y-6">
      {/* Header and Filters */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <h2 className="text-2xl font-bold text-slate-900 dark:text-white">
          Visitor Records
        </h2>
        
        <div className="flex flex-col sm:flex-row gap-4">
          <select
            value={pageFilter}
            onChange={(e) => {
              setPageFilter(e.target.value);
              setCurrentPage(1);
            }}
            className="px-3 py-2 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            {pageOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          
          <select
            value={dateRange}
            onChange={(e) => {
              setDateRange(Number(e.target.value));
              setCurrentPage(1);
            }}
            className="px-3 py-2 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            {dateRangeOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          
          <select
            value={perPage}
            onChange={(e) => {
              setPerPage(Number(e.target.value));
              setCurrentPage(1);
            }}
            className="px-3 py-2 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            {perPageOptions.map((option) => (
              <option key={option} value={option}>
                {option} per page
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Visitor List */}
      <Card>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
            <thead className="bg-slate-50 dark:bg-slate-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                  Page
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                  Browser
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                  Device
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                  Location
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                  Source
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                  Visit Time
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-slate-900 divide-y divide-slate-200 dark:divide-slate-700">
              {visitors.map((visitor) => (
                <tr key={visitor.id} className="hover:bg-slate-50 dark:hover:bg-slate-800">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-slate-900 dark:text-white capitalize">
                        {visitor.page_name}
                      </div>
                      <div className="text-sm text-slate-500 dark:text-slate-400 truncate max-w-xs">
                        {visitor.page_url}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <Icon icon={getBrowserIcon(visitor.browser_name)} className="w-4 h-4" />
                      <div>
                        <div className="text-sm text-slate-900 dark:text-white">
                          {visitor.browser_name}
                        </div>
                        <div className="text-xs text-slate-500 dark:text-slate-400">
                          v{visitor.browser_version}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <Icon icon={getDeviceIcon(visitor.device_type)} className="w-4 h-4" />
                      <div>
                        <div className="text-sm text-slate-900 dark:text-white">
                          {visitor.device_type}
                        </div>
                        <div className="flex items-center space-x-1">
                          <Icon icon={getOSIcon(visitor.operating_system)} className="w-3 h-3" />
                          <span className="text-xs text-slate-500 dark:text-slate-400">
                            {visitor.operating_system}
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm text-slate-900 dark:text-white">
                        {visitor.timezone?.split('/')[1]?.replace('_', ' ') || 'Unknown'}
                      </div>
                      <div className="text-xs text-slate-500 dark:text-slate-400">
                        {visitor.language}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm text-slate-900 dark:text-white">
                        {visitor.utm_source || 'Direct'}
                      </div>
                      {visitor.utm_campaign && (
                        <div className="text-xs text-slate-500 dark:text-slate-400">
                          {visitor.utm_campaign}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {(() => {
                      const localTime = formatLocalTime(visitor.visited_at);
                      return (
                        <div>
                          <div className="text-sm text-slate-900 dark:text-white">
                            {localTime.date}
                          </div>
                          <div className="text-xs text-slate-500 dark:text-slate-400">
                            {localTime.time} ({localTime.timezone})
                          </div>
                        </div>
                      );
                    })()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {renderPagination()}
      </Card>
    </div>
  );
};

export default VisitorList;
