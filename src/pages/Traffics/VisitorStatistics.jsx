import React, { useState } from "react";
import Card from "@/components/ui/Card";
import { useGetVisitorStatsQuery } from "@/store/api/master/visitorTrackingSlice";
import { Icon } from "@iconify/react";
import Loading from "@/components/Loading";

const VisitorStatistics = () => {
  const [dateRange, setDateRange] = useState(7);
  
  const { data: statsData, isLoading, error } = useGetVisitorStatsQuery({ 
    date_range: dateRange 
  });

  const stats = statsData;

  console.log(stats); 

  const dateRangeOptions = [
    { value: 1, label: "Last 24 Hours" },
    { value: 7, label: "Last 7 Days" },
    { value: 30, label: "Last 30 Days" },
    { value: 90, label: "Last 90 Days" },
  ];

  if (isLoading) return <Loading />;
  if (error) return <div className="text-red-500">Error loading statistics</div>;

  return (
    <div className="space-y-6">
      {/* Header with Date Range Selector */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-slate-900 dark:text-white">
          Visitor Analytics
        </h2>
        <select
          value={dateRange}
          onChange={(e) => setDateRange(Number(e.target.value))}
          className="px-4 py-2 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
        >
          {dateRangeOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600 dark:text-slate-400">
                Total Visitors
              </p>
              <p className="text-3xl font-bold text-slate-900 dark:text-white">
                {stats?.summary?.total_visitors?.toLocaleString() || 0}
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Icon icon="heroicons:users" className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </Card>

        <Card>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600 dark:text-slate-400">
                Total Page Views
              </p>
              <p className="text-3xl font-bold text-slate-900 dark:text-white">
                {stats?.summary?.total_page_views?.toLocaleString() || 0}
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <Icon icon="heroicons:eye" className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </Card>

        <Card>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600 dark:text-slate-400">
                Avg. Views per Visitor
              </p>
              <p className="text-3xl font-bold text-slate-900 dark:text-white">
                {stats?.summary?.total_visitors > 0 
                  ? (stats?.summary?.total_page_views / stats?.summary?.total_visitors).toFixed(1)
                  : 0
                }
              </p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <Icon icon="heroicons:chart-bar" className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Page Statistics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card title="Page Performance">
          <div className="space-y-4">
            {stats?.page_stats?.map((page, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
                <div>
                  <p className="font-medium text-slate-900 dark:text-white capitalize">
                    {page.page_name}
                  </p>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    {page.unique_visitors} unique visitors
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold text-slate-900 dark:text-white">
                    {page.views}
                  </p>
                  <p className="text-sm text-slate-600 dark:text-slate-400">views</p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        <Card title="Device Types">
          <div className="space-y-4">
            {stats?.device_stats?.map((device, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Icon 
                    icon={
                      device.device_type === 'Desktop' ? 'heroicons:computer-desktop' :
                      device.device_type === 'Mobile' ? 'heroicons:device-phone-mobile' :
                      'heroicons:device-tablet'
                    } 
                    className="w-5 h-5 text-slate-600 dark:text-slate-400" 
                  />
                  <span className="font-medium text-slate-900 dark:text-white">
                    {device.device_type}
                  </span>
                </div>
                <span className="text-lg font-bold text-slate-900 dark:text-white">
                  {device.count}
                </span>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Browser Statistics and UTM Sources */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card title="Browser Usage">
          <div className="space-y-4">
            {stats?.browser_stats?.map((browser, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Icon 
                    icon={
                      browser.browser_name === 'Chrome' ? 'logos:chrome' :
                      browser.browser_name === 'Firefox' ? 'logos:firefox' :
                      browser.browser_name === 'Safari' ? 'logos:safari' :
                      browser.browser_name === 'Edge' ? 'logos:microsoft-edge' :
                      'heroicons:globe-alt'
                    } 
                    className="w-5 h-5" 
                  />
                  <span className="font-medium text-slate-900 dark:text-white">
                    {browser.browser_name}
                  </span>
                </div>
                <span className="text-lg font-bold text-slate-900 dark:text-white">
                  {browser.count}
                </span>
              </div>
            ))}
          </div>
        </Card>

        <Card title="Traffic Sources">
          <div className="space-y-4">
            {stats?.utm_stats?.map((source, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Icon 
                    icon={
                      source.utm_source === 'google' ? 'logos:google' :
                      source.utm_source === 'facebook' ? 'logos:facebook' :
                      source.utm_source === 'direct' ? 'heroicons:link' :
                      'heroicons:globe-alt'
                    } 
                    className="w-5 h-5" 
                  />
                  <span className="font-medium text-slate-900 dark:text-white capitalize">
                    {source.utm_source || 'Direct'}
                  </span>
                </div>
                <span className="text-lg font-bold text-slate-900 dark:text-white">
                  {source.count}
                </span>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Daily Statistics */}
      <Card title="Daily Trends">
        <div className="space-y-4">
          {stats?.daily_stats?.map((day, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
              <div>
                <p className="font-medium text-slate-900 dark:text-white">
                  {new Date(day.date).toLocaleDateString('en-US', { 
                    weekday: 'short', 
                    month: 'short', 
                    day: 'numeric' 
                  })}
                </p>
                <p className="text-sm text-slate-600 dark:text-slate-400">
                  {day.unique_visitors} unique visitors
                </p>
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-slate-900 dark:text-white">
                  {day.page_views}
                </p>
                <p className="text-sm text-slate-600 dark:text-slate-400">views</p>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default VisitorStatistics;
