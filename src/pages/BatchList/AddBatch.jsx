import React, { useState } from "react";
import { Formik, Form, ErrorMessage } from "formik";
import {
  useGetApiQuery,
  usePostApiMutation,
} from "@/store/api/master/commonSlice";
import { useParams } from "react-router-dom";
import InputField from "@/components/ui/InputField";
import Textarea from "@/components/ui/Textarea";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import Fileinput from "@/components/ui/Fileinput";
import * as yup from "yup";
import SelectionModal from "./SelectionModal";
import Select from "react-select";
import { useNavigate } from "react-router-dom";

const AddBatch = ({ onClose }) => {

  const navigate = useNavigate();

  const [courseId, setCourseId] = useState("");
  const {
    data: courseList,
    isLoading,
    error,
  } = useGetApiQuery(`admin/course-list-for-filter`);
  const selectedCourse = courseList?.find((course) => course.id === courseId);
  // console.log(selectedCourse);

  const { data: course } = useGetApiQuery(
    `admin/course-details/${selectedCourse?.id}`
  );
  const Student = useGetApiQuery(
    `/admin/student-list-for-batch-by-course-id/${selectedCourse?.id || 0}`
  );
  const Mentors = course?.course_mentor;
  console.log(course);
  const [postApi] = usePostApiMutation();
  const Students = Student?.data?.student_list || [];

  const createUrl = "admin/batches";

  const validationSchema = yup.object({
    name: yup.string().required("Batch name is required"),
    capacity: yup
      .number()
      .required("Capacity is required")
      .min(1, "Capacity must be at least 1")
      .typeError("Capacity must be a number"),
    course_id: yup.number().required("Please select a course"),
    image: yup
      .mixed()
      .required("Please select a course image")
      .test("fileType", "Only images are allowed (jpg, png)", (value) => {
        return value && ["image/jpeg", "image/png"].includes(value.type);
      })
      .test("fileSize", "Image size must be less than 2MB", (value) => {
        return value && value.size <= 2 * 1024 * 1024; // 2MB
      }),
    description: yup
      .string()
      .min(3, "Minimum 3 item is required")
      .max(255, "Description should be within 255 characters"),
    student_ids: yup.array().required("Please select minimum one student"),
    mentor_ids: yup.array().required("Please select minimum one mentor"),
  });

  const createBatch = async (values) => {
    try {
      const response = await postApi({ end_point: createUrl, body: values });

      if (response?.data?.status) {
        navigate("/batch-list");
      }

      // if (response.error) {
      //   setErrors(response.error?.data.errors || {});
      // }

    } catch (error) {
      setSubmitting(false);
      console.error("Error creating batch:", error);
    }
  };

  const onSubmit = async (values, { resetForm }) => {
    const mentorIds = values.mentor_ids || [];
    const studentIds = values.student_ids || [];
    const formData = new FormData();
    formData.append("course_id", courseId);
    formData.append("name", values.name);
    formData.append("description", values.description || "");
    formData.append("capacity", values.capacity);
    if (values.image && typeof values.image !== "string") {
      formData.append("image", values.image);
    }
    formData.append("mentor_ids", JSON.stringify(mentorIds));
    formData.append("student_ids", JSON.stringify(studentIds));
    await createBatch(formData);
    resetForm();
    if (onClose) onClose();
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-2xl mb-4">Add New Batch</h2>
      <Formik
        initialValues={{
          course_id: courseId || "",
          name: "",
          description: "",
          capacity: "",
          image: "",
          student_ids: [],
          mentor_ids: [],
        }}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
      >
        {({ isSubmitting, setFieldValue, values }) => {
          const [isMentorModalOpen, setMentorModalOpen] = useState(false);
          const [tempSelectedMentors, setTempSelectedMentors] = useState(
            values.mentor_ids || []
          );
          const handleMentorCheckboxChange = (mentorId) => {
            if (tempSelectedMentors.includes(mentorId)) {
              setTempSelectedMentors(
                tempSelectedMentors.filter((id) => id !== mentorId)
              );
            } else {
              setTempSelectedMentors([...tempSelectedMentors, mentorId]);
            }
          };
          const handleSelectAllMentors = () => {
            if (tempSelectedMentors.length === Mentors.length) {
              setTempSelectedMentors([]);
            } else {
              const allMentorIds = Mentors.map((mentor) => mentor.mentor_id);
              setTempSelectedMentors(allMentorIds);
            }
          };
          const handleMentorModalSubmit = () => {
            setFieldValue("mentor_ids", tempSelectedMentors);
            setMentorModalOpen(false);
          };

          const [isStudentModalOpen, setStudentModalOpen] = useState(false);
          const [tempSelectedStudents, setTempSelectedStudents] = useState(
            values.student_ids || []
          );
          const handleStudentCheckboxChange = (studentId) => {
            if (tempSelectedStudents.includes(studentId)) {
              setTempSelectedStudents(
                tempSelectedStudents.filter((id) => id !== studentId)
              );
            } else {
              setTempSelectedStudents([...tempSelectedStudents, studentId]);
            }
          };
          const handleSelectAllStudents = () => {
            if (tempSelectedStudents.length === Students.length) {
              setTempSelectedStudents([]);
            } else {
              const allStudentIds = Students.map((student) => student.id);
              setTempSelectedStudents(allStudentIds);
            }
          };
          const handleStudentModalSubmit = () => {
            setFieldValue("student_ids", tempSelectedStudents);
            setStudentModalOpen(false);
          };

          return (
            <>
              <Form>
                <div>
                  <div className="w-full mb-2">
                    <label className="block text-gray-600 text-sm font-medium mb-2">
                      Select Course
                      <span className="text-red-500"> *</span>
                    </label>
                    <Select
                      required
                      placeholder="Select Course"
                      options={courseList?.map((course) => ({
                        value: course.id,
                        label: course.title,
                      }))}
                      name="course_id"
                      onChange={(e) => {
                        // console.log(e.value);
                        setCourseId(e.value);
                        setFieldValue("course_id", e.value);
                      }}
                    />
                    <ErrorMessage name="course_id">
                      {(msg) => (
                        <div className="text-red-500 text-sm mt-1">{msg}</div>
                      )}
                    </ErrorMessage>
                  </div>
                </div>
                <div>
                  <InputField
                    label="Name"
                    name="name"
                    type="text"
                    placeholder="Enter Batch Name"
                    required
                  />
                </div>
                <div className="mt-2">
                  <Textarea
                    label="Description"
                    placeholder="Enter Description"
                    name="description"
                    value={values.description}
                    onChange={(e) =>
                      setFieldValue("description", e.target.value)
                    }
                    
                  />
                </div>
                <div className="grid grid-cols-2 gap-4 items-center">
                  <div>
                    <NumberInput
                      label="Batch Capacity"
                      type="number"
                      name="capacity"
                      id="capacity"
                      placeholder="Batch capacity"
                      required
                    />
                  </div>
                  <div className="mb-4">
                    <label className="block capitalize mt-4 pb-1">
                      Batch Image
                    </label>
                    <Fileinput
                      label="Choose Image"
                      className="py-2"
                      name="image"
                      accept="image/*"
                      type="file"
                      placeholder="Batch Image"
                      preview={true}
                      selectedFile={values.image}
                      required
                      onChange={(e) => {
                        setFieldValue("image", e.target.files[0]);
                      }}
                    />
                    <ErrorMessage name="image">
                      {(msg) => (
                        <div className="text-red-500 text-sm mt-1">{msg}</div>
                      )}
                    </ErrorMessage>
                  </div>
                </div>

                <div className={`flex space-x-4 mt-4`}>
                  {values.mentor_ids.length  > 0 && <button
                    type="button"
                    onClick={() => {
                      setTempSelectedMentors(values.mentor_ids || []);
                      setMentorModalOpen(true);
                    }}
                    className="px-4 py-2 bg-green-600 text-white rounded-md shadow-lg hover:bg-green-700 transition-colors duration-200"
                  >
                    Add Mentor
                  </button>}
                  {values.student_ids.length > 0 && <button
                    type="button"
                    onClick={() => {
                      setTempSelectedStudents(values.student_ids || []);
                      setStudentModalOpen(true);
                    }}
                    className="px-4 py-2 bg-green-600 text-white rounded-md shadow-lg hover:bg-green-700 transition-colors duration-200"
                  >
                    Add Students
                  </button>}
                </div>

                <div className="mt-4">
                  {values.mentor_ids.length > 0 && <h6 className="text-base">
                    Assigned Mentors: {values.mentor_ids.length}
                  </h6>}
                  {values.mentor_ids.length > 0 && (
                    <ul className="list-disc list-inside">
                      {Mentors.filter((mentor) =>
                        values.mentor_ids.includes(mentor.mentor_id)
                      ).map((mentor) => (
                        <li key={mentor.mentor_id}>{mentor?.mentor?.name}</li>
                      ))}
                    </ul>
                  )}
                </div>

                <div className="mt-4">
                  {values.student_ids.length > 0 && <h6 className="text-base">
                    Assigned Students: {values.student_ids.length}
                  </h6>}
                  {values.student_ids.length > 0 && (
                    <ul className="list-disc list-inside">
                      {Students.filter((student) =>
                        values.student_ids.includes(student.id)
                      ).map((student) => (
                        <li key={student.id}>{student.name}</li>
                      ))}
                    </ul>
                  )}
                </div>

                <div className="flex justify-end space-x-2 mt-6">
                  {onClose && (
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200"
                    >
                      Cancel
                    </button>
                  )}
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-lg hover:bg-blue-700 transition-colors duration-200"
                  >
                    {isSubmitting ? "Submitting..." : "Submit"}
                  </button>
                </div>
              </Form>
              {isMentorModalOpen && (
                <SelectionModal
                  title="Select Mentors"
                  items={Mentors?.map((m) => ({
                    value: m.mentor_id,
                    label: m?.mentor?.name,
                    email: m?.mentor?.email,
                    image: m?.mentor?.image,
                  }))}
                  selectedItems={tempSelectedMentors}
                  onChange={handleMentorCheckboxChange}
                  onSelectAll={handleSelectAllMentors}
                  onClose={() => setMentorModalOpen(false)}
                  onSubmit={handleMentorModalSubmit}
                />
              )}

              {isStudentModalOpen && (
                <SelectionModal
                  title="Select Students"
                  items={Students.map((s) => ({
                    value: s.id,
                    label: s.name,
                    email: s?.email,
                    image: s?.image,
                  }))}
                  selectedItems={tempSelectedStudents}
                  onChange={handleStudentCheckboxChange}
                  onSelectAll={handleSelectAllStudents}
                  onClose={() => setStudentModalOpen(false)}
                  onSubmit={handleStudentModalSubmit}
                />
              )}
            </>
          );
        }}
      </Formik>
    </div>
  );
};

export default AddBatch;