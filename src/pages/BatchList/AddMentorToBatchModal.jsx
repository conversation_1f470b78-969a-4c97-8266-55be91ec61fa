import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { useDispatch, useSelector } from "react-redux";
import {
  useGetApiQuery,
  usePostApiMutation,
} from "@/store/api/master/commonSlice";
import { useState } from "react";
import avater from "@/assets/images/avatar/av-1.svg";

const AddMentorToBatchModal = ({ showEditModal, setEditShowModal, data }) => {


  const dispatch = useDispatch();
  const [courseId, setCourseId] = useState("");
  const [selectedItems, setSelectedItems] = useState(data?.mentorList?.map((item) => item.mentor_id));
  const [postApi, { isLoading: isMentorsSubmitting }] = usePostApiMutation();

  const {
    data: mentorList,
    isLoading,
    error,
  } = useGetApiQuery(`admin/course-mentor-assign-list/${data?.course_id}`);


  console.log(data?.mentorList);
  console.log(mentorList);

  const handleSubmit = async () => {
    

    try {
      const response = await postApi({
        end_point: "admin/batches-add-mentors",
        body: { mentor_ids: selectedItems, batch_id: data?.batch_id },
      });

      if (response?.data?.status) {
        dispatch(setEditShowModal(false));
      }
    } catch (error) {
      console.error("Error adding mentors to batch:", error);
    }
  };

  const handleSelectAll = () => {
    if (selectedItems.length === mentorList.length) {
      setSelectedItems([]); // Deselect all
    } else {
      setSelectedItems(mentorList.map((mentor) => mentor.id)); // Select all
    }
  };

  const handleCheckboxChange = (id) => {
    setSelectedItems(
      (prev) =>
        prev.includes(id)
          ? prev.filter((itemId) => itemId !== id) // Remove if already selected
          : [...prev, id] // Add if not selected
    );
  };

  return (
    <Modal
      activeModal={showEditModal}
      onClose={() => dispatch(setEditShowModal(false))}
      title="Add Mentor"
      className="max-w-2xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setEditShowModal(false))}
        />
      }
    >
      <div>
        <div className="flex justify-between items-center mb-4">
          {mentorList?.length > 0 && (
            <button
              className="px-4 py-2 border border-blue-600 rounded-md shadow-lg hover:border-blue-700 text-blue-500 transition-colors"
              onClick={handleSelectAll}
            >
              {selectedItems.length === mentorList.length
                ? "Deselect All"
                : "Select All"}
            </button>
          )}
        </div>
        <div className="grid grid-cols-2 gap-4">
          {isLoading ? (
            <p className="text-start py-3 ">Loading...</p>
          ) : mentorList?.length > 0 ? (
            mentorList?.map((mentor) => {
              const isSelected = selectedItems.includes(mentor.mentor_id);
              return (
                <div
                  key={mentor.id}
                  className={`flex items-center rounded-md border border-gray-200 p-2 cursor-pointer transition-colors ${
                    isSelected ? "bg-blue-50" : "hover:bg-gray-50"
                  }`}
                  onClick={() => handleCheckboxChange(mentor.mentor_id)}
                >
                  <input
                    type="checkbox"
                    checked={isSelected}
                    onChange={() => handleCheckboxChange(mentor.id)}
                    className="form-checkbox h-4 w-4 text-blue-600 mr-3"
                    onClick={(e) => e.stopPropagation()}
                  />
                  <div className="flex items-center space-x-3">
                    {/* {mentor?.image && ( */}
                    <img
                      src={
                        mentor.image
                          ? import.meta.env.VITE_ASSET_HOST_URL + mentor.image
                          : avater
                      }
                      className="md:block hidden rounded-full w-8 h-8 md:mr-2 md:mb-0 mb-2"
                      alt="avatar"
                    />
                    {/* )} */}
                    <div className="flex flex-col">
                      <span className="font-medium text-gray-900 text-sm">
                        {mentor.mentor_name}
                      </span>
                      {mentor?.mentor_email && (
                        <span className="text-gray-500 text-xs">
                          {mentor.mentor_email}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              );
            })
          ) : (
            <p>No Teacher available</p>
          )}
        </div>
      </div>
      {mentorList?.length > 0 && (
        <div className="flex justify-end pt-8">
          <button
            type="submit"
            disabled={isMentorsSubmitting || mentorList?.length < 1}
            onClick={handleSubmit}
            className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-lg hover:bg-blue-700 transition-colors duration-200"
          >
            {isMentorsSubmitting ? "Submitting..." : "Submit"}
          </button>
        </div>
      )}
    </Modal>
  );
};

export default AddMentorToBatchModal;
