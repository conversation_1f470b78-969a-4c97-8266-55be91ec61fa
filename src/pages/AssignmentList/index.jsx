import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import Button from "@/components/ui/Button";
import CreateAssignment from "./createAssignment";
import { useGetApiQuery } from "@/store/api/master/commonSlice";

import Select from "@/components/ui/Select";
import avatar from "@/assets/images/avatar/av-1.svg";
import { useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";

const Filter = ({ setApiParam }) => {
  const {
    data: courseList,
  } = useGetApiQuery(`admin/course-list-for-filter`);
  return (
    <div className="flex gap-2">
      <Select
        className="w-72"
        defaultValue=""
        placeholder="Select Course"
        options={courseList?.map((item) => {
          return { label: item.title, value: item.id };
        })}
        name="class_level"
        onChange={(e) => {
          setApiParam("?course_id=" + e.target.value);
        }}
      />
    </div>
  );
};

const index = () => {
  const navigate = useNavigate();
  const [apiParam, setApiParam] = useState("");
  const { showModal } = useSelector((state) => state.commonReducer);
  const {
    data: assignments,
  } = useGetApiQuery(`admin/assignments${apiParam}`);

  const [filter] = useState("");
  // console.log(assignments);
  const changePage = (val) => {
    setApiParam(val);
  };

  const columns = [
    {
      label: "SL",
      field: "index",
    },
    {
      label: "Title",
      field: "title",
    },
    {
      label: "Mentor",
      field: "mentor",
    },
    {
      label: "Student Number",
      field: "student_count",
    },
    {
      label: "Deadline",
      field: "deadline",
    },
    {
      label: "Status",
      field: "is_active",
    },
  ];
  const tableData = assignments?.data?.map((item, index) => {
    return {
      index: index + 1,
      title: item.title,
      student_count: item.students_count,
      deadline: new Date(item.deadline).toLocaleString("en-IN", {
        year: "numeric",
        month: "short",
        day: "numeric",
      }),
      mentor: (
        <div className="flex items-center">
          <img
            src={
              item.mentor.image
                ? import.meta.env.VITE_ASSET_HOST_URL + item.mentor.image
                : avatar
            }
            className="rounded-full w-8 h-8 mr-2"
            alt="avatar"
          />
          <span className="hover:text-primary-500 hover:underline">
            {item.mentor.name}
          </span>
        </div>
      ),
      is_free: (
        <Badge
          className={
            item.is_free == "true"
              ? `bg-danger-500 text-white`
              : `bg-success-500 text-white`
          }
        >
          {item.is_free ? "Yes" : "No"}
        </Badge>
      ),
      is_active: (
        <Badge
          className={
            item.is_active == "true"
              ? `bg-danger-500 text-white`
              : `bg-success-500 text-white`
          }
        >
          {item.is_active ? "Yes" : "No"}
        </Badge>
      ),
    };
  });

  // Actions can be added here if needed
  //   const changePage = (item) => {
  //     console.log(item);
  //   };
  const handleSubmit = () => {
    setShowModal(false);
  };
  // const CourseFilter = <Filter setApiParam={setApiParam} />;
  // const createPage = <CreateAssignment />;
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h4 className="text-xl font-medium">Assignment List</h4>
        <Button
          text="Create New Assignment"
          icon="heroicons-outline:plus"
          btnClass="btn-primary"
          onClick={() => navigate("/create-assignment")}
        />
      </div>

      {/* {tableData?.length > 0 && ( */}
      <BasicTablePage
        tableHeaderExtra={<Filter setApiParam={setApiParam} />}
        title="Assignment Management"
        columns={columns}
        data={tableData}
        filter={filter}
        submitForm={handleSubmit}
        changePage={changePage}
        currentPage={assignments?.current_page}
        totalPages={Math.ceil(assignments?.total / assignments?.per_page)}
        setFilter={setApiParam}
      />
      {showModal && <CreateAssignment showModal={showModal} setShowModal={setShowModal} />}
      {/* )} */}
    </div>
  );
};

export default index;
