import React, { useState } from "react";
import { Formik, Form, ErrorMessage } from "formik";
import { useNavigate } from "react-router-dom";
import * as Yup from "yup";
import {
  useGetApiQuery,
  usePostApiMutation,
} from "@/store/api/master/commonSlice";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import InputField from "@/components/ui/InputField";
import Select from "react-select";
import Textarea from "@/components/ui/Textarea";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import Fileinput from "@/components/ui/Fileinput";
import { toast } from "react-toastify";
import StudentSelectionModal from "./StudentSelectionModal";

const CreateAssignmentPage = () => {
  const navigate = useNavigate();
  const [courseId, setCourseId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showStudentModal, setShowStudentModal] = useState(false);
  const [selectedStudents, setSelectedStudents] = useState([]);
  const [selectedBatchId, setSelectedBatchId] = useState(null);
  const [selectionMode, setSelectionMode] = useState(null); // 'students' or 'batch'
  const [postApi] = usePostApiMutation();

  // Fetch course list
  const { data: courseList } = useGetApiQuery("admin/course-list-for-filter");

  // Fetch course mentors
  const { data: couseMentorList } = useGetApiQuery(
    courseId ? `admin/course-mentor-assign-list/${courseId}` : 'admin/course-mentor-assign-list'
  );

  // Fetch batches for the selected course
  const { data: batchList } = useGetApiQuery(
    courseId ? `admin/batches?pagination=false&is_active=1&mentor=1&course_id=${courseId}` : null,
    { skip: !courseId }
  );

  const validationSchema = Yup.object({
    title: Yup.string()
      .required("Assignment title is required")
      .max(100, "Title cannot exceed 100 characters"),
    course_id: Yup.string().required("Please select a course"),
    mentor_id: Yup.string().required("Please select a mentor"),
    publish_date: Yup.date().required("Publish date is required"),
    deadline: Yup.date()
      .required("Deadline is required")
      .min(Yup.ref("publish_date"), "Deadline must be after the publish date"),
    mark: Yup.number()
      .required("Mark is required")
      .min(1, "Mark must be at least 1"),
    supporting_doc: Yup.mixed().test(
      "fileType",
      "Only PDF files are allowed",
      (value) => !value || value.type === "application/pdf"
    )
  });

  const handleSubmit = async (values) => {
    try {
      // Validate selection mode
      if (selectionMode === 'students' && selectedStudents.length === 0) {
        toast.error("Please select at least one student");
        return;
      } else if (selectionMode === 'batch' && !selectedBatchId) {
        toast.error("Please select a batch");
        return;
      } else if (!selectionMode) {
        toast.error("Please select either students or a batch");
        return;
      }

      setIsLoading(true);

      const formData = new FormData();
      formData.append("title", values.title);
      formData.append("course_id", values.course_id);
      formData.append("mentor_id", values.mentor_id);

      const publishDate = new Date(values.publish_date);
      const formattedPublishDate = publishDate.toISOString().split("T")[0];
      formData.append("publish_date", formattedPublishDate);

      const date = new Date(values.deadline);
      const formattedDate = date.toISOString().split("T")[0];
      formData.append("deadline", formattedDate);

      formData.append("mark", values.mark);
      if (values.supporting_doc) {
        formData.append("supporting_doc", values.supporting_doc);
      }
      formData.append("description", values.description);
      formData.append("instructions", values.instructions);
      formData.append("status", "Ongoing");

      // Add either selected students or batch_id based on selection mode
      if (selectionMode === 'students') {
        selectedStudents.forEach(studentId => {
          formData.append("student_ids[]", studentId);
        });
      } else if (selectionMode === 'batch') {
        formData.append("batch_id", selectedBatchId);
      }

      await postApi({
        end_point: `admin/create-assignment`,
        body: formData,
      }).unwrap();
      navigate("/assignment-list");
    } catch (error) {
      console.error("Error creating assignment:", error);
      toast.error("Failed to create assignment. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleStudentSelection = (data) => {
    if (data.mode === 'students') {
      setSelectionMode('students');
      setSelectedStudents(data.selectedStudentIds);
      setSelectedBatchId(null);
    } else if (data.mode === 'batch') {
      setSelectionMode('batch');
      setSelectedBatchId(data.batchId);
      setSelectedStudents([]);
    }
    setShowStudentModal(false);
  };

  return (
    <div className="space-y-5">
      <div className="flex justify-between items-center">
        <h4 className="text-xl font-medium">Create New Assignment</h4>
        <Button
          text="Back to Assignment List"
          icon="heroicons-outline:arrow-left"
          btnClass="btn-outline-primary"
          onClick={() => navigate("/assignment-list")}
        />
      </div>

      <Card title="Assignment Details">
        <Formik
          initialValues={{
            title: "",
            description: "",
            instructions: "",
            course_id: "",
            mentor_id: "",
            publish_date: "",
            deadline: "",
            mark: "",
            supporting_doc: "",
            batch_id: "",
          }}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ values, setFieldValue }) => (
            <Form>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <InputField
                      label="Assignment Title"
                      name="title"
                      type="text"
                      placeholder="Assignment Name"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-gray-600 text-sm font-medium mb-2">
                      Select Course
                      <span className="text-red-500">*</span>
                    </label>
                    <Select
                      placeholder="Select Course"
                      options={courseList?.map((course) => ({
                        value: course.id,
                        label: course.title,
                      }))}
                      name="course_id"
                      onChange={(e) => {
                        setCourseId(e.value);
                        setFieldValue("course_id", e.value);
                      }}
                    />
                    <ErrorMessage name="course_id">
                      {(msg) => (
                        <div className="text-red-500 text-sm mt-1">{msg} </div>
                      )}
                    </ErrorMessage>
                  </div>

                  <div>
                    <label className="block text-gray-600 text-sm font-medium mb-2">
                      Select Students or Batch
                    </label>
                    <div className="flex items-center gap-2">
                      <Button
                        text={
                          selectionMode === 'students' && selectedStudents.length > 0
                            ? `${selectedStudents.length} Students Selected`
                            : selectionMode === 'batch' && selectedBatchId
                              ? `Batch Selected: ${batchList?.find(b => b.id === selectedBatchId)?.name || 'Batch'}`
                              : "Select Students or Batch"
                        }
                        icon="heroicons-outline:user-group"
                        btnClass={`btn-outline-primary w-full ${!courseId ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => courseId ? setShowStudentModal(true) : toast.error("Please select a course first")}
                        disabled={!courseId}
                      />
                      {(selectionMode === 'students' && selectedStudents.length > 0) ||
                       (selectionMode === 'batch' && selectedBatchId) ? (
                        <Button
                          icon="heroicons-outline:x"
                          btnClass="btn-outline-danger"
                          onClick={() => {
                            setSelectionMode(null);
                            setSelectedStudents([]);
                            setSelectedBatchId(null);
                          }}
                        />
                      ) : null}
                    </div>
                    {!selectionMode && (
                      <div className="text-amber-500 text-xs mt-1">
                        Please select either students or a batch
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-gray-600 text-sm font-medium mb-2">
                    Select Mentor<span className="text-red-500"> *</span>
                  </label>
                  <Select
                    placeholder="Select Mentor"
                    options={
                      couseMentorList?.map((mentor) => ({
                        value: mentor.mentor_id,
                        label: mentor.mentor_name,
                      }))
                    }
                    name="mentor_id"
                    onChange={(e) => setFieldValue("mentor_id", e.value)}
                    isDisabled={!courseId}
                  />
                  <ErrorMessage
                    name="mentor_id"
                    component="div"
                    className="text-red-500 text-xs mt-1"
                  />
                </div>

                <div>
                  <Textarea
                    label="Description"
                    name="description"
                    type="text"
                    placeholder="Assignment Description"
                    required
                    onChange={(e) => {
                      setFieldValue("description", e.target.value);
                    }}
                  />
                  <ErrorMessage name="description">
                    {(msg) => (
                      <div className="text-red-500 text-sm mt-1">{msg}</div>
                    )}
                  </ErrorMessage>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <DatePicker
                      label="Publish Date"
                      placeholder="YYYY-MM-DD"
                      format="YYYY/MM/DD"
                      name="publish_date"
                      required
                      onChange={(e) => {
                        setFieldValue("publish_date", e);
                      }}
                    />
                    <ErrorMessage name="publish_date">
                      {(msg) => (
                        <div className="text-red-500 text-sm mt-1">{msg}</div>
                      )}
                    </ErrorMessage>
                  </div>
                  <div>
                    <DatePicker
                      label="Deadline"
                      placeholder="YYYY-MM-DD"
                      format="YYYY/MM/DD"
                      name="deadline"
                      required
                      onChange={(e) => {
                        setFieldValue("deadline", e);
                      }}
                    />
                    <ErrorMessage name="deadline">
                      {(msg) => (
                        <div className="text-red-500 text-sm mt-1">{msg}</div>
                      )}
                    </ErrorMessage>
                  </div>
                  <div>
                    <InputField
                      label="Total Mark"
                      name="mark"
                      type="number"
                      min="0"
                      placeholder="Enter Assignment Mark"
                      required
                    />
                  </div>
                </div>

                <div>
                  <Fileinput
                    name="supporting_doc"
                    accept=".pdf"
                    type="file"
                    placeholder="Select Document"
                    title="Question (Document)"
                    selectedFile={values.supporting_doc}
                    onChange={(e) => {
                      setFieldValue("supporting_doc", e.target.files[0]);
                    }}
                  />
                  <ErrorMessage name="supporting_doc">
                    {(msg) => (
                      <div className="text-red-500 text-sm mt-1">{msg}</div>
                    )}
                  </ErrorMessage>
                </div>

                <div>
                  <Textarea
                    label="Instructions"
                    name="instructions"
                    type="text"
                    placeholder="Instructions for students"
                    required
                    onChange={(e) => {
                      setFieldValue("instructions", e.target.value);
                    }}
                  />
                  <ErrorMessage name="instructions">
                    {(msg) => (
                      <div className="text-red-500 text-sm mt-1">{msg}</div>
                    )}
                  </ErrorMessage>
                </div>
              </div>

              <div className="flex justify-end mt-6 space-x-3">
                <Button
                  text="Cancel"
                  btnClass="btn-outline-danger"
                  onClick={() => navigate("/assignment-list")}
                />
                <Button
                  text="Create Assignment"
                  type="submit"
                  btnClass="btn-primary"
                  isLoading={isLoading}
                />
              </div>
            </Form>
          )}
        </Formik>
      </Card>

      {showStudentModal && (
        <StudentSelectionModal
          courseId={courseId}
          onClose={() => setShowStudentModal(false)}
          onSubmit={handleStudentSelection}
          selectedStudentIds={selectedStudents}
        />
      )}
    </div>
  );
};

export default CreateAssignmentPage;
