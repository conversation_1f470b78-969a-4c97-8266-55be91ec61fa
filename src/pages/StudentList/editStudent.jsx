import React, { useState, useEffect } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import Textarea from "@/components/ui/Textarea";
import Select from "@/components/ui/Select";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import { Formik, Form, useField } from "formik";
import { editValidationSchema } from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal } from "@/features/commonSlice";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import { useDropzone } from "react-dropzone";
import { X, Camera, Edit2 } from "lucide-react";
import { toast } from "react-toastify";
// Modern Image Uploader Component
const ImageUploader = ({ name, label, initialImage }) => {
  const [field, meta, helpers] = useField(name);
  const [preview, setPreview] = useState(null);
  const assetBaseURL = import.meta.env.VITE_ASSET_HOST_URL || '';
  const { labels } = useSelector((state) => state.languageReducer);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif']
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        const file = acceptedFiles[0];
        helpers.setValue(file);
        const objectUrl = URL.createObjectURL(file);
        setPreview(objectUrl);
      }
    }
  });

  useEffect(() => {
    // Handle initial image from server
    if (initialImage && typeof initialImage === 'string') {
      setPreview(`${assetBaseURL}${initialImage}`);
    }

    // Cleanup function
    return () => {
      if (preview && preview.startsWith('blob:')) {
        URL.revokeObjectURL(preview);
      }
    };
  }, [initialImage, assetBaseURL]);

  const handleRemove = (e) => {
    e.stopPropagation();
    helpers.setValue(null);
    setPreview(null);
  };

  return (
    <div className="mb-2">
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          {labels[label.toLowerCase()] || label}
        </label>
      )}

      <div
        {...getRootProps()}
        className={`relative cursor-pointer ${preview ? 'h-40 w-full' : 'h-40 border-2 border-dashed rounded-lg p-2 flex flex-col items-center justify-center'}
        ${isDragActive ? 'border-primary-500 bg-primary-50' : 'border-gray-300 hover:border-primary-500'}`}
      >
        <input {...getInputProps()} />

        {preview ? (
          <>
            <img
              src={preview}
              alt={labels['preview'] || "Preview"}
              className="h-full w-full object-cover rounded-lg"
            />
            <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    getRootProps().onClick(e);
                  }}
                  className="p-1 bg-white rounded-full text-gray-700 hover:text-primary-500"
                  title={labels['edit'] || "Edit"}
                >
                  <Edit2 size={16} />
                </button>
                <button
                  type="button"
                  onClick={handleRemove}
                  className="p-1 bg-white rounded-full text-gray-700 hover:text-red-500"
                  title={labels['remove'] || "Remove"}
                >
                  <X size={16} />
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="text-center">
            <Camera className="mx-auto h-8 w-8 text-gray-400" />
            <div className="mt-1">
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {isDragActive ? labels['drop image here'] || 'Drop image here' : labels['drag & drop or click'] || 'Drag & drop or click'}
              </p>
              <p className="text-xs text-gray-500">
                {labels['image formats'] || 'PNG, JPG, GIF'}
              </p>
            </div>
          </div>
        )}
      </div>

      {meta.touched && meta.error && (
        <div className="text-red-500 text-xs mt-1">{meta.error}</div>
      )}
    </div>
  );
};

const editStudent = () => {
  const { editData } = useSelector((state) => state.commonReducer);
  const { labels } = useSelector((state) => state.languageReducer);
  const dispatch = useDispatch();
  const { showEditModal } = useSelector((state) => state.commonReducer);
  const [updateApi, { isLoading }] = useUpdateApiMutation();

  // Switch States
  const [isActive, setIsActive] = useState(editData?.is_active || false);

  // Select options
  const genderOptions = [
    { label: labels['male'] || "Male", value: "Male" },
    { label: labels['female'] || "Female", value: "Female" },
    { label: labels['others'] || "Others", value: "Others" },
  ];

  const bloodGroupOptions = [
    { label: "A+", value: "A+" },
    { label: "A-", value: "A-" },
    { label: "B+", value: "B+" },
    { label: "B-", value: "B-" },
    { label: "O+", value: "O+" },
    { label: "O-", value: "O-" },
    { label: "AB+", value: "AB+" },
    { label: "AB-", value: "AB-" }
  ];

  const religionOptions = [
    { label: labels['islam'] || "Islam", value: "Islam" },
    { label: labels['hinduism'] || "Hinduism", value: "Hinduism" },
    { label: labels['christianity'] || "Christianity", value: "Christianity" },
    { label: labels['buddhism'] || "Buddhism", value: "Buddhism" },
    { label: labels['others'] || "Others", value: "Others" }
  ];

  const maritalStatusOptions = [
    { label: labels['single'] || "Single", value: "Single" },
    { label: labels['married'] || "Married", value: "Married" },
    { label: labels['divorced'] || "Divorced", value: "Divorced" },
    { label: labels['widowed'] || "Widowed", value: "Widowed" }
  ];

  const onSubmit = async (values, { resetForm }) => {
    try {
      const formData = new FormData();

      // Basic information
      formData.append("name", values.name || "");

      // Only send email if it has been changed
      if (values.email !== editData.email) {
        formData.append("email", values.email || "");
      }

      formData.append("contact_no", values.contact_no || "");
      formData.append("alternative_contact_no", values.alternative_contact_no || "");

      // Handle image upload
      if (values.image && values.image instanceof File) {
        formData.append("image", values.image);
      }

      // Education & Professional details
      formData.append("education", values.education || "");
      formData.append("institute", values.institute || "");
      formData.append("bio", values.bio || "");

      // Personal details
      formData.append("gender", values.gender || "");
      formData.append("blood_group", values.blood_group || "");
      formData.append("father_name", values.father_name || "");
      formData.append("mother_name", values.mother_name || "");
      formData.append("religion", values.religion || "");
      formData.append("marital_status", values.marital_status || "");

      if (values.date_of_birth) {
        const formattedDate = typeof values.date_of_birth === 'string'
          ? values.date_of_birth
          : values.date_of_birth.format('YYYY-MM-DD');
        formData.append("date_of_birth", formattedDate);
      }

      // Address details
      formData.append("current_address", values.current_address || "");
      formData.append("permanent_address", values.permanent_address || "");

      // ID documents
      formData.append("nid_no", values.nid_no || "");
      formData.append("birth_certificate_no", values.birth_certificate_no || "");
      formData.append("passport_no", values.passport_no || "");

      // Status flags
      formData.append("is_active", values.is_active ? 1 : 0);
      formData.append("_method", "PUT");

      const response = await updateApi({
        end_point: "admin/student-update/" + editData.id,
        body: formData,
      });
      console.log(response);
      if (response.error) {
        toast.error(response?.error?.data?.message);
        console.error(labels['error updating student'] || "Error updating student:", response.error);
      } else {
        dispatch(setEditShowModal(false));
      }
    } catch (error) {
      console.error(labels['error in form submission'] || "Error in form submission:", error);
    }
  };

  return (
    <Modal
      activeModal={showEditModal}
      onClose={() => dispatch(setEditShowModal(false))}
      title={labels['update student profile'] || "Update Student Profile"}
      className="max-w-6xl"
      centered
    >
      <Formik
        validationSchema={editValidationSchema}
        initialValues={{...editData, contact_no: editData?.contact_no || '', alternative_contact_no: editData?.alternative_contact_no || ''}}
        onSubmit={onSubmit}
      >
        {({ setFieldValue, values, isSubmitting }) => (
          <Form className="space-y-6">
            <div className="grid md:grid-cols-3 gap-6">
              {/* Left column - Image uploader and status toggles */}
              <div className="md:col-span-1">
                <ImageUploader
                  name="image"
                  label={labels['profile photo'] || "Profile Photo"}
                  initialImage={editData.image}
                />

                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">{labels['basic information'] || "Basic Information"}</h3>
                  <div className="space-y-4">
                    <InputField
                      label={labels['full name'] || "Full Name"}
                      name="name"
                      type="text"
                      placeholder={labels['enter student\'s full name'] || "Enter student's full name"}
                      required
                    />

                    <div className="grid grid-cols-1 gap-4">
                      <InputField
                        label={labels['email address'] || "Email Address"}
                        name="email"
                        type="email"
                        placeholder={labels['enter email address'] || "Enter email address"}
                      />
                    </div>

                    <div className="grid grid-cols-1 gap-4">
                      <NumberInput
                        label={labels['contact number'] || "Contact Number"}
                        name="contact_no"
                        type="text"
                        placeholder={labels['enter contact number'] || "Enter contact number"}
                      />

                      <NumberInput
                        label={labels['alternative contact'] || "Alternative Contact"}
                        name="alternative_contact_no"
                        type="text"
                        placeholder={labels['enter alternative contact'] || "Enter alternative contact"}
                      />
                    </div>
                  </div>
                </div>

                {editData && 
                  <div className="mt-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">{labels['status settings'] || "Status Settings"}</h3>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <label className="block text-sm text-gray-700">{labels['active status'] || "Active Status"}</label>
                      
                      <Switch
                        activeClass="bg-success-500"
                        value={values.is_active}
                        name="is_active"
                        onChange={(e) => setFieldValue("is_active", e.target.checked)}
                      />
                    </div>
                  </div>
                </div>
                }
              </div>

              {/* Right column - Form fields */}
              <div className="md:col-span-2 space-y-5">
                {/* Bio section */}
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <div className="space-y-4">
                    <Textarea
                      label={labels['bio'] || "Bio"}
                      name="bio"
                      placeholder={labels['enter a short bio'] || "Enter a short bio"} 
                      row={3}
                      value={values.bio || ""}
                      onChange={(e) => setFieldValue("bio", e.target.value)}
                    />
                  </div>
                </div>

                {/* Education & Professional Details */}
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">{labels['education & professional details'] || "Education & Professional Details"}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <InputField
                      label={labels['education'] || "Education"}
                      name="education"
                      type="text"
                      placeholder={labels['highest education level'] || "Highest education level"}
                    />

                    <InputField
                      label={labels['institute'] || "Institute"}
                      name="institute"
                      type="text"
                      placeholder={labels['educational institute'] || "Educational institute"}
                    />
                  </div>
                </div>

                {/* Personal Details */}
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">{labels['personal details'] || "Personal Details"}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Select
                      label={labels['gender'] || "Gender"}
                      name="gender"
                      placeholder={labels['select gender'] || "Select gender"}
                      options={genderOptions}
                      onChange={(e) => setFieldValue("gender", e.target.value)}
                      defaultValue={values.gender}
                    />

                    <Select
                      label={labels['blood group'] || "Blood Group"}
                      name="blood_group"
                      placeholder={labels['select blood group'] || "Select blood group"}
                      options={bloodGroupOptions}
                      onChange={(e) => setFieldValue("blood_group", e.target.value)}
                      defaultValue={values.blood_group}
                    />

                    <InputField
                      label={labels['father\'s name'] || "Father's Name"}
                      name="father_name"
                      type="text"
                      placeholder={labels['enter father\'s name'] || "Enter father's name"}
                    />

                    <InputField
                      label={labels['mother\'s name'] || "Mother's Name"}
                      name="mother_name"
                      type="text"
                      placeholder={labels['enter mother\'s name'] || "Enter mother's name"}
                    />

                    <Select
                      label={labels['religion'] || "Religion"}
                      name="religion"
                      placeholder={labels['select religion'] || "Select religion"}
                      options={religionOptions}
                      onChange={(e) => setFieldValue("religion", e.target.value)}
                      defaultValue={values.religion}
                    />

                    <Select
                      label={labels['marital status'] || "Marital Status"}
                      name="marital_status"
                      placeholder={labels['select marital status'] || "Select marital status"}
                      options={maritalStatusOptions}
                      onChange={(e) => setFieldValue("marital_status", e.target.value)}
                      defaultValue={values.marital_status}
                    />

                    <DatePicker
                      label={labels['date of birth'] || "Date of Birth"}
                      name="date_of_birth"
                      placeholder={labels['yyyy-mm-dd'] || "YYYY-MM-DD"}
                      format="YYYY-MM-DD"
                      defaultValue={values.date_of_birth}
                      onChange={(date) => setFieldValue("date_of_birth", date)}
                    />
                  </div>
                </div>

                {/* Address Information */}
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">{labels['address information'] || "Address Information"}</h3>
                  <div className="space-y-4">
                    <Textarea
                      label={labels['current address'] || "Current Address"}
                      name="current_address"
                      placeholder={labels['enter current address'] || "Enter current address"}
                      row={2}
                      value={values.current_address || ""}
                      onChange={(e) => setFieldValue("current_address", e.target.value)}
                    />

                    <Textarea
                      label={labels['permanent address'] || "Permanent Address"}
                      name="permanent_address"
                      placeholder={labels['enter permanent address'] || "Enter permanent address"}
                      row={2}
                      value={values.permanent_address || ""}
                      onChange={(e) => setFieldValue("permanent_address", e.target.value)}
                    />
                  </div>
                </div>

                {/* ID Documents */}
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">{labels['id documents'] || "ID Documents"}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <InputField
                      label={labels['nid'] || "NID"}
                      name="nid_no"
                      type="text"
                      placeholder={labels['enter nid number'] || "Enter NID number"}
                    />

                    <InputField
                      label={labels['birth certificate no'] || "Birth Certificate No."}
                      name="birth_certificate_no"
                      type="text"
                      placeholder={labels['enter birth certificate number'] || "Enter birth certificate number"}
                    />

                    <InputField
                      label={labels['passport number'] || "Passport Number"}
                      name="passport_no"
                      type="text"
                      placeholder={labels['enter passport number'] || "Enter passport number"}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <Button
                text={labels['cancel'] || "Cancel"}
                className="btn-outline-dark"
                onClick={() => dispatch(setEditShowModal(false))}
                type="button"
              />
              <Button
                text={labels['save changes'] || "Save Changes"}
                className="btn-primary"
                type="submit"
                isLoading={isSubmitting || isLoading}
                disabled={isSubmitting || isLoading}
              />
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default editStudent;