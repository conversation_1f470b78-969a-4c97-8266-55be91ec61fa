import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { useDispatch, useSelector } from "react-redux";
import { useDeleteApiMutation } from "@/store/api/master/commonSlice";

const StudentDelete = ({ showDeleteModal, setShowDeleteModal, data }) => {
  const dispatch = useDispatch();
  const { labels } = useSelector((state) => state.languageReducer);
  const [deleteApi, { isLoading }] = useDeleteApiMutation();

  const onSubmit = async () => {
    await deleteApi({
      end_point: "/admin/student-delete/" + data?.id,
      body: {}
    });
    setShowDeleteModal(false);
  };

  return (
    <Modal
      activeModal={showDeleteModal}
      onClose={() => setShowDeleteModal(false)}
      title={labels['delete student'] || "Delete Student"}
      className="max-w-3xl"
      footer={
        <Button
          text={labels['close'] || "Close"}
          btnClass="btn-primary"
          onClick={() => setShowDeleteModal(false)}
        />
      }
    >
      <h3 className="text-center">{labels['are you sure'] || "Are you sure?"}</h3>
      <p className="text-center text-slate-500 text-sm mt-4">
        {labels['you are about to delete'] || "You are about to delete"}{" "}
        <b>
          "{labels['student'] || "Student"}: {data?.name}"
        </b>{" "}
        . {labels['once deleted warning'] || "Once you have done this, there is no going back."}
      </p>

      <div className="ltr:text-right rtl:text-left mt-5 gap-4">
        <Button
          type="button"
          className="btn text-center btn-primary mr-4"
          onClick={() => setShowDeleteModal(false)}
          text={labels['cancel'] || "Cancel"}
        />
        <Button
          isLoading={isLoading}
          type="button"
          className="btn text-center btn-danger"
          onClick={onSubmit}
          text={labels['delete'] || "Delete"}
        />
      </div>
    </Modal>
  );
};

export default StudentDelete;