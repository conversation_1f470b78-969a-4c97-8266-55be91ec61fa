import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import moment from "moment";
import Icon from "@/components/ui/Icon";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import avatar from "@/assets/images/avatar/av-1.svg";
import EditStudent from "./editStudent";
import { Mail, Phone, Award, MapPin, BookOpen, User, Calendar, Heart, Flag, CreditCard, CheckCircle, Clock, XCircle } from "lucide-react";

// Modern detail item with icon
const DetailItem = ({ label, value, icon }) => {
  const { labels } = useSelector((state) => state.languageReducer);
  return (
    <div className="flex items-start space-x-3 py-2">
      {icon && (
        <div className="flex-shrink-0 mt-1">
          {icon}
        </div>
      )}
      <div className="flex-1">
        <p className="text-sm text-gray-500 font-medium">{labels[label.toLowerCase()] || label}</p>
        <p className="text-base text-gray-800">{value || "---"}</p>
      </div>
    </div>
  );
};

// Section card with title
const SectionCard = ({ title, icon, children }) => {
  const { labels } = useSelector((state) => state.languageReducer);
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
      <div className="border-b border-gray-100 px-5 py-3 flex items-center">
        {icon && <span className="text-primary-500 mr-2">{icon}</span>}
        <h3 className="text-base font-medium text-gray-800">{labels[title.toLowerCase()] || title}</h3>
      </div>
      <div className="p-5">{children}</div>
    </div>
  );
};

const detailsStudent = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams();
  const { labels } = useSelector((state) => state.languageReducer);

  const {
    data: student,
    isLoading,
  } = useGetApiQuery(`admin/student-details-for-admin?id=${id}`);

  const handleEditStudentClick = () => {
    dispatch(setEditData(student));
    dispatch(setEditShowModal(true));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (!student) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <Icon icon="heroicons-outline:exclamation-circle" className="w-12 h-12 text-gray-400 mb-2" />
        <h2 className="text-xl font-medium text-gray-600">{labels['no student details available'] || "No student details available"}</h2>
        <button
          onClick={() => navigate("/student-list")}
          className="mt-4 px-4 py-2 text-sm font-medium text-primary-600 bg-primary-50 rounded-md hover:bg-primary-100"
        >
          {labels['back to student list'] || "Back to Student List"}
        </button>
      </div>
    );
  }

  return (
    <>
      {/* Header with action buttons */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">{labels['student profile'] || "Student Profile"}</h1>
        <div className="flex space-x-3">
          <button
            onClick={() => navigate("/student-list")}
            className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <Icon
              icon="material-symbols:arrow-back"
              className="w-4 h-4 mr-2"
            />
            {labels['back to list'] || "Back to List"}
          </button>
          <button
            onClick={handleEditStudentClick}
            className="flex items-center px-4 py-2 text-sm font-medium text-white bg-primary-500 rounded-md hover:bg-primary-600"
          >
            <Icon
              icon="akar-icons:edit"
              className="w-4 h-4 mr-2"
            />
            {labels['edit profile'] || "Edit Profile"}
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        {/* Left column - Profile card */}
        <div className="lg:col-span-1 space-y-6">
          {/* Profile Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
            <div className="p-6 flex flex-col items-center text-center">
              <div className="relative mb-4 group">
                <div className="h-32 w-32 rounded-full overflow-hidden ring-4 ring-white shadow-md">
                  <img
                    src={
                      student.image
                        ? import.meta.env.VITE_ASSET_HOST_URL + student.image
                        : avatar
                    }
                    alt={student.name || labels['student'] || "Student"}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>

              <h2 className="text-xl font-bold text-gray-800 mb-1">
                {student.name}
              </h2>

              {student.student_code && (
                <p className="text-sm text-gray-500 mb-3">
                  {labels['id'] || "ID"}: {student.student_code}
                </p>
              )}

              <div className="flex flex-wrap items-center justify-center gap-2 mb-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  student.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {student.is_active ? labels['active'] || 'Active' : labels['inactive'] || 'Inactive'}
                </span>
              </div>

              <div className="w-full space-y-2 border-t border-gray-100 pt-4">
                {student.email && (
                  <div className="flex items-center text-sm">
                    <Mail size={16} className="text-gray-400 mr-2" />
                    <span className="text-gray-600">{student.email}</span>
                  </div>
                )}

                {student.contact_no && (
                  <div className="flex items-center text-sm">
                    <Phone size={16} className="text-gray-400 mr-2" />
                    <span className="text-gray-600">{student.contact_no}</span>
                  </div>
                )}

                {student.alternative_contact_no && (
                  <div className="flex items-center text-sm">
                    <Phone size={16} className="text-gray-400 mr-2" />
                    <span className="text-gray-600">{student.alternative_contact_no} ({labels['alt'] || "Alt"})</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Status Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
            <div className="border-b border-gray-100 px-5 py-3">
              <h3 className="text-base font-medium text-gray-800">{labels['status information'] || "Status Information"}</h3>
            </div>
            <div className="p-5 space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">{labels['active status'] || "Active Status"}</span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  student.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {student.is_active ? labels['active'] || 'Active' : labels['inactive'] || 'Inactive'}
                </span>
              </div>

              {student.created_at && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">{labels['registration date'] || "Registration Date"}</span>
                  <span className="text-sm text-gray-800">
                    {moment(student.created_at).format("MMM D, YYYY")}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right column - Details sections */}
        <div className="lg:col-span-2 space-y-6">
          {/* Education section */}
          <SectionCard title={labels['education details'] || "Education Details"} icon={<BookOpen size={18} />}>
            <div className="grid md:grid-cols-2 gap-6">
              <DetailItem
                icon={<Award size={18} className="text-gray-400" />}
                label={labels['education'] || "Education"}
                value={student.education}
              />
              <DetailItem
                icon={<MapPin size={18} className="text-gray-400" />}
                label={labels['institute'] || "Institute"}
                value={student.institute}
              />
            </div>
          </SectionCard>

          {/* Address section */}
          {(student.current_address || student.permanent_address) && (
            <SectionCard title={labels['address information'] || "Address Information"} icon={<MapPin size={18} />}>
              <div className="grid md:grid-cols-2 gap-6">
                {student.current_address && (
                  <DetailItem
                    icon={<MapPin size={18} className="text-gray-400" />}
                    label={labels['current address'] || "Current Address"}
                    value={student.current_address}
                  />
                )}
                {student.permanent_address && (
                  <DetailItem
                    icon={<MapPin size={18} className="text-gray-400" />}
                    label={labels['permanent address'] || "Permanent Address"}
                    value={student.permanent_address}
                  />
                )}
              </div>
            </SectionCard>
          )}

          {/* Personal details section */}
          <SectionCard title={labels['personal information'] || "Personal Information"} icon={<User size={18} />}>
            <div className="grid md:grid-cols-2 gap-6">
              {student.date_of_birth && (
                <DetailItem
                  icon={<Calendar size={18} className="text-gray-400" />}
                  label={labels['date of birth'] || "Date of Birth"}
                  value={moment(student.date_of_birth).format("MMMM D, YYYY")}
                />
              )}
              {student.gender && (
                <DetailItem
                  icon={<User size={18} className="text-gray-400" />}
                  label={labels['gender'] || "Gender"}
                  value={student.gender}
                />
              )}
              {student.blood_group && (
                <DetailItem
                  icon={<Heart size={18} className="text-gray-400" />}
                  label={labels['blood group'] || "Blood Group"}
                  value={student.blood_group}
                />
              )}
              {student.religion && (
                <DetailItem
                  icon={<Flag size={18} className="text-gray-400" />}
                  label={labels['religion'] || "Religion"}
                  value={student.religion}
                />
              )}
              {student.marital_status && (
                <DetailItem
                  icon={<User size={18} className="text-gray-400" />}
                  label={labels['marital status'] || "Marital Status"}
                  value={student.marital_status}
                />
              )}
              {student.father_name && (
                <DetailItem
                  icon={<User size={18} className="text-gray-400" />}
                  label={labels['father\'s name'] || "Father's Name"}
                  value={student.father_name}
                />
              )}
              {student.mother_name && (
                <DetailItem
                  icon={<User size={18} className="text-gray-400" />}
                  label={labels['mother\'s name'] || "Mother's Name"}
                  value={student.mother_name}
                />
              )}
            </div>
          </SectionCard>

          {/* ID Documents section */}
          {(student.nid_no || student.birth_certificate_no || student.passport_no) && (
            <SectionCard title={labels['id documents'] || "ID Documents"} icon={<Award size={18} />}>
              <div className="grid md:grid-cols-2 gap-6">
                {student.nid_no && (
                  <DetailItem
                    icon={<Award size={18} className="text-gray-400" />}
                    label={labels['nid'] || "NID"}
                    value={student.nid_no}
                  />
                )}
                {student.birth_certificate_no && (
                  <DetailItem
                    icon={<Award size={18} className="text-gray-400" />}
                    label={labels['birth certificate no'] || "Birth Certificate No."}
                    value={student.birth_certificate_no}
                  />
                )}
                {student.passport_no && (
                  <DetailItem
                    icon={<Award size={18} className="text-gray-400" />}
                    label={labels['passport number'] || "Passport Number"}
                    value={student.passport_no}
                  />
                )}
              </div>
            </SectionCard>
          )}

          {/* System Information */}
          <SectionCard title={labels['system information'] || "System Information"} icon={<Icon icon="heroicons-outline:information-circle" className="w-5 h-5" />}>
            <div className="grid md:grid-cols-2 gap-6">
              {student.created_at && (
                <DetailItem
                  icon={<Calendar size={18} className="text-gray-400" />}
                  label={labels['created at'] || "Created At"}
                  value={moment(student.created_at).format("MMMM D, YYYY, h:mm A")}
                />
              )}
              {student.updated_at && (
                <DetailItem
                  icon={<Calendar size={18} className="text-gray-400" />}
                  label={labels['last updated'] || "Last Updated"}
                  value={moment(student.updated_at).format("MMMM D, YYYY, h:mm A")}
                />
              )}
            </div>
          </SectionCard>
        </div>
      </div>

      {/* Enrollment History */}
      {student.payments && student.payments.length > 0 && (
        <SectionCard title={labels['enrollment history'] || "Enrollment History"} icon={<CreditCard size={18} />}>
          <div className="space-y-4">
            {student.payments.map((payment) => (
              <div key={payment.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-1">
                      <img
                        src={payment.course?.thumbnail
                          ? import.meta.env.VITE_ASSET_HOST_URL + payment.course.thumbnail
                          : avatar
                        }
                        alt={payment.course?.title || labels['course'] || "Course"}
                        className="w-12 h-12 rounded-lg object-cover"
                      />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-base font-medium text-gray-800 mb-1">
                        {payment.course?.title || labels['course'] || "Course"}
                      </h4>
                      <p className="text-sm text-gray-500">
                        {labels['transaction id'] || "Transaction ID"}: {payment.transaction_id}
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mb-1 ${
                      payment.status === 'Completed'
                        ? 'bg-green-100 text-green-800'
                        : payment.status === 'Pending'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {payment.status === 'Completed' && <CheckCircle size={12} className="mr-1" />}
                      {payment.status === 'Pending' && <Clock size={12} className="mr-1" />}
                      {payment.status === 'Failed' && <XCircle size={12} className="mr-1" />}
                      {labels[payment.status.toLowerCase()] || payment.status}
                    </span>
                    <span className="text-sm font-medium text-gray-800">
                      {payment.paid_amount} {payment.currency}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500 font-medium">{labels['payment method'] || "Payment Method"}</p>
                    <p className="text-gray-800 capitalize">{payment.payment_method}</p>
                  </div>
                  <div>
                    <p className="text-gray-500 font-medium">{labels['payment type'] || "Payment Type"}</p>
                    <p className="text-gray-800">{payment.payment_type}</p>
                  </div>
                  <div>
                    <p className="text-gray-500 font-medium">{labels['enrollment date'] || "Enrollment Date"}</p>
                    <p className="text-gray-800">
                      {moment(payment.created_at).format("MMM D, YYYY")}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-500 font-medium">{labels['verification'] || "Verification"}</p>
                    <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                      payment.is_verified_payment
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {payment.is_verified_payment ? labels['verified'] || 'Verified' : labels['unverified'] || 'Unverified'}
                    </span>
                  </div>
                </div>

                {payment.discount_amount > 0 && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-500">{labels['original amount'] || "Original Amount"}:</span>
                      <span className="text-gray-800">{payment.payable_amount + payment.discount_amount} {payment.currency}</span>
                    </div>
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-500">{labels['discount'] || "Discount"}:</span>
                      <span className="text-green-600">-{payment.discount_amount} {payment.currency}</span>
                    </div>
                    <div className="flex justify-between items-center text-sm font-medium">
                      <span className="text-gray-800">{labels['final amount'] || "Final Amount"}:</span>
                      <span className="text-gray-800">{payment.paid_amount} {payment.currency}</span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Summary */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div className="bg-blue-50 rounded-lg p-3">
                <p className="text-2xl font-bold text-blue-600">{student.payments.length}</p>
                <p className="text-sm text-blue-600 font-medium">{labels['total enrollments'] || "Total Enrollments"}</p>
              </div>
              <div className="bg-green-50 rounded-lg p-3">
                <p className="text-2xl font-bold text-green-600">
                  {student.payments.filter(p => p.status === 'Completed').length}
                </p>
                <p className="text-sm text-green-600 font-medium">{labels['completed payments'] || "Completed Payments"}</p>
              </div>
              <div className="bg-purple-50 rounded-lg p-3">
                <p className="text-2xl font-bold text-purple-600">
                  {student.payments.reduce((total, payment) => {
                    if (payment.status === 'Completed') {
                      return total + payment.paid_amount;
                    }
                    return total;
                  }, 0).toLocaleString()}
                  <span className="text-sm font-normal ml-1">
                    {student.payments[0]?.currency || 'BDT'}
                  </span>
                </p>
                <p className="text-sm text-purple-600 font-medium">{labels['total paid'] || "Total Paid"}</p>
              </div>
            </div>
          </div>
        </SectionCard>
      )}

      {/* Edit Student Modal */}
      <EditStudent student={student} onClose={() => dispatch(setEditShowModal(false))} />
    </>
  );
};

export default detailsStudent;