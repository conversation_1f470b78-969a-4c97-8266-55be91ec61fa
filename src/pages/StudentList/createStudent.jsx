import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Button from "@/components/ui/Button";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import { Formik, Form, Field } from "formik";
import { initialValues, validationSchema } from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import { setStudent } from "@/features/studentSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";

const createStudent = () => {
  const [errors, setErrors] = useState([]);
  const [postApi, { isLoading }] = usePostApiMutation();
  const [isActive, setIsActive] = useState(false);
  const dispatch = useDispatch();
  const { showModal } = useSelector((state) => state.commonReducer);
  const { labels } = useSelector((state) => state.languageReducer);

  const onSubmit = async (values, { resetForm }) => {
    let formData = new FormData();
    formData.append("name", values.name);
    formData.append("password", values.password);
    formData.append("email", values.email);
    formData.append("contact_no", values.contact_no);
    formData.append("is_active", isActive ? 1 : 0);
    
    const response = await postApi({
      end_point: "admin/student-create",
      body: values,
    });

    if (response.data.status) {
      dispatch(setStudent(response.data.data));
      dispatch(setShowModal(false));
    }
    if (!response.status) {
      setErrors(response.error?.data.errors);
    } else {
      dispatch(setShowModal(false));
    }
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={() => dispatch(setShowModal(false))}
      title={labels['add new student'] || "Add New Student"}
      className="max-w-3xl"
      footer={
        <Button
          text={labels['close'] || "Close"}
          btnClass="btn-primary"
          onClick={() => dispatch(setShowModal(false))}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={initialValues}
        onSubmit={onSubmit}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          handleSubmit,
          setFieldValue,
          isSubmitting,
        }) => (
          <Form>
            <div className="grid md:grid-cols-2 gap-4">
              <InputField
                label={labels['name'] || "Name"}
                name="name"
                type="text"
                placeholder={labels['enter name'] || "Enter Name"}
                required
              />
              <InputField
                label={labels['email'] || "Email"}
                name="email"
                type="email"
                placeholder={labels['enter email'] || "Enter Email"}
              />       
              <InputField
                label={labels['contact no'] || "Contact No"}
                name="contact_no"
                type="text"
                placeholder={labels['enter contact number'] || "Enter Contact Number"}
              />
              <InputField
                label={labels['password'] || "Password"}
                name="password"
                type="text"
                placeholder={labels['enter password'] || "Enter Password"}
                required
              />
            </div>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
                text={labels['submit'] || "Submit"}
              />
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default createStudent;