import Button from "@/components/ui/Button";
import Fileinput from "@/components/ui/Fileinput";
import Modal from "@/components/ui/Modal";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { Form, Formik } from "formik";
import React, { useState } from "react";
import * as Yup from "yup";
import { useSelector } from "react-redux";

// Validation schema
const validationSchema = Yup.object().shape({
  file: Yup.mixed()
    .required("A file is required")
    .test(
      "fileType",
      "Only Excel or CSV files are allowed",
      (value) =>
        value &&
        [
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // Excel (.xlsx)
          "application/vnd.ms-excel", // Excel (.xls)
          "text/csv", // CSV (.csv)
        ].includes(value.type)
    ),
});

const bulkUpload = ({ showBulkModal, setShowBulkModal }) => {
  const [postApi, { isLoading }] = usePostApiMutation();
  const { labels } = useSelector((state) => state.languageReducer);

  const handleSubmit = async (values) => {
    const formData = new FormData();
    formData.append("file", values.file);
    const response = await postApi({
      end_point: "/admin/import-student",
      body: formData,
    });
    setShowBulkModal(false);
  };

  return (
    <Modal
      activeModal={showBulkModal}
      onClose={() => setShowBulkModal(false)}
      title={labels['upload bulk'] || "Upload Bulk"}
      className="max-w-3xl"
      footer={
        <Button
          text={labels['close'] || "Close"}
          btnClass="btn-primary"
          onClick={() => setShowBulkModal(false)}
        />
      }
    >
      <Formik
        initialValues={{ file: "" }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ values, setFieldValue, errors, touched }) => (
          <Form>
            <Fileinput
              name="file"
              accept=".xlsx,.xls,.csv"
              type="file"
              placeholder={labels['select bulk'] || "Select Bulk"}
              title={labels['bulk document'] || "Bulk (Document)"}
              selectedFile={values.file}
              onChange={(e) => {
                setFieldValue("file", e.target.files[0]);
              }}
            />
            {errors.file && touched.file && (
              <div className="text-red-500 text-sm mt-1">
                {errors.file === "A file is required" 
                  ? labels['a file is required'] || errors.file
                  : labels['only excel or csv files are allowed'] || errors.file}
              </div>
            )}

            <div className="flex justify-end mt-4">
              <Button
                text={labels['submit'] || "Submit"}
                type="submit"
                btnClass="btn-primary"
                isLoading={isLoading}
              />
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default bulkUpload;