import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import { useGetStudentListQuery } from "@/store/api/master/studentSlice";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Badge from "@/components/ui/Badge";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";
import { useNavigate } from "react-router-dom";
import CreateStudent from "./createStudent";
import EditStudent from "./editStudent";
import StudentDelete from "./StudentDelete";
import StudentBulkUpload from "./StudentBulkUpload";
import ChangePasswordModal from "@/components/partials/common-modals/ChangePasswordModal";

const StudentList = () => {
  // const [showModal, setShowModal] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [apiParam, setApiParam] = useState('');
  const [deleteData, setDeleteData] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const { data, isLoading, isFetching } = useGetApiQuery("admin/all-student-list-admin" + apiParam);
  const [showBulkModal, setShowBulkModal] = useState(false);
  const changePage = (val) => {
    setApiParam(val);
  };

  // const data = res.data;
  const columns = [
    {
      label: "Student ID",
      field: "student_id",
    },
    {
      label: "Name",
      field: "name",
    },
    {
      label: "Email or Phone",
      field: "email_or_phone",
    },
    {
      label: "Total Enrollment",
      field: "payments_count",
    },
    {
      label: "Status",
      field: "status",
    },
    {
      label: "Action",
      field: "",
    },
  ];

  const handleIndexNameClick = (id) => {
    const selectedStudent = data?.data.find((item) => item.id === id);
    navigate(`/student-details/${id}`, { state: { student: selectedStudent } });
    // navigate(`/student-dashboard/${id}`, { state: { student: selectedStudent } });
  };

  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      student_id: <p>{item?.student_code}</p>,
      name: (
        <button
          type="button"
          onClick={() => handleIndexNameClick(item.id)}
          className="hover:text-primary-500 hover:underline"
        >
          {item.name}
        </button>
      ),
      email_or_phone: <span>{item.email } 
       {(item.email && item.contact_no)  && <br /> }
       {item.contact_no} </span>,
      payments_count: item.payments_count || ' ',
      status: (
        <Badge
          className={
            item.is_active
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {" "}
          {item.is_active ? "Active" : "Inactive"}
        </Badge>
      ),
    };
  });

  const actions = [
    {
      name: "view",
      icon: "heroicons-outline:eye",
      onClick: (val) => {
        const selectedStudent = data?.data[val];
        navigate(`/student-details/${selectedStudent.id}`, {
          state: { student: selectedStudent },
        });
      },
    },
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        dispatch(setEditData(data.data[val]));
        dispatch(setEditShowModal(true));
      },
    },
    {
      name: "Change Password",
      icon: "heroicons-outline:key",
      onClick: (val) => {
        setSelectedUser(data.data[val]);
        setShowPasswordModal(true);
      },
    },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  const createPage = <CreateStudent />;
  const editPage = <EditStudent />;
  return (
    <div>
      <BasicTablePage
        sampleFile="https://api.edupackbd.com/uploads/csv/student_informations.csv"
        loading={isLoading || isFetching}
        title="Student List"
        createButton="Add New Student"
        setShowBulkModal={setShowBulkModal}
        createPage={createPage}
        editPage={editPage}
        actions={actions}
        columns={columns}
        data={tableData}
        changePage={changePage}
        currentPage={data?.current_page}
        setFilter={setApiParam}
        totalPages={Math.ceil(data?.total / data?.per_page)}
      />

      <StudentDelete
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />

      <StudentBulkUpload showBulkModal={showBulkModal} setShowBulkModal={setShowBulkModal} />

      {selectedUser && (
        <ChangePasswordModal
          showModal={showPasswordModal}
          setShowModal={setShowPasswordModal}
          userId={selectedUser.user_id}
          userName={selectedUser.name}
        />
      )}
    </div>
  );
};

export default StudentList;
