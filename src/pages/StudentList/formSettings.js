import React from "react";
import * as yup from "yup";

export const initialValues = {
    name:'',
    password:'',
    email:'',
    contact_no:'',
    is_active: 1,
    student_id:'',
};


export const validationSchema = yup.object({
    name: yup
      .string()
      .matches(
        /^([a-zA-Z. ]){2,30}$/,
        "Name should not contain numbers or special characters"
      )
      .max(50, "Should not be more than 50 characters")
      .min(3, "Should not be less than 3 characters")
      .required("Student Name is Required"),


      password: yup
      .string()
      .min(6, "Password must be at least 6 characters")
      .required("Password is required"),
      email: yup
      .string()
      .email("Invalid email format")
      .test(
        "email-or-contact",
        "Either email or contact number is required",
        function (value) {
          const { contact_no } = this.parent;
          return value || contact_no;
        }
      ),
    contact_no: yup
      .string()
      .matches(
        /^(\+?[1-9]\d{1,14})$/,
        "Contact number must be a valid mobile number"
      )
      .test(
        "contact-or-email",
        "Either contact number or email is required",
        function (value) {
          const { email } = this.parent;
          return value || email;
        }
      ),
  });


  export const editValidationSchema = yup.object({
    name: yup
      .string()
      .max(50, "Should not be more than 50 characters")
      .min(3, "Should not be less than 3 characters")
      .required("Teacher Name is Required"),

    email: yup.string().nullable().email("Invalid email address"),

    contact_no: yup
    .string()
    .nullable()
    .matches(
      /^(\+?[0-9]{1,4})?0?\d{6,14}$/,
      "Contact number must be a valid number"
    ),
  });

