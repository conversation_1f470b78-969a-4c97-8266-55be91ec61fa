import React, { useState, useEffect } from "react";
import { useGetApiQuery, usePostApiMutation } from "@/store/api/master/commonSlice";
import { useParams } from "react-router-dom";
import Items from "./Items";
import Editor from "./Editor";
const CategoryDetails = () => {
  const { id } = useParams();
  const [postApi] = usePostApiMutation();
  const { data: categoryDetails, isLoading, isError } = useGetApiQuery(`/admin/menu/${id}`);
  const [newDescription, setNewDescription] = useState("");

  useEffect(() => {
    if (categoryDetails?.category_items.length === 0) {
      setNewDescription("");
    }
  }, [categoryDetails]);

  const handleSubmit = async () => {
    await postApi({ end_point: `admin/add-items-to-menu`, body: { 
        menu_id: id,
        description: newDescription } });
  };

  if (isLoading) return <p>Loading...</p>;
  if (isError || !categoryDetails) return <p>Error fetching category details.</p>;

  return (
    <div className="p-4 bg-white dark:bg-gray-800 rounded-xl shadow-lg">
      <h1 className="text-2xl font-bold mb-4">{categoryDetails.name}</h1>
      {categoryDetails.icon && <img src={import.meta.env.VITE_ASSET_HOST_URL + categoryDetails.icon} alt={categoryDetails.name} className="w-24 h-24 mb-4" />}
      {categoryDetails.category_items.length === 0 ? (
        <div>
          <Editor description={newDescription} setNewDescription={setNewDescription} />
          <button className="mt-4 px-4 py-2 bg-green-500 text-white rounded" onClick={handleSubmit}>Submit</button>
        </div>
      ) : (
        <ul className="list-disc">
          {categoryDetails.category_items.map((item) => (
            <Items item={item} key={item.id} />
          ))}
        </ul>
      )}
    </div>
  );
};

export default CategoryDetails;
