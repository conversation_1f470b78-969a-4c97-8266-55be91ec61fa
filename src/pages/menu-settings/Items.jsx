import React, { useState, useEffect } from "react";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import { CKEditor } from "@ckeditor/ckeditor5-react";
import {
  ClassicEditor,
  Bold,
  Essentials,
  Heading,
  Indent,
  IndentBlock,
  Italic,
  Link,
  List,
  MediaEmbed,
  Paragraph,
  Table,
  Undo,
  FontSize,
  FontColor,
  Image,
  ImageToolbar,
  ImageCaption,
  ImageStyle,
  ImageResize,
  ImageUpload,
  GeneralHtmlSupport,
  SourceEditing,
} from "ckeditor5";

import "ckeditor5/ckeditor5.css";
import "@/assets/scss/custom-ckeditor.css";
import "@/assets/scss/image-styles.css";

class MyUploadAdapter {
  constructor(loader) {
    this.loader = loader;
  }

  upload() {
    return this.loader.file.then(
      (file) =>
        new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = function () {
            resolve({
              default: reader.result
            });
          };
          reader.onerror = function () {
            reject("Error occurred during reading the file.");
          };
          reader.readAsDataURL(file);
        })
    );
  }

  abort() {
  }
}

function MyCustomUploadAdapterPlugin(editor) {
  editor.plugins.get('FileRepository').createUploadAdapter = (loader) => {
    return new MyUploadAdapter(loader);
  };
}

const Items = ({ item }) => {
  const [updateApi, { isLoading }] = useUpdateApiMutation();
  const [value, setValue] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [editorInstance, setEditorInstance] = useState(null);

  useEffect(() => {
    if (item?.description) {
      setValue(item.description.trim());
    }
  }, [item]);

  useEffect(() => {
    if (editorInstance && item?.description) {
      editorInstance.setData(item.description.trim());
    }
  }, [editorInstance, item]);

  const handleSubmit = async () => {
    try {
    const formData = new FormData();
    formData.append("id", item.id);
    formData.append("description", value);
      const response = await updateApi({
        end_point: "admin/update-items-of-menu",
        body: formData
      }).unwrap();
      console.log("Update successful:", response);
      setIsEditing(false);
    } catch (error) {
      console.error("Error updating description:", error);
    }
  };

  return (
    <div className="p-4 border-b">
      <h1 className="text-2xl font-bold mb-4">{item?.name || "New Item"}</h1>
      {item?.icon && (
        <img
          src={import.meta.env.VITE_ASSET_HOST_URL + item.icon}
          alt={item.name}
          className="w-24 h-24 mb-4"
        />
      )}

      <div className="mb-4">
        {isEditing ? (
          <div>
            <CKEditor
              editor={ClassicEditor}
              config={{
              extraPlugins: [MyCustomUploadAdapterPlugin],
              toolbar: [
                "undo",
                "redo",
                "|",
                "heading",
                "fontSize",
                "fontColor",
                "fontBackgroundColor",
                "|",
                "bold",
                "italic",
                "|",
                "link",
                "insertTable",
                "mediaEmbed",
                "uploadImage",
                "|",
                "bulletedList",
                "numberedList",
                "indent",
                "outdent",
                "|",
                "sourceEditing",
              ],
              plugins: [
                Bold,
                Essentials,
                Heading,
                Indent,
                IndentBlock,
                Italic,
                Link,
                List,
                MediaEmbed,
                Paragraph,
                Table,
                Undo,
                FontSize,
                FontColor,
                Image,
                ImageToolbar,
                ImageCaption,
                ImageStyle,
                ImageResize,
                ImageUpload,
                GeneralHtmlSupport,
                SourceEditing,
              ],
              fontSize: {
                options: [10, 12, 14, 16, 18, 20, 24, 28, 32, 36],
              },
              fontColor: {
                columns: 6,
                documentColors: 12,
              },
              htmlSupport: {
                allow: [
                  {
                    name: /.*/, // Allow all tags
                    attributes: true,
                    classes: true,
                    styles: true,
                  },
                ],
              },
              image: {
                toolbar: [
                  'imageStyle:inline',
                  'imageStyle:block',
                  'imageStyle:side',
                  'imageStyle:alignLeft',
                  'imageStyle:alignCenter',
                  'imageStyle:alignRight',
                  '|',
                  'toggleImageCaption',
                  'imageTextAlternative'
                ],
                styles: {
                  full: {
                    name: 'full',
                    title: 'Full size image',
                    icon: 'full',
                    isDefault: true
                  },
                  alignLeft: {
                    name: 'alignLeft',
                    title: 'Left aligned image',
                    icon: 'left',
                    className: 'image-style-align-left'
                  },
                  alignCenter: {
                    name: 'alignCenter',
                    title: 'Center aligned image',
                    icon: 'center',
                    className: 'image-style-align-center'
                  },
                  alignRight: {
                    name: 'alignRight',
                    title: 'Right aligned image',
                    icon: 'right',
                    className: 'image-style-align-right'
                  },
                  side: {
                    name: 'side',
                    title: 'Side image',
                    icon: 'right',
                    className: 'image-style-side'
                  }
                },
                resizeOptions: [
                  {
                    name: 'resizeImage:original',
                    label: 'Original',
                    value: null
                  },
                  {
                    name: 'resizeImage:50',
                    label: '50%',
                    value: '50'
                  },
                  {
                    name: 'resizeImage:75',
                    label: '75%',
                    value: '75'
                  }
                ],
              },
              clipboard: {
                contentsCss: ['figure.image img { max-width: 100%; }'],
              },
            }}
            data={value}
            onReady={(editor) => {
              setEditorInstance(editor);
            }}
            onChange={(_, editor) => {
              setValue(editor.getData());
            }}
          />
          <div className="mt-2 text-sm text-gray-500">
            <p><strong>Tip:</strong> To remove an image, click on it to select it and then press the Delete key on your keyboard. To edit HTML source code directly, click the <strong>Source</strong> button in the toolbar.</p>
          </div>
          </div>
        ) : (
          <div
            className="text-gray-700 ck-content"
            dangerouslySetInnerHTML={{ __html: item.description }}
          ></div>
        )}
      </div>

      <button
        className="px-3 py-1 bg-blue-500 text-white rounded mr-2"
        onClick={() => setIsEditing(!isEditing)}
      >
        {isEditing ? "Cancel" : "Edit"}
      </button>

      {isEditing && (
        <button
          className="px-3 py-1 bg-green-500 text-white rounded"
          onClick={handleSubmit}
          disabled={isLoading}
        >
          {isLoading ? "Saving..." : "Submit"}
        </button>
      )}
    </div>
  );
};

export default Items;
