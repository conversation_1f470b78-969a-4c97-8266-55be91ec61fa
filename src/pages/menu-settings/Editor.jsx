import React, { useState, useEffect } from "react";
import { CKEditor } from "@ckeditor/ckeditor5-react";
import {
  ClassicEditor,
  Bold,
  Essentials,
  Heading,
  Indent,
  IndentBlock,
  Italic,
  Link,
  List,
  MediaEmbed,
  Paragraph,
  Table,
  Undo,
  FontSize,
  FontColor,
  Image,
  ImageToolbar,
  ImageCaption,
  ImageStyle,
  ImageResize,
  ImageUpload,
  GeneralHtmlSupport,
  SourceEditing,
} from "ckeditor5";

import "ckeditor5/ckeditor5.css";
import "@/assets/scss/custom-ckeditor.css";
import "@/assets/scss/image-styles.css";
import "@/assets/scss/editor-fix.css";

// Upload Adapter
class MyUploadAdapter {
  constructor(loader) {
    this.loader = loader;
  }

  upload() {
    return this.loader.file.then(
      (file) =>
        new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = function () {
            resolve({ default: reader.result });
          };
          reader.onerror = function () {
            reject("Error occurred during reading the file.");
          };
          reader.readAsDataURL(file);
        })
    );
  }

  abort() {}
}

function MyCustomUploadAdapterPlugin(editor) {
  editor.plugins.get("FileRepository").createUploadAdapter = (loader) => {
    return new MyUploadAdapter(loader);
  };
}

// Cursor Position Fix
const fixCursorPosition = (editor) => {
  if (!editor) return;

  const selection = editor.model.document.selection;
  if (!selection.isCollapsed) return;

  editor.model.change((writer) => {
    const position = editor.model.document.selection.getLastPosition();
    if (position) {
      writer.setSelection(position);
    }
  });
};

const Editor = ({ description, setNewDescription }) => {
  const [editorInstance, setEditorInstance] = useState(null);

  useEffect(() => {
    if (description) {
      const processedDescription = description.trim();
      setNewDescription(processedDescription);
    }
  }, [description]);

  return (
    <div className="flex-1">
      <CKEditor
        editor={ClassicEditor}
        config={{
          extraPlugins: [MyCustomUploadAdapterPlugin],
          toolbar: [
            "undo",
            "redo",
            "|",
            "heading",
            "fontSize",
            "fontColor",
            "fontBackgroundColor",
            "|",
            "bold",
            "italic",
            "|",
            "link",
            "insertTable",
            "mediaEmbed",
            "uploadImage",
            "|",
            "bulletedList",
            "numberedList",
            "indent",
            "outdent",
            "|",
            "sourceEditing",
          ],
          plugins: [
            Bold,
            Essentials,
            Heading,
            Indent,
            IndentBlock,
            Italic,
            Link,
            List,
            MediaEmbed,
            Paragraph,
            Table,
            Undo,
            FontSize,
            FontColor,
            Image,
            ImageToolbar,
            ImageCaption,
            ImageStyle,
            ImageResize,
            ImageUpload,
            GeneralHtmlSupport,
            SourceEditing,
          ],
          htmlSupport: {
            allow: [
              {
                name: /.*/, // Allow all tags
                attributes: true,
                classes: true,
                styles: true,
              },
            ],
          },
          fontSize: {
            options: [10, 12, 14, 16, 18, 20, 24, 28, 32, 36],
          },
          fontColor: {
            columns: 6,
            documentColors: 12,
          },
          image: {
            toolbar: [
              "imageStyle:inline",
              "imageStyle:block",
              "imageStyle:side",
              "imageStyle:alignLeft",
              "imageStyle:alignCenter",
              "imageStyle:alignRight",
              "|",
              "toggleImageCaption",
              "imageTextAlternative",
            ],
            styles: {
              full: {
                name: "full",
                title: "Full size image",
                icon: "full",
                isDefault: true,
              },
              alignLeft: {
                name: "alignLeft",
                title: "Left aligned image",
                icon: "left",
                className: "image-style-align-left",
              },
              alignCenter: {
                name: "alignCenter",
                title: "Center aligned image",
                icon: "center",
                className: "image-style-align-center",
              },
              alignRight: {
                name: "alignRight",
                title: "Right aligned image",
                icon: "right",
                className: "image-style-align-right",
              },
              side: {
                name: "side",
                title: "Side image",
                icon: "right",
                className: "image-style-side",
              },
            },
            resizeOptions: [
              {
                name: "resizeImage:original",
                label: "Original",
                value: null,
              },
              {
                name: "resizeImage:50",
                label: "50%",
                value: "50",
              },
              {
                name: "resizeImage:75",
                label: "75%",
                value: "75",
              },
            ],
          },
        }}
        data={description}
        onReady={(editor) => {
          setEditorInstance(editor);

          editor.editing.view.change((writer) => {
            const root = editor.editing.view.document.getRoot();
            writer.setAttribute("dir", "ltr", root);
            writer.setAttribute("lang", "en", root);
          });

          editor.editing.view.document.on("keydown", () => {
            setTimeout(() => fixCursorPosition(editor), 0);
          });

          editor.editing.view.focus();
          setTimeout(() => fixCursorPosition(editor), 100);
        }}
        onChange={(_, editor) => {
          setNewDescription(editor.getData());
          setTimeout(() => fixCursorPosition(editor), 0);
        }}
      />
      <div className="mt-2 text-sm text-gray-500">
        <p>
          <strong>Tip:</strong> To remove an image, click on it and press
          <strong> Delete</strong>. To edit HTML source code directly, click the
          <strong> Source</strong> button in the toolbar.
        </p>
      </div>
    </div>
  );
};

export default Editor;
