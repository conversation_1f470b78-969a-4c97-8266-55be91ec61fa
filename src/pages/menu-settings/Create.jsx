import React from "react";
import Modal from "@/components/ui/Modal";
import { Formik, Form } from "formik";
import InputField from "@/components/ui/InputField";
import Fileinput from "@/components/ui/Fileinput";
import Button from "@/components/ui/Button";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";
import { validationSchema } from "./formSettings";
import Switch from "@/components/ui/Switch";
const CreateMenu = () => {
  const dispatch = useDispatch();
  const { showModal } = useSelector((state) => state.commonReducer);
  const [postApi, { isLoading: isLoadingPost }] = usePostApiMutation();

  const onSubmit = async (values, { resetForm }) => {
    const formData = new FormData();
    formData.append("name", values.name);
    formData.append("is_content", 1);
    formData.append("is_footer", values.is_footer ? 1 : 0);
    if (values.icon) {
      formData.append("icon", values.icon);
    }

    const response = await postApi({
      end_point: "admin/menu-save-or-update",
      body: formData,
    });

    if (response?.error) {
      toast.error("Category Already Exists");
    } else {
      resetForm();
      dispatch(setShowModal(false));
    }
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={() => dispatch(setShowModal(false))}
      title="Add Menu"
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setShowModal(false))}
        />
      }
    >
      <Formik
      validationSchema={validationSchema}
        initialValues={{
          name: "",
          icon: null,
          is_content: 1,
          is_footer: false,
        }}
        onSubmit={onSubmit}
      >
        {({ values, setFieldValue }) => (
          <Form>
            <div className="space-y-6">
              <div className="flex w-full gap-4">
                <div className="w-full">
                  <InputField
                    label="Menu Name"
                    name="name"
                    type="text"
                    placeholder="Menu Name"
                    autoComplete="off"
                    required
                  />
                </div>
                {/* <div className="w-full">
                  <label className="block text-gray-600 text-md font-medium mb-2">
                  Menu Icon
                  </label>
                  <Fileinput
                    name="icon"
                    accept="image/*"
                    type="file"
                    label="Choose"
                    placeholder="Select Icon"
                    preview={false}
                    selectedFile={values.icon}
                    onChange={(e) =>
                      setFieldValue("icon", e.currentTarget.files[0])
                    }
                  />
                </div> */}
              </div>
              <Switch
                  label="Footer Menu"
                  activeClass="bg-success-500"
                  value={values.is_footer}
                  name="is_footer"
                  onChange={() => setFieldValue("is_footer", !values.is_footer)}
                />

                {values.is_footer && 
                  <Switch
                  label="Show in Login / Register Page"
                  activeClass="bg-success-500"
                  value={values.is_auth_footer}
                  name="is_auth_footer"
                  onChange={() => setFieldValue("is_auth_footer", !values.is_auth_footer)}
                />
                }
            </div>
            <div className="ltr:text-right rtl:text-left mt-10">
              <Button
                isLoading={isLoadingPost}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default CreateMenu;
