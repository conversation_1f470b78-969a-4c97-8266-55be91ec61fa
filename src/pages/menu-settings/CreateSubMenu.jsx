import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Button from "@/components/ui/Button";
import Textarea from "@/components/ui/Textarea";
import Switch from "@/components/ui/Switch";
import { Formik, Form } from "formik";
import { initialValues, validationSchema } from "./formSettings";
import { useDispatch } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";

const CreateSubMenu = ({ isOpen, onClose, category_id }) => {
  const [postApi, { isLoading }] = usePostApiMutation();
  const dispatch = useDispatch();

  console.log(category_id);
  const [isActive, setIsActive] = useState(false);

  const onSubmit = async (values, { resetForm }) => {
    let formData = new FormData();
    formData.append("name", values.name);
    formData.append("description", values.description);
    formData.append("is_active", isActive ? 1 : 0);
    formData.append("category_id", category_id);

    const response = await postApi({
      end_point: "admin/sub-menu",
      body: formData,
    });
    resetForm();
    onClose();
    dispatch(setShowModal(false));
  };

  return (
    <Modal
      activeModal={isOpen}
      onClose={onClose}
      title="Create Sub Menu"
      className="max-w-5xl"
      footer={<Button text="Close" btnClass="btn-primary" onClick={onClose} />}
    >
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          handleSubmit,
          setFieldValue,
          isSubmitting,
        }) => (
          <Form>
            <div className="mb-4">
              <InputField
                label="Sub Menu Name"
                name="name"
                type="text"
                placeholder="Sub Menu Name"
              />
            </div>
            <div className="grid md:grid-cols-1 mt-3">
              <>
                <label className="block text-[#1D1D1F] text-base font-medium">
                  {" "}
                  Description{" "}
                </label>
                <Textarea
                  placeholder="Enter Description"
                  name="description"
                  onChange={(e) => {
                    setFieldValue("description", e.target.value);
                  }}
                />
              </>
            </div>
            <div className="grid grid-4-md gap-4 my-5">
              <Switch
                label="Active"
                activeClass="bg-success-500"
                value={isActive}
                name="is_active"
                onChange={() => setIsActive(!isActive)}
              />
            </div>
            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default CreateSubMenu;
