import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  setEditShowModal,
  setEditData,
  setShowModal,
} from "@/features/commonSlice";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Icon from "@/components/ui/Icon";
import MenuSettingTable from "@/components/partials/common-table/menu-setting-table";
import CategorySkeleton from "@/components/partials/common-table/CategorySkeleton";
import CreateMenu from "./Create";
import EditMenu from "./Edit";
import Delete from "./Delete";
import DeleteSubCategory from "./DeleteSubCategory";
import { Link, useSearchParams } from "react-router-dom";
import Card from "@/components/ui/Card";
import CategoryModal from "../Categories/CategoryModal";
const MenuSettings = () => {
  const dispatch = useDispatch();
  const [searchParams, setSearchParams] = useSearchParams();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);
  const [showDeleteModalSubCategory, setShowDeleteModalSubCategory] = useState(false);
  const [deleteDataSubCategory, setDeleteDataSubCategory] = useState(null);
  const [apiParam, setApiParam] = useState("");
  const [filter, setFilter] = useState("");
  const [activeTab, setActiveTab] = useState("all");


  useEffect(() => {
    if (searchParams.has("footer") && searchParams.get("footer") === "1") {
      setActiveTab("footer");
    } else if (searchParams.has("custom_page") && searchParams.has("header") && searchParams.get("header") === "1") {
      setActiveTab("header");
    } else if (searchParams.has("course_category") && searchParams.get("course_category") === "1") {
      setActiveTab("category");
    } else {
      setActiveTab("all");
    }
  }, [searchParams]);


  // Build the API endpoint based on the active tab
  const getApiEndpoint = () => {
    let endpoint = "admin/menu-list";

    switch (activeTab) {
      case "footer":
        endpoint += "?footer=1";
        break;
      case "header":
        endpoint += "?custom_page=1";
        break;
      case "category":
        endpoint += "?course_category=1";
        break;
      default:
        break;
    }

    return `${endpoint}${apiParam}`;
  };

  const { data, isLoading, isFetching } = useGetApiQuery(getApiEndpoint());

  const changePage = (val) => {
    setApiParam(val);
  };

  const handleFilterChange = (searchParam) => {
    setFilter(searchParam);
    if (searchParam.includes('?search=')) {
      const formattedSearch = searchParam.replace('?search=', '&search=');
      setApiParam(formattedSearch);
    } else {
      setApiParam(searchParam);
    }
  };

  const columns = [
    { label: "Name", field: "name" },
    { label: "Action", field: "" },
  ];

  const tableData = data?.data?.map((item) => ({
    id: item.id,
    name: (
      <div className="flex items-start gap-4">
        {item.icon && (
          <img
            src={`${import.meta.env.VITE_ASSET_HOST_URL}${item.icon}`}
            alt="icon"
            className="w-12 h-12 border rounded-full"
          />
        )}
        <div>
          <div className="">
            <h4 className="text-lg font-semibold text-gray-700">
              <Link to={`/course-category-details/${item.id}`}>
                {item.name}
              </Link>

              {item.courses_count > 0 && <small className="ml-4">(Total Course:  {item.courses_count})</small>}
            </h4>
          </div>{item.sub_categories.length > 0 && (
            <>
              <p className="text-gray-500 mt-2 font-medium">Sub Categories:</p>
              <div className="flex flex-wrap gap-3 mt-3">
                {item.sub_categories.map((subCategory, index) => (
                  <div
                    key={index}
                    className={`flex items-center justify-between px-4 py-2 rounded-lg border shadow-sm w-full md:w-auto ${subCategory.is_active
                      ? "bg-green-50 border-green-400 text-green-700"
                      : "bg-red-50 border-red-400 text-red-700"
                      }`}
                  >
                    <span className="flex items-center gap-2">
                      {subCategory.name}
                      {subCategory.courses_count > 0 && (
                        <span className="text-sm font-medium text-gray-600">
                          (Courses: {subCategory.courses_count})
                        </span>
                      )}
                    </span>

                    {subCategory.courses_count === 0 && (
                      <button
                        className="ml-auto text-red-600 hover:bg-red-200 rounded p-1 transition"
                        onClick={() => {
                          setDeleteDataSubCategory(subCategory);
                          setShowDeleteModalSubCategory(true);
                        }}
                      >
                        <Icon
                          icon="mdi:trash-can-outline"
                          className="w-5 h-5"
                          aria-label="Delete"
                        />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </>
          )}

        </div>
      </div>
    ),
    course_count: item.courses_count,

  }));

  const actions = [
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        dispatch(setEditData(data.data[val]));
        if (activeTab === "category") {
          dispatch(setShowModal(true));
        } else {
          data.data[val].id
            ? dispatch(setEditShowModal(true))
            : dispatch(setShowModal(true));
        }
      },
    },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  const handleTabChange = (tab) => {
    switch (tab) {
      case "footer":
        setSearchParams({ footer: "1" });
        break;
      case "header":
        setSearchParams({ custom_page: "1", header: "1" });
        break;
      case "category":
        setSearchParams({ course_category: "1" });
        break;
      default:
        setSearchParams({});
        break;
    }
    setActiveTab(tab);
    setApiParam("");
    setFilter("");
  };

  // Get the appropriate title based on active tab
  const getTabTitle = () => {
    switch (activeTab) {
      case "footer":
        return "Footer Menu Settings";
      case "header":
        return "Header Menu Settings";
      case "category":
        return "Course Category Menu Settings";
      default:
        return "All Menu Settings";
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <div className="border-b border-gray-200">
          <ul className="flex flex-wrap -mb-px text-sm font-medium text-center">
            <li className="mr-2">
              <button
                className={`inline-block p-4 rounded-t-lg ${activeTab === "all"
                  ? "text-blue-600 border-b-2 border-blue-600 active"
                  : "text-gray-500 hover:text-gray-600 hover:border-gray-300 border-b-2 border-transparent"
                  }`}
                onClick={() => handleTabChange("all")}
              >
                All
              </button>
            </li>
            <li className="mr-2">
              <button
                className={`inline-block p-4 rounded-t-lg ${activeTab === "header"
                  ? "text-blue-600 border-b-2 border-blue-600 active"
                  : "text-gray-500 hover:text-gray-600 hover:border-gray-300 border-b-2 border-transparent"
                  }`}
                onClick={() => handleTabChange("header")}
              >
                Header Menu
              </button>
            </li>
            <li className="mr-2">
              <button
                className={`inline-block p-4 rounded-t-lg ${activeTab === "footer"
                  ? "text-blue-600 border-b-2 border-blue-600 active"
                  : "text-gray-500 hover:text-gray-600 hover:border-gray-300 border-b-2 border-transparent"
                  }`}
                onClick={() => handleTabChange("footer")}
              >
                Footer Menu
              </button>
            </li>
            <li>
              <button
                className={`inline-block p-4 rounded-t-lg ${activeTab === "category"
                  ? "text-blue-600 border-b-2 border-blue-600 active"
                  : "text-gray-500 hover:text-gray-600 hover:border-gray-300 border-b-2 border-transparent"
                  }`}
                onClick={() => handleTabChange("category")}
              >
                Course Category As Menu
              </button>
            </li>
          </ul>
        </div>
      </Card>

      {isLoading || isFetching ? (
        <CategorySkeleton />
      ) : (
        <MenuSettingTable
          loading={false}
          title={getTabTitle()}
          createButton={activeTab == "category" ? "Create Course Category" : "Create Menu"}
          actions={actions}
          columns={columns}
          data={tableData}
          changePage={changePage}
          filter={filter}
          setFilter={handleFilterChange}
          currentPage={data?.current_page}
          totalPages={Math.ceil(data?.total / data?.per_page)}
          enableDragDrop={activeTab === "all"}
        />
      )}

      <Delete
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />
      <DeleteSubCategory
        showDeleteModal={showDeleteModalSubCategory}
        setShowDeleteModal={setShowDeleteModalSubCategory}
        data={deleteDataSubCategory}
      />
      {
        activeTab === "category" ? <CategoryModal /> : <>
          <CreateMenu />
          <EditMenu /></>}
    </div>
  );
};

export default MenuSettings;
