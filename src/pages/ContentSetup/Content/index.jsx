import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetContentListQuery } from "@/store/api/master/contentSetupContentListSlice";
import CreateContent from "./creteContent";
import EditContent from "./editContent";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

const index = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [showModal, setShowModal] = useState(false);

  const res = useGetContentListQuery();
  const data = res.data;
  const columns = [
    {
      label: "Content Name",
      field: "title",
    },
    {
      label: "Menu/Category",
      field: "category_name",
    },
    {
      label: "From",
      field: "appeared_from",
    },
    {
      label: "To",
      field: "appeared_to",
    },
    {
      label: "Free",
      field: "is_free",
    },
    {
      label: "Status",
      field: "is_active",
    },
    {
      label: "Action",
      field: "",
    },
  ];
  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      title: (
        <>
          {"EN : "}
          {item.title} <br /> {"BN : "}
          {item.title_bn}
        </>
      ),
      category_name: item.category_name,
      appeared_from: item.appeared_from.split(" ")[0],
      appeared_to: item.appeared_to.split(" ")[0],
      is_free: (
        <Badge
          className={
            item.is_free
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {item.is_free ? "YES" : "NO"}{" "}
        </Badge>
      ),
      is_active: (
        <Badge
          className={
            item.is_active
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {item.is_active ? "YES" : "NO"}
        </Badge>
      ),
    };
  });

  const actions = [
    {
      name: "edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        dispatch(setEditData(data.data[val]));
        dispatch(setEditShowModal(true));
      },
    },
    {
      name: "Subject",
      icon: "uis:subject",
      // onClick: (val) => {
      //   console.log(val);
      // },
      onClick: (val) => {
        navigate("/content-subject/" + tableData[val].id);
      },
    },
  ];

  const changePage = (item) => {
    console.log(item);
  };

  const handleSubmit = () => {
    setShowModal(false);
  };
  const createPage = <CreateContent />;
  const editPage = <EditContent />;

  return (
    <div>
      <BasicTablePage
        title="Content List"
        createButton="Add New Content"
        createPage={createPage}
        editPage={editPage}
        actions={actions}
        columns={columns}
        data={tableData}
        submitForm={handleSubmit}
        changePage={changePage}
        currentPage={data?.current_page}
        totalPages={Math.ceil(data?.total / data?.per_page)}
      />
    </div>
  );
};

export default index;
