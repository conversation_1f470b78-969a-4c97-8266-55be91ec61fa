import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import Checkbox from "@/components/ui/Checkbox";
import Button from "@/components/ui/Button";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import Select from "@/components/ui/Select";
import { Formik, Form, Field } from "formik";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { initialValues, validationSchema } from "./formSettings";
import { useDispatch } from "react-redux";
import { useGetClassListQuery } from "@/store/api/master/rowContentClassListSlice";
import { useGetSubjectChapterListQuery } from "@/store/api/master/rowContentChapterListSlice";
import { useParams } from "react-router-dom";

const createSubject = ({ showModal, setShowModal, quiz }) => {
  const dispatch = useDispatch();

  const { id } = useParams();
  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();
  const [classLevelId, setClassLevelId] = useState(null);

  const [isActive, setIsActive] = useState(false);

  const classList = useGetClassListQuery({
    pagination: false,
  })?.data;
  // console.log(classList);
  const subjectList = useGetSubjectChapterListQuery(classLevelId)?.data;

  const onSubmit = async (values, { resetForm }) => {
    let list = [];
    values.is_active = isActive;
    list.push(values);
    let obj = list;
    const response = await postApi({
      end_point: "admin/content-subject-assign-save-or-update",
      body: { content_id: id, subjectArr: obj },
    });
    setShowModal(false);
  };
  return (
    <Modal
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      title="Add New Subject"
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowModal(false)}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={initialValues}
        onSubmit={onSubmit}
      >
        {({ values, errors, touched, setFieldValue }) => (
          <Form>
            <>
              <div className="grid md:grid-cols-3 gap-4 border-b p-5 border-slate-200 dark:border-slate-700">
                <>
                  <Select
                    defaultValue=""
                    label="Class"
                    placeholder="Select Class"
                    options={classList?.map((item) => {
                      return { label: item.name, value: item.id };
                    })}
                    name="class_level_id"
                    onChange={(e) => {
                      setFieldValue("class_level_id", e.target.value);
                      setClassLevelId(e.target.value);
                    }}
                  />
                </>
                <>
                  <Select
                    defaultValue=""
                    label="Subject"
                    placeholder="Select Subject"
                    options={subjectList?.map((item) => {
                      return { label: item.name, value: item.id };
                    })}
                    name="subject_id"
                    onChange={(e) => {
                      setFieldValue("subject_id", e.target.value);
                    }}
                  />
                </>
                <div className="flex items-center justify-center mt-5">
                  <Checkbox
                    name="is_active"
                    label="Active"
                    value={values.is_active}
                    onChange={(e) =>
                      setFieldValue("is_active", e.target.checked)
                    }
                  />
                </div>
              </div>
            </>

            <div className="ltr:text-right rtl:text-left mt-5">
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default createSubject;
