import React, { useState, useEffect } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import FileInput from "@/components/ui/Fileinput";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { Formik, Form, ErrorMessage } from "formik";
import { initialValues, validationSchema } from "./formClass";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import { useClassCreateOrUpdateMutation } from "@/store/api/master/rowContentClassListSlice";
import Select from "react-select";
import { useGetApiQuery, usePostApiMutation } from "@/store/api/master/commonSlice";
import DateTimePicker from "@/components/ui/DateTimePicker";

const CreateClass = ({ selectedDate }) => {

  const dispatch = useDispatch();
  const { showModal } = useSelector((state) => state.commonReducer);
  const [courseId, setCourseId] = useState(null);
  const [isPhysical, setIsPhysical] = useState(true);
  const [mentorList, setMentorList] = useState([]);
  const [classCreateOrUpdate, { isLoading }] = useClassCreateOrUpdateMutation();

  const { data: courseList, isLoading: courseLoading } = useGetApiQuery(
    `admin/course-list-for-filter`
  );

  const { data: batches, isLoading: batchLoading } = useGetApiQuery(
    `admin/batches?pagination=false&is_active=1&mentor=1${courseId ? `&course_id=${courseId}` : ""}`
  );


  const { data: couseMentorList, isLoading: couseMentorLoading } = useGetApiQuery(`admin/course-mentor-assign-list/${courseId}`);




  const [postApi, { isLoading: isPostLoading }] = usePostApiMutation();
  const onSubmit = async (values, { resetForm }) => {
    
    const date = new Date(values.schedule_datetime);
    date.setHours(date.getHours() + 6);
    values.schedule_datetime = date.toISOString().slice(0, 19).replace('T', ' ');

    const formData = new FormData();
    Object.keys(values).forEach((key) => {
      formData.append(key, values[key]);
      if (key === "is_physical") {
        formData.append(key, values[key] ? 1 : 0);
      }
    });
    const response = await postApi({
      end_point: `admin/create-live-class-schedule`,
      body: formData,
    });
    
    if (response?.error) {
      setErrors(response.error?.data.errors);
    } else {
      resetForm();
      dispatch(setShowModal(false));
    }
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={() => dispatch(setShowModal(false))}
      title="Add New Class"
      className="max-w-4xl p-6"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setShowModal(false))}
        />
      }
    >
      {selectedDate && 
      <Formik
        validationSchema={validationSchema}
        initialValues={{
          ...initialValues,
          schedule_datetime: selectedDate,
        }} 
        onSubmit={onSubmit}
      >
        {({ values, setFieldValue }) => (
          <Form className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <InputField
                label="Class Title"
                name="title"
                type="text"
                placeholder="Class Name"
                required
              />
              <div>
                <label className="block text-gray-600 text-sm font-medium mb-2">
                  Course<span className="text-red-500"> *</span>
                </label>
                <Select
                  placeholder="Select Course"
                  options={
                    courseList?.map((course) => ({
                      value: course.id,
                      label: course.title,
                    }))
                  }
                  name="course_id"
                  onChange={(e) => {
                    setCourseId(e.value);
                    setFieldValue("course_id", e.value);
                  }}
                />
                <ErrorMessage
                  name="course_id"
                  component="div"
                  className="text-red-500 text-xs mt-1"
                />
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-gray-600 text-sm font-medium mb-2">
                  Batch   <small className="text-red-400"> If no batch is selected, all enrolled students will be included.</small>
                  {/* <span className="text-red-500"> *</span> */}
                </label>
                <Select
                  isDisabled={batchLoading}
                  placeholder="Select Batch"
                  options={
                    batches?.map((batch) => ({
                      value: batch.id,
                      label: batch.name,
                    }))
                  }
                  name="batch_id"
                  onChange={(e) => {
                    setFieldValue("batch_id", e.value);
                    setMentorList(
                      batches?.find((batch) => batch.id === e.value)?.mentors
                    );
                  }}
                />
                <ErrorMessage
                  name="batch_id"
                  component="div"
                  className="text-red-500 text-xs mt-1"
                />
              </div>

              <div>
                <label className="block text-gray-600 text-sm font-medium mb-2">
                  Select Mentor<span className="text-red-500"> *</span>
                </label>
                <Select
                  placeholder="Select Mentor"
                  options={
                    mentorList?.length > 0
                      ? mentorList?.map((mentor) => ({
                          value: mentor.mentor_id,
                          label: mentor.name,
                        }))
                      : couseMentorList?.map((mentor) => ({
                          value: mentor.mentor_id,
                          label: mentor.mentor_name,
                        }))
                  }
                  name="mentor_id"
                  onChange={(e) => setFieldValue("mentor_id", e.value)}
                />
                <ErrorMessage
                  name="mentor_id"
                  component="div"
                  className="text-red-500 text-xs mt-1"
                />
              </div>

              <InputField
                label="Duration"
                name="duration"
                type="text"
                placeholder="Class Duration (e.g., 1hr 30min)"
                required
              />
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <DateTimePicker
                time={1}
                label="Class Date & Time"
                placeholder="Select Date & Time"
                format="YYYY-MM-DD HH:mm"
                name="schedule_datetime"
                value={values.schedule_datetime}
                onChange={(e) => setFieldValue("schedule_datetime", e[0])}
                required
              />
              <div className="mt-8">
                <Switch
                  label={`Class Mode: ${values.is_physical ? "Physical" : "Live Class"}`}
                  activeClass="bg-success-500"
                  name="is_physical"
                  value={values.is_physical}
                  onChange={() => setFieldValue("is_physical", !values.is_physical)}
                />
              </div>
            </div>

            {!values.is_physical && (
              <InputField
                label="Class URL"
                name="class_url"
                type="text"
                placeholder="Enter Class URL"
              />
            )}

            <div className="flex justify-end">
              <Button
                isLoading={isPostLoading}
                type="submit"
                className="btn btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
      }
    </Modal>
  );
};

export default CreateClass;

