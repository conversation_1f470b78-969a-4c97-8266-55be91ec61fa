import React, { useState, useMemo, useEffect } from "react";
import { Calendar, momentLocalizer } from "react-big-calendar";
import moment from "moment";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import CreateClass from "./createClass";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import Attendance from "./Attendance";
import Select from "react-select";

const localizer = momentLocalizer(moment);

const ClassList = () => {
  const dispatch = useDispatch();
  const [isAttendancePage, setIsAttendancePage] = useState(false);
  const { showModal } = useSelector((state) => state.commonReducer);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [eventData, setEventData] = useState(null);
  const [contextMenu, setContextMenu] = useState(null);
  const [queryParam, setQueryParam] = useState("");
  const [selectedCourseId, setSelectedCourseId] = useState("");
  const [currentDateRange, setCurrentDateRange] = useState({
    startDate: moment().startOf("month").format("YYYY-MM-DD"),
    endDate: moment().endOf("month").format("YYYY-MM-DD")
  });

  // Build query string
  const buildQueryParams = (startDate, endDate, courseId = selectedCourseId) => {
    const params = new URLSearchParams();
    if (startDate) params.append("start_date", startDate);
    if (endDate) params.append("end_date", endDate);
    if (courseId) params.append("course_id", courseId);
    return params.toString() ? `?${params.toString()}` : "";
  };

  // Set default query on initial load or course change
  useEffect(() => {
    // Use current date range if available, otherwise use current month
    const initialQueryParam = buildQueryParams(currentDateRange.startDate, currentDateRange.endDate);
    setQueryParam(initialQueryParam);
  }, [selectedCourseId, currentDateRange.startDate, currentDateRange.endDate]);

  // Memoized query key
  const apiKey = useMemo(() => `admin/class-schedules${queryParam}`, [queryParam]);
  const {
    data: res,
    isLoading,
    isError,
    error,
    refetch,
  } = useGetApiQuery(apiKey, {
    skip: !queryParam,
  });

  // Refetch when query changes
  useEffect(() => {
    if (queryParam) {
      refetch();
    }
  }, [queryParam, refetch]);

  // Course list
  const { data: coursesData } = useGetApiQuery("admin/course-list-for-filter");

  // Calendar events
  const events = useMemo(() => {
    return res?.map((schedule) => ({
      id: schedule.id,
      title: `${schedule.title} - ${schedule.mentor_name}`,
      start: new Date(schedule.schedule_datetime),
      end: new Date(
        new Date(schedule.schedule_datetime).getTime() + schedule.duration * 60000
      ),
      resource: schedule,
    }));
  }, [res]);

  const handleSlotSelect = (slotInfo) => {
    setSelectedDate(slotInfo.start);
    dispatch(setShowModal(true));
  };

  const closeContextMenu = () => {
    setContextMenu(null);
  };

  const handleCreateClass = () => {
    dispatch(setShowModal(true));
    closeContextMenu();
  };

  const handleEventSelect = (event) => {
    setEventData(res.find((item) => item.id === event.id));
    setIsAttendancePage(true);
  };

  const handleCourseChange = (selectedOption) => {
    setSelectedCourseId(selectedOption ? selectedOption.value : "");
  };

  const courseOptions = useMemo(() => {
    if (!coursesData) return [];
    return [
      { value: "", label: "All Courses" },
      ...coursesData.map((course) => ({
        value: course.id.toString(),
        label: course.title || course.name,
      })),
    ];
  }, [coursesData]);

  // When calendar view range changes
  const handleRangeChange = (range, view) => {
    let start, end;

    if (view === "month" && Array.isArray(range)) {
      start = moment(range[0]).startOf("month").format("YYYY-MM-DD");
      end = moment(range[range.length - 1]).endOf("month").format("YYYY-MM-DD");
    } else if (view === "week" && Array.isArray(range)) {
      start = moment(range[0]).format("YYYY-MM-DD");
      end = moment(range[range.length - 1]).format("YYYY-MM-DD");
    } else if (view === "day" && range instanceof Date) {
      start = moment(range).format("YYYY-MM-DD");
      end = moment(range).format("YYYY-MM-DD");
    } else {
      // Fallback for other cases
      start = moment(range.start || range[0]).startOf("month").format("YYYY-MM-DD");
      end = moment(range.end || range[range.length - 1]).endOf("month").format("YYYY-MM-DD");
    }

    // Update current date range state
    setCurrentDateRange({ startDate: start, endDate: end });

    // Update query parameters
    const newQueryParam = buildQueryParams(start, end);
    setQueryParam(newQueryParam);
  };

  return isAttendancePage ? (
    <Attendance eventData={eventData} setIsAttendancePage={setIsAttendancePage} />
  ) : (
    <div style={{ margin: "20px" }}>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-semibold">Class List for Attendance</h2>

        {/* Course Filter Dropdown */}
        <div className="w-64">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
            Filter by Course
          </label>
          <Select
            options={courseOptions}
            value={courseOptions.find(option => option.value === selectedCourseId)}
            onChange={handleCourseChange}
            placeholder="Search and select a course..."
            isSearchable={true}
            isClearable={true}
            className="react-select"
            classNamePrefix="select"
            styles={{
              control: (base) => ({
                ...base,
                minHeight: '38px',
                borderColor: '#d1d5db',
                '&:hover': {
                  borderColor: '#9ca3af'
                }
              }),
              menu: (base) => ({
                ...base,
                zIndex: 9999
              })
            }}
          />
        </div>
      </div>

      {isLoading && <div>Loading...</div>}
      {isError && <div>Error: {error.message}</div>}

      {!isLoading && !isError && (
        <div style={{ height: "800px" }} onClick={closeContextMenu}>
          <Calendar
            localizer={localizer}
            events={events}
            startAccessor="start"
            endAccessor="end"
            style={{ height: 800 }}
            selectable
            onSelectSlot={handleSlotSelect}
            onSelectEvent={handleEventSelect}
            views={["month", "week", "day"]}
            defaultView="month"
            defaultDate={new Date()}
            dayLayoutAlgorithm="no-overlap"
            onRangeChange={handleRangeChange}
          />
        </div>
      )}

      {showModal && <CreateClass selectedDate={selectedDate} />}
    </div>
  );
};

export default ClassList;
