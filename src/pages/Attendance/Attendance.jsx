import React, { useState, useEffect } from "react";
import { useGetApiQuery, usePostApiMutation } from "@/store/api/master/commonSlice";

const Attendance = ({ eventData, setIsAttendancePage }) => {
  const [attendance, setAttendance] = useState({});
  const [postApi, { isLoading }] = usePostApiMutation();

  const { data: students, isLoading: studentsLoading } = useGetApiQuery(
    `admin/student-list-for-attendance?class_schedule_id=${eventData.id}&batch_id=${eventData.batch_id}`
  );

  useEffect(() => {
    if (students) {
      const initialAttendance = students.reduce((acc, student) => {
        if (student.attendance_id !== null && student.is_present) {
          acc[student.id] = true;
        }
        return acc;
      }, {});
      setAttendance(initialAttendance);
    }
  }, [students]);

  const handleAttendanceChange = (studentId) => {
    setAttendance((prevAttendance) => ({
      ...prevAttendance,
      [studentId]: !prevAttendance[studentId],
    }));
  };

  const handleSubmit = async () => {
    const attendanceData = {
      course_id: eventData?.course_id,
      batch_id: eventData?.batch_id,
      class_schedule_id: eventData?.id,
      attendance_date: new Date(eventData?.schedule_datetime).toISOString().split("T")[0],
      students: students.map((student) => ({
        id: student.id,
        is_present: !!attendance[student.id],
      })),
    };

    const response = await postApi({
      end_point: "admin/save-attendance",
      body: attendanceData,
    });

    if (response?.error) {
      alert("Error submitting attendance.");
      console.error(response.error);
    } else {
      setIsAttendancePage(false);
    }
  };

  return (
    <div className="px-6 py-4">
      {/* Header Section */}
      <div className="bg-white shadow-md rounded-lg p-6 mb-6">
        <button
          className="text-blue-600 hover:text-blue-800 font-semibold mb-4"
          onClick={() => setIsAttendancePage(false)}
        >
          &larr; Back
        </button>
        <div className="flex flex-col items-start space-y-2">
          <h2 className="text-xl font-semibold text-gray-700">{eventData?.title}</h2>
          <h1 className="text-sm font-bold text-gray-800">{eventData?.course}</h1>
          <h3 className="text-sm font-medium text-gray-600">{eventData?.batch_name}</h3>
          <h4 className="text-xl font-medium">
            Mentor: <span className="font-bold">{eventData?.mentor_name}</span>
          </h4>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-white shadow-md rounded-lg p-6">
        {students?.length > 0 && (
          <div>
            <h2 className="text-xl font-bold mb-4">Student Attendance</h2>

            {studentsLoading ? (
              <p>Loading students...</p>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {students.map((student) => (
                  <div
                    key={student.id}
                    className="border rounded-lg p-4 bg-gray-50 shadow-sm hover:shadow-md transition duration-150 cursor-pointer"
                    onClick={() => handleAttendanceChange(student.id)}
                  >
                    <div className="flex items-center space-x-4">
                      {student.image ? (
                        <img
                          src={`${import.meta.env.VITE_ASSET_HOST_URL}${student.image}`}
                          alt={student.name}
                          className="w-12 h-12 rounded-full border object-cover"
                        />
                      ) : (
                        <div className="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center text-gray-500 font-bold">
                          {student.name[0]}
                        </div>
                      )}
                      <div className="flex-1">
                        <span className="text-md font-medium text-gray-800">{student.name}</span>
                        <p className="text-sm text-gray-500">
                          {student.student_id || "No ID Available"}
                        </p>
                      </div>
                      <div>
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            aria-label={`Mark ${student.name} as present`}
                            className="form-checkbox bg-blue-500 rounded-full h-6 w-6 border-2 border-transparent focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            checked={!!attendance[student.id]}
                            readOnly
                          />
                          <span className="text-md font-medium text-blue-500">Present</span>
                        </label>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            <button
              onClick={handleSubmit}
              className="mt-6 bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition duration-150"
              disabled={isLoading}
            >
              {isLoading ? "Submitting..." : "Submit"}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Attendance;
