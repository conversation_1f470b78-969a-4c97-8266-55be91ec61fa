import * as yup from "yup";


export const initialValues = {
  title: "",
  course_id: "",
  batch_id: "",
  is_physical: true,
  schedule_datetime: "",
  duration: "40",
  class_url: "",
};


export const validationSchema = yup.object({
  title: yup.string().required("Title is required"),
  course_id: yup.string().required("Please select a course"),
  batch_id: yup.string().nullable(),
  schedule_datetime: yup
    .string()
    .required("Schedule date and time are required"),
  duration: yup.string().required("Duration is required"),
  class_url: yup
    .string()
    .url("Class URL must be a valid URL"),
});
