import React, { useState } from "react";
import { useDispatch } from "react-redux";
import {
  setEditData,
  setShowModal,
} from "@/features/commonSlice";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Icon from "@/components/ui/Icon";
import MenuSettingTable from "@/components/partials/common-table/menu-setting-table";
import CategorySkeleton from "@/components/partials/common-table/CategorySkeleton";
import CategoryModal from "./CategoryModal";
import Delete from "./Delete";
import DeleteSubCategory from "./DeleteSubCategory";
import { Link } from "react-router-dom";

const Categories = () => {
  const dispatch = useDispatch();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);
  const [showDeleteModalSubCategory, setShowDeleteModalSubCategory] = useState(false);
  const [deleteDataSubCategory, setDeleteDataSubCategory] = useState(null);
  const [apiParam, setApiParam] = useState("");
  const [filter, setFilter] = useState("");

  const { data, isLoading, isFetching } = useGetApiQuery(
    `admin/menu-list?course_category=1${apiParam}`
  );

  const changePage = (val) => {
    setApiParam(val);
  };

  const handleFilterChange = (searchParam) => {
    setFilter(searchParam);
    if (searchParam.includes('?search=')) {
      const formattedSearch = searchParam.replace('?search=', '&search=');
      setApiParam(formattedSearch);
    } else {
      setApiParam(searchParam);
    }
  };

  const columns = [
    { label: "Name", field: "name" },
    { label: "Action", field: "" },
  ];

  const tableData = data?.data?.map((item) => ({
    id: item.id,
    name: (
      <div className="flex items-start gap-4">
        {item.icon && (
          <img
            src={`${import.meta.env.VITE_ASSET_HOST_URL}${item.icon}`}
            alt="icon"
            className="w-12 h-12 border rounded-full"
          />
        )}
        <div>
          <div className="">
            <h4 className="text-lg font-semibold text-gray-700">
              <Link to={`/course-category-details/${item.id}`}>
              {item.name}
              </Link>

              {item.courses_count > 0 && <small className="ml-4">(Total Course:  {item.courses_count})</small>}
            </h4>
          </div>{item.sub_categories.length > 0 && (
            <>
              <p className="text-gray-500 mt-2 font-medium">Sub Categories:</p>
              <div className="flex flex-wrap gap-3 mt-3">
                {item.sub_categories.map((subCategory, index) => (
                  <div
                    key={index}
                    className={`flex items-center justify-between px-4 py-2 rounded-lg border shadow-sm w-full md:w-auto ${subCategory.is_active
                        ? "bg-green-50 border-green-400 text-green-700"
                        : "bg-red-50 border-red-400 text-red-700"
                      }`}
                  >
                    <span className="flex items-center gap-2">
                      {subCategory.name}
                      {subCategory.courses_count > 0 && (
                        <span className="text-sm font-medium text-gray-600">
                          (Courses: {subCategory.courses_count})
                        </span>
                      )}
                    </span>

                    {subCategory.courses_count === 0 && (
                      <button
                        className="ml-auto text-red-600 hover:bg-red-200 rounded p-1 transition"
                        onClick={() => {
                          setDeleteDataSubCategory(subCategory);
                          setShowDeleteModalSubCategory(true);
                        }}
                      >
                        <Icon
                          icon="mdi:trash-can-outline"
                          className="w-5 h-5"
                          aria-label="Delete"
                        />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </>
          )}

        </div>
      </div>
    ),
    course_count: item.courses_count,
  }));

  const actions = [
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        dispatch(setEditData(data.data[val]));
        dispatch(setShowModal(true));
      },
    },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  return (
    <div className="space-y-6">
      {isLoading || isFetching ? (
        <CategorySkeleton />
      ) : (
        <MenuSettingTable
          loading={false}
          title="Category Settings"
          createButton="Create Category"
          actions={actions}
          columns={columns}
          data={tableData}
          changePage={changePage}
          filter={filter}
          setFilter={handleFilterChange}
          currentPage={data?.current_page}
          totalPages={Math.ceil(data?.total / data?.per_page)}
        />
      )}

      <Delete
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />
      <DeleteSubCategory
        showDeleteModal={showDeleteModalSubCategory}
        setShowDeleteModal={setShowDeleteModalSubCategory}
        data={deleteDataSubCategory}
      />
      <CategoryModal />
    </div>
  );
};

export default Categories;
