import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { useDeleteApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";

const DeleteSubCategory = ({ showDeleteModal, setShowDeleteModal, data }) => {
  console.log("DeleteSubCategory data", data);
  const [deleteApi, { isLoading }] = useDeleteApiMutation();

  const handleDelete = async () => {
    try {
      const response = await deleteApi({
        end_point: `admin/sub-menu/${data?.id}`,
        body: {}
      });
    } catch (error) {
      console.error("Error deleting sub-category:", error);
      toast.error("Failed to delete sub-category");
    }
  };

  return (
    <Modal
      activeModal={showDeleteModal}
      onClose={() => setShowDeleteModal(false)}
      title="Delete Sub-Category"
      className={"max-w-4xl"}
      footerContent={
        <div className="flex items-center gap-3">
          <Button
            text="Cancel"
            btnClass="btn-outline-dark"
            onClick={() => setShowDeleteModal(false)}
          />
          <Button
            text="Delete"
            btnClass="btn-danger"
            onClick={handleDelete}
            isLoading={isLoading}
          />
        </div>
      }
    >
      <div className="text-center py-5">
        <div className="text-red-500 mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="60"
            height="60"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="1"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mx-auto"
          >
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
            <line x1="12" y1="9" x2="12" y2="13"></line>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </svg>
        </div>
        <div className="max-w-sm mx-auto">
          <h2 className="text-xl font-medium mb-3">Are you sure?</h2>
          <p className="mb-6 text-sm text-slate-600">
            Do you really want to delete this sub-category? This action cannot be undone.
            {data?.courses_count > 0 && (
              <span className="block mt-2 text-red-500 font-medium">
                Warning: This sub-category has {data.courses_count} courses associated with it.
              </span>
            )}
          </p>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteSubCategory;
