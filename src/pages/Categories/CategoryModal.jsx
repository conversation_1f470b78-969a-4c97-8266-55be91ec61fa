import React, { useEffect, useRef, useState } from "react";
import Modal from "@/components/ui/Modal";
import { Formik, Form, FieldArray } from "formik";
import InputField from "@/components/ui/InputField";
import Fileinput from "@/components/ui/Fileinput";
import Button from "@/components/ui/Button";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";
import { validationSchema, initialValues } from "./formSettings";
import Icon from "@/components/ui/Icon";

const CategoryModal = () => {
  const dispatch = useDispatch();
  const { showModal, editData } = useSelector((state) => state.commonReducer);
  const [postApi, { isLoading: isLoadingPost }] = usePostApiMutation();
  const [formSubmit, setFormSubmit] = useState(false);
  const [addSubCategory, setAddSubCategory] = useState(false);
  const [newSubCategory, setNewSubCategory] = useState("");
  const newSubCategoryInputRef = useRef(null);

  // Determine if we're in edit mode
  const isEditMode = !!editData;

  useEffect(() => {
    if (addSubCategory && newSubCategoryInputRef.current) {
      newSubCategoryInputRef.current.focus();
    }
  }, [addSubCategory]);

  const onSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    // If editing, include the ID
    if (isEditMode) {
      formData.append("id", values.id);
    }

    formData.append("name", values.name);
    formData.append("is_content", values.is_content ? 1 : 0);

    if (values.icon) {
      formData.append("icon", values.icon);
    }

    values.sub_categories.forEach((sub, index) => {
      if (sub.id) {
        formData.append(`sub_categories[${index}][id]`, sub.id);
      }
      formData.append(`sub_categories[${index}][name]`, sub.name);
      formData.append(`sub_categories[${index}][icon]`, sub.icon || "");
    });

    try {
      const response = await postApi({
        end_point: "admin/menu-save-or-update",
        body: formData,
      });

      if (response?.error) {
        // toast.error("Error While Creating Category");
      } else {
        console.error(isEditMode
          ? "Category updated successfully"
          : "Category created successfully"
        );
        resetForm();
        dispatch(setShowModal(false));
      }
    } catch (error) {
      console.error("Error saving category:", error);
      // toast.error("Failed to save category");
    }
  };

  // Get initial values based on edit data if available
  const getInitialValues = () => {
    if (isEditMode) {
      return {
        id: editData.id,
        name: editData.name || "",
        icon: editData.icon || null,
        is_content: editData.is_content || false,
        sub_categories: editData.sub_categories || [],
      };
    }
    return initialValues;
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={() => dispatch(setShowModal(false))}
      title={isEditMode ? "Edit Category" : "Add Category"}
      className="max-w-5xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setShowModal(false))}
        />
      }
    >
      <Formik
        validationSchema={validationSchema}
        initialValues={getInitialValues()}
        enableReinitialize={true}
        onSubmit={onSubmit}
      >
        {({ values, setFieldValue }) => (
          <Form>
            <div className="space-y-6">
              <div className="flex w-full gap-4">
                <div className="w-full">
                  <InputField
                    label="Category Name"
                    name="name"
                    type="text"
                    placeholder="Category Name"
                    autoComplete="off"
                    required
                  />
                </div>
                <div className="w-full">
                  <label className="block text-gray-600 text-md font-medium mb-2">
                    Category Icon
                  </label>
                  <Fileinput
                    name="icon"
                    accept="image/*"
                    type="file"
                    label="Choose"
                    placeholder="Select Icon"
                    preview={true}
                    selectedFile={values.icon}
                    onChange={(e) =>
                      setFieldValue("icon", e.currentTarget.files[0])
                    }
                  />
                </div>
              </div>
              {/* <Switch
                label="Footer Menu"
                activeClass="bg-success-500"
                value={values.is_content}
                name="is_content"
                onChange={() => setFieldValue("is_content", !values.is_content)}
              />  */}
              {!values.is_content && (
                <div className="mt-6">
                  <h3 className="text-base font-medium mb-3">Sub Categories</h3>
                  <FieldArray
                    name="sub_categories"
                    render={(arrayHelpers) => (
                      <>
                        <div className="space-y-3">
                          {values.sub_categories.map((sub, index) => (
                            <div
                              key={index}
                              className="flex items-center gap-3 border border-gray-200 rounded-lg p-3"
                            >
                              <div className="flex-1">
                                <InputField
                                  name={`sub_categories.${index}.name`}
                                  type="text"
                                  placeholder="Sub Category Name"
                                  value={sub.name}
                                  onChange={(e) =>
                                    setFieldValue(
                                      `sub_categories.${index}.name`,
                                      e.target.value
                                    )
                                  }
                                  className="bg-white"
                                  autoComplete="off"
                                />
                              </div>
                              <button
                                type="button"
                                className="text-red-500 hover:text-red-700"
                                onClick={() => arrayHelpers.remove(index)}
                              >
                                <Icon icon="heroicons-outline:trash" className="text-xl" />
                              </button>
                            </div>
                          ))}
                        </div>

                        <div className="mt-4">
                          {!addSubCategory && (
                            <button
                              type="button"
                              onClick={() => setAddSubCategory(true)}
                              className="border border-indigo-400 text-gray-500 rounded-lg p-2 py-1"
                            >
                              + Create New
                            </button>
                          )}
                        </div>

                        {addSubCategory && (
                          <div className="flex items-center justify-center gap-3 mt-4 border-t border-gray-300 pt-5">
                            <InputField
                              name="newSubCategory"
                              type="text"
                              placeholder="Sub Category Name"
                              value={newSubCategory}
                              onChange={(e) => setNewSubCategory(e.target.value)}
                              ref={newSubCategoryInputRef}
                              className="bg-white"
                              autoComplete="off"
                            />
                            <Button
                              type="button"
                              onClick={() => {
                                if (newSubCategory.trim()) {
                                  arrayHelpers.push({ name: newSubCategory });
                                  setNewSubCategory("");
                                  setAddSubCategory(false);
                                }
                              }}
                              className="py-1.5 btn text-center btn-primary"
                            >
                              Create
                            </Button>
                          </div>
                        )}
                      </>
                    )}
                  />
                </div>
              )}
            </div>
            <div className="ltr:text-right rtl:text-left mt-10">
              <Button
                isLoading={isLoadingPost}
                onClick={() => setFormSubmit(true)}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default CategoryModal;
