import { useEffect, useRef } from 'react';
import { useTrackVisitorMutation } from '@/store/api/master/visitorTrackingSlice';

const useVisitorTracking = (pageName, additionalData = {}) => {
  const [trackVisitor] = useTrackVisitorMutation();
  const hasTracked = useRef(false);

  // Function to get browser information
  const getBrowserInfo = () => {
    const userAgent = navigator.userAgent;
    let browserName = 'Unknown';
    let browserVersion = 'Unknown';

    // Detect browser
    if (userAgent.indexOf('Chrome') > -1) {
      browserName = 'Chrome';
      browserVersion = userAgent.match(/Chrome\/([0-9.]+)/)?.[1] || 'Unknown';
    } else if (userAgent.indexOf('Firefox') > -1) {
      browserName = 'Firefox';
      browserVersion = userAgent.match(/Firefox\/([0-9.]+)/)?.[1] || 'Unknown';
    } else if (userAgent.indexOf('Safari') > -1) {
      browserName = 'Safari';
      browserVersion = userAgent.match(/Version\/([0-9.]+)/)?.[1] || 'Unknown';
    } else if (userAgent.indexOf('Edge') > -1) {
      browserName = 'Edge';
      browserVersion = userAgent.match(/Edge\/([0-9.]+)/)?.[1] || 'Unknown';
    } else if (userAgent.indexOf('Opera') > -1) {
      browserName = 'Opera';
      browserVersion = userAgent.match(/Opera\/([0-9.]+)/)?.[1] || 'Unknown';
    }

    return { browserName, browserVersion };
  };

  // Function to get device information
  const getDeviceInfo = () => {
    const userAgent = navigator.userAgent;
    let deviceType = 'Desktop';
    let operatingSystem = 'Unknown';

    // Detect device type
    if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
      deviceType = 'Mobile';
    } else if (/iPad/i.test(userAgent)) {
      deviceType = 'Tablet';
    }

    // Detect operating system
    if (userAgent.indexOf('Windows') > -1) {
      operatingSystem = 'Windows';
    } else if (userAgent.indexOf('Mac') > -1) {
      operatingSystem = 'macOS';
    } else if (userAgent.indexOf('Linux') > -1) {
      operatingSystem = 'Linux';
    } else if (userAgent.indexOf('Android') > -1) {
      operatingSystem = 'Android';
    } else if (userAgent.indexOf('iPhone') > -1 || userAgent.indexOf('iPad') > -1) {
      operatingSystem = 'iOS';
    }

    return { deviceType, operatingSystem };
  };

  // Function to get screen information
  const getScreenInfo = () => {
    return {
      screenWidth: window.screen.width,
      screenHeight: window.screen.height,
      viewportWidth: window.innerWidth,
      viewportHeight: window.innerHeight,
    };
  };

  // Function to get location information (approximate)
  const getLocationInfo = () => {
    return new Promise((resolve) => {
      // Try to get timezone
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const language = navigator.language || navigator.userLanguage;
      
      resolve({
        timezone,
        language,
      });
    });
  };

  // Function to generate session ID
  const getSessionId = () => {
    let sessionId = sessionStorage.getItem('visitor_session_id');
    if (!sessionId) {
      sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      sessionStorage.setItem('visitor_session_id', sessionId);
    }
    return sessionId;
  };

  // Function to get visitor ID (persistent across sessions)
  const getVisitorId = () => {
    let visitorId = localStorage.getItem('visitor_id');
    if (!visitorId) {
      visitorId = 'visitor_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      localStorage.setItem('visitor_id', visitorId);
    }
    return visitorId;
  };

  // Function to get referrer information
  const getReferrerInfo = () => {
    const referrer = document.referrer;
    const urlParams = new URLSearchParams(window.location.search);
    
    return {
      referrer: referrer || 'Direct',
      utm_source: urlParams.get('utm_source') || null,
      utm_medium: urlParams.get('utm_medium') || null,
      utm_campaign: urlParams.get('utm_campaign') || null,
      utm_term: urlParams.get('utm_term') || null,
      utm_content: urlParams.get('utm_content') || null,
    };
  };

  useEffect(() => {
    // Only track once per component mount
    if (hasTracked.current) return;

    const trackPageVisit = async () => {
      try {
        const browserInfo = getBrowserInfo();
        const deviceInfo = getDeviceInfo();
        const screenInfo = getScreenInfo();
        const locationInfo = await getLocationInfo();
        const referrerInfo = getReferrerInfo();

        const visitorData = {
          // Page information
          page_name: pageName,
          page_url: window.location.href,
          page_path: window.location.pathname,
          
          // Session information
          session_id: getSessionId(),
          visitor_id: getVisitorId(),
          
          // Browser information
          browser_name: browserInfo.browserName,
          browser_version: browserInfo.browserVersion,
          user_agent: navigator.userAgent,
          
          // Device information
          device_type: deviceInfo.deviceType,
          operating_system: deviceInfo.operatingSystem,
          
          // Screen information
          screen_width: screenInfo.screenWidth,
          screen_height: screenInfo.screenHeight,
          viewport_width: screenInfo.viewportWidth,
          viewport_height: screenInfo.viewportHeight,
          
          // Location information
          timezone: locationInfo.timezone,
          language: locationInfo.language,
          
          // Referrer information
          referrer: referrerInfo.referrer,
          utm_source: referrerInfo.utm_source,
          utm_medium: referrerInfo.utm_medium,
          utm_campaign: referrerInfo.utm_campaign,
          utm_term: referrerInfo.utm_term,
          utm_content: referrerInfo.utm_content,
          
          // Timestamp
          visited_at: new Date().toISOString(),
          
          // Additional data passed from component
          ...additionalData,
        };

        // Track the visitor
        await trackVisitor(visitorData).unwrap();
        hasTracked.current = true;
        
        console.log('Visitor tracked successfully for page:', pageName);
      } catch (error) {
        console.error('Error tracking visitor:', error);
      }
    };

    // Small delay to ensure page is fully loaded
    const timer = setTimeout(trackPageVisit, 1000);

    return () => clearTimeout(timer);
  }, [pageName, trackVisitor, additionalData]);

  return null;
};

export default useVisitorTracking;
