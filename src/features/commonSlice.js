import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  showModal: false,
  showEditModal: false,
  editData: {},
  common: [],
  quiz: {},
};

export const commonSlice = createSlice({
  name: "common",
  initialState,
  reducers: {
    quizSet: (state, action) => {
      state.quiz = action.payload;
    },
    setShowModal: (state, action) => {
      state.showModal = action.payload;
    },
    setEditShowModal: (state, action) => {
      state.showEditModal = action.payload;
    },
    setEditData: (state, action) => {
      state.editData = action.payload;
    },
  },
});

export const { quizSet, setShowModal,setEditShowModal, setEditData, showModal } = commonSlice.actions;
export default commonSlice.reducer;
