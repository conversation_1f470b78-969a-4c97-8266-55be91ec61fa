import React, { useRef, useEffect } from 'react';
import { Draggable } from 'react-beautiful-dnd';
import '../../assets/css/vertical-drag.css';

/**
 * VerticalDraggable - A component that restricts dragging to the vertical axis only
 * 
 * @param {Object} props - Component props
 * @param {string} props.id - Unique identifier for the draggable item
 * @param {number} props.index - Index of the item in the list
 * @param {React.ReactNode} props.children - Child elements
 * @param {Object} props.style - Additional styles to apply
 * @param {string} props.className - Additional CSS classes
 * @returns {React.ReactElement}
 */
const VerticalDraggable = ({ 
  id, 
  index, 
  children, 
  style = {}, 
  className = '',
  ...rest 
}) => {
  const nodeRef = useRef(null);

  // Function to handle drag start
  const handleDragStart = () => {
    // Add a class to the body to indicate vertical dragging is in progress
    document.body.classList.add('vertical-dragging');
    
    // Disable text selection during drag
    document.body.style.userSelect = 'none';
    
    // Disable scrolling during drag for better performance
    document.body.style.overflow = 'hidden';
  };

  // Function to handle drag end
  const handleDragEnd = () => {
    // Remove the dragging class
    document.body.classList.remove('vertical-dragging');
    
    // Re-enable text selection
    document.body.style.userSelect = '';
    
    // Re-enable scrolling
    document.body.style.overflow = '';
  };

  // Clean up event listeners when component unmounts
  useEffect(() => {
    return () => {
      document.body.classList.remove('vertical-dragging');
      document.body.style.userSelect = '';
      document.body.style.overflow = '';
    };
  }, []);

  return (
    <Draggable
      draggableId={id.toString()}
      index={index}
      axis="y" // Restrict to vertical movement only
      {...rest}
    >
      {(provided, snapshot) => (
        <div
          ref={(el) => {
            // Combine refs
            nodeRef.current = el;
            provided.innerRef(el);
          }}
          {...provided.draggableProps}
          style={{
            ...provided.draggableProps.style,
            ...style,
            // Override transform to ensure vertical-only movement
            transform: snapshot.isDragging
              ? `translate(0px, ${provided.draggableProps.style.transform.split(',')[1]})`
              : provided.draggableProps.style.transform,
            // Disable transition during drag for smoother movement
            transition: snapshot.isDragging ? 'none' : 'transform 0.2s cubic-bezier(0.2, 0, 0, 1)',
            // Ensure higher z-index during drag
            zIndex: snapshot.isDragging ? 1000 : 'auto',
          }}
          className={`vertical-draggable ${className} ${snapshot.isDragging ? 'dragging' : ''}`}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
        >
          <div className="vertical-draggable-wrapper">
            {children(provided, snapshot)}
          </div>
        </div>
      )}
    </Draggable>
  );
};

export default VerticalDraggable;
