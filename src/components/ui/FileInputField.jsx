import { useEffect, useState } from 'react';
import { useField } from 'formik';
import { useDropzone } from 'react-dropzone';
import { X } from 'lucide-react';

const assetBaseURL = import.meta.env.VITE_ASSET_HOST_URL || '';

export const FileInputField = ({ label, accept, accepts, multiple = false, isPdfOnly = false, required = false, ...props }) => {
    const [field, meta, helpers] = useField(props);
    const [preview, setPreview] = useState(null);
    const [formatError, setFormatError] = useState('');
    const [fileType, setFileType] = useState(null);

    const onDrop = (acceptedFiles) => {
        if (!acceptedFiles.length) return;
        const file = acceptedFiles[0];

        // Check file type
        if (accepts && !file.type.match(accepts)) {
            setFormatError(`Only ${accepts} file types are allowed.`);
            helpers.setValue(null);
            setPreview(null);
            setFileType(null);
            return;
        }

        setFormatError('');
        helpers.setValue(file);
        setFileType(file.type);
    };

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        multiple,
        accept
    });

    const removeFile = () => {
        helpers.setValue(null);
        setPreview(null);
        setFormatError('');
        setFileType(null);
    };

    useEffect(() => {
        if (typeof field.value === 'string' && field.value !== '') {
            // Handle existing file from server
            setPreview(`${assetBaseURL}${field.value}`);

            // Determine file type from extension
            const extension = field.value.split('.').pop().toLowerCase();
            if (extension === 'pdf') {
                setFileType('application/pdf');
            } else if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
                setFileType('image/' + (extension === 'jpg' ? 'jpeg' : extension));
            }
        } else if (field.value instanceof File) {
            // Handle newly selected file
            const objectUrl = URL.createObjectURL(field.value);
            setPreview(objectUrl);
            setFileType(field.value.type);
            return () => URL.revokeObjectURL(objectUrl);
        } else {
            setPreview(null);
            setFileType(null);
        }
    }, [field.value]);

    const renderPreview = () => {
        if (!preview) return null;

        const isPDF = fileType === 'application/pdf' || preview.toLowerCase().endsWith('.pdf');

        return (
            <div className="relative p-2 bg-gray-100 rounded mt-2">
                {isPDF ? (
                    <div className="flex flex-col items-center p-2">
                        <div className="flex items-center mb-2">
                            <svg className="w-8 h-8 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                            </svg>
                            <span className="text-sm font-medium">
                                {field.value instanceof File ? field.value.name : preview.split('/').pop()}
                            </span>
                        </div>
                        <embed src={preview} type="application/pdf" width="100%" height="200px" className="rounded" />
                    </div>
                ) : (
                    <img src={preview} alt="Preview" className="h-40 object-contain mx-auto" />
                )}
                <button
                    type="button"
                    onClick={removeFile}
                    className="absolute top-1 right-1 bg-white rounded-full p-1 text-red-600 shadow"
                >
                    <X size={18} />
                </button>
            </div>
        );
    };

    return (
        <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
                {label} {required && <span className="text-red-500">*</span>}
            </label>
            {!preview && (
                <div
                    {...getRootProps()}
                    className={`min-h-[150px] flex flex-col items-center justify-center border-2 border-dashed rounded-lg p-4 text-center cursor-pointer
                    ${isDragActive ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 dark:border-gray-600'}`}
                >
                    <input {...getInputProps()} />
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                        {isDragActive
                            ? 'Drop the file here...'
                            : `Drag and drop ${isPdfOnly ? 'a PDF' : 'a file'} here, or click to select`
                        }
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                        {isPdfOnly ? 'Only PDF files are accepted' : 'Accepted formats: JPG, JPEG, PNG, GIF'}
                        {required && ' (Required)'}
                    </p>
                </div>
            )}
            {renderPreview()}
            {(meta.touched && meta.error) || formatError ? (
                <div className="text-red-600 dark:text-red-400 text-sm mt-1">
                    {meta.error || formatError}
                </div>
            ) : null}
        </div>
    );
};

export default FileInputField;
