import React from 'react';
import { Formik, Form } from 'formik';
import CompactFileInput from './CompactFileInput';
import Button from './Button';

const CompactFileInputDemo = () => {
  const initialValues = {
    question_image: '',
    option1_image: '',
    option2_image: '',
    option3_image: '',
    option4_image: '',
    explanation_image: ''
  };

  const onSubmit = (values) => {
    console.log('Form values:', values);
    // Show which files are selected
    Object.entries(values).forEach(([key, value]) => {
      if (value instanceof File) {
        console.log(`${key}: ${value.name} (${value.size} bytes)`);
      } else if (value) {
        console.log(`${key}: ${value}`);
      }
    });
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
        Compact File Input Demo - Question Image Uploads
      </h2>
      
      <Formik
        initialValues={initialValues}
        onSubmit={onSubmit}
      >
        {({ values }) => (
          <Form className="space-y-6">
            {/* Question Image */}
            <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
              <h3 className="text-lg font-medium mb-3 text-gray-900 dark:text-white">
                Question Image
              </h3>
              <CompactFileInput
                name="question_image"
                label="Question Image"
                placeholder="Upload Question Image"
              />
            </div>

            {/* Option Images */}
            <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
              <h3 className="text-lg font-medium mb-3 text-gray-900 dark:text-white">
                Option Images
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <CompactFileInput
                  name="option1_image"
                  label="Option 1"
                  placeholder="Option 1 Image"
                />
                <CompactFileInput
                  name="option2_image"
                  label="Option 2"
                  placeholder="Option 2 Image"
                />
                <CompactFileInput
                  name="option3_image"
                  label="Option 3"
                  placeholder="Option 3 Image"
                />
                <CompactFileInput
                  name="option4_image"
                  label="Option 4"
                  placeholder="Option 4 Image"
                />
              </div>
            </div>

            {/* Explanation Image */}
            <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
              <h3 className="text-lg font-medium mb-3 text-gray-900 dark:text-white">
                Explanation Image
              </h3>
              <CompactFileInput
                name="explanation_image"
                label="Explanation Image"
                placeholder="Upload Explanation Image"
              />
            </div>

            {/* Form Values Display */}
            <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
              <h4 className="text-md font-medium mb-2 text-gray-900 dark:text-white">
                Current Form Values:
              </h4>
              <pre className="text-sm text-gray-600 dark:text-gray-300 overflow-auto">
                {JSON.stringify(
                  Object.fromEntries(
                    Object.entries(values).map(([key, value]) => [
                      key,
                      value instanceof File ? `File: ${value.name}` : value || 'No file selected'
                    ])
                  ),
                  null,
                  2
                )}
              </pre>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <Button
                type="submit"
                className="btn btn-primary"
              >
                Submit Form
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default CompactFileInputDemo;
