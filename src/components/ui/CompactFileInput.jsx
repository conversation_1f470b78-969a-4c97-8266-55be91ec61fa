import { useEffect, useState } from 'react';
import { useField } from 'formik';
import { useDropzone } from 'react-dropzone';
import { X, Upload, Image, Eye } from 'lucide-react';
import Modal from '@/components/ui/Modal';

const assetBaseURL = import.meta.env.VITE_ASSET_HOST_URL || '';

export const CompactFileInput = ({ 
  label, 
  accept = "image/*", 
  placeholder = "Upload Image", 
  required = false, 
  ...props 
}) => {
  const [field, meta, helpers] = useField(props);
  const [preview, setPreview] = useState(null);
  const [formatError, setFormatError] = useState('');
  const [showPreviewModal, setShowPreviewModal] = useState(false);

  const onDrop = (acceptedFiles) => {
    if (!acceptedFiles.length) return;
    const file = acceptedFiles[0];

    // Basic image validation
    if (!file.type.startsWith('image/')) {
      setFormatError('Only image files are allowed.');
      helpers.setValue(null);
      setPreview(null);
      return;
    }

    setFormatError('');
    helpers.setValue(file);
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: false,
    accept: { 'image/*': ['.jpg', '.jpeg', '.png', '.gif', '.webp'] }
  });

  const removeFile = () => {
    helpers.setValue(null);
    setPreview(null);
    setFormatError('');
  };

  useEffect(() => {
    if (typeof field.value === 'string' && field.value !== '') {
      // Handle existing file from server
      setPreview(`${assetBaseURL}${field.value}`);
    } else if (field.value instanceof File) {
      // Handle newly selected file
      const objectUrl = URL.createObjectURL(field.value);
      setPreview(objectUrl);
      return () => URL.revokeObjectURL(objectUrl);
    } else {
      setPreview(null);
    }
  }, [field.value]);

  return (
    <div className="w-full h-10">
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      
      <div className="relative">
        {!preview ? (
          <div
            {...getRootProps()}
            className={`h-10 flex items-center justify-center border-2 border-dashed rounded-lg cursor-pointer transition-colors
              ${isDragActive 
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
              }`}
          >
            <input {...getInputProps()} />
            <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
              <Upload size={16} />
              <span className="text-sm">
                {isDragActive ? 'Drop here...' : placeholder}
              </span>
            </div>
          </div>
        ) : (
          <div className="relative h-10 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden flex items-center">
            <div className="flex items-center justify-between w-full px-3">
              <div className="flex items-center space-x-2 flex-1 min-w-0">
                <Image size={16} className="text-gray-600 flex-shrink-0" />
                <span className="text-sm text-gray-700 dark:text-gray-300 truncate">
                  {field.value instanceof File ? field.value.name : 'Image uploaded'}
                </span>
              </div>
              <div className="flex items-center space-x-1 flex-shrink-0">
                <button
                  type="button"
                  onClick={() => setShowPreviewModal(true)}
                  className="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full p-1 hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                  title="Preview image"
                >
                  <Eye size={14} />
                </button>
                <button
                  type="button"
                  onClick={removeFile}
                  className="bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 rounded-full p-1 hover:bg-red-200 dark:hover:bg-red-800 transition-colors"
                  title="Remove image"
                >
                  <X size={14} />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {(meta.touched && meta.error) || formatError ? (
        <div className="text-red-600 dark:text-red-400 text-xs mt-1">
          {meta.error || formatError}
        </div>
      ) : null}

      {/* Image Preview Modal */}
      <Modal
        activeModal={showPreviewModal}
        onClose={() => setShowPreviewModal(false)}
        title="Image Preview"
        className="max-w-4xl"
        centered={true}
        themeClass="bg-slate-900 dark:bg-slate-800"
      >
        {preview && (
          <div className="flex justify-center items-center">
            <img
              src={preview}
              alt="Image Preview"
              className="max-w-full max-h-[70vh] object-contain rounded"
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default CompactFileInput;
