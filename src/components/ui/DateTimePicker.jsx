import React from 'react';
import { useField } from 'formik';
import Flatpickr from 'react-flatpickr';
import dayjs from 'dayjs';

const DateTimePicker = ({ label, ...props }) => {
  const [field, meta, helpers] = useField(props);

  return (
    <div>
      <label
        htmlFor={props.id || props.name}
        className="block text-gray-600 text-sm font-medium mb-2"
      >
        {label}
      </label>
      <Flatpickr
        className="w-full border rounded p-2"
        {...props}
        value={field.value ? new Date(field.value) : null}
        onChange={(dates) => {
          helpers.setValue(dates[0]);
        }}
        options={{
          dateFormat: "Y-m-d H:i",
          enableTime: true,
          time_24hr: true,
        }}
      />
      {meta.touched && meta.error && (
        <div className="text-red-500 text-xs">{meta.error}</div>
      )}
    </div>
  );
};

export default DateTimePicker;
