import React from 'react';
import Icon from '@/components/ui/Icon';

/**
 * VerticalDragHandle - A component that provides a handle for vertical dragging
 * 
 * @param {Object} props - Component props
 * @param {Object} props.dragHandleProps - Props from react-beautiful-dnd
 * @param {string} props.className - Additional CSS classes
 * @returns {React.ReactElement}
 */
const VerticalDragHandle = ({ dragHandleProps, className = '' }) => {
  return (
    <div
      {...dragHandleProps}
      className={`vertical-drag-handle ${className}`}
      title="Drag to reorder"
    >
      <Icon icon="heroicons-outline:dots-vertical" className="text-xl" />
    </div>
  );
};

export default VerticalDragHandle;
