import React, { useEffect, useRef } from 'react';
import Hls from 'hls.js';

const HLSVideoPlayer = ({ videoUrl}) => {
  const videoRef = useRef(null);
  // const videoUrl = "https://bacbonschool.s3.ap-south-1.amazonaws.com/uploads/C_Unit/Bangla/Lecture_Sheet_1/lectures/16035162501/index.m3u8";

  useEffect(() => {
    if (Hls.isSupported()) {
      const hls = new Hls();
      hls.loadSource(videoUrl);
      hls.attachMedia(videoRef.current);

      hls.on(Hls.Events.MANIFEST_PARSED, function () {
        videoRef.current.play();
      });

      return () => {
        hls.destroy();
      };
    } else if (videoRef.current.canPlayType('application/vnd.apple.mpegurl')) {
      videoRef.current.src = videoUrl;
      videoRef.current.addEventListener('loadedmetadata', function () {
        videoRef.current.play();
      });
    }
  }, [videoUrl]);

  return (
    <div className="video-player-container">
      <video ref={videoRef} controls width="100%" height="500px">
        Your browser does not support the video tag.
      </video>
    </div>
  );
};

export default HLSVideoPlayer;
