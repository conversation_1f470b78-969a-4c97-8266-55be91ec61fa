import React from "react";
import useFooterType from "@/hooks/useFooterType";
import { Link } from "react-router-dom";
import { Icon } from "@iconify/react";
import { useTranslation } from "react-i18next";

const Footer = ({ className = "custom-class" }) => {
  const [footerType] = useFooterType();
  const { t } = useTranslation();
  const footerclassName = () => {
    switch (footerType) {
      case "sticky":
        return "sticky bottom-0 z-[999]";
      case "static":
        return "static";
      case "hidden":
        return "hidden";
    }
  };
  return (
    <footer className={className + " " + footerclassName()}>
      <div className="site-footer px-6 bg-gray-200 dark:bg-slate-800 text-slate-500 dark:text-slate-300 py-4">
        <div className="grid md:grid-cols-3 grid-cols-1 md:gap-5">
          <div className="text-center ltr:md:text-start rtl:md:text-right text-sm">
            {t('promotional.copyright')}
          </div>
          <div className=" text-sm flex items-center  gap-4 justify-center">
            <p className="font-semibold hidden 2xl:block">Contact Us:</p>
            <div className="flex justify-center items-center space-x-2">
              <a
                href="https://www.facebook.com/edupackbd"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-1 text-indigo-600 hover:text-indigo-800"
              >
                <Icon icon="ic:baseline-facebook" className="w-5 h-5" />
                <span>{t('promotional.facebook')}</span>
              </a>
              <a
                href="tel:09611900205"
                className="flex items-center space-x-1 text-indigo-600 hover:text-indigo-800"
              >
                <Icon icon="ic:baseline-phone" className="w-5 h-5" />
                <span>{t('promotional.phone_numbers')}</span>
              </a>
            </div>
          </div>

          <div className="ltr:md:text-right rtl:md:text-end text-center text-sm">
            {t('promotional.handcrafted')}{" "}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
