import React from "react";
import useFooterType from "@/hooks/useFooterType";
import { Link } from "react-router-dom";
import { Icon } from "@iconify/react";
import { useTranslation } from "react-i18next";

const MobileFooter = ({ className = "custom-class" }) => {
  const [footerType] = useFooterType();
  const { t } = useTranslation();

  const footerClassName = () => {
    switch (footerType) {
      case "sticky":
        return "sticky bottom-0 z-[999]";
      case "static":
        return "static";
      case "hidden":
        return "hidden";
      default:
        return "static";
    }
  };

  return (
    <footer
      className={`${className} ${footerClassName()} p-6 bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-300 shadow-md`}
    >
      <div className="container mx-auto">
        <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 items-center">
          {/* Left Section */}
          <div className="text-center sm:text-left text-sm font-medium">
            <p>
              {t('promotional.copyright')}
            </p>
          </div>

          {/* Center Section */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <p className="font-semibold">Contact Us:</p>
            <div className="flex space-x-4">
              <a
                href="https://www.facebook.com/edupackbd"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 text-indigo-600 hover:text-indigo-800"
              >
                <Icon icon="ic:baseline-facebook" className="w-5 h-5" />
                <span>{t('promotional.facebook')}</span>
              </a>
              <a
                href="tel:09611900205"
                className="flex items-center space-x-2 text-indigo-600 hover:text-indigo-800"
              >
                <Icon icon="ic:baseline-phone" className="w-5 h-5" />
                <span>{t('promotional.phone_numbers')}</span>
              </a>
            </div>
          </div>

          {/* Right Section */}
          <div className="text-center sm:text-right text-sm font-medium">
            <p>
              {t('promotional.handcrafted')}
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default MobileFooter;
