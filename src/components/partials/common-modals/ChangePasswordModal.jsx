import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import InputField from "@/components/ui/InputField";

const ChangePasswordModal = ({ showModal, setShowModal, userId, userName }) => {
  const [postApi, { isLoading }] = usePostApiMutation();
  const [showPassword, setShowPassword] = useState(false);

  const validationSchema = Yup.object().shape({
    password: Yup.string()
      .min(8, "Password must be at least 8 characters")
      .required("Password is required"),
  });

  const initialValues = {
    password: "",
  };

  const handleSubmit = async (values, { resetForm }) => {
    try {
      const response = await postApi({
        end_point: "admin/change-password",
        body: {
          id: userId,
          password: values.password,
        },
      });
      console.log(response);
      if (response.data?.status) {
        resetForm();
        setShowModal(false);
      }
    } catch (error) {
      console.error("Error changing password:", error);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Modal
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      title={`Change Password for ${userName || "User"}`}
      className="max-w-md"
    >
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ values, errors, touched, handleChange, handleBlur }) => (
          <Form className="space-y-4">
            <div className="relative">
              <InputField
                label="New Password"
                name="password"
                type={showPassword ? "text" : "password"}
                placeholder="Enter new password"
                onChange={handleChange}
                onBlur={handleBlur}
                value={values.password}
                error={errors.password && touched.password}
                errorText={errors.password}
              />
              <button
                type="button"
                className="absolute right-3 top-9 text-gray-500"
                onClick={togglePasswordVisibility}
              >
                {showPassword ? (
                  <span className="text-sm">Hide</span>
                ) : (
                  <span className="text-sm">Show</span>
                )}
              </button>
            </div>

            <div className="flex justify-end space-x-3 mt-4">
              <Button
                text="Cancel"
                btnClass="btn-outline-dark"
                onClick={() => setShowModal(false)}
              />
              <Button
                text={isLoading ? "Updating..." : "Update Password"}
                btnClass="btn-primary"
                type="submit"
                disabled={isLoading}
              />
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default ChangePasswordModal;
