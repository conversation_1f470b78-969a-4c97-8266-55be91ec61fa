import React, { useRef, useEffect } from "react";
import Flatpickr from "react-flatpickr";
import { Icon } from '@iconify/react';

const DatePicker = ({
  label,
  placeholder = "mm/dd/yyyy",
  classLabel = "form-label",
  className = "",
  classGroup = "",
  register,
  name,
  readonly,
  value,
  error,
  icon,
  disabled,
  id,
  horizontal,
  validate,
  onChange,
  options,
  onFocus,
  defaultValue,
  msgTooltip,
  description,
  required,
  ...rest
}) => {
  const datepickerRef = useRef(null);

  useEffect(() => {
    if (datepickerRef.current && value) {
      datepickerRef.current.flatpickr.setDate(value);
    }
  }, [value]);

  const handleIconClick = () => {
    if (datepickerRef.current) {
      datepickerRef.current.flatpickr.open();
    }
  };

  return (
    <div
      className={`formGroup ${error ? "has-error" : ""} ${
        horizontal ? "flex" : ""
      } ${validate ? "is-valid" : ""}`}
    >
      {label && (
        <label
          htmlFor={id}
          className={`block capitalize ${classLabel} ${
            horizontal ? "flex-0 mr-6 md:w-[100px] w-[60px] break-words" : ""
          }`}
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <div className={`relative ${horizontal ? "flex-1" : ""}`}>
        <Flatpickr
          ref={datepickerRef}
          {...(register ? register(name) : {})}
          {...rest}
          value={value || ''}
          onChange={(dates) => {
            if (onChange) {
              onChange(dates[0]);
            }
          }}
          options={{
            dateFormat: "Y-m-d",
            ...options
          }}
          className={`${
            error ? " has-error" : " "
          } appearance-none border rounded h-10 w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:-outline ${className}`}
          placeholder={placeholder}
          readOnly={readonly}
          disabled={disabled}
          onFocus={onFocus}
          id={id}
        />
        <div 
          className="absolute right-4 top-1/2 transform -translate-y-1/2 cursor-pointer" 
          onClick={handleIconClick}
        >
          <Icon icon="simple-line-icons:calender" />
        </div>
      </div>
      {error && (
        <div
          className={`mt-2 ${
            msgTooltip
              ? "inline-block bg-danger-500 text-white text-[10px] px-2 py-1 rounded"
              : "text-danger-500 block text-sm"
          }`}
        >
          {error}
        </div>
      )}
      {validate && (
        <div
          className={`mt-2 ${
            msgTooltip
              ? "inline-block bg-success-500 text-white text-[10px] px-2 py-1 rounded"
              : "text-success-500 block text-sm"
          }`}
        >
          {validate}
        </div>
      )}
      {description && <span className="input-description">{description}</span>}
    </div>
  );
};

export default DatePicker;