import React, { useState, useEffect, useRef } from "react";
import Card from "@/components/ui/Card";
import GlobalFilter from "./GlobalFilter";
import Dropdown from "@/components/ui/Dropdown";
import Icon from "@/components/ui/Icon";
import { Menu } from "@headlessui/react";
import Pagination from "./pagination";
import { useDispatch, useSelector } from "react-redux";
import { setEditData, setShowModal } from "@/features/commonSlice";
import Loading from "@/components/Loading";
import { useDrag, useDrop, DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import { toast } from "react-toastify";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import "./menu-drag-styles.css";

const DraggableItem = ({ id, index, moveItem, onDragEnd, children }) => {
  const ref = useRef(null);

  const [{ isDragging }, drag] = useDrag({
    type: 'MENU_ITEM',
    item: { id, index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    end: (_, monitor) => {
      if (monitor.didDrop()) {
        onDragEnd();
      }
    },
  });

  const [, drop] = useDrop({
    accept: 'MENU_ITEM',
    hover: (item, monitor) => {
      if (!ref.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;
      if (dragIndex === hoverIndex) {
        return;
      }
      const hoverBoundingRect = ref.current.getBoundingClientRect();
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }
      moveItem(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });
  drag(drop(ref));

  return (
    <div
      ref={ref}
      className={`border rounded-lg bg-gray-50 border-gray-200 flex justify-between items-center mb-2 draggable-menu-item ${isDragging ? "dragging" : ""
        }`}
    >
      {children}
    </div>
  );
};

const MenuSettingTable = ({
  tableHeaderExtra = null,
  title,
  createButton,
  createPage,
  editPage,
  actions = [],
  columns,
  changePage,
  data,
  filter,
  setFilter,
  currentPage,
  totalPages,
  enableDragDrop = true,
  onSortEnd,
}) => {
  const [gridView, setGridView] = useState(false);
  const [items, setItems] = useState([]);
  const [isSorting, setIsSorting] = useState(false);
  const [hasOrderChanged, setHasOrderChanged] = useState(false);
  const dispatch = useDispatch();
  const { showModal, showEditModal } = useSelector(
    (state) => state.commonReducer
  );

  const [postApi] = usePostApiMutation();

  useEffect(() => {
    if (data) {
      setItems(data);
    }
  }, [data]);

  const handlePageChange = (value) => {
    changePage(`?page=${value}`);
  };

  const openCreateModal = () => {
    dispatch(setEditData(null));
    dispatch(setShowModal(true));
  };
  const moveItem = (dragIndex, hoverIndex) => {
    setItems(prevItems => {
      const draggedItem = prevItems[dragIndex];
      const newItems = [...prevItems];
      newItems.splice(dragIndex, 1);
      newItems.splice(hoverIndex, 0, draggedItem);
      setHasOrderChanged(true);
      return newItems;
    });
  };
  const handleDragEnd = async () => {
    if (!hasOrderChanged) return;
    setHasOrderChanged(false);
    const updatedItems = items.map((item, index) => ({
      ...item,
      sequence: index + 1
    }));
    setIsSorting(true);
    try {
      const formData = new FormData();
      updatedItems.forEach((item, index) => {
        formData.append(`items[${index}][id]`, item.id);
        formData.append(`items[${index}][sequence]`, item.sequence);
      });
      await postApi({
        end_point: "admin/sort-menu",
        body: formData,
      }).unwrap();
      setItems(updatedItems);
      if (onSortEnd) {
        onSortEnd(updatedItems);
      }
    } catch (error) {
      console.error('Error updating menu order:', error);
      toast.error('Failed to update menu order. Please try again.');
    } finally {
      setIsSorting(false);
    }
  };
  return (
    <div className="">
      <div>
        <Card>
          <div className="md:flex justify-between items-center mb-5 border-b pb-3">
            <h4 className="card-title">{title}</h4>
            <div className="flex gap-2">
              {tableHeaderExtra}
              <GlobalFilter filter={filter} setFilter={setFilter} />
              {createButton && (
                <button
                  className="btn btn-primary btn-sm"
                  onClick={() => openCreateModal()}
                >
                  {createButton}
                </button>
              )}
              {showModal && createPage}
              {showEditModal && editPage}
            </div>
          </div>

          <div className="flex justify-end mb-4">
            <div className="flex items-center divide-x rounded border">
              <button
                onClick={() => setGridView(true)}
                className={`p-2 px-3.5 rounded-l transition-all duration-300 ${gridView ? "bg-gray-300" : "bg-gray-100"
                  }`}
              >
                <Icon icon="nrk:category" className={"text-xl"} />
              </button>
              <button
                onClick={() => setGridView(false)}
                className={`p-2 px-3.5 rounded-r transition-all duration-300 ${!gridView ? "bg-gray-300" : "bg-gray-100"
                  }`}
              >
                <Icon icon="ph:rows-bold" className={"text-xl"} />
              </button>
            </div>
          </div>

          {isSorting && (
            <div className="fixed inset-0 bg-black bg-opacity-30 z-50 flex items-center justify-center">
              <div className="bg-white p-4 rounded-lg shadow-lg flex items-center">
                <div className="animate-spin rounded-full h-6 w-6 border-2 border-blue-500 border-t-transparent"></div>
                <span className="ml-3 text-sm font-medium">Updating menu order...</span>
              </div>
            </div>
          )}

          {enableDragDrop ? (
            <DndProvider backend={HTML5Backend}>
              <div className={`bg-white smooth-droppable ${gridView ? "grid gap-5 grid-cols-2" : ""
                }`}>
                {items.map((row, dataIndex) => (
                  <DraggableItem
                    key={row.id.toString()}
                    id={row.id.toString()}
                    index={dataIndex}
                    moveItem={moveItem}
                    onDragEnd={handleDragEnd}
                  >
                    {/* Drag handle indicator */}
                    <div className="px-3 py-4 flex flex-col items-center justify-center text-gray-500 drag-handle">
                      <Icon icon="heroicons-outline:chevron-up" className="text-xs mb-1" />
                      <Icon icon="heroicons-outline:dots-vertical" className="text-lg" />
                      <Icon icon="heroicons-outline:chevron-down" className="text-xs mt-1" />
                    </div>

                    {/* Column data */}
                    {columns.map(
                      (column, index) =>
                        column.field && (
                          <div key={index} className="table-td p-4 flex-1">
                            {row[column.field]}
                          </div>
                        )
                    )}

                    {/* Actions dropdown */}
                    {actions.length > 0 && (
                      <div className="table-td">
                        <div className="flex items-center space-x-2">
                          <button
                            type="button"
                            onClick={() => actions[0].onClick(dataIndex)}
                            className="p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <Icon icon={actions[0].icon} />
                          </button>
                          <button
                            type="button"
                            onClick={() => actions[1].onClick(dataIndex)}
                            className="p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <Icon icon={actions[1].icon} />
                          </button>
                        </div>
                      </div>
                    )}
                  </DraggableItem>
                ))}
              </div>
            </DndProvider>
          ) : (
            <div
              className={`bg-white transition-all duration-300 ease-in-out transform ${gridView
                ? "grid gap-5 grid-cols-2 opacity-100 scale-100"
                : "opacity-100 scale-100"
                }`}
            >
              {items.map((row, dataIndex) => (
                <div
                  key={dataIndex}
                  className={`border rounded-lg bg-gray-50 border-gray-200 flex justify-between items-center hover:border-gray-300 hover:shadow-sm transition-all duration-200 ${gridView
                    ? "scale-100 opacity-100"
                    : "scale-100 opacity-100"
                    } ${!gridView ? "mb-3" : ""}`}
                >
                  {columns.map(
                    (column, index) =>
                      column.field && (
                        <div key={index} className="table-td p-4 flex-1">
                          {row[column.field]}
                        </div>
                      )
                  )}
                      
                      {actions.length > 0 && (
                      <div className="table-td">
                        <div className="flex items-center space-x-2">
                          <button
                            type="button"
                            onClick={() => actions[0].onClick(dataIndex)}
                            className="p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <Icon icon={actions[0].icon} />
                          </button>
                          <button
                            type="button"
                            onClick={() => actions[1].onClick(dataIndex)}
                            className="p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <Icon icon={actions[1].icon} />
                          </button>
                        </div>
                      </div>
                    )}
                </div>
              ))}
            </div>
          )}

          {totalPages > 0 && (
            <Pagination
              totalPages={totalPages}
              currentPage={currentPage}
              handlePageChange={handlePageChange}
            />
          )}
        </Card>
      </div>
    </div>
  );
};

export default MenuSettingTable;