import React, { useState } from "react";
import Card from "@/components/ui/Card";
import GlobalFilter from "./GlobalFilter";
import Dropdown from "@/components/ui/Dropdown";
import Icon from "@/components/ui/Icon";
import { Menu } from "@headlessui/react";
import Pagination from "./pagination";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import Loading from "@/components/Loading";
import Button from "@/components/ui/Button";

const BasicTablePage = ({
  tableHeaderExtra = null,
  title,
  createButton,
  createPage,
  editPage,
  actions = [],
  columns,
  changePage,
  data,
  filter = true,
  setFilter,
  currentPage,
  totalPages,
  submitForm,
  loading,
  setShowBulkModal,
  sampleFile = '#'
}) => {
  const dispatch = useDispatch();
  const { showModal, showEditModal } = useSelector(
    (state) => state.commonReducer
  );


    const { labels } = useSelector((state) => state.languageReducer);


  const handlePageChange = (value) => {
    changePage(`?page=${value}`);
  };
  const openCreateModal = () => {
    dispatch(setShowModal(true));
  };

  return (
    <div className="">
      <Card noborder>
        <div className="md:flex justify-between items-center mb-6">
          <h4 className="card-title">  { labels[title.toLowerCase()] || title} </h4>
          <div className="flex gap-2">
            {tableHeaderExtra}
            {setShowBulkModal && (
              <>
              <a
                className="bg-gray-200 rounded p-2 flex items-center gap-2"
                href={sampleFile}
                target="_blank"
              >
                <Icon icon="clarity:download-cloud-line" className="text-lg" />
                Sample Download
              </a>
              <Button
                className="bg-gray-200 rounded py-2 flex items-center gap-2"
                onClick={() => setShowBulkModal(true)}
              >
                <Icon icon="clarity:upload-cloud-line" className="text-lg" />
                Bulk Upload
              </Button>
              </>
            )}
            {filter && <GlobalFilter filter={filter} setFilter={setFilter} /> }
            {createButton && (
              <button
                className="btn btn-primary btn-sm"
                onClick={() => openCreateModal()}
              >
                 { labels[createButton.toLowerCase()] || createButton}
                
              </button>
            )}
            {showModal && createPage}
            {showEditModal && editPage}
          </div>
        </div>
        {loading ? (
          <Loading />
        ) : data?.length > 0 ? (
          <table className="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
            <thead className="bg-slate-200 dark:bg-slate-700">
              <tr>
                {columns.map((column, i) => (
                  <th key={i} scope="col" className=" table-th ">
                    {column.label}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
              {data?.map((row, dataIndex) => (
                <tr
                  key={dataIndex}
                  className="hover:bg-slate-200 dark:hover:bg-slate-700"
                >
                  {columns.map(
                    (column, index) =>
                      column.field && (
                        <td key={index} className="table-td">
                          {row[column.field]}
                        </td>
                      )
                  )}
                  {actions.length > 0 && (
                    <td className="table-td ">
                      <Dropdown
                        classMenuItems="right-0 top-[110%] flex"
                        label={
                          <span className="text-xl text-center block px-2">
                            <Icon icon="heroicons-outline:dots-vertical" />
                          </span>
                        }
                      >
                        <div className="divide-y divide-slate-100 dark:divide-slate-800">
                          {actions.map((item, i) => (
                            <Menu.Item key={i}>
                              <div
                                className={`
                          border-b border-b-gray-500 border-opacity-10 px-4 py-2 text-sm cursor-pointer 
                          first:rounded-t last:rounded-b flex gap-2 items-center rtl:space-x-reverse whitespace-nowrap hover:bg-gray-50`}
                                onClick={() => item.onClick(dataIndex)}
                              >
                                <span className="text-base">
                                  <Icon icon={item?.icon} />
                                </span>
                                <span>{item.name} </span>
                              </div>
                            </Menu.Item>
                          ))}
                        </div>
                      </Dropdown>
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          "No data found"
        )}
        {totalPages > 0 && (
          <div className="p-4 flex justify-end">
            <Pagination
              totalPages={totalPages}
              currentPage={currentPage}
              handlePageChange={handlePageChange}
            />
          </div>
        )}
      </Card>
    </div>
  );
};

export default BasicTablePage;
