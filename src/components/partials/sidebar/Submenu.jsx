import React from "react";
import { Collapse } from "react-collapse";
import { NavLink, useLocation } from "react-router-dom";
import Icon from "@/components/ui/Icon";
import Multilevel from "./Multi";

const Submenu = ({
  activeSubmenu,
  item,
  i,
  toggleMultiMenu,
  activeMultiMenu,
  labels
}) => {
  const location = useLocation();
  const locationName = location.pathname.replace("/", "");

  // Function to check if a NavLink should be active
  const isNavLinkActive = (path) => {
    return locationName === path || locationName.startsWith(`${path}/`);
  };
  return (
    <Collapse isOpened={activeSubmenu === i}>
      <ul className="sub-menu  space-y-4  ">
        {item.child?.map((subItem, j) => (
          <li key={j} className="block pl-4 pr-1 first:pt-4  last:pb-4">
            {subItem?.multi_menu ? (
              <div>
                <div
                  onClick={() => toggleMultiMenu(j)}
                  className={`${
                    activeMultiMenu
                      ? " text-black dark:text-white font-medium"
                      : "text-slate-600 dark:text-slate-300"
                  } text-sm flex space-x-3 items-center transition-all duration-150 cursor-pointer rtl:space-x-reverse`}
                >
                  <span
                    className={`${
                      activeMultiMenu === j
                        ? " bg-slate-900 dark:bg-slate-300 ring-4 ring-opacity-[15%] ring-black-500 dark:ring-slate-300 dark:ring-opacity-20"
                        : ""
                    } h-2 w-2 rounded-full border border-slate-600 dark:border-white inline-block flex-none `}
                  ></span>
                  <span className="flex-1">{subItem.childtitle}</span>
                  <span className="flex-none">
                    <span
                      className={`menu-arrow transform transition-all duration-300 ${
                        activeMultiMenu === j ? " rotate-90" : ""
                      }`}
                    >
                      <Icon icon="ph:caret-right" />
                    </span>
                  </span>
                </div>
                <Multilevel
                  activeMultiMenu={activeMultiMenu}
                  j={j}
                  subItem={subItem}
                />
              </div>
            ) : (
              <NavLink
                to={subItem.childlink}
                className={({ isActive }) => (isActive || isNavLinkActive(subItem.childlink)) ? "active" : ""}
              >
                {({ isActive }) => {
                  const isItemActive = isActive || isNavLinkActive(subItem.childlink);
                  return (
                    <span
                      className={`${
                        isItemActive
                          ? " text-primary-500 dark:text-primary-500 font-medium"
                          : "text-slate-600 dark:text-slate-300"
                      } text-sm flex space-x-3 items-center transition-all duration-150 rtl:space-x-reverse`}
                    >
                      <span
                        className={`${
                          isItemActive
                            ? " bg-primary-500 dark:bg-primary-500 ring-4 ring-opacity-[15%] ring-primary-500 dark:ring-primary-500 dark:ring-opacity-20 border-primary-500 dark:border-primary-500"
                            : ""
                        } h-2 w-2 rounded-full border border-slate-600 dark:border-white inline-block flex-none`}
                      ></span>
                      <span className="flex-1"> { labels[subItem.childtitle.toLowerCase()] || subItem.childtitle} </span>
                    </span>
                  );
                }}
              </NavLink>
            )}
          </li>
        ))}
      </ul>
    </Collapse>
  );
};

export default Submenu;
