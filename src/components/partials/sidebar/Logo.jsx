import React from "react";
import { Link } from "react-router-dom";
import Icon from "@/components/ui/Icon";
import useDarkMode from "@/hooks/useDarkMode";
import useSidebar from "@/hooks/useSidebar";
import useSemiDark from "@/hooks/useSemiDark";
import useSkin from "@/hooks/useSkin";
import { useSelector } from "react-redux";
import logo from "@/assets/images/promotion/logo.png";

// import images
import Mobile<PERSON>ogo from "@/assets/images/logo/logo24X24.ico";
import MobileLogoWhite from "@/assets/images/logo/logo24X24.ico";

const SidebarLogo = ({ menuHover, organization }) => {
  const [isDark] = useDarkMode();
  const [collapsed, setMenuCollapsed] = useSidebar();

  const [isSemiDark] = useSemiDark();

  const [skin] = useSkin();
  return (
    <div
      className={` logo-segment flex justify-between items-center bg-white dark:bg-slate-800 z-[9] py-6  px-4 
      ${menuHover ? "logo-hovered" : ""}
      ${
        skin === "bordered"
          ? " border-b border-r-0 border-slate-200 dark:border-slate-700"
          : " border-none"
      }
      
      `}
    >
      <Link to="/dashboard" rel="noopener noreferrer">
        <div className="flex items-center justify-center space-x-4">
          <div className="logo-icon">
            <img className="h-8 w-auto" src={organization ? import.meta.env.VITE_ASSET_HOST_URL + organization?.logo: logo} alt={organization?.title} />
          </div>
        </div>
      </Link>

      {(!collapsed || menuHover) && (
        <div
          onClick={() => setMenuCollapsed(!collapsed)}
          className={` transition-all duration-150
          ${
            collapsed
              ? ""
              : "ring-2 ring-inset ring-offset-4 ring-black-900 dark:ring-slate-400 bg-slate-900 dark:bg-slate-400 dark:ring-offset-slate-700"
          }
          `}
        ></div>
      )}
    </div>
  );
};

export default SidebarLogo;
