import React, { useEffect, useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
import Icon from "@/components/ui/Icon";
import { useDispatch, useSelector } from "react-redux";
import useMobileMenu from "@/hooks/useMobileMenu";
import Submenu from "./Submenu";

const Navmenu = ({ menus, organization }) => {

  const [activeSubmenu, setActiveSubmenu] = useState(null);

  const { user } = useSelector((state) => state.auth);
  const { labels } = useSelector((state) => state.languageReducer);
  
  const toggleSubmenu = (i) => {
    if (activeSubmenu === i) {
      setActiveSubmenu(null);
    } else {
      setActiveSubmenu(i);
    }
  };
  const location = useLocation();
  const locationName = location.pathname.replace(/^\//, "");
  const [mobileMenu, setMobileMenu] = useMobileMenu();
  const [activeMultiMenu, setMultiMenu] = useState(null);
  const dispatch = useDispatch();

  const toggleMultiMenu = (j) => {
    if (activeMultiMenu === j) {
      setMultiMenu(null);
    } else {
      setMultiMenu(j);
    }
  };

  const isLocationMatch = (targetLocation) => {
    const normalizedTarget = targetLocation ? targetLocation.replace(/^\//, '') : '';
    const normalizedLocation = location.pathname.replace(/^\//, '');
    return (
      normalizedLocation === normalizedTarget ||
      normalizedLocation.startsWith(`${normalizedTarget}/`)
    );
  };

  const isNavLinkActive = (path) => {
    const normalizedPath = path ? path.replace(/^\//, '') : '';
    const normalizedLocation = location.pathname.replace(/^\//, '');
    return normalizedLocation === normalizedPath || normalizedLocation.startsWith(`${normalizedPath}/`);
  };


  useEffect(() => {
    let submenuIndex = null;
    let multiMenuIndex = null;
    menus.forEach((item, i) => {
      if (item.link && isLocationMatch(item.link)) {
        submenuIndex = i;
      }

      if (item.child) {
        item.child.forEach((childItem, j) => {
          if (childItem.childlink && isLocationMatch(childItem.childlink)) {
            submenuIndex = i;
          }

          if (childItem.multi_menu) {
            childItem.multi_menu.forEach((nestedItem) => {
              if (nestedItem.multiLink && isLocationMatch(nestedItem.multiLink)) {
                submenuIndex = i;
                multiMenuIndex = j;
              }
            });
          }
        });
      }
    });

    const title = user?.organization?.name || 'EduPack';
    document.title = ` ${title} | ${locationName.charAt(0).toUpperCase() + locationName.slice(1)}`;

    setActiveSubmenu(submenuIndex);
    setMultiMenu(multiMenuIndex);
    if (mobileMenu) {
      setMobileMenu(false);
    }
  }, [location]);

  return (
    <>    
    
  <div className="mb-4 px-4 py-2 text-sm bg-gray-100 dark:bg-gray-800 rounded-lg">
    <div className="font-semibold text-gray-800 dark:text-white">
      {organization?.name || "No Organization"}
    </div>
    <div className="text-xs text-gray-600 dark:text-gray-400">
      Code: {organization?.organization_code || "N/A"}
    </div>
  </div>

      <ul>
        {menus.map((item, i) => (
          <li
            key={i}
            className={` single-sidebar-menu
              ${item.child ? "item-has-children" : ""}
              ${activeSubmenu === i ? "open" : ""}
              ${(item.link && isNavLinkActive(item.link)) || (item.child && item.child.some(child =>
              (child.childlink && isNavLinkActive(child.childlink)) ||
              (child.multi_menu && child.multi_menu.some(multi => multi.multiLink && isNavLinkActive(multi.multiLink)))
            )) ? "menu-item-active" : ""}
              ${item.child && item.child.some(child =>
              (child.childlink && isNavLinkActive(child.childlink)) ||
              (child.multi_menu && child.multi_menu.some(multi => multi.multiLink && isNavLinkActive(multi.multiLink)))
            ) ? "has-active-child" : ""}`}
          >
            {/* single menu with no childred*/}
            {!item.child && !item.isHeadr && (
              <>

                <NavLink
                  className={({ isActive }) => (isActive || (item.link && isNavLinkActive(item.link))) ? "menu-link active" : "menu-link"}
                  to={item.link}
                >
                  <span className="menu-icon flex-grow-0">
                    <Icon icon={item.icon} />
                  </span>
                  <div className="text-box flex-grow"> { labels[item.title.toLowerCase()] || item.title} </div>
                  {item.badge && <span className="menu-badge">{item.badge}</span>}
                </NavLink>

              </>
            )}
            {/* only for menulabel */}
            {item.isHeadr && !item.child && (
              <div className="menulabel"> { labels[item.title.toLowerCase()] || item.title} </div>
            )}
            {/* !!sub menu parent   */}

            {item.child && (
              <div
                className={`menu-link ${activeSubmenu === i
                  ? "parent_active not-collapsed"
                  : "collapsed"
                  } ${item.child && item.child.some(child =>
                    (child.childlink && isNavLinkActive(child.childlink)) ||
                    (child.multi_menu && child.multi_menu.some(multi => multi.multiLink && isNavLinkActive(multi.multiLink)))
                  ) ? "active-parent" : ""
                  } ${user?.organization_id == null ? "disabled" : ""}`}
                onClick={() =>
                  user?.organization_id == null ? null : toggleSubmenu(i)
                }
              >
                <div className="flex-1 flex items-start">
                  <span className="menu-icon">
                    <Icon icon={item.icon} />
                  </span>
                  <div className="text-box"> { labels[item.title.toLowerCase()] || item.title} </div>
                </div>
                <div className="flex-0">
                  <div
                    className={`menu-arrow transform transition-all duration-300 ${activeSubmenu === i ? " rotate-90" : ""
                      }`}
                  >
                    <Icon icon="heroicons-outline:chevron-right" />
                  </div>
                </div>
              </div>
            )}

            <Submenu
              activeSubmenu={activeSubmenu}
              item={item}
              i={i}
              toggleMultiMenu={toggleMultiMenu}
              activeMultiMenu={activeMultiMenu}
              labels={labels}
            />
          </li>
        ))}
      </ul>
    </>
  );
};

export default Navmenu;