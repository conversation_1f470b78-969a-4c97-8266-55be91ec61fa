// src/components/EdupakLoader.jsx
import React from "react";
import Loader from "../assets/images/logo/loader.jpeg";

const EdupakLoader = () => {
  return (
    <div className="fixed inset-0 flex items-center justify-center backdrop-blur-md">
      <div className="relative w-64 h-64">
        {/* Main loader container */}
        <div className="relative w-full h-full animate-pulse">
          {/* EduPack Image */}
          <img
            src={Loader}
            alt="EduPack Loader"
            className="w-full h-full object-contain opacity-90"
          />
          
          {/* Animated progress bar */}
          <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-[80%]">
            <div className="h-2 bg-gray-300 rounded-full overflow-hidden">
              <div className="h-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 animate-progress"></div>
            </div>
          </div>
          
          {/* Floating elements */}
          <div className="absolute inset-0">
            <div className="absolute top-0 left-[15%] w-4 h-4 bg-white rounded-full opacity-75 animate-float-fast"></div>
            <div className="absolute top-[20%] right-[10%] w-3 h-3 bg-blue-400 rounded-full opacity-75 animate-float-slow"></div>
            <div className="absolute bottom-[30%] left-[10%] w-2 h-2 bg-orange-300 rounded-full opacity-75 animate-float-slowest"></div>
          </div>
        </div>
      </div>

      {/* Injecting custom CSS styles */}
      <style jsx>{`
        @keyframes animate-progress {
          0% {
            width: 0%;
          }
          100% {
            width: 100%;
          }
        }

        @keyframes animate-float {
          0% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-10px);
          }
          100% {
            transform: translateY(0);
          }
        }

        @keyframes animate-float-slow {
          0% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-8px);
          }
          100% {
            transform: translateY(0);
          }
        }

        @keyframes animate-float-slower {
          0% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-6px);
          }
          100% {
            transform: translateY(0);
          }
        }

        .animate-progress {
          animation: animate-progress 2s ease-in-out forwards;
        }

        .animate-float-fast {
          animation: animate-float 1.5s ease-in-out infinite;
        }

        .animate-float-slow {
          animation: animate-float-slow 2s ease-in-out infinite;
        }

        .animate-float-slowest {
          animation: animate-float-slowest 2.5s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
};

export default EdupakLoader;
