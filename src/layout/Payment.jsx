import React, { useState, useEffect } from "react";
import Card from "@/components/ui/Card";
import Select from "@/components/ui/Select";
import { Formik, Form, Field } from "formik";
import {
  useGetApiQuery,
  usePostApiMutation,
} from "@/store/api/master/commonSlice";
import * as yup from "yup";

const PaymentPage = ({ organization }) => {
  const [pricePackage, setPricePackage] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState("bkash");

  const { data: packageList, isLoading, error } = useGetApiQuery(
    `admin/package-list`
  );

  const [postApi] = usePostApiMutation();



  const validationSchema = yup.object().shape({
    package_id: yup.string().required("Package is required"),
  });

  const initialValues = {
    package_id: organization?.last_payment?.package_id || "",
  };

  const selectPackage = (id) => {
    const selectedPackage = packageList?.find((item) => item.id == id);
    setPricePackage(selectedPackage);
  };

  useEffect(() => {
    if (packageList && organization?.last_payment?.package_id) {
      const selectedPackage = packageList.find(
        (item) => item.id == organization.last_payment.package_id
      );
      setPricePackage(selectedPackage);
    }
  }, [packageList, organization?.last_payment?.package_id]);

  const paymentValidationSchema = yup.object().shape({
    cardholder_name: yup.string().when("payment_method", {
      is: "card",
      then: yup.string().required("Cardholder Name is required"),
    }),
    card_number: yup.string().when("payment_method", {
      is: "card",
      then: yup
        .string()
        .matches(/^\d{16}$/, "Card number must be 16 digits")
        .required("Card Number is required"),
    }),
    expiration_date: yup.string().when("payment_method", {
      is: "card",
      then: yup
        .string()
        .matches(/^(0[1-9]|1[0-2])\/?([0-9]{2})$/, "Invalid expiration date")
        .required("Expiration Date is required"),
    }),
    cvv: yup.string().when("payment_method", {
      is: "card",
      then: yup
        .string()
        .matches(/^\d{3}$/, "CVV must be 3 digits")
        .required("CVV is required"),
    }),
    bkash_number: yup.string().when("payment_method", {
      is: "bkash",
      then: yup
        .string()
        .matches(/^\d{11}$/, "bKash number must be 11 digits")
        .required("bKash number is required"),
    }),
  });

  const paymentInitialValues = {
    payment_method: "bkash",
    cardholder_name: "",
    card_number: "",
    expiration_date: "",
    cvv: "",
    bkash_number: "",
  };

  const submitPayment = async (values) => {
    values.package_id = pricePackage?.id;
    const response = await postApi({
      end_point: "admin/make-payment-organization",
      body: values,
    });
    console.log(response.data);
  };

  return (
    <div className="relative">
      

        <div className="alert alert-danger mt-4 mb-4">
          <strong>Alert:</strong> Your package validity has expired. Please
          renew now to continue accessing the system.
        </div>


      <div className="grid md:grid-cols-2 grid-cols-1 gap-5 mt-16">
        <Card className="flex-1">
          <h3 className="text-2xl font-semibold mb-4">Select a Package</h3>
          {organization && (
            <Formik
              validationSchema={validationSchema}
              initialValues={initialValues}
            >
              {({ setFieldValue, errors, touched, values }) => (
                <Form>
                  <Select
                    label="Package"
                    placeholder="Select Package"
                    options={packageList?.map((item) => ({
                      label: item.plan_name,
                      value: item.id,
                    }))}
                    name="package_id"
                    value={values.package_id}
                    onChange={(e) => {
                      selectPackage(e.target.value);
                      setFieldValue("package_id", e.target.value);
                    }}
                    error={
                      errors.package_id && touched.package_id
                        ? errors.package_id
                        : null
                    }
                  />

                  {values.package_id && (
                    <div className="mt-4">
                      <h4 className="text-lg font-medium">
                        Selected Package Details
                      </h4>
                      <p>Plan Name: {pricePackage?.plan_name}</p>
                      <p>Price: {pricePackage?.price || "N/A"} BDT</p>
                    </div>
                  )}
                </Form>
              )}
            </Formik>
          )}
        </Card>

        <Card className="flex-1">
          <h3 className="text-2xl font-semibold mb-4">Payment Details</h3>
          <Formik
            validationSchema={paymentValidationSchema}
            initialValues={paymentInitialValues}
            onSubmit={(values) => {
              submitPayment(values);
            }}
          >
            {({ values, setFieldValue, errors, touched }) => (
              <Form>
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-2">
                    Payment Method
                  </label>
                  <Select
                    options={[
                      { label: "bKash", value: "bkash" },
                      { label: "Credit/Debit Card", value: "card" },
                    ]}
                    name="payment_method"
                    value={values.payment_method}
                    onChange={(e) => {
                      setFieldValue("payment_method", e.target.value);
                      setPaymentMethod(e.target.value);
                    }}
                  />
                </div>

                {paymentMethod === "card" && (
                  <>
                    {/* Card Payment Fields */}
                  </>
                )}

                {paymentMethod === "bkash" && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-2">
                      bKash Number
                    </label>
                    <Field
                      name="bkash_number"
                      type="text"
                      className="w-full border border-gray-300 rounded px-4 py-2"
                    />
                    {errors.bkash_number && touched.bkash_number && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors.bkash_number}
                      </p>
                    )}
                  </div>
                )}

                <button
                  type="submit"
                  className="w-full bg-blue-500 text-white rounded px-4 py-2 mt-4"
                >
                  Submit Payment
                </button>
              </Form>
            )}
          </Formik>
        </Card>
      </div>
    </div>
  );
};

export default PaymentPage;
