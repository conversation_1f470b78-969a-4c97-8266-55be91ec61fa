import React, { useState } from "react";
import { Icon } from "@iconify/react";
import logo from "../assets/images/promotion/logo.png";
import { Link, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import { Popover } from "@headlessui/react";
import img from "../assets/images/svg/Intersect.svg";
import Button from "@/components/ui/Button";
import { useTranslation } from 'react-i18next';

const resources = [
  {
    icon: "mdi:teach", // Teaching icon from Iconify
    title: "Coaching",
    description:
      "Discover personalized coaching programs to help you grow personally and professionally with expert guidance.",
    link: "/resources",
  },
  {
    icon: "mdi:account", // Individual icon from Iconify
    title: "Individual",
    description:
      "Explore resources tailored for individual development, focusing on self-improvement and personal skills.",
    link: "/resources",
  },
  {
    icon: "mdi:bank", // Bank icon from Iconify
    title: "Training for Banks",
    description:
      "Comprehensive training solutions for banking professionals to stay updated with industry practices and regulations.",
    link: "/resources",
  },
  {
    icon: "mdi:bullhorn", // Marketing icon from Iconify
    title: "Marketing Training",
    description:
      "Boost your marketing skills with in-depth training programs designed for real-world applications and success.",
    link: "/resources",
  },
  {
    icon: "mdi:heart-pulse", // Health icon from Iconify
    title: "Health",
    description:
      "Access health-related resources and training to improve well-being and promote a healthy lifestyle.",
    link: "/resources",
  },
];

const PromotionHeader = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user } = useSelector((state) => state.auth);
  const location = useLocation(); // Get the current location
  const [selectedResource, setSelectedResource] = useState(0);
  const { t, i18n } = useTranslation();

  // Function to check if the link is active
  const isActive = (path) => location.pathname === path;

  const handleChangeLanguage = (lang) => {
    const scrollPosition = window.scrollY; 
    i18n.changeLanguage(lang);
    localStorage.setItem("lang", lang);
    window.scrollTo(0, scrollPosition); 
  };

  const LanguageButton = ({ langCode, label }) => (
    <button
      onClick={() => handleChangeLanguage(langCode)}
      className={`block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 ${
        i18n.language === langCode ? "bg-gray-100 font-semibold" : ""
      }`}
    >
      {label}
    </button>
  );

  return (
    <header className="bg-white py-4 px-6 fixed w-full top-0 z-50 shadow-sm">
      <div className="max-w-7xl mx-auto">
        <nav className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <div className="">
              <Link to="/">
                <img
                  src={logo}
                  alt="Logo"
                  className="h-10 w-auto"
                />
              </Link>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              to="/"
              className={`${
                isActive("/")
                  ? "text-[#4C1D95] bg-[#F5F3FF] px-3 py-1 rounded-md font-medium"
                  : " text-[#4B5563]"
              } hover:text-[#4C1D95]`}
            >
              Home
            </Link>

            <Popover>
              {({ open }) => (
                <>
                  {/* <Popover.Button className="block text-md text-gray-600 hover:text-[#4C1D95] focus:outline-none">
                    Solutions
                    <Icon
                      icon="mdi:chevron-down"
                      className={`inline-block mb-0.5 w-5 h-5 text-gray-500 transition-transform duration-300 ${
                        open ? "rotate-180" : "rotate-0"
                      }`}
                    />
                  </Popover.Button> */}
                  <Popover.Panel className="absolute mt-8 bg-white left-1/2 -translate-x-1/2 w-[700px] border border-gray-200 rounded-lg shadow-lg">
                    <div className="flex">
                      <div className="w-[70%] p-3 h-80 rounded-br-[100px] rounded-l-lg bg-gray-900 flex flex-col justify-center overflow-hidden">
                        <p className="text-sm text-white pb-2">
                          Promotional Video
                        </p>
                   

                        <iframe
                          width="100%"
                          height="100%"
                          src={`https://www.youtube.com/embed/88jH_04zmIw`}
                          frameBorder="0"
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                          allowFullScreen
                          className="rounded-lg"
                        ></iframe>
      
                      </div>
                      <div className="p-3 flex flex-col justify-center gap-3 w-full">
                        <Link
                          className="block rounded-lg bg-gradient-to-r border border-transparent rounded-lg hover:border-indigo-400 transition bg-gray-100 flex items-center gap-2 font-semibold"
                          to="#"
                        >
                          <img src='http://localhost:3000/src/assets/images/promotion/logo.png' className="w-16 h-full object-contain ml-2" alt="" />
                          <p className="py-3 px-1">EduPack</p>
                        </Link>
                        <Link
                          className="block rounded-lg bg-gradient-to-r border border-transparent rounded-lg hover:border-indigo-400 transition bg-gray-100 flex items-center gap-2 font-semibold"
                          to="https://bacbontutors.com/" target="_blank"
                        >
                          <img src="https://bacbontutors.com/assets/img/BacBon-Tutors.png" className="w-16 h-full object-contain ml-2" alt="" />
                          <p className="py-3 px-1">BacBon Tutors</p>
                        </Link>
                        <Link
                          className="block rounded-lg bg-gradient-to-r border border-transparent rounded-lg hover:border-indigo-400 transition bg-gray-100 flex items-center gap-2 font-semibold"
                          to="https://www.bacbonschool.com/" target="_blank"
                        >
                          <img src="https://www.bacbonschool.com/assets/images/New-BacBon-School-trans-black.png" className="w-16 h-full object-contain ml-2" alt="" />
                          <p className="py-3 px-1">BacBon School</p>
                        </Link>
                 
                      </div>
                    </div>
                  </Popover.Panel>
                </>
              )}
            </Popover>



            <Popover>
              {({ open }) => (
                <>
                  <Popover.Button className="block text-md text-gray-600 hover:text-[#4C1D95] focus:outline-none transition-all duration-300">
                    Tutorial
                    <Icon
                      icon="mdi:chevron-down"
                      className={`inline-block mb-0.5 w-5 h-5 text-gray-500 transition-transform duration-300 ${
                        open ? "rotate-180" : "rotate-0"
                      }`}
                    />
                  </Popover.Button>

                  <Popover.Panel className="absolute mt-8 bg-white left-1/2 -translate-x-1/2 w-[700px] border border-gray-200 rounded-lg shadow-lg transition-all duration-300 ease-in-out transform">
                    <div className="flex items-center p-5 h-96">
                      <iframe
                        width="100%"
                        height="100%"
                        src={`https://www.youtube.com/embed/0vGRHgvjPqM`}
                        frameBorder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                        className="rounded-lg"
                      ></iframe>
                    </div>
                  </Popover.Panel>
                </>
              )}
            </Popover>

            
            <Link
              to="/pricing"
              className={`${
                isActive("/pricing")
                  ? "text-[#4C1D95]  bg-[#F5F3FF] px-3 py-1 rounded-md font-medium"
                  : "text-[#4B5563]"
              } hover:text-[#4C1D95]`}
            >
              Pricing
            </Link>
          </div>

          {/* CTA Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <div className="relative">
              <Popover className="relative">
                {({ open }) => (
                  <>
                    <Popover.Button
                      className={`${
                        open ? "text-[#4C1D95]" : "text-[#4B5563]"
                      } hover:text-[#4C1D95] flex items-center gap-2`}
                    >
                      <Icon icon="ic:baseline-language" />
                      {i18n.language == "en" ? "English" : i18n.language == "bn" ? "Bangla" : i18n.language == "jp" ? "Japanese" : i18n.language == "kr" ? "Korean" : "Arabic"}
                      <Icon
                        icon="mdi:chevron-down"
                        className={`inline-block mb-0.5 w-5 h-5 text-gray-500 transition-transform duration-300 ${
                          open ? "rotate-180" : "rotate-0"
                        }`}
                      />
                    </Popover.Button>

                    <Popover.Panel className="absolute z-10 mt-2 w-44 bg-white rounded-md shadow-lg">
                      <div className="py-1">
                        <LanguageButton langCode="en" label="English" />
                        <LanguageButton langCode="bn" label="Bangla" />
                        <LanguageButton langCode="jp" label="Japanese" />
                        <LanguageButton langCode="kr" label="Korean" />
                      </div>
                    </Popover.Panel>
                  </>
                )}
              </Popover>
            </div>
            
          </div>

          <div className="hidden md:flex items-center space-x-4">
            {user ? (
              <Link
                to="/dashboard"
                className="text-gray-900 hover:text-[#4C1D95] "
              >
                Dashboard
              </Link>
            ) : (
              <Link
                to="/login"
                className="text-gray-900 font-medium hover:text-[#4C1D95] "
              >
                {t("layout.login")}
              </Link>
            )}
            {/* <button className="bg-gradient-to-r from-indigo-500 to-purple-900 text-white px-4 py-2 rounded-md shadow-[-2px_4px_8px_0px_rgba(0,0,0,0.15)]">
              See Demo
            </button> */}
            {!user?.id && (
              <Link
                to="/try-new-lms"
                className="btn-sm bg-gradient-to-r from-indigo-500 to-purple-900 text-white px-8 py-3 rounded-md hover:opacity-90 transition-opacity"
              >
                {t("layout.start_free_trial")}
              </Link>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? (
              <Icon icon="mdi:close" className="w-6 h-6" />
            ) : (
              <Icon icon="mdi:menu" className="w-6 h-6" />
            )}
          </button>
        </nav>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden mt-4 pb-4">
            <div className="flex flex-col space-y-4">
              <a
                href="/"
                className={`${
                  isActive("/") ? "text-white" : "text-gray-900"
                } hover:text-indigo-600`}
              >
                Home
              </a>
              <a
                href="/demo"
                className={`${
                  isActive("/demo") ? "text-white" : "text-gray-900"
                } hover:text-indigo-600`}
              >
                Demo
              </a>
              <a
                href="/blog"
                className={`${
                  isActive("/blog") ? "text-white" : "text-gray-900"
                } hover:text-indigo-600`}
              >
                Blog
              </a>
              <a
                href="/pricing"
                className={`${
                  isActive("/pricing") ? "text-white" : "text-gray-900"
                } hover:text-indigo-600`}
              >
                Pricing
              </a>
              <div className="pt-4 space-y-4">
                <button className="w-full text-center text-gray-900 hover:text-[#4C1D95]">
                  Sign in
                </button>
                <button className="w-full bg-gradient-to-r from-indigo-500 to-purple-900 text-white px-4 py-2 rounded-md">
                  See Demo
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default PromotionHeader;
