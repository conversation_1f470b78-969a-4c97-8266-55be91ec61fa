import React, { useEffect, Suspense } from "react";
import { Outlet, useNavigate } from "react-router-dom";
import Header from "@/components/partials/header";
import useWidth from "@/hooks/theme/useWidth";
import useSidebar from "@/hooks/theme/useSidebar";
import useContentWidth from "@/hooks/theme/useContentWidth";
import useMenuHidden from "@/hooks/theme/useMenuHidden";
import Breadcrumbs from "@/components/ui/Breadcrumbs";
import MobileMenu from "../components/partials/sidebar/MobileMenu";
import useMobileMenu from "@/hooks/theme/useMobileMenu";
import MobileFooter from "@/components/partials/footer/MobileFooter";
import Footer from "@/components/partials/footer";
import { ToastContainer } from "react-toastify";
import { useSelector } from 'react-redux';
// import { setOrganization } from "@/store/common";
// import useFetch from "@/hooks/useFetch";
import Loading from "@/components/Loading";
import PromotionHeader from "./PromotionHeader";

const PromotionalLayout = () => {
  
//   const { organization } = useSelector((state) => state.commonSlice);

  const { width, breakpoints } = useWidth();
  const [collapsed] = useSidebar();
  const navigate = useNavigate();
  // const token = localStorage.getItem("_token");
  // useEffect(() => {
  //   if (!isAuth) {
  //     navigate("login");
  //   }
  // }, [token, navigate]);

  const switchHeaderClass = () => {
    if (menuHidden) {
      return "ltr:ml-0 rtl:mr-0";
    } else if (collapsed) {
      return "ltr:ml-[72px] rtl:mr-[72px]";
    } else {
      return "ltr:ml-[0px] rtl:mr-[248px]";
    }
  };

  // content width
  const [contentWidth] = useContentWidth();

  const [menuHidden] = useMenuHidden();
  // mobile menu
  const [mobileMenu, setMobileMenu] = useMobileMenu();

  return (
    <>
      <ToastContainer />
      <div
        className={`content-wrapper transition-all duration-150 ${width > 1280 ? switchHeaderClass() : ""
          }`}
      >
        {/* md:min-h-screen will h-full*/}
        <div >
          <div
            className={
              contentWidth === "boxed" ? "container mx-auto" : "container-fluid"
            }
          >
            <Suspense fallback={<Loading />}>
              <PromotionHeader/>
              <div>
                <Outlet />
              </div>
              
            </Suspense>
          </div>
        </div>
        <div className="">
        <Footer />

        </div>
      </div>
      {width < breakpoints.md && <MobileFooter />}
    </>
  );
};

export default PromotionalLayout;
