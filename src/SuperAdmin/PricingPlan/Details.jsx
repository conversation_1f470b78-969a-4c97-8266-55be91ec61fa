import React, { useState } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import {
  useGetApiQuery,
  useUpdateApiMutation,
} from "@/store/api/master/commonSlice";
import Badge from "@/components/ui/Badge";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";

const OrganizationDetails = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const {
    data,
    isLoading,
    isFetching,
    error: getDataError,
  } = useGetApiQuery(`admin/package-details/${id}`);
  const [updateApi, { isLoading: isUpdating, error: updateError }] =
    useUpdateApiMutation();
  const [isEditing, setIsEditing] = useState(false);
  const [packageData, setPackageData] = useState(null);

  React.useEffect(() => {
    if (data) {
      setPackageData({
        plan_name: data.plan_name,
        description: data.description,
        price: data.price,
        sale_price: data.sale_price,
        dollar_price: data.dollar_price,
        dollar_sale_price: data.dollar_sale_price,
        yen_price: data.yen_price,
        yen_sale_price: data.yen_sale_price,
        krw_price: data.krw_price,
        krw_sale_price: data.krw_sale_price,
        features: JSON.parse(data.features),
        disabled_features: data.disabled_features
          ? JSON.parse(data.disabled_features)
          : [],
      });
    }
  }, [data]);

  if (isLoading || isFetching || !packageData) {
    return (
      <div className="text-center py-10">Loading organization details...</div>
    );
  }

  if (getDataError) {
    return (
      <div className="text-center py-10 text-red-500">Error loading data</div>
    );
  }

  const handleChange = (key, value) => {
    setPackageData({ ...packageData, [key]: value });
  };

  const handleFeatureChange = (index, value, disabled = false) => {
    const updated = [
      ...(disabled ? packageData.disabled_features : packageData.features),
    ];
    updated[index] = value;
    handleChange(disabled ? "disabled_features" : "features", updated);
  };

  const addFeature = (disabled = false) => {
    const updated = [
      ...(disabled ? packageData.disabled_features : packageData.features),
      "",
    ];
    handleChange(disabled ? "disabled_features" : "features", updated);
  };

  const removeFeature = (index, disabled = false) => {
    const updated = [
      ...(disabled ? packageData.disabled_features : packageData.features),
    ];
    updated.splice(index, 1);
    handleChange(disabled ? "disabled_features" : "features", updated);
  };

  const handleSave = async () => {
    let formData = new FormData();
    Object.keys(packageData).forEach((key) => {
      formData.append(key, packageData[key]);
    });
    const response = await updateApi({
      end_point: `admin/super-admin-update-package/${id}`,
      body: formData,
    });
    console.log("API Response:", response);
    setIsEditing(false);
  };

  return (
    <div>
      <div className="flex items-center mb-4">
        <Link
          to="/pricing-plan"
          className="btn btn-secondary btn-sm flex items-center gap-1"
        >
          <Icon icon="heroicons:chevron-left" className="w-5 h-5" />
          Back
        </Link>
      </div>
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between">
          {!isEditing ? (
            <h2 className="text-2xl font-semibold">
              {packageData.plan_name}
              <Badge
                className={`${
                  data.is_active
                    ? "bg-success-500 text-white"
                    : "bg-danger-500 text-white"
                } ml-2`}
              >
                {data.is_active ? "Active" : "Inactive"}
              </Badge>
            </h2>
          ) : (
            <input
              value={packageData.plan_name}
              onChange={(e) => handleChange("plan_name", e.target.value)}
              className="text-2xl font-semibold border-none outline-none bg-transparent focus:bg-gray-100"
            />
          )}

          <div className="flex gap-2">
            {!isEditing ? (
              <Button
                className="btn btn-primary btn-sm"
                onClick={() => setIsEditing(true)}
              >
                Edit
              </Button>
            ) : (
              <>
                <Button onClick={handleSave} className="btn btn-success btn-sm">
                  Save
                </Button>
                <Button
                  onClick={() => setIsEditing(false)}
                  className="btn btn-primary btn-sm"
                >
                  Cancel
                </Button>
              </>
            )}
          </div>
        </div>

        {!isEditing ? (
          <p className="text-gray-600 mt-4">{packageData.description}</p>
        ) : (
          <textarea
            value={packageData.description}
            onChange={(e) => handleChange("description", e.target.value)}
            className="w-full border-none outline-none bg-transparent resize-none focus:bg-gray-100"
            rows={3}
          />
        )}

        <div className="mt-6 grid grid-cols-4 gap-6">
          {["price", "dollar_price", "yen_price", "krw_price"].map((field) => {
            const saleField = field.replace("price", "sale_price");
            const label = {
              price: "Price in BDT",
              dollar_price: "Price in USD",
              yen_price: "Price in JPY",
              krw_price: "Price in KRW",
            }[field];
            const currency = {
              price: "৳",
              dollar_price: "$",
              yen_price: "¥",
              krw_price: "₩",
            }[field];

            return (
              <div key={field}>
                <h3 className="font-semibold text-2xl">{label}</h3>
                {!isEditing ? (
                  <div className="flex gap-2">
                    <p className="text-gray-400 line-through">
                      {currency + packageData[field]}
                    </p>
                    <p>{currency + packageData[saleField]}</p>
                  </div>
                ) : (
                  <div className="grid gap-6">
                    <div className="flex flex-col space-y-1.5">
                      <label
                        htmlFor={field}
                        className="text-sm font-medium text-gray-700"
                      >
                        Regular Price
                      </label>
                      <input
                        type="number"
                        id={field}
                        value={packageData[field]}
                        onChange={(e) =>
                          handleChange(field, e.target.value)
                        }
                        className="w-full border-none outline-none bg-transparent focus:bg-gray-100"
                        placeholder="Enter Regular Price"
                      />
                    </div>

                    <div className="flex flex-col space-y-1.5">
                      <label
                        htmlFor={saleField}
                        className="text-sm font-medium text-gray-700"
                      >
                        Offer Price
                      </label>
                      <input
                        type="number"
                        id={saleField}
                        value={packageData[saleField]}
                        onChange={(e) =>
                          handleChange(saleField, e.target.value)
                        }
                        className="w-full border-none outline-none bg-transparent focus:bg-gray-100"
                        placeholder="Enter Offer Price"
                      />
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Features */}
        <div className="mt-6">
          <h3 className="font-semibold text-2xl">Features</h3>
          <ul className="ml-6">
            {packageData.features.map((feature, index) => (
              <li key={index} className="flex items-center gap-2 w-full">
                {!isEditing ? (
                  <span className="text-gray-600 list-disc">{feature}</span>
                ) : (
                  <>
                    <input
                      value={feature}
                      onChange={(e) =>
                        handleFeatureChange(index, e.target.value)
                      }
                      className="border-none outline-none bg-transparent focus:bg-gray-100 w-full"
                    />
                    <Button
                      onClick={() => removeFeature(index)}
                      size="sm"
                      variant="destructive"
                      className="btn-sm"
                    >
                      X
                    </Button>
                  </>
                )}
              </li>
            ))}
          </ul>
          {isEditing && (
            <Button onClick={() => addFeature()} className="mt-2" size="sm">
              + Add Feature
            </Button>
          )}
        </div>

        {/* Disabled Features */}
        <div className="mt-6">
          <h3 className="font-semibold text-red-600 text-2xl">
            Disabled Features
          </h3>
          <ul className="ml-6 text-red-600">
            {packageData.disabled_features.map((feature, index) => (
              <li key={index} className="flex items-center gap-2  w-full">
                {!isEditing ? (
                  <span className="list-disc">{feature}</span>
                ) : (
                  <>
                    <input
                      value={feature}
                      onChange={(e) =>
                        handleFeatureChange(index, e.target.value, true)
                      }
                      className="border-none outline-none bg-transparent focus:bg-gray-100 w-full"
                    />
                    <Button
                      onClick={() => removeFeature(index, true)}
                      size="sm"
                      variant="destructive"
                      className="btn-sm"
                    >
                      X
                    </Button>
                  </>
                )}
              </li>
            ))}
          </ul>
          {isEditing && (
            <Button onClick={() => addFeature(true)} className="mt-2" size="sm">
              + Add Disabled Feature
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrganizationDetails;
