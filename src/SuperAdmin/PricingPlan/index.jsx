import React, { useState } from "react";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { Link } from "react-router-dom";
import Delete from "./Delete";
const PricingPlan = () => {

  // Get packages
  const { data, isLoading, isFetching, error } = useGetApiQuery(`admin/package-list`);

  const [deleteData, setDeleteData] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const packages = data || [];
  // Prepare table rows
  const tableData = packages?.map((pkg, index) => ({
    plan_name: (
      <Link
        to={`/pricing-plan-details/${pkg.id}`}
        style={{ color: "#3498db", textDecoration: "none" }}
        onMouseOver={(e) => e.target.style.textDecoration = "underline"}
        onMouseOut={(e) => e.target.style.textDecoration = "none"}
      >
        {pkg.plan_name}
      </Link>
    ),
    price: `${pkg.price} ৳`,
    sale_price: `${pkg.sale_price} ৳`,
    trial_period_days: `${pkg.trial_period_days} Days`,
    is_popular: (
      <Badge
        className={pkg.is_popular ? "bg-primary-500 text-white" : "bg-slate-300 text-gray-700"}
      >
        {pkg.is_popular ? "Popular" : "Standard"}
      </Badge>
    ),
    support_level: pkg.support_level,
    

  }));

  // Define table columns
  const columns = [
    { label: "Plan", field: "plan_name" },
    { label: "Price", field: "price" },
    { label: "Sale Price", field: "sale_price" },
    { label: "Trial", field: "trial_period_days" },
    { label: "Support Level", field: "support_level" },
    { label: "Popular", field: "is_popular" },
    { label: "Action", field: "" },
  ];

 
   const actions = [
     {
       name: "Delete",
       icon: "heroicons-outline:trash",
       onClick: (val) => {
         setDeleteData(data[val]);
         setShowDeleteModal(true);
       },
     },
   ];
 

  return (
    <>
    
    <BasicTablePage
      tableHeaderExtra={<Link to="/pricing-plan-create" className="btn btn-primary btn-sm">Create New Plan</Link>}
      loading={isLoading || isFetching}
      title="Pricing Plan List"
      actions={actions}
      columns={columns}
      data={tableData}
      filter={false}
    />
    {showDeleteModal && <Delete showDeleteModal={showDeleteModal} setShowDeleteModal={setShowDeleteModal} data={deleteData} /> }
    </>
  );
};

export default PricingPlan;
