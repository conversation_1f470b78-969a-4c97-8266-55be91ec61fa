import { useState } from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { usePostApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";

const ChangePassword = ({ changePasswordModal, setChangePasswordModal, user }) => {
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [postApi, { isLoading }] = usePostApiMutation();

  const onSubmit = async () => {
    if (!password) {
      setError("Password is required");
      return;
    }

    if (password.length < 8) {
      setError("Password must be at least 8 characters");
      return;
    }

    try {
      setError(""); // Clear error if valid
      await postApi({
        end_point: `/admin/change-password`,
        body: { id: user?.id, password },
      }).unwrap();
      setChangePasswordModal(false);
      setPassword("");
    } catch (err) {
        setChangePasswordModal(false);
      setError("Something went wrong. Please try again.");
    }
  };

  return (
    <Modal
      activeModal={changePasswordModal}
      onClose={() => setChangePasswordModal(false)}
      title="Change Password"
      className="max-w-xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setChangePasswordModal(false)}
        />
      }
    >
      <div className="space-y-4">
        <p className="text-sm text-slate-600">
          Change password for <b>{user?.name}</b> ({user?.email})
        </p>

        <div>
          <input
            type="text"
            className="input w-full border mt-2 p-2"
            placeholder="Enter new password"
            value={password}
            onChange={(e) => {
              setPassword(e.target.value);
              setError(""); // Clear error as user types
            }}
          />
          {error && (
            <p className="text-red-500 text-sm mt-1">{error}</p>
          )}
        </div>

        <div className="flex justify-end gap-3 pt-4">
          <Button
            type="button"
            className="btn btn-outline"
            onClick={() => setChangePasswordModal(false)}
          >
            Cancel
          </Button>
          <Button
            type="button"
            isLoading={isLoading}
            className="btn btn-primary"
            onClick={onSubmit}
          >
            Update Password
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ChangePassword;
