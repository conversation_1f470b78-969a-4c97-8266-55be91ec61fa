import React, { useState } from "react";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { Link } from "react-router-dom";
import Icon from "@/components/ui/Icon";

const Organizations = () => {
  const [apiParam, setApiParam] = useState("");
  const { data, isLoading, isFetching } = useGetApiQuery(`admin/super-admin-organization-list${apiParam}`);

  const organizations = data?.data || [];
  const changePage = (val) => {
    setApiParam(val);
  };

  const handleFilterChange = (searchParam) => {
    if (searchParam.includes('?search=')) {
      setApiParam(searchParam);
    } else {
      setApiParam(searchParam);
    }
  };
  const tableData = organizations.map((org) => ({
    name: (
      <Link
        to={`/organization-details/${org.id}`}
        className="flex items-center text-blue-500 hover:text-blue-700 group"
        title="Click to view organization details"
      >
        {org.logo && (
          <img
            src={`${import.meta.env.VITE_ASSET_HOST_URL}${org.logo}`}
            alt={`${org.name} logo`}
            className="w-16 h-auto rounded-full mr-2 object-cover"
          />
        )}
        <div className="flex items-center">
          <span className="group-hover:underline">{org.name}</span>
          <Icon
            icon="heroicons-outline:eye"
            className="ml-1 text-blue-400 group-hover:text-blue-600 transition-colors"
            size={16}
          />
        </div>
      </Link>
    ),
    email: org.email,
    contact: org.contact_no || org.hotline_number || 'N/A',
    status: (
      <Badge
        className={
          org.is_active
            ? "bg-success-500 text-white"
            : "bg-danger-500 text-white"
        }
      >
        {org.is_active ? "Active" : "Inactive"}
      </Badge>
    ),
    host_url: (
      <div className="flex items-center gap-2">
        <a
          href={org.host_url}
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center text-blue-500 hover:text-blue-700 hover:underline transition duration-150"
        >
          <Icon className="mr-1" icon="heroicons-outline:eye" size={24} /> Site
        </a>
        /
        <Link
          to={`/organization-dashboard/${org.id}`}
          className="text-blue-500 hover:text-blue-700 hover:underline transition duration-150"
        >
          Dashboard
        </Link>
      </div>
    ),
    created_at: org.created_at ? new Date(org.created_at).toLocaleString("en-IN", { year: "numeric", month: "short", day: "numeric" }) : 'N/A',
  }));

  const columns = [
    { label: "Name", field: "name" },
    { label: "Email", field: "email" },
    { label: "Contact", field: "contact" },
    { label: "Status", field: "status" },
    { label: "View", field: "host_url" },
    { label: "Created At", field: "created_at" },
  ];

  const actions = [];

  return (
    <BasicTablePage
      loading={isLoading || isFetching}
      title="Organization List"
      actions={actions}
      columns={columns}
      data={tableData}
      changePage={changePage}
      filter={true}
      setFilter={handleFilterChange}
      currentPage={data?.current_page}
      totalPages={Math.ceil(data?.total / data?.per_page)}
    />
  );
};

export default Organizations;
