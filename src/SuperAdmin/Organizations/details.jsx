import React, { useState } from "react";
import { useParams } from "react-router-dom";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Badge from "@/components/ui/Badge";
import ChangePassword from "./ChangePassword";
import { Link } from "react-router-dom";

const OrganizationDetails = () => {
  const { id } = useParams();
  const [changePasswordModal, setChangePasswordModal] = useState(false);
  const [user, setUser] = useState(false);
  const { data, isLoading, isFetching, error } = useGetApiQuery(`admin/super-admin-organization-details/${id}`);

  const org = data;

  if (isLoading || isFetching) {
    return <div className="text-center py-10">Loading organization details...</div>;
  }

  if (error || !org) {
    return <div className="text-center py-10 text-red-500">Error loading data</div>;
  }

  return (
    <div className="">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <div className="flex items-center justify-center w-24 h-24 rounded-full bg-gray-200 border">
            {org.logo ? (
              <img
                src={`${import.meta.env.VITE_ASSET_HOST_URL}${org.logo}`}
                alt="Logo"
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              <span className="text-gray-500 text-sm text-center">No Logo</span>
            )}
          </div>
          <div>
            <h1 className="text-2xl font-bold">{org.name}</h1>
            <Badge className={org.is_active ? "bg-green-500" : "bg-red-500"}>
              {org.is_active ? "Active" : "Inactive"}
            </Badge>
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="w-40 h-20 bg-gray-100 rounded shadow flex items-center justify-center">
            {org.banner ? (
              <img
                src={`${import.meta.env.VITE_ASSET_HOST_URL}${org.banner}`}
                alt="Banner"
                className="w-full h-full object-cover rounded"
              />
            ) : (
              <span className="text-gray-500 text-sm">No Banner</span>
            )}
          </div>
          <Link to={`/organization-dashboard/${id}`}>
            <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
              Dashboard
            </button>
          </Link>
        </div>
      </div>

      {/* Basic Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 bg-white p-6 rounded shadow mb-8">
        <div>
          <p><strong>Short Name:</strong> {org.short_name}</p>
          <p><strong>Email:</strong> {org.email}</p>
          <p><strong>Contact No:</strong> {org.contact_no}</p>
          <p><strong>Hotline:</strong> {org.hotline_number}</p>
        </div>
        <div>
          <p><strong>Host URL:</strong> 
            <a
              href={org.host_url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 hover:underline ml-1"
            >
              {org.host_url}
            </a>
          </p>
          <p><strong>Headline:</strong> {org.headline}</p>
          <p><strong>Sub-headline:</strong> {org.sub_headline}</p>
          <p><strong>Created At:</strong> {new Date(org.created_at).toLocaleString("en-IN", { year: "numeric", month: "short", day: "numeric" })}</p>
        </div>
      </div>

      {/* Last Payment */}
      <div className="bg-white p-6 rounded shadow mb-8">
        <h2 className="text-lg font-semibold mb-4">Last Payment Info</h2>
        {org.last_payment ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <p><strong>Package:</strong> {org?.last_payment?.package?.plan_name}</p>
            <p><strong>Amount Paid:</strong> {org?.last_payment?.paid_amount} {org?.last_payment?.currency}</p>
            <p><strong>Expiry Date:</strong> {new Date(org?.last_payment?.expiry_date).toLocaleString("en-IN", { year: "numeric", month: "short", day: "numeric" })}</p>
            <p><strong>Trial Taken:</strong> {org?.last_payment?.is_trial_taken ? "Yes" : "No"}</p>
          </div>
        ) : (
          <p className="text-gray-500">No payment info available.</p>
        )}
      </div>

      {/* Admins */}
      <div className="bg-white p-6 rounded shadow mb-8">
        <h2 className="text-lg font-semibold mb-4">Admins</h2>
        {org.admins && org.admins.length > 0 ? (
          <table className="min-w-full border">
            <thead>
              <tr className="bg-gray-100 text-left">
                <th className="px-4 py-2 border">Name</th>
                <th className="px-4 py-2 border">Email</th>
                <th className="px-4 py-2 border">Username</th>
                <th className="px-4 py-2 border">Contact No</th>
                <th className="px-4 py-2 border">Actions</th>
              </tr>
            </thead>
            <tbody>
              {org.admins.map((admin) => (
                <tr key={admin.id} className="hover:bg-gray-50">
                  <td className="px-4 py-2 border">{admin.name}</td>
                  <td className="px-4 py-2 border">{admin.email}</td>
                  <td className="px-4 py-2 border">{admin.username}</td>
                  <td className="px-4 py-2 border">{admin.contact_no}</td>
                  <td className="px-4 py-2 border">
                    <button
                      type="button"
                      onClick={() => {
                        setChangePasswordModal(true);
                        setUser(admin);
                      }}
                      className="text-blue-500 hover:underline focus:outline-none"
                    >
                      Change Password
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p className="text-gray-500">No admins found.</p>
        )}
      </div>

      {/* All Payments */}
      <div className="bg-white p-6 rounded shadow mb-8">
        <h2 className="text-lg font-semibold mb-4">All Payments</h2>
        {org.all_payments && org.all_payments.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full border">
              <thead>
                <tr className="bg-gray-100 text-left">
                  <th className="px-4 py-2 border">Package</th>
                  <th className="px-4 py-2 border">Amount</th>
                  <th className="px-4 py-2 border">Discount</th>
                  <th className="px-4 py-2 border">Currency</th>
                  <th className="px-4 py-2 border">Expiry</th>
                  <th className="px-4 py-2 border">Trial?</th>
                  <th className="px-4 py-2 border">Date</th>
                </tr>
              </thead>
              <tbody>
                {org.all_payments.map((p) => (
                  <tr key={p.id} className="hover:bg-gray-50">
                    <td className="px-4 py-2 border">{p.package?.plan_name}</td>
                    <td className="px-4 py-2 border">{p.paid_amount}</td>
                    <td className="px-4 py-2 border">{p.discount_amount}</td>
                    <td className="px-4 py-2 border">{p.currency}</td>
                    <td className="px-4 py-2 border">
                      {new Date(p.expiry_date).toLocaleDateString()}
                    </td>
                    <td className="px-4 py-2 border">{p.is_trial_taken ? "Yes" : "No"}</td>
                    <td className="px-4 py-2 border">
                      {new Date(p.created_at).toLocaleString("en-IN", { year: "numeric", month: "short", day: "numeric" })}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-gray-500">No payment history found.</p>
        )}
      </div>

      {changePasswordModal && (
        <ChangePassword
          changePasswordModal={changePasswordModal}
          setChangePasswordModal={setChangePasswordModal}
          user={user}
          orgId={org.id}
        />
      )}
    </div>
  );
};

export default OrganizationDetails;