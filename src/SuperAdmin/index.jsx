import React from "react";
import { useGetApiQuery } from "@/store/api/master/commonSlice";


const SuperAdmin = () => {
    const { data, isLoading, isFetching, error } = useGetApiQuery(`admin/super-admin-dashboard`);
    console.log(data);

    if (isLoading || isFetching) {
      return <div>Loading...</div>;
    }
  
    if (error) {
      return (
        <div>
          <h1>Error loading data</h1>
          <p>{error?.data?.message || "Something went wrong."}</p>
        </div>
      );
    }
  return (
    <div>
      <h1>Hello Super Admin</h1>
    </div>
  );
};

export default SuperAdmin;
